> **Prompt to AI:**

I am working on migrating the frontend of a full-stack Laravel project to **Next.js**, while the backend will eventually move to Node.js with MongoDB (but for now remains Laravel). I want to rewrite the frontend **fully in Next.js** using **Material UI** as the component library.

Please help me **migrate the Laravel Blade-based frontend to a Next.js structure**. Here's what I need:

---

### 📦 Project Requirements

1. **Frontend Framework**: Next.js (latest stable version)
2. **Component Library**: Material UI (MUI, latest version)
3. **Styling Preference**: CSS Modules or Emotion (used by default with MUI)
4. **Routing**: Convert Laravel routes/views to Next.js file-based routing
5. **API Consumption**: For now, fetch data from the existing Laravel backend using Axios or Fetch
6. **Authentication**: Assume token-based authentication (JWT/local storage)
7. **State Management**: Keep it minimal — use React Context or basic hooks if needed
8. **Directory Structure**:

   ```
   /pages
     - index.js
     - about.js
     - dashboard.js
     - login.js
   /components
     - Navbar.js
     - Footer.js
     - CardComponent.js
   /styles
     - globals.css
   ```

---

### 🛠️ Task Instructions for Migration

1. **Analyze Laravel Blade templates** and convert them into React components using Material UI elements. Use clean component structure and modular styling.

2. **Map all Laravel route-based views** (like `/dashboard`, `/login`, `/profile`, etc.) to individual `.js` pages in Next.js under the `/pages` directory.

3. **Migrate layout components** like navbar, footer, and sidebar into reusable React components inside the `/components` folder using MUI equivalents.

4. **Set up Material UI theme** and base styling using `ThemeProvider` and `CssBaseline`.

5. **Integrate Axios or Fetch** to make API calls to existing Laravel endpoints. Use `.env.local` for API base URLs.

6. **Example Conversions**:

   * Blade `<x-card>` or `@include('components.card')` should be turned into `<CardComponent />` using MUI `<Card />`
   * `@csrf` tokens used in forms should be handled using JWT or token headers in API requests

7. **For forms** like login or registration:

   * Use Material UI’s `<TextField />`, `<Button />`, and form validation
   * Show error handling via helperText or Snackbar

8. **Migrate Auth Logic**:

   * If the current Laravel app uses session-based auth, set up the Next.js app to store tokens and send them via headers
   * Use `getServerSideProps` or middleware in Next.js to redirect unauthorized users (if SSR is used)

---

### 🧠 Extras

If you need to retain the current Laravel styling or structure, help convert Laravel Blade logic (like `@if`, `@foreach`, `{{ $variable }}`) to JSX syntax and conditional rendering in React.

Also, help identify **reusable UI components** from Laravel and suggest how to abstract them in React using Material UI.



## **MIGRATION STATUS TRACKING**

### **Phase 1: Project Setup & Foundation** ✅ COMPLETED
- [x] Next.js project initialization
- [x] Material UI setup with theme
- [x] Project structure creation
- [x] Basic routing setup
- [x] TypeScript configuration
- [x] Dummy data structure
- [x] Theme provider setup

### **Phase 2: Core Layout & Navigation** ✅ COMPLETED - PIXEL PERFECT
- [x] AdminLTE layout with exact styling
- [x] Pixel-perfect sidebar with treeview functionality
- [x] Exact AdminLTE top navigation bar
- [x] Role-based navigation menu with expand/collapse
- [x] Responsive design matching original
- [x] Dashboard page with exact Laravel structure
- [x] AdminLTE login page (pixel-perfect replica)
- [x] Authentication routing
- [x] Bootstrap grid system implementation
- [x] Font Awesome icons integration
- [x] AdminLTE skin-blue theme exact replica

### **Phase 3: Authentication & User Management** ⏳ PENDING
- [ ] Login/logout pages
- [ ] User management interface
- [ ] Role management
- [ ] Permission system

### **Phase 4: Dashboard & Analytics** ⏳ PENDING
- [ ] Main dashboard
- [ ] Charts and statistics
- [ ] Reports interface

### **Phase 5: Course Management** ⏳ PENDING
- [ ] Course listing and CRUD
- [ ] Category management
- [ ] Lesson management
- [ ] Quiz system

### **Phase 6: Student & Teacher Management** ⏳ PENDING
- [ ] Student interface
- [ ] Teacher interface
- [ ] Class scheduling
- [ ] Assignment system

### **Phase 7: Additional Features** ⏳ PENDING
- [ ] Community posts
- [ ] Live classes
- [ ] File uploads
- [ ] Notifications

---

Important Note

> Recreate the Laravel project's frontend exactly as it is, using Next.js with Material UI.
>
> ✅ Do **not** integrate with the actual backend APIs for now — instead, use **dummy JSON data** that mimics the real API responses.
>
> ✅ The new implementation should **strictly mirror** the existing Laravel app in terms of:
>
> * Layout and structure
> * Look and feel (including colors, fonts, spacing, etc.)
> * All functionalities and interactivity seen in the Laravel version
>
> 🚫 Do **not** add or change any features that are **not present** in the Laravel project.
>
> 🎯 Goal: Achieve a **100% match** in design and behavior with the current Laravel application using dummy data.