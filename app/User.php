<?php
namespace App;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;
use Hash;
use App\Models\Franchise;
use App\Models\StudentClass;
use App\Models\UserPoint;
use App\Models\UserSubscription;

/**
 * Class User
 *
 * @package App
 * @property string $name
 * @property string $email
 * @property string $password
 * @property string $remember_token
*/
class User extends Authenticatable
{
    use Notifiable;
    use HasRoles;

    protected $table = 'users';
    protected $guarded = [];
    //protected $fillable = ['name', 'email', 'phone', 'password', 'remember_token'];

    public function franchise_data()
    {
        return $this->hasOne(Franchise::class, 'user_id', 'franchise_user_id');
    }

    public function subscription_data()
    {
        return $this->hasOne(UserSubscription::class, 'user_id', 'id')->where('paymentStatus', 1)->orderBy('id', 'DESC');
    }

    public function class_data()
    {
        return $this->hasOne('App\Models\StudentClass', 'id', 'class_id');
    }

    public function user_points_debit()
    {
        return $this->hasMany('App\Models\UserPoint', 'user_id')->where('transaction_type',config('constant.TRANSACTION_TYPE.DEBIT'));
    }

    public function user_points_credit()
    {
        return $this->hasMany('App\Models\UserPoint', 'user_id')->where('transaction_type',config('constant.TRANSACTION_TYPE.CREDIT'));
    }

    /**
     * Hash password
     * @param $input
     */
    public function setPasswordAttribute($input)
    {
        if ($input)
            $this->attributes['password'] = app('hash')->needsRehash($input) ? Hash::make($input) : $input;
    }
    
    
    public function role()
    {
        return $this->belongsToMany(Role::class, 'role_user');
    }

    public function getrole()
    {
        return $this->hasMany(Role::class, 'role_user');
    }

    public function generateToken()
    {
        $this->api_token = str_random(60);
        $this->save();

        return $this->api_token;
    }


    
    
    
}
