<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use App\Models\Ages;


class AgesController extends Controller
{	
	public function index()
	{
		$data = Ages::where('deleted',0)->orderBy('id', 'desc');
		$totalData = $data->count();
		$data = $data->paginate(50);

		return view('admin.ages.index', compact('data'));
	}

	public function create(){
		return View('admin.ages.create');
	}

	public function store(Request $request){
			$validator = Validator::make($request->all(), [
			'start_age' => 'required',
			'end_age' => 'required',
		]);
		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$age  = new Ages();
			$age->start_age = $request->start_age;
			$age->end_age = $request->end_age;
			$age->save();
			\Session::flash('msg', 'Age Added Successfully.');
			return back();
		}
	}
	
	public function edit($id){
		$data  = Ages::findOrFail($id);
		return view('admin.ages.edit',compact('data'));
	}

	public function delete($id){
		Ages::where('id',$id)->update(array('deleted'=>1));
		\Session::flash('msg', 'Age Deleted Successfully.');
		return back();
	}

	public function updateStatus($id,$status){
		$data  = Ages::findOrFail($id);
		 $data->status=$status;
        $data->update();
        return redirect()->back();
	}
	public function update(Request $request){

		$id = $request->id;
		$data  = Ages::findOrFail($id);
		$data->start_age = isset($request->start_age) ? $request->start_age : $data->start_age;
		$data->end_age = isset($request->end_age) ? $request->end_age : $data->end_age;
        $data->update();
    	\Session::flash('msg', 'Age Updated Successfully.');
		return back();

	}
}
?>