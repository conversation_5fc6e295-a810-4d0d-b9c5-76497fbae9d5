<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Banner;
use App\Models\Assignment;
use App\Models\Chapter;
use App\Models\ChatGroup;
use App\Models\Courses;
use App\Models\Lession;
use App\Models\CommunityPost;
use App\Models\Quiz;
use App\Models\StudentClass;
use App\Models\Subscription;
use App\Models\User;

class BannerController extends Controller
{
	public function index(Request $request)
	{
		$class_id = ($request->class_id) ? $request->class_id : '';
		$module_name = $request->module_name;
		$status = $request->status;
		$classes = StudentClass::orderBy("id", "ASC")->get();
		$data = Banner::orderBy("sort_id", "ASC")
	                ->Where(function($query) use ($class_id) {
	                    if (isset($class_id) && !empty($class_id)) {
	                        $query->where('class_id', $class_id);
	                    }
	                })
	                ->Where(function($query) use ($module_name) {
	                    if (isset($module_name) && !empty($module_name)) {
	                        $query->where('module_name', $module_name);
	                    }
	                })
	                ->Where(function($query) use ($status) {
	                    if (isset($status) && !empty($status)) {
	                        $query->where('status', $status);
	                    }
	                });

		$totalData = $data->count();
		$data = $data->paginate(10);
		return view('admin.banner.index', compact('classes','data','totalData'));
	}

	public function create()
	{
		$classes = StudentClass::orderBy("id", "ASC")->get();
		return view('admin.banner.create', compact('classes'));
	}

	public function store(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'image' => 'required',
			//'module_name' => 'required',
		]);
		if (!empty($request->input('web_url'))) {
			$validator = Validator::make($request->all(), [
				'web_url' => 'required|url',
			]);
		}

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$file = $request->file('image');
			$filename = NULL;
			$destinationPath = public_path().'/upload/banners/';
			if($file){
				$originalFile = $file->getClientOriginalName();
				$filename = time().$originalFile;
				$file->move($destinationPath, $filename);
			}

			$banner = new Banner();
			$banner->class_id = $request->input('class_id');
			$banner->image = $filename;
			$banner->module_name = $request->input('module_name');
			$banner->module_id = $request->input('module_id');
			$banner->web_url = $request->input('web_url');
			$banner->save();
			$bannerId=$banner->id;
			if($bannerId){
				$record = Banner::findOrFail($bannerId);
				$record->sort_id = $bannerId;
				$record->update();
			}
			
			\Session::flash('msg', 'Banner Added Successfully.');
			return redirect('admin/banners');
		}
	}

	public function show(Banner $banner)
	{
		//
	}

	public function edit($id)
	{
		$classes = StudentClass::orderBy("id", "ASC")->get();
		$banner = Banner::find($id);
		$class_id = $banner->class_id;
		$moduleDataArr = [];
		if ($banner->module_name=='COURSE') {
			$getModuleData = Courses::whereRaw("find_in_set($class_id,class_ids)")->where('status',1)->where('deleted',0)->orderBy('sort_id','ASC')->get();
			foreach ($getModuleData as $key => $value) {
				$moduleData['id'] = $value['id'];
				$moduleData['name'] = $value['name'];
				array_push($moduleDataArr, $moduleData);
			}
		} elseif ($banner->module_name=='LESSON') {
			$courses = Courses::select('id')->whereRaw("find_in_set($class_id,class_ids)")->where('status',1)->where('deleted',0)->orderBy('sort_id','ASC')->get();
			$courseIds = [];
			foreach ($courses as $key => $val) {
				$courseIds[] = $val->id;
			}
			$getModuleData = Lession::with('courses')->whereIn("courseId", $courseIds)->where('status',1)->where('deleted',0)->orderBy('sort_id','ASC')->get();
			foreach ($getModuleData as $key => $value) {
				$moduleData['id'] = $value['id'];
				$moduleData['name'] = $value['name'].' - ( '.$value['courses']['name'].' )';
				array_push($moduleDataArr, $moduleData);
			}
		} elseif ($banner->module_name=='TOPIC') {
			$courses = Courses::select('id')->whereRaw("find_in_set($class_id,class_ids)")->where('status',1)->where('deleted',0)->orderBy('sort_id','ASC')->get();
			$courseIds = [];
			foreach ($courses as $key => $val) {
				$courseIds[] = $val->id;
			}
			$getModuleData = Chapter::with('courses','lession')->whereIn("courseId", $courseIds)->where('status',1)->where('deleted',0)->orderBy('sort_id','ASC')->get();
			foreach ($getModuleData as $key => $value) {
				$moduleData['id'] = $value['id'];
				$moduleData['name'] = $value['name'].' - ( '.$value['courses']['name'].' - '.$value['lession']['name'].' )';
				array_push($moduleDataArr, $moduleData);
			}
		} elseif ($banner->module_name=='ASSIGNMENT') {
			$courses = Courses::select('id')->whereRaw("find_in_set($class_id,class_ids)")->where('status',1)->where('deleted',0)->orderBy('sort_id','ASC')->get();
			$courseIds = [];
			foreach ($courses as $key => $val) {
				$courseIds[] = $val->id;
			}
			$getModuleData = Assignment::with('course')->whereIn("course_id", $courseIds)->where('status',1)->where('deleted',0)->orderBy('sort_id','ASC')->get();
			foreach ($getModuleData as $key => $value) {
				$moduleData['id'] = $value['id'];
				$moduleData['name'] = $value['title'].' - ( '.$value['course']['name'].' )';
				array_push($moduleDataArr, $moduleData);
			}
		} elseif ($banner->module_name=='QUIZ') {
			$courses = Courses::select('id')->whereRaw("find_in_set($class_id,class_ids)")->where('status',1)->where('deleted',0)->orderBy('sort_id','ASC')->get();
			$courseIds = [];
			foreach ($courses as $key => $val) {
				$courseIds[] = $val->id;
			}
			$getModuleData = Quiz::with('courses','lession','topic')->whereIn("courseId", $courseIds)->where('status',1)->where('deleted',0)->orderBy('id','DESC')->get();
			foreach ($getModuleData as $key => $value) {
				$courseName = $lessonName = $topicName = '';
				if($value['courseId']>0){ $courseName = $value['courses']['name']; }
				if($value['lessionId']>0){ $lessonName = $value['lession']['name']; }
				if($value['topicId']>0){ $topicName = $value['topic']['name']; }
				$moduleData['id'] = $value['id'];
				$moduleData['name'] = $value['name'].' - ( '.$courseName.' - '.$lessonName.' - '.$topicName.' )';
				array_push($moduleDataArr, $moduleData);
			}
		} elseif ($banner->module_name=='CHAT') {
			$getModuleData = ChatGroup::whereRaw("find_in_set($class_id,class_ids)")->where('status',1)->orderBy('sort_id','ASC')->get();
			foreach ($getModuleData as $key => $value) {
				$moduleData['id'] = $value['id'];
				$moduleData['name'] = $value['title'];
				array_push($moduleDataArr, $moduleData);
			}
		} elseif ($banner->module_name=='COMMUNITY_POST') {
			$getModuleData = CommunityPost::where('status',1)->where('deleted',0)->orderBy('sort_id','ASC')->get();
			foreach ($getModuleData as $key => $value) {
				$moduleData['id'] = $value['id'];
				$moduleData['name'] = $value['title'];
				array_push($moduleDataArr, $moduleData);
			}
		} elseif ($banner->module_name=='SUBSCRIPTION') {
			$getModuleData = Subscription::where('status',1)->where('deleted',0)->orderBy('id','ASC')->get();
			foreach ($getModuleData as $key => $value) {
				$moduleData['id'] = $value['id'];
				$moduleData['name'] = $value['name'];
				array_push($moduleDataArr, $moduleData);
			}
		} else {
		}

		return view('admin.banner.edit',compact('classes','banner','moduleDataArr'));
	}

	public function update(Request $request, $id)
	{
		$validator = Validator::make($request->all(), [
			//'image' => 'required',
			//'module_name' => 'required',
		]);
		if (!empty($request->input('web_url'))) {
			$validator = Validator::make($request->all(), [
				'web_url' => 'required|url',
			]);
		}

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$banner = Banner::findOrFail($id);

			$file = $request->file('image');
			$destinationPath = public_path().'/upload/banners/';
			if($file){
				$originalFile = $file->getClientOriginalName();
				$filename = time() . $originalFile;
				$file->move($destinationPath, $filename);
			}else{
			   $filename=$banner->image;  
			}

			$banner->class_id = $request->input('class_id');
			$banner->image = $filename;
			$banner->module_name = $request->input('module_name');
			$banner->module_id = $request->input('module_id');
			$banner->web_url = $request->input('web_url');
			$banner->save();

			\Session::flash('msg', 'Banner updated Successfully.');
			return redirect('/admin/banners');
		}
	}

	public function delete($id)
	{
		$data = Banner::findOrFail($id);
		$data->deleted=1;
		$data->update();
		
		\Session::flash('msg', 'Banner deleted Successfully.');
	    return redirect('/admin/banners');
	}

	public function updateStatus($id,$status)
	{
		$data = Banner::findOrFail($id);
		$data->status=$status;
		$data->update();
	\Session::flash('msg', 'Banner Deleted Successfully.');
	    return redirect()->back();
	}

	public function sortUp($id,$sort_id)
	{
		$sort_id = $sort_id - 1;
		$record = Banner::where("sort_id", $sort_id);
		$record->update(array("sort_id"=>$sort_id + 1));
		$record1 = Banner::findOrFail($id);
		$record1->sort_id = $sort_id;
		$record1->save();

		\Session::flash('msg', 'Banner Order Changed Successfully.');
	    return redirect()->back();
	}

	public function sortDown($id,$sort_id)
	{
		$sort_id = $sort_id + 1;
		$record = Banner::where("sort_id", $sort_id);
		$record->update(array("sort_id"=>$sort_id - 1));
		$record1 = Banner::findOrFail($id);
		$record1->sort_id = $sort_id;
		$record1->save();

		\Session::flash('msg', 'Banner Order Changed Successfully.');
	    return redirect()->back();
	}

	public function imageremove($id)
	{
		$banner = Banner::findOrFail($id);
		if(file_exists( public_path().'/upload/banners/'.$banner->image )) {
			unlink( public_path().'/upload/banners/'.$banner->image );
		}
		$banner->image = NULL;
		$banner->update();

		\Session::flash('msg', 'Banner Image Removed Successfully.');
	    return redirect()->back();
	}

	public function getModuleList(Request $request)
	{
		$class_id = $request->input('banner_classid');
		$module_name = $request->input('module_name');
		?>
		<option value="0">--Select--</option>
		<?php
		if ($module_name=='COURSE') {
			$getModuleData = Courses::whereRaw("find_in_set($class_id,class_ids)")->where('status',1)->where('deleted',0)->orderBy('sort_id','ASC')->get();
			foreach ($getModuleData as $key => $value) {
				?>
				<option value="<?php echo $value['id']; ?>"><?php echo $value['name']; ?></option>
				<?php
			}
		} elseif ($module_name=='LESSON') {
			$courses = Courses::select('id')->whereRaw("find_in_set($class_id,class_ids)")->where('status',1)->where('deleted',0)->orderBy('sort_id','ASC')->get();
			$courseIds = [];
			foreach ($courses as $key => $val) {
				$courseIds[] = $val->id;
			}
			$getModuleData = Lession::with('courses')->whereIn("courseId", $courseIds)->where('status',1)->where('deleted',0)->orderBy('sort_id','ASC')->get();
			foreach ($getModuleData as $key => $value) {
				?>
				<option value="<?php echo $value['id']; ?>"><?php echo $value['name']; ?> - (<?php echo $value['courses']['name']; ?>)</option>
				<?php
			}
		} elseif ($module_name=='TOPIC') {
			$courses = Courses::select('id')->whereRaw("find_in_set($class_id,class_ids)")->where('status',1)->where('deleted',0)->orderBy('sort_id','ASC')->get();
			$courseIds = [];
			foreach ($courses as $key => $val) {
				$courseIds[] = $val->id;
			}
			$getModuleData = Chapter::with('courses','lession')->whereIn("courseId", $courseIds)->where('status',1)->where('deleted',0)->orderBy('sort_id','ASC')->get();
			foreach ($getModuleData as $key => $value) {
				?>
				<option value="<?php echo $value['id']; ?>"><?php echo $value['name']; ?> - (<?php echo $value['courses']['name']; ?> - <?php echo $value['lession']['name']; ?>)</option>
				<?php
			}
		} elseif ($module_name=='ASSIGNMENT') {
			$courses = Courses::select('id')->whereRaw("find_in_set($class_id,class_ids)")->where('status',1)->where('deleted',0)->orderBy('sort_id','ASC')->get();
			$courseIds = [];
			foreach ($courses as $key => $val) {
				$courseIds[] = $val->id;
			}
			$getModuleData = Assignment::with('course')->whereIn("course_id", $courseIds)->where('status',1)->where('deleted',0)->orderBy('sort_id','ASC')->get();
			foreach ($getModuleData as $key => $value) {
				?>
				<option value="<?php echo $value['id']; ?>"><?php echo $value['title']; ?> - (<?php echo $value['course']['name']; ?>)</option>
				<?php
			}
		} elseif ($module_name=='QUIZ') {
			$courses = Courses::select('id')->whereRaw("find_in_set($class_id,class_ids)")->where('status',1)->where('deleted',0)->orderBy('sort_id','ASC')->get();
			$courseIds = [];
			foreach ($courses as $key => $val) {
				$courseIds[] = $val->id;
			}
			$getModuleData = Quiz::with('courses','lession','topic')->whereIn("courseId", $courseIds)->where('status',1)->where('deleted',0)->orderBy('id','DESC')->get();
			foreach ($getModuleData as $key => $value) {
				?>
				<option value="<?php echo $value['id']; ?>"><?php echo $value['name']; ?> - (<?php if($value['courseId']>0){ echo $value['courses']['name']; ?> - <?php }if($value['lessionId']>0){ echo $value['lession']['name']; ?> - <?php }if($value['topicId']>0){ echo $value['topic']['name']; } ?>)</option>
				<?php
			}
		} elseif ($module_name=='CHAT') {
			$getModuleData = ChatGroup::whereRaw("find_in_set($class_id,class_ids)")->where('status',1)->orderBy('sort_id','ASC')->get();
			foreach ($getModuleData as $key => $value) {
				?>
				<option value="<?php echo $value['id']; ?>"><?php echo $value['title']; ?></option>
				<?php
			}
		} elseif ($module_name=='COMMUNITY_POST') {
			$getModuleData = CommunityPost::where('status',1)->where('deleted',0)->orderBy('sort_id','ASC')->get();
			foreach ($getModuleData as $key => $value) {
				?>
				<option value="<?php echo $value['id']; ?>"><?php echo $value['title']; ?></option>
				<?php
			}
		} elseif ($module_name=='SUBSCRIPTION') {
			$getModuleData = Subscription::where('status',1)->where('deleted',0)->orderBy('id','ASC')->get();
			foreach ($getModuleData as $key => $value) {
				?>
				<option value="<?php echo $value['id']; ?>"><?php echo $value['name']; ?></option>
				<?php
			}
		} else {
		}
	}

}
