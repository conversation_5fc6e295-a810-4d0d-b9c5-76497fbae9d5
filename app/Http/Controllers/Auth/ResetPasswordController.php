<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\ResetsPasswords;
use Illuminate\Http\Request;
use App\User;
use Hash;
use Validator;

class ResetPasswordController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Password Reset Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling password reset requests
    | and uses a simple trait to include this behavior. You're free to
    | explore this trait and override any methods you wish to tweak.
    |
    */

    use ResetsPasswords;

    /**
     * Where to redirect users after resetting their password.
     *
     * @var string
     */
    protected $redirectTo = '/admin/home';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest');
    }
     protected function validator(array $data)
    {
        return Validator::make($data, [
            'password' => 'required',
            'password_confirmation' => 'required|min:6|confirmed',
        ]);
    }


    public function reset(Request $request)
    {
        if($request->all())
        {
             $token = $request->token;//id
             
             $password = $request->password;
             $password_confirm = $request->password_confirmation;//id
             $getuser=User::where("id",$token)->first();
             if(!empty($getuser))
             {
                if($password!=$password_confirm)
                {
                return redirect()->back()->withErrors('The password confirmation confirmation does not match');
                    
                }
                else
                {
                    $getuser->userpass = $password;
                    $getuser->password =  Hash::make($password);
                    $getuser->save();
                    return redirect('login')->with('success', 'Password change successfully!');
                }
                //
               //dd($this->validator($request->all())->validate());
             }
             else
             {
                return redirect()->back()->withErrors('Invalid Email Request');
             }
           

        }

    }
}
