<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\SendsPasswordResetEmails;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Password;
use P<PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON>Mailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;
//use App\Models\User;
use App\User;
class ForgotPasswordController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Password Reset Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling password reset emails and
    | includes a trait which assists in sending these notifications from
    | your application to your users. Feel free to explore this trait.
    |
    */

use SendsPasswordResetEmails;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    // public function __construct()
    // {
    //     $this->middleware('guest');
    //  }
    public function sendResetLinkEmail(Request $request){
   //    dd($request->email);
        $email = $request->email;
     
       $userState = User::where('email', $email)->first();
        if($userState!=null)
        {
            $stubject = "forget Password";
             $message = "<a href='".url('/')."/password/reset/". $userState->id."'>Click</a>";

            $this->sendMail($email, $stubject, $message);
            $data['message'] = "Please send link Your Registered Email address";
            return view("/thanks",$data);
        }
        else
        {
            return redirect()->back()->withErrors('Invalid Email Address');
        }

    }
     public function sendMail($email, $stubject = NULL, $message = NULL)
    {

        require base_path("vendor/autoload.php");
        $mail = new PHPMailer(true);     // Passing `true` enables exceptions
        try {
            $mail->SMTPDebug = 0;
            $mail->isSMTP();
            $mail->Host = "smtp.gmail.com";
            $mail->Port = 587;
            $mail->SMTPSecure = "tls";
            $mail->SMTPAuth = true;
            $mail->Username = "<EMAIL>";
            $mail->Password = "audnjvohywazsdqo";
            $mail->addAddress($email, "User Name");
            $mail->Subject = $stubject;
            $mail->isHTML();
            $mail->Body = $message;
            $mail->setFrom("<EMAIL>");
            $mail->FromName = "Guru At Home";

            if ($mail->send()) {
                return 1;
            } else {
                return 0;
            }
        } catch (Exception $e) {
            return 0;
        }
    }
}
