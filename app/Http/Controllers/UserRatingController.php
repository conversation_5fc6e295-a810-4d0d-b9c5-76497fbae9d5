<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Chapter;
use App\Models\Courses;
use App\Models\Lession;
use App\Models\RatingMessage;
use App\Models\RatingType;
use App\Models\RatingUser;
use App\Models\User;


class UserRatingController extends Controller
{
	/**
	 * Display a listing of the resource.
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function index(Request $request)
	{
		$users = User::where("role_id", 3)->where("status", 1)->where("deleted", 0)->orderBy("name", "ASC")->get();
		$courses  = Courses::where("deleted", 0)->orderBy("sort_id", "ASC")->get();
		$lessions = Lession::where("deleted", 0)->orderBy("sort_id", "ASC")->get();
		$topics   = Chapter::where("deleted", 0)->orderBy("sort_id", "ASC")->get();
		$ratingTypes = RatingMessage::orderBy("ratingType", "ASC")->get();

		$userId   = ($request->user) ? $request->user : '';
		$class_type = ($request->class_type) ? $request->class_type : '';
		$courseId = ($request->course) ? $request->course : '';
		$lessonId = ($request->lession) ? $request->lession : '';
		$topicId  = ($request->topic) ? $request->topic : '';
		$ratingType = ($request->type) ? $request->type : '';
		$rating 	= ($request->rating) ? $request->rating : '';
		$from     = ($request->from) ? $request->from : '';
		$to       = ($request->to) ? $request->to.' 23:59:59' : date('Y-m-d H:i:s');
		$subscription = ($request->subscription) ? $request->subscription : '';

		if (!empty($ratingType)) {
			$ratingTypeData = RatingType::where("ratingType", 'like', "%" . $ratingType . "%")->first();
			if (!empty($ratingTypeData)) {
				$ratingTypes = RatingMessage::where("ratingType",$ratingTypeData->id)->orderBy("message", "ASC")->get();
			}
		}

		$data = RatingUser::orderBy("id", "DESC")
			 ->Where(function($query) use ($userId) {
				if (isset($userId) && !empty($userId)) {
					$query->where("userId", $userId);
				}
			 })
			 ->whereHas("course", function($query) use ($class_type) {
				if (isset($class_type) && !empty($class_type)) {
					$query->where("class_type", $class_type);
				}
			 })
			 ->Where(function($query) use ($courseId) {
				if (isset($courseId) && !empty($courseId)) {
					$query->where("courseId", $courseId);
				}
			 })
			 ->Where(function($query) use ($lessonId) {
				if (isset($lessonId) && !empty($lessonId)) {
					$query->where("lessionId", $lessonId);
				}
			 })
			 ->Where(function($query) use ($topicId) {
				if (isset($topicId) && !empty($topicId)) {
					$query->where("topicId", $topicId);
				}
			 })
			 ->Where(function($query) use ($ratingType) {
				if (isset($ratingType) && !empty($ratingType)) {
					$query->where("ratingType", $ratingType);
				}
			 })
			 ->Where(function($query) use ($rating) {
				if (isset($rating) && !empty($rating)) {
					$query->where("ratingMessage", 'like', "%" . $rating . "%");
				}
			 })
			 ->Where(function($query) use ($from, $to) {
				if (isset($from) && !empty($from)) {
					$query->whereBetween("created_at", [$from, $to]);
				}
			 });
		if (isset($subscription) && !empty($subscription)) {	 
			if ($subscription=='paid') {
				$data->whereHas('subscription_data', function($query) use ($subscription) {
					$query->whereDate("end_date", ">=", date('Y-m-d'));
				});
			} elseif ($subscription=='unpaid') {
				$data->whereHas('subscription_data', function($query) use ($subscription) {
					if (isset($subscription) && !empty($subscription)) {
						$query->whereDate("end_date", "<", date('Y-m-d'));
					}
				});/*
			} elseif ($subscription=='trial') {
				$data->whereHas('subscription_data', function($query) use ($subscription) {
					$query->whereDate("end_date", ">=", date('Y-m-d'))->where("mode", "FREE");
				});*/
			}
		}
		$data = $data->paginate(50);

		return view('admin.usersrating.index', compact('users','courses','lessions','topics','ratingTypes','data'));
	}
	
	/**
	 * Show the form for creating a new resource.
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function create()
	{
		$users = User::where("status", 1)->get();

		return view('admin.usersrating.create', compact('users'));
	}

	/**
	 * Store a newly created resource in storage.
	 *
	 * @param  \Illuminate\Http\Request $request
	 * @return \Illuminate\Http\Response
	 */
	public function store(Request $request)
	{
		//echo '<pre />'; print_r($request->all()); die;
		$validator = Validator::make($request->all(), [
			'userId' => 'required|numeric',
			'courseId' => 'required|numeric',
			'lessionId' => 'required|numeric',
			'topicId' => 'required|numeric',
			'ratingType' => 'required',
			'ratingMessage' => 'required',
		]);

		if ($validator->fails()) {
			return back()
				->withErrors($validator)
				->withInput();
		}else { 
			//echo '<pre />'; print_r($request->all()); die;
			$ratinguser = new RatingUser();
			$ratinguser->userId = $request->get('userId');
			$ratinguser->courseId = $request->get('courseId');
			$ratinguser->lessionId = $request->get('lessionId');
			$ratinguser->topicId = $request->get('topicId');
			$ratinguser->ratingType = $request->get('ratingType');
			$ratinguser->ratingMessage = $request->get('ratingMessage');
			$ratinguser->message = $request->get('message');
			$ratinguser->save();
			$ratinguserId = $ratinguser->id;

			\Session::flash('msg', 'Rating Added Successfully.');
			return back();
		}
	}

	/**
	 * Display the specified resource.
	 *
	 * @param  \App\Models\RatingUser $ratinguser
	 * @return \Illuminate\Http\Response
	 */
	public function show(RatingUser $ratinguser, $id)
	{
		$data = RatingUser::findOrFail($id);
		if ($data->status==0) {
			$data->status=1;
			$data->update();
		}

		return view('admin.usersrating.view',compact('data'));
	}

	/**
	 * Show the form for editing the specified resource.
	 *
	 * @param  \App\Models\RatingUser $ratinguser
	 * @return \Illuminate\Http\Response
	 */
	public function edit(RatingUser $ratinguser, $id)
	{
		$data = RatingUser::findOrFail($id);
		$users = User::where("status", 1)->get();

		return view('admin.usersrating.edit',compact('data','users'));
	}

	/**
	 * Update the specified resource in storage.
	 *
	 * @param  \Illuminate\Http\Request $request
	 * @param  \App\Models\RatingUser $ratinguser
	 * @return \Illuminate\Http\Response
	 */
	public function update(Request $request, RatingUser $ratinguser, $id)
	{
		$validator = Validator::make($request->all(), [
			'userId' => 'required|numeric',
			'courseId' => 'required|numeric',
			'lessionId' => 'required|numeric',
			'topicId' => 'required|numeric',
			'ratingType' => 'required',
			'ratingMessage' => 'required',
		]);

		if ($validator->fails()) {
			return back()
				->withErrors($validator)
				->withInput();
		} else {
			$ratinguser = RatingUser::findOrFail($id);
			$ratinguser->userId = $request->get('userId');
			$ratinguser->courseId = $request->get('courseId');
			$ratinguser->lessionId = $request->get('lessionId');
			$ratinguser->topicId = $request->get('topicId');
			$ratinguser->ratingType = $request->get('ratingType');
			$ratinguser->ratingMessage = $request->get('ratingMessage');
			$ratinguser->message = $request->get('message');
			$ratinguser->save();
			$ratinguserId = $ratinguser->id;

			\Session::flash('msg', 'Rating Updated Successfully.');
			return redirect('/admin/usersratings');
		}
	}

	/**
	 * Remove the specified resource from storage.
	 *
	 * @param  \App\Models\RatingUser
	 * @return \Illuminate\Http\Response
	 */
	public function delete($id)
	{
		$ratinguser = RatingUser::findOrFail($id);
		$ratinguser->deleted=1;
		$ratinguser->update();

		return redirect()->back();
	}

	public function updateStatus($id,$status)
	{
		$ratinguser = RatingUser::findOrFail($id);
		$ratinguser->status=$status;
		$ratinguser->update();

		return redirect()->back();
	}

	public function get_rating_types(Request $request)
	{
		$ratingType = $request->ratingType;
		?>
		<option value="">--Select--</option>
		<?php
		if (!empty($ratingType)) {
			$ratingTypeData = RatingType::where("ratingType", 'like', "%" . $ratingType . "%")->first();
			if (!empty($ratingTypeData)) {
				$ratingMessages = RatingMessage::where("ratingType",$ratingTypeData->id)->orderBy("message", "ASC")->get();
				foreach ($ratingMessages as $key => $value) {
					?>
					<option value="<?php echo $value['message']; ?>"><?php echo $value['message']; ?></option>
					<?php
				}
			}
		}
	}

	public function storeRemark(Request $request)
	{
		//dd($request->all());
		if (isset($request->remark_id)) {
			$rating_user_id = $request->remark_id;
			$ratinguser = RatingUser::findOrFail($rating_user_id);
			$ratinguser->remark = $request->get('remark');
			$ratinguser->save();

			\Session::flash('msg', 'Rating Remark Added Successfully.');
		}
		return redirect()->back();
	}

}
