<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Http\Helper as Helper;
use App\Models\ChatGroup;
use App\Models\ChatGroupUser;
use App\Models\ChatMessage;
use App\Models\StudentClass;
use App\Models\User;
use AWS;

class ChatController extends Controller
{
	protected $helper;

	public function __construct(Helper $helper)
	{
		$this->helper = $helper;
	}
	
	/**
	 * Display a listing of the resource.
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function index(Request $request)
	{
		$class_type = ($request->class_type) ? $request->class_type : '';
		$class_id 	= ($request->class_id) ? $request->class_id : '';
		$type = $request->type;
		$status = $request->status;

		$classes = StudentClass::orderBy("id", "ASC")->get();
		
		if(auth()->user()->role_id!=2){
			/*if (isset($type) && $type==0 && isset($status) && $status==0) {
				$data = ChatGroup::where('type', 0)->where('status', 0)->orderBy('sort_id','ASC')->get();
			} elseif (isset($type) && $type==0) {
				$data = ChatGroup::where('type', 0)->orderBy('sort_id','ASC')->get();
			} elseif (isset($status) && $status==0) {
				$data = ChatGroup::where('status', 0)->orderBy('sort_id','ASC')->get();
			} else {*/
				$data = ChatGroup::OrderBy('sort_id','ASC')
					->Where(function($query) use ($class_type) {
						if (isset($class_type) && !empty($class_type)) {
							$query->where('class_type', $class_type);
						}
					})
					->Where(function($query) use ($class_id) {
						if (isset($class_id) && !empty($class_id)) {
							$query->whereRaw("find_in_set($class_id,class_ids)");
						}
					})
					->Where(function($query) use ($type) {
						if (isset($type) && !empty($type)) {
							//$query->where('type', '=', $type);
							$query->whereRaw("find_in_set($type,type)");
						}
					})
					->Where(function($query) use ($status) {
						if (isset($status) && !empty($status)) {
							$query->where('status', $status);
						}
					});
				$totalData = $data->count();
				$data = $data->paginate(50);
			//}
		}else{
			$allowedGroups = auth()->user()->allow_groups;
			$allowedGroups = explode(",", trim($allowedGroups));
			/*if (isset($type) && $type==0 && isset($status) && $status==0) {
				$data = ChatGroup::whereIn("id", $allowedGroups)->where('type', 0)->where('status', 0)->orderBy('sort_id','ASC')->get();
			} elseif (isset($type) && $type==0) {
				$data = ChatGroup::whereIn("id", $allowedGroups)->where('type', 0)->orderBy('sort_id','ASC')->get();
			} elseif (isset($status) && $status==0) {
				$data = ChatGroup::whereIn("id", $allowedGroups)->where('status', 0)->orderBy('sort_id','ASC')->get();
			} else {*/
				$data = ChatGroup::whereIn("id", $allowedGroups)->OrderBy('sort_id','ASC')
					->Where(function($query) use ($class_type) {
						if (isset($class_type) && !empty($class_type)) {
							$query->where('class_type', $class_type);
						}
					})
					->Where(function($query) use ($class_id) {
						if (isset($class_id) && !empty($class_id)) {
							$query->whereRaw("find_in_set($class_id,class_ids)");
						}
					})
					->Where(function($query) use ($type) {
						if (isset($type) && !empty($type)) {
							//$query->where('type', '=', $type);
							$query->whereRaw("find_in_set($type,type)");
						}
					})
					->Where(function($query) use ($status) {
						if (isset($status) && !empty($status)) {
							$query->where('status', $status);
						}
					});
				$totalData = $data->count();
				$data = $data->paginate(50);
			//}
		}

		return view('admin.chat.index', compact('classes','data','totalData'));
	}

	/**
	 * Show the form for creating a new resource.
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function create()
	{
		if(auth()->user()->role_id==2){
			return redirect()->route('admin.chat.index');
		}
		$classes = StudentClass::orderBy("id", "ASC")->get();

		return view('admin.chat.create', compact('classes'));
	}

	/**
	 * Store a newly created resource in storage.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @return \Illuminate\Http\Response
	 */
	public function store(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'title' => 'required',
			'image' => 'required',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			//dd($request->all());

			try
			{
				$input = $request->all();
				$file = $request->file('image');
				$filename = NULL;
				$destinationPath = public_path().'/upload/chat_images/';
				if($file){
					$originalFile = $file->getClientOriginalName();
					$filename = time().$originalFile;
					$file->move($destinationPath, $filename);
					$input['image'] = $filename;
				}
				$input['class_ids'] = !empty($request->class_ids) ? implode(",", $request->class_ids) : NULL;
				$input['type'] = !empty($request->type) ? implode(",", $request->type) : NULL;

				\DB::beginTransaction();
					$chat = new ChatGroup();
					$chat->fill($input)->save();
					$groupId = $chat->id;
					if($groupId){
						$group = ChatGroup::findOrFail($groupId);
						$group->sort_id = $groupId;
						$group->update();
					}
				\DB::commit();
				\Session::flash('msg', 'Chat Group Added Successfully.');
				return redirect()->route('admin.chat.index');
			}
			catch (\Throwable $e)
			{
				\DB::rollback();
				\Session::flash('msg', $e->getMessage().' on line '.$e->getLine());
				return back();  
			}
		}
	}

	/**
	 * Display the specified resource.
	 *
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function show($id)
	{
		$group = ChatGroup::findOrFail($id);
		
		return view('admin.chat.user_chat_view',compact('group'));
	}

	/**
	 * Show the form for editing the specified resource.
	 *
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function edit($id)
	{
		if(auth()->user()->role_id==2){
			return redirect()->route('admin.chat.index');
		}
		$group = ChatGroup::findOrFail($id);
		$classes = StudentClass::orderBy("id", "ASC")->get();
		
		return view('admin.chat.edit',compact('group','classes'));
	}

	/**
	 * Update the specified resource in storage.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function update(Request $request, $id)
	{
		$group = ChatGroup::find($id);
		if (!$group) {
			\Session::flash('msg', 'Chat Group Not Found!');
			return redirect()->back();
		}
		$validator = Validator::make($request->all(), [
			'title' => 'required',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			//dd($request->all());

			try
			{
				$input = $request->all();
				$file = $request->file('image');
				$filename = NULL;
				$destinationPath = public_path().'/upload/chat_images/';
				if($file){
					$originalFile = $file->getClientOriginalName();
					$filename = time().$originalFile;
					$file->move($destinationPath, $filename);
					$input['image'] = $filename;
				}else{
					$input['image'] = $group->image;
				}
				$input['class_ids'] = !empty($request->class_ids) ? implode(",", $request->class_ids) : NULL;
				$input['type'] = !empty($request->type) ? implode(",", $request->type) : NULL;
				
				\DB::beginTransaction();
					$group->fill($input)->save();
				\DB::commit();
				\Session::flash('msg', 'Chat Group Updated Successfully.');
				return redirect()->route('admin.chat.index');
			}
			catch (\Throwable $e)
			{
				\DB::rollback();
				\Session::flash('msg', $e->getMessage().' on line '.$e->getLine());
				return back();  
			}
		}
	}

	/**
	 * Remove the specified resource from storage.
	 *
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function destroy($id)
	{
		$group = ChatGroup::findOrFail($id);
		$group->delete();

		\Session::flash('msg', 'Chat Group Deleted Successfully.');
		return redirect()->back();
	}

	public function imageremove($id)
	{
		$group = ChatGroup::findOrFail($id);
		if(file_exists( public_path().'/upload/chat_images/'.$group->image )) {
			unlink( public_path().'/upload/chat_images/'.$group->image );
		}
		$group->image = NULL;
		$group->update();

		\Session::flash('msg', 'Group Image Removed Successfully.');
	    return redirect()->back();
	}

	public function updateStatus($id,$status)
	{
		$group = ChatGroup::findOrFail($id);
		$group->status=$status;
		$group->update();

	    return redirect()->back();
	}

	public function sortUp($id,$sort_id)
	{
		$sort_id = $sort_id - 1;
		$group = ChatGroup::where("sort_id", $sort_id);
		$group->update(array("sort_id"=>$sort_id + 1));
		$group1 = ChatGroup::findOrFail($id);
		$group1->sort_id = $sort_id;
		$group1->save();

		return redirect()->back();
	}

	public function sortDown($id,$sort_id)
	{
		$sort_id = $sort_id + 1;
		$group = ChatGroup::where("sort_id", $sort_id);
		$group->update(array("sort_id"=>$sort_id - 1));
		$group1 = ChatGroup::findOrFail($id);
		$group1->sort_id = $sort_id;
		$group1->save();

		return redirect()->back();
	}

	public function groupUsers(Request $request, $id)
	{
		if (!$id) {
			\Session::flash('msg', 'Chat Group Not Found!');
			return redirect()->route('admin.chat.index');
		}
		$search = $request->search;
		$data = ChatGroupUser::with('user_data')->where('group_id', $id)->paginate(50);
		//dd($data);

		return view('admin.chat.userslist', compact('data'));
	}

	public function blockUser($group_id,$user_id)
	{
		$group = ChatGroupUser::where('group_id', $group_id)->where('user_id', $user_id)->first();
		$status = $group->block_status;
		if ($status == 0) {
			$status = 1;
		} else {
			$status = 0;
		}
		$group->block_status = $status;
		$group->update();

		return redirect()->back();
	}

	public function sendMessage(Request $request, $id)
	{
		$s3 = AWS::createClient('s3');
		//dd($id);
		$groupId = $id;
		$userId = Auth::user()->id;
		$validator = Validator::make($request->all(), [
			//'message' => 'required',
			//'image' => 'required',
			'video' => 'mimes:mp4',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			//dd($request->all());

			try
			{
				$input = $request->all();
				$input['group_id'] = $groupId;
				$input['user_id']  = $userId;
				$image = $request->file('image');
				$video = $request->file('video');
				$imageUrl = $videoUrl = NULL;
				$destinationPath = public_path().'/upload/chat_message_images/';
				if($image){
					$originalFile = $image->getClientOriginalName();
					$imageFilename = "chat_".time().$originalFile;
					$image->move($destinationPath, $imageFilename);
					//S3 Upload
					$s3->putObject(array(
						'Bucket'     => env('AWS_BUCKET'),
						'Key'        => "assignment_data/".$imageFilename,
						'SourceFile' => $destinationPath.$imageFilename,
					));
					$imageUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $imageFilename;
					if(!empty($image) && file_exists( $destinationPath.$imageFilename )) {
						unlink( $destinationPath.$imageFilename );
					}
					$input['image'] = $imageUrl;
				}
				if($video){
					$originalFile = $video->getClientOriginalName();
					$videoFilename = "chat_".time().".mp4";
					$video->move($destinationPath, $videoFilename);
					//S3 Upload
					$s3->putObject(array(
						'Bucket'     => env('AWS_BUCKET'),
						'Key'        => "assignment_data/".$videoFilename,
						'SourceFile' => $destinationPath.$videoFilename,
					));
					$videoUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $videoFilename;
					if(!empty($video_file) && file_exists( $destinationPath.$videoFilename )) {
						unlink( $destinationPath.$videoFilename );
					}
					$input['video'] = $videoUrl;
				}
				
				$input['message'] = ($request->message) ? $request->message : NULL;
				
				\DB::beginTransaction();
					$chatMsg = new ChatMessage();
					$chatMsg->fill($input)->save();
					$insertId = $chatMsg->id;
					$chatMessage = ChatMessage::with('user_data','group_data')->where("id", $insertId)->first();
					if (!empty($chatMessage)) {
						$userName = $chatMessage->user_data->name;
						$groupName = $chatMessage->group_data->title;
						$getGroupUsers = ChatGroupUser::select("user_id")->where("group_id", $groupId)->where("block_status", 0)->where("mute_status", 0)->get();
						if (!empty($getGroupUsers) && !empty($getGroupUsers->toArray())) {
							foreach ($getGroupUsers as $value) {
								//$groupUserIds[] = $value['user_id'];
								$user = User::select("deviceToken")->where("id", "!=", $userId)->where("id", $value['user_id'])->first();
								$token = isset($user->deviceToken) ? $user->deviceToken : '';
								if ($token!='') {
									$title = 'BrainyWood';
									$message = $userName . ' sent a message in ' . $groupName . ' ' . $chatMessage->message;
									$click_action = 'Chat';
									$module_id = $groupId;
									//$this->helper->notificationsend($token, $title, $message, $click_action, $module_id);//ak
								}
							}
						}
					}
				\DB::commit();
				\Session::flash('msg', 'Chat Message Sent Successfully.');
				return redirect()->back();
			}
			catch (\Throwable $e)
			{
				\DB::rollback();
				\Session::flash('msg', $e->getMessage().' on line '.$e->getLine());
				return back();  
			}
		}
	}
	public function messageDelete($id)
	{
		$groupMessage = ChatMessage::findOrFail($id);
		/*if (!empty($groupMessage)) {
			$parentId = $groupMessage->parent_id;
			if ($parentId == 0) {
				$replyMessages1 = ChatMessage::where("parent_id",$groupMessage->id)->get();
				foreach ($replyMessages1 as $replyMsg1) {
					$replyparentId1 = $replyMsg1->parent_id;
					if ($replyparentId1 > 0) {
						$replyMessages2 = ChatMessage::where("parent_id",$replyMsg1->id)->get();
						foreach ($replyMessages2 as $replyMsg2) {
							$replyparentId2 = $replyMsg2->parent_id;
							$replyMsg2->delete();
						}
					}
					$replyMsg1->delete();
				}
			}
		}*/
		//$groupMessage->delete();
		$groupMessage->update(["status" => 0]);

		\Session::flash('msg', 'Chat Message Deleted Successfully.');
		return redirect()->back();
	}
}
