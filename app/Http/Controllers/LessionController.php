<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Chapter;
use App\Models\Courses;
use App\Models\Lession;
use App\Models\StudentClass;
use App\Models\VideoTemp;
use AWS;


class LessionController extends Controller
{
	public function index(Request $request)
	{
		$classes = StudentClass::orderBy("id", "ASC")->get();
		$courses  = Courses::Where("deleted", 0)->get();

		$class_type = ($request->class_type) ? $request->class_type : '';
		$class_id 	= ($request->class_id) ? $request->class_id : '';
		$courseId 	= ($request->course) ? $request->course : '';
		$search		= ($request->search) ? $request->search : '';
		$data = lession::with("courses")->orderBy("sort_id", "ASC")
				 ->whereHas("courses", function($query) use ($class_type) {
					if (isset($class_type) && !empty($class_type)) {
						$query->where("class_type", $class_type);
					}
				 })
				 ->whereHas("courses", function($query) use ($class_id) {
					if (isset($class_id) && !empty($class_id)) {
						$query->whereRaw("find_in_set($class_id,class_ids)");
					}
				 })
				 ->Where(function($query) use ($courseId) {
					if (isset($courseId) && !empty($courseId)) {
						$query->where("courseId", $courseId);
					}
				 })
				 ->Where(function($query) use ($search) {
					if (isset($search) && !empty($search)) {
						$query->where("name", 'like', "%" . $search . "%");
					}
				 })
				 ->where("deleted", 0);
		$totalData = $data->count();
		$data = $data->paginate(50);
	
		return view('admin.lession.index', compact('classes','courses','data','totalData'));
	}

	public function create()
	{
		$course = Courses::where("deleted", 0)->orderBy("sort_id", "ASC")->get();
		return view('admin.lession.create',compact('course'));
	}

	public function store(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'courseId' => 'required',
			'name' => 'required',
			//ak'video' => 'required|mimes:mp4',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			//dd($request->all());
			$thumbfilename = $videofilename = $pdffilename = NULL;
			$thumb = $request->file('thumb');
			$video = $request->file('video');
			$pdf = $request->file('pdf');
			
			$destinationPath = public_path() . '/lessions/';
			if($thumb){
				$thumbOriginalName = $thumb->getClientOriginalName();
				$thumbfilename = time() . $thumbOriginalName;
				$thumb->move($destinationPath, $thumbfilename);
			}
			if($video){
				$videoOriginalName = $video->getClientOriginalName();
				//$videofilename=rand(111,999).strtotime(date('Y-m-d-H:isa')).$videoOriginalName;
				$videofilename = time() . "_org.mp4";
				$video->move($destinationPath, $videofilename);
			}
			if($pdf){
				$pdfOriginalName = $pdf->getClientOriginalName();
				$pdffilename = time() . $pdfOriginalName;
				$pdf->move($destinationPath, $pdffilename);
			}
				
			$lession = new Lession();
			$lession->courseId = $request->get('courseId');
			$lession->name = $request->get('name');
			$lession->video_thumb = $thumbfilename;
			$lession->fullvideo = $videofilename;
			$lession->pdf = $pdffilename;
			$lession->content = $request->get('content');
			$lession->isFree = $request->get('free');
			$lession->save();
			$lessionId=$lession->id;
			if($lessionId){
				$lession = Lession::findOrFail($lessionId);
				$lession->sort_id = $lessionId;
				$lession->save();
			}
			
			\Session::flash('msg', 'Lesson Added Successfully.');
			return back();
		}
	}

	public function show(Lession $lession)
	{
		//
	}

	public function edit($id)
	{
		$lession = Lession::find($id);
		//$course = Courses::Where('deleted',0)->get();
		$course = Courses::orderBy("sort_id", "ASC")->get();

		return view('admin.lession.edit',compact('lession','course'));
	}

	public function update(Request $request, Courses $courses, $id)
	{
		$s3 = AWS::createClient('s3');
		
		$validator = Validator::make($request->all(), [
			'courseId' => 'required',
			'name' => 'required',
			'video' => 'mimes:mp4',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		}else {
			//print_r($request->all());die;
			$lession = lession::findOrFail($id);

			$thumb = $request->file('thumb');
			$video = $request->file('video');
			$pdf = $request->file('pdf');

			$destinationPath = public_path() . '/lessions/';
			if($thumb){
				$thumbOriginalName = $thumb->getClientOriginalName();
				$thumbfilename = time() . $thumbOriginalName;
				$thumb->move($destinationPath, $thumbfilename);
			}else {
				$thumbfilename = $lession->video_thumb;
			}
			if($video){
				$videoOriginalName = $video->getClientOriginalName();
				//$videofilename=rand(111,999).strtotime(date('Y-m-d-H:isa')).$videoOriginalName;
				$videofilename = time() . "_org.mp4";
				$video->move($destinationPath, $videofilename);
			    $uploads3 = 0;
			    $lession->video_1 = $lession->video_2 = $lession->video_3 = $lession->starttime = $lession->endtime = NULL;
				$lession->uploads3v1 = $lession->uploads3v2 = $lession->uploads3v3 = $lession->processtatus = 0;
			}else {
				$videofilename = $lession->fullvideo;
			    $uploads3 = $lession->uploads3;
			}
			if($pdf){
				$pdfOriginalName = $pdf->getClientOriginalName();
				$pdffilename = time() . $pdfOriginalName;
				$pdf->move($destinationPath, $pdffilename);
			}else {
				$pdffilename = $lession->pdf;
			}
			
			if($video){
				if(!empty($lession->fullvideo) && file_exists( public_path().'/lessions/'.$lession->fullvideo )) {
					unlink( public_path().'/lessions/'.$lession->fullvideo );
				}
				if(!empty($lession->fullvideo)){
					$videoName = $lession->fullvideo;
					$s3->deleteObject(array(
				        'Bucket' => env('AWS_BUCKET'),
				        'Key'    => $videoName
				    ));
					$s3->deleteObject(array(
				        'Bucket' => env('AWS_BUCKET'),
				        'Key'    => "mobile_app_data/".$videoName
				    ));
				}
				$deleteVideoTemp = VideoTemp::where('lessionId',$id)->where('topicId',0)->delete();
			}

			$lession->courseId = $request->get('courseId');
			$lession->name = $request->get('name');
			$lession->video_thumb = $thumbfilename;
			$lession->fullvideo = $videofilename;
			$lession->pdf = $pdffilename;
			$lession->content = $request->get('content');
			$lession->isFree = $request->get('free');
			$lession->uploads3 = $uploads3;
			$lession->save();
			$lessionId=$id;

			\Session::flash('msg', 'Lesson Updated Successfully.');
			return redirect('/admin/lession');
		}

	}

	public function delete($id)
	{
		$data = Lession::findOrFail($id);
		$data->deleted=1;
		$data->update();

	    return redirect()->back();
	}

	public function updateStatus($id,$status)
	{
		$data = Lession::findOrFail($id);
		$data->status=$status;
		$data->update();
		return redirect()->back();
	}

	public function sortUp($id,$sort_id)
	{
		$sort_id = $sort_id - 1;
		$lession = Lession::where("sort_id", $sort_id);
		$lession->update(array("sort_id"=>$sort_id + 1));
		$lession1 = Lession::findOrFail($id);
		$lession1->sort_id = $sort_id;
		$lession1->save();

	    return redirect()->back();
	}

	public function sortDown($id,$sort_id)
	{
		$sort_id = $sort_id + 1;
		$lession = Lession::where("sort_id", $sort_id);
		$lession->update(array("sort_id"=>$sort_id - 1));
		$lession1 = Lession::findOrFail($id);
		$lession1->sort_id = $sort_id;
		$lession1->save();

	    return redirect()->back();
	}

	public function convertVideo()
	{
		$data = VideoTemp::where('lessionId','!=',0)->where('topicId',0)->orderBy('id', 'ASC')->get();
		return view('admin.lession.convertVideo', compact('data'));
	}

	public function approveVideo($id)
	{
		$videoTemp = VideoTemp::where('id',$id)->first();
		$lessionId = $videoTemp->lessionId;
		$low_status = $videoTemp->low_status;
		$med_status = $videoTemp->med_status;
		$high_status = $videoTemp->high_status;
		$lession = Lession::findOrFail($lessionId);
		if ($low_status == 1) {
			$lession->video_1 = $videoTemp->low_video;
		}
		if ($med_status == 1) {
			$lession->video_2 = $videoTemp->med_video;
		}
		if ($high_status == 1) {
			$lession->video_3 = $videoTemp->high_video;
		}
		$lession->update();
		
		$delete = VideoTemp::where('id',$id)->delete();

		\Session::flash('msg', 'Converted Video Approved Successfully.');
		return redirect('/admin/lession/convertVideo');
	}

	public function imgremove($id)
	{
		$lession = Lession::findOrFail($id);
		if(file_exists( public_path().'/lessions/'.$lession->video_thumb )) {
			unlink( public_path().'/lessions/'.$lession->video_thumb );
		}
		$lession->video_thumb = NULL;
		$lession->update();

		\Session::flash('msg', 'Lession Image Removed Successfully.');
		return redirect()->back();
	}

	public function vidremove($id, $video_type)
	{
		$s3 = AWS::createClient('s3');
		
		$lession = Lession::findOrFail($id);
		if(!empty($lession->fullvideo) && file_exists( public_path().'/lessions/'.$lession->fullvideo )) {
			unlink( public_path().'/lessions/'.$lession->fullvideo );
		}
		if(!empty($lession->fullvideo)){
			$videoName = $lession->fullvideo;
			$s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => $videoName
		    ));
			$s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => "mobile_app_data/".$videoName
		    ));
		}
		$lession->fullvideo = NULL;
		$lession->uploads3 = 0;
		if(!empty($lession->video_1) && file_exists( public_path().'/lessions/'.$lession->video_1 ) && $lession->video_1!='NA') {
			unlink( public_path().'/lessions/'.$lession->video_1 );
		}
		if(!empty($lession->video_1)){
			$videoName1 = $lession->video_1;
			$s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => "mobile_app_data/".$videoName1
		    ));
		}
		$lession->video_1 = NULL;
		$lession->uploads3v1 = 0;
		if(!empty($lession->video_2) && file_exists( public_path().'/lessions/'.$lession->video_2 ) && $lession->video_2!='NA') {
			unlink( public_path().'/lessions/'.$lession->video_2 );
		}
		if(!empty($lession->video_2)){
			$videoName2 = $lession->video_2;
			$s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => "mobile_app_data/".$videoName2
		    ));
		}
		$lession->video_2 = NULL;
		$lession->uploads3v2 = 0;
		if(!empty($lession->video_3) && file_exists( public_path().'/lessions/'.$lession->video_3 ) && $lession->video_3!='NA') {
			unlink( public_path().'/lessions/'.$lession->video_3 );
		}
		if(!empty($lession->video_3)){
			$videoName3 = $lession->video_3;
			$s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => "mobile_app_data/".$videoName3
		    ));
		}
		$lession->video_3 = NULL;
		$lession->uploads3v3 = 0;
		$deleteVideoTemp = VideoTemp::where('lessionId',$id)->where('topicId',0)->delete();
		$lession->processtatus = 0;
		$lession->starttime = NULL;
		$lession->endtime = NULL;
		$lession->update();

		\Session::flash('msg', 'Lession Video Removed Successfully.');
		return redirect()->back();
	}

	public function pdfremove($id)
	{
		$lession = Lession::findOrFail($id);
		if(file_exists( public_path().'/lessions/'.$lession->pdf )) {
			unlink( public_path().'/lessions/'.$lession->pdf );
		}
		$lession->pdf = NULL;
		$lession->update();

		\Session::flash('msg', 'Lession PDF Removed Successfully.');
		return redirect()->back();
	}

	public function getcourselession(Request $request)
	{
		$courseId=$request->get('courseId');

		//$getLession=Lession::where('courseId',$courseId)->where('status',1)->where('deleted',0)->get();
		$getLession = Lession::where('courseId',$courseId)->orderBy("sort_id", "ASC")->get();
		?>
		<option value="">--Select--</option>
		<?php
		foreach ($getLession as $key => $value) {
			?>
			<option value="<?php echo $value['id'] ?>"><?php echo $value['name'] ?></option>
			<?php
		}
	}

	public function getlessiontopic(Request $request)
	{
		$lessionId=$request->get('lessionId');

		//$getTopics=Chapter::where('lessionId',$lessionId)->where('status',1)->where('deleted',0)->get();
		$getTopics = Chapter::where('lessionId',$lessionId)->orderBy("sort_id", "ASC")->get();
		?>
		<option value="">--Select--</option>
		<?php
		foreach ($getTopics as $key => $value) {
			?>
			<option value="<?php echo $value['id'] ?>"><?php echo $value['name'] ?></option>
			<?php
		}
	}

}
