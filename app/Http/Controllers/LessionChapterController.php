<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Courses;
use App\Models\Lession;
use App\Models\Chapter;
use App\Models\StudentClass;
use App\Models\VideoTemp;
use AWS;


class LessionChapterController extends Controller
{
	public function index(Request $request)
	{
		$classes	= StudentClass::orderBy("id", "ASC")->get();
		$courses	= Courses::where("deleted", 0)->orderBy("sort_id", "ASC")->get();
		$lessions	= Lession::where("deleted", 0)->orderBy("sort_id", "ASC")->get();

		$class_type = ($request->class_type) ? $request->class_type : '';
		$class_id 	= ($request->class_id) ? $request->class_id : '';
		$courseId 	= ($request->course) ? $request->course : '';
		$lessonId 	= ($request->lession) ? $request->lession : '';
		$search		= ($request->search) ? $request->search : '';

		$data = Chapter::with("courses","lession")->orderBy("sort_id", "ASC")
				 ->whereHas("courses", function($query) use ($class_type) {
					if (isset($class_type) && !empty($class_type)) {
						$query->where("class_type", $class_type);
					}
				 })
				 ->whereHas("courses", function($query) use ($class_id) {
					if (isset($class_id) && !empty($class_id)) {
						$query->whereRaw("find_in_set($class_id,class_ids)");
					}
				 })
				 ->Where(function($query) use ($courseId) {
					if (isset($courseId) && !empty($courseId)) {
						$query->where("courseId", $courseId);
					}
				 })
				 ->Where(function($query) use ($lessonId) {
					if (isset($lessonId) && !empty($lessonId)) {
						$query->where("lessionId", $lessonId);
					}
				 })
				 ->Where(function($query) use ($search) {
					if (isset($search) && !empty($search)) {
						$query->where("name", 'like', "%" . $search . "%");
					}
				 })
				 ->where("deleted", 0);
		$totalData = $data->count();
		$data = $data->paginate(50);
		
		return view('admin.chapter.index', compact('classes','courses','lessions','data','totalData'));
	}

	public function create()
	{
		$courses = Courses::where("deleted", 0)->orderBy("sort_id", "ASC")->get();

		return view('admin.chapter.create',compact('courses'));
	}

	public function store(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'name' => 'required',
			'video' => 'required|mimes:mp4',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$thumbfilename = $videofilename = $pdffilename = NULL;
			$thumb = $request->file('thumb');
			$video = $request->file('video');
			$pdf = $request->file('pdf');
			
			$destinationPath = public_path() . '/lessions/';
			if($thumb){
				$thumbOriginalName = $thumb->getClientOriginalName();
				$thumbfilename = time() . $thumbOriginalName;
				$thumb->move($destinationPath, $thumbfilename);
			}
			if($video){
				$videoOriginalName = $video->getClientOriginalName();
				//$videofilename = rand(111,999).strtotime(date('Y-m-d-H:isa')).$videoOriginalName;
				$videofilename = time() . "_org.mp4";
				$video->move($destinationPath, $videofilename);
			}
			if($pdf){
				$pdfOriginalName = $pdf->getClientOriginalName();
				$pdffilename = time() . $pdfOriginalName;
				$pdf->move($destinationPath, $pdffilename);
			}
				
			$chapter = new Chapter();
			$chapter->courseId = $request->get('courseId');
			$chapter->lessionId = $request->get('lessionId');
			$chapter->name = $request->get('name');
			$chapter->video_thumb = $thumbfilename;
			$chapter->fullvideo = $videofilename;
			$chapter->pdf = $pdffilename;
			$chapter->content = $request->get('content');
			$chapter->isFree = $request->get('free');
			$chapter->save();
			$chapterId=$chapter->id;
			if($chapterId){
				$chapter = Chapter::findOrFail($chapterId);
				$chapter->sort_id = $chapterId;
				$chapter->save();
			}
			
			\Session::flash('msg', 'Lesson Chapter Added Successfully.');
			return back();
		}
	}

	public function show(Courses $courses)
	{
		//
	}

	public function edit($id)
	{
		$chapter = Chapter::find($id);
		$courses = Courses::where('deleted', 0)->orderBy('sort_id', 'ASC')->get();
		$lession = Lession::where('courseId', $chapter->courseId)->where('deleted', 0)->orderBy('sort_id', 'ASC')->get();

		return view('admin.chapter.edit',compact('chapter','courses','lession'));
	}

	public function update(Request $request, Courses $courses, $id)
	{
		//ak $s3 = AWS::createClient('s3');
		
		$validator = Validator::make($request->all(), [
			'name' => 'required',
			'video' => 'mimes:mp4',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		}else {
			$chapter = Chapter::findOrFail($id);

			$thumb = $request->file('thumb');
			$video = $request->file('video');
			$pdf = $request->file('pdf');

			$destinationPath = public_path() . '/lessions/';
			if($thumb){
				$thumbOriginalName = $thumb->getClientOriginalName();
				$thumbfilename = "topic_" . time() . $thumbOriginalName;
				$thumb->move($destinationPath, $thumbfilename);
			}else {
				$thumbfilename = $chapter->video_thumb;
			}
			if($video){
				$videoOriginalName = $video->getClientOriginalName();
				//$videofilename = rand(111,999).strtotime(date('Y-m-d-H:isa')).$videoOriginalName;
				$videofilename = "topic_" . time() . "_org.mp4";
				$video->move($destinationPath, $videofilename);
			    $uploads3 = 0;
			    $chapter->video_1 = $chapter->video_2 = $chapter->video_3 = $chapter->starttime = $chapter->endtime = NULL;
				$chapter->uploads3v1 = $chapter->uploads3v2 = $chapter->uploads3v3 = $chapter->processtatus = 0;
			} else {
				$videofilename = $chapter->fullvideo;
			    $uploads3 = $chapter->uploads3;
			}
			if($pdf){
				$pdfOriginalName = $pdf->getClientOriginalName();
				$pdffilename = "topic_" . time() . $pdfOriginalName;
				$pdf->move($destinationPath, $pdffilename);
			}else {
				$pdffilename=$chapter->pdf;
			}
			
			if($video){
				if(!empty($chapter->fullvideo) && file_exists( public_path().'/lessions/'.$chapter->fullvideo )) {
					unlink( public_path().'/lessions/'.$chapter->fullvideo );
				}
/* //ak				if(!empty($chapter->fullvideo)){
					$videoName = $chapter->fullvideo;
					$s3->deleteObject(array(
				        'Bucket' => env('AWS_BUCKET'),
				        'Key'    => $videoName
				    ));
					$s3->deleteObject(array(
				        'Bucket' => env('AWS_BUCKET'),
				        'Key'    => "mobile_app_data/".$videoName
				    ));
				}
				*/
				$deleteVideoTemp = VideoTemp::where('topicId',$id)->delete();
			}

			$chapter->courseId = $request->get('courseId');
			$chapter->lessionId = $request->get('lessionId');
			$chapter->name = $request->get('name');
			$chapter->video_thumb = $thumbfilename;
			$chapter->fullvideo = $videofilename;
			$chapter->pdf = $pdffilename;
			$chapter->content = $request->get('content');
			$chapter->isFree = $request->get('free');
			$chapter->uploads3 = $uploads3;
			$chapter->save();
			$chapterId=$id;

			\Session::flash('msg', 'Lesson Chapter Updated Successfully.');
			return redirect('/admin/lession-chapter');
		}

	}

	public function updateStatus($id,$status)
	{
		$chapter = Chapter::findOrFail($id);
		$chapter->status=$status;
		$chapter->update();

	    return redirect()->back();
	}

	public function delete($id)
	{
		$chapter = Chapter::findOrFail($id);
		$chapter->deleted=1;
		$chapter->update();

	    return redirect()->back();
	}

	public function sortUp($id,$sort_id)
	{
		$sort_id = $sort_id - 1;
		$chapter = Chapter::where("sort_id", $sort_id);
		$chapter->update(array("sort_id"=>$sort_id + 1));
		$chapter1 = Chapter::findOrFail($id);
		$chapter1->sort_id = $sort_id;
		$chapter1->save();

	    return redirect()->back();
	}

	public function sortDown($id,$sort_id)
	{
		$sort_id = $sort_id + 1;
		$chapter = Chapter::where("sort_id", $sort_id);
		$chapter->update(array("sort_id"=>$sort_id - 1));
		$chapter1 = Chapter::findOrFail($id);
		$chapter1->sort_id = $sort_id;
		$chapter1->save();

	    return redirect()->back();
	}

	public function convertVideo()
	{
		$data = VideoTemp::where('topicId','!=',0)->orderBy('id', 'ASC')->get();
		
		return view('admin.chapter.convertVideo', compact('data'));
	}

	public function approveVideo($id)
	{
		$videoTemp = VideoTemp::where('id',$id)->first();
		$topicId = $videoTemp->topicId;
		$low_status = $videoTemp->low_status;
		$med_status = $videoTemp->med_status;
		$high_status = $videoTemp->high_status;
		$chapter = Chapter::findOrFail($topicId);
		if ($low_status == 1) {
			$chapter->video_1 = $videoTemp->low_video;
		}
		if ($med_status == 1) {
			$chapter->video_2 = $videoTemp->med_video;
		}
		if ($high_status == 1) {
			$chapter->video_3 = $videoTemp->high_video;
		}
		$chapter->update();
		
		$delete = VideoTemp::where('id',$id)->delete();

		\Session::flash('msg', 'Converted Video Approved Successfully.');
	    return redirect('/admin/lession-chapter/convertVideo');
	}

	public function imgremove($id)
	{
		$chapter = Chapter::findOrFail($id);
		if(file_exists( public_path().'/lessions/'.$chapter->video_thumb )) {
			unlink( public_path().'/lessions/'.$chapter->video_thumb );
		}
		$chapter->video_thumb = NULL;
		$chapter->update();

		\Session::flash('msg', 'Chapter Image Removed Successfully.');
	    return redirect()->back();
	}

	public function vidremove($id, $video_type)
	{
		$s3 = AWS::createClient('s3');
		
		$chapter = Chapter::findOrFail($id);
		if(!empty($chapter->fullvideo) && file_exists( public_path().'/lessions/'.$chapter->fullvideo )) {
			unlink( public_path().'/lessions/'.$chapter->fullvideo );
		}
		if(!empty($chapter->fullvideo)){
			$videoName = $chapter->fullvideo;
			$s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => $videoName
		    ));
			$s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => "mobile_app_data/".$videoName
		    ));
		}
		$chapter->fullvideo = NULL;
		$chapter->uploads3 = 0;
		if(!empty($chapter->video_1) && file_exists( public_path().'/lessions/'.$chapter->video_1 ) && $chapter->video_1!='NA') {
			unlink( public_path().'/lessions/'.$chapter->video_1 );
		}
		if(!empty($chapter->video_1)){
			$videoName1 = $chapter->video_1;
			$s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => "mobile_app_data/".$videoName1
		    ));
		}
		$chapter->video_1 = NULL;
		$chapter->uploads3v1 = 0;
		if(!empty($chapter->video_2) && file_exists( public_path().'/lessions/'.$chapter->video_2 ) && $chapter->video_2!='NA') {
			unlink( public_path().'/lessions/'.$chapter->video_2 );
		}
		if(!empty($chapter->video_2)){
			$videoName2 = $chapter->video_2;
			$s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => "mobile_app_data/".$videoName2
		    ));
		}
		$chapter->video_2 = NULL;
		$chapter->uploads3v2 = 0;
		if(!empty($chapter->video_3) && file_exists( public_path().'/lessions/'.$chapter->video_3 ) && $chapter->video_3!='NA') {
			unlink( public_path().'/lessions/'.$chapter->video_3 );
		}
		if(!empty($chapter->video_3)){
			$videoName3 = $chapter->video_3;
			$s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => "mobile_app_data/".$videoName3
		    ));
		}
		$chapter->video_3 = NULL;
		$chapter->uploads3v3 = 0;
		$deleteVideoTemp = VideoTemp::where('topicId',$id)->delete();
		$chapter->processtatus = 0;
		$chapter->starttime = NULL;
		$chapter->endtime = NULL;
		$chapter->update();

		\Session::flash('msg', 'Chapter Video Removed Successfully.');
	    return redirect()->back();
	}

	public function pdfremove($id)
	{
		$chapter = Chapter::findOrFail($id);
		if(file_exists( public_path().'/lessions/'.$chapter->pdf )) {
			unlink( public_path().'/lessions/'.$chapter->pdf );
		}
		$chapter->pdf = NULL;
		$chapter->update();

		\Session::flash('msg', 'Chapter PDF Removed Successfully.');
	    return redirect()->back();
	}

	public function getchapterbylession(Request $request)
	{
		$lessionId = $request->get('lessionId');

		$getChapters = Chapter::where('lessionId', $lessionId)->where('status', 1)->where('deleted', 0)->orderBy('sort_id', 'ASC')->get();
		?>
		<option>--Select--</option>
		<?php
		foreach ($getChapters as $key => $value) {
			?>
			<option value="<?php echo $value['id'] ?>"><?php echo $value['name'] ?></option>
			<?php
		}
	}

}
