<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use App\Http\Helper as Helper;
use App\Models\Assignment;
use App\Models\AssignmentSubmission;
use App\Models\Courses;
use App\Models\StudentClass;
use App\Models\User;
use AWS;

class AssignmentsController extends Controller
{
	protected $helper;

	public function __construct(Helper $helper)
	{
		$this->helper = $helper;
	}

	public function index(Request $request)
	{
		$users = User::where("role_id", "!=", 3)->where("deleted", 0)->orderBy("name", "ASC")->get();
		$classes = StudentClass::orderBy("id", "ASC")->get();
		
		$added_by = $request->added_by;
		$class_type = ($request->class_type) ? $request->class_type : '';
		$class_id 	= ($request->class_id) ? $request->class_id : '';
		$course   = $request->course;

		$user = Auth::user();
		//dd($user);
		$role_id = $user->role_id;
		if($role_id==2){
			$added_by = $user->id;
			//dd($added_by);
		}

		$data = Assignment::with('user','course')
					->where('deleted',0)
	                ->Where(function($query) use ($added_by) {
	                    if (isset($added_by) && !empty($added_by)) {
	                        $query->where('added_by', $added_by);
	                    }
	                })
					->whereHas("course", function($query) use ($class_type) {
						if (isset($class_type) && !empty($class_type)) {
							$query->where("class_type", $class_type);
						}
					})
					->whereHas("course", function($query) use ($class_id) {
						if (isset($class_id) && !empty($class_id)) {
							$query->whereRaw("find_in_set($class_id,class_ids)");
						}
					})
	                ->Where(function($query) use ($course) {
	                    if (isset($course) && !empty($course)) {
	                        //$query->where('title', 'LIKE', "%".$search."%");
	                        $query->where('course_id', $course);
	                    }
	                })
	            	->OrderBy("sort_id", "ASC");
		$totalData = $data->count();
		$data = $data->paginate(50);

		if($role_id==2){
			$allowedCourses = $user->allow_courses;
			$allowedCourses = explode(",", trim($allowedCourses));
			$courses = Courses::whereIn("id", $allowedCourses)->where("deleted",0)->orderBy("sort_id", "ASC")->get();
		}else{
			$courses = Courses::where("status",1)->where("deleted",0)->orderBy("sort_id", "ASC")->get();
		}
		//echo '<pre />'; print_r($data); die;

		return view('admin.assignment.index', compact('users','classes','data','totalData','courses'));
	}

	public function create()
	{
		$classes = StudentClass::where("status", 1)->where("deleted", 0)->orderBy("id", "ASC")->get();
		$user = Auth::user();
		$role_id = $user->role_id;
		if($role_id==2){
			$allowedCourses = $user->allow_courses;
			$allowedCourses = explode(",", trim($allowedCourses));
			$courses = Courses::whereIn("id", $allowedCourses)->where("deleted",0)->orderBy("sort_id", "ASC")->get();
		}else{
			$courses = Courses::where("status",1)->where("deleted",0)->orderBy("sort_id", "ASC")->get();
		}
		return view('admin.assignment.create',compact('classes','courses'));
	}

	public function store(Request $request)
	{
		// echo '<pre />'; print_r($request->all());die;
		$validator = Validator::make($request->all(), [
			'course_id' => 'required',
			'title' => 'required',
			'image_file' => 'mimes:jpeg,jpg,png,gif,webp',
			'video_file' => 'mimes:mp4',
			'content' => 'required',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$imageUrl = $videoUrl = $otherUrl = NULL;
			$image_file = $request->file('image_file');
			$video_file = $request->file('video_file');
			$another_file = $request->file('another_file');
			
			if($image_file){
				$destinationPath = public_path().'/upload/assignments/';
				$imageOriginalFile = $image_file->getClientOriginalName();
				$imageFilename = "assignment_0_".time().$imageOriginalFile;
				$image_file->move($destinationPath, $imageFilename);
				//S3 Upload
				$s3 = AWS::createClient('s3');
				$s3->putObject(array(
				    'Bucket'     => env('AWS_BUCKET'),
				    'Key'        => "assignment_data/".$imageFilename,
				    'SourceFile' => $destinationPath.$imageFilename,
				));
				$imageUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $imageFilename;
				if(!empty($image_file) && file_exists( $destinationPath.$imageFilename )) {
					unlink( $destinationPath.$imageFilename );
				}
			}
			if($video_file){
				$destinationPath = public_path().'/upload/assignments/';
				$videoOriginalFile = $video_file->getClientOriginalName();
				//$videoFilename=rand(111,999).strtotime(date('Y-m-d-H:isa')).$videoOriginalFile;
				$videoFilename = "assignment_0_".time()."_org.mp4";
				$video_file->move($destinationPath, $videoFilename);
				//S3 Upload
				$s3 = AWS::createClient('s3');
				$s3->putObject(array(
				    'Bucket'     => env('AWS_BUCKET'),
				    'Key'        => "assignment_data/".$videoFilename,
				    'SourceFile' => $destinationPath.$videoFilename,
				));
				$videoUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $videoFilename;
				if(!empty($video_file) && file_exists( $destinationPath.$videoFilename )) {
					unlink( $destinationPath.$videoFilename );
				}
			}
			if($another_file){
				$destinationPath = public_path().'/upload/assignments/';
				$otherOriginalFile = $another_file->getClientOriginalName();
				$otherFilename = "assignment_other_".time().$otherOriginalFile;
				$another_file->move($destinationPath, $otherFilename);
				//S3 Upload
				$s3 = AWS::createClient('s3');
				$s3->putObject(array(
				    'Bucket'     => env('AWS_BUCKET'),
				    'Key'        => "assignment_data/".$otherFilename,
				    'SourceFile' => $destinationPath.$otherFilename,
				));
				$otherUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $otherFilename;
				if(!empty($another_file) && file_exists( $destinationPath.$otherFilename )) {
					unlink( $destinationPath.$otherFilename );
				}
			}
			
			$assignment = new Assignment();
			$assignment->added_by = auth()->user()->id;
			$assignment->course_id = $request->get('course_id');
			$assignment->title = $request->get('title');
			$assignment->content = $request->get('content');
			$assignment->image = $imageUrl;
			$assignment->video = $videoUrl;
			$assignment->other_file = $otherUrl;
			$assignment->save();
			$assignmentId=$assignment->id;
			if($assignmentId){
				$assign = Assignment::findOrFail($assignmentId);
				$assign->sort_id = $assignmentId;
				$assign->update();
			}
			
			\Session::flash('msg', 'Assignment Added Successfully.');
			return back();
		}
	}

	public function show(Courses $courses)
	{
		//
	}

	public function edit($id)
	{
		$classes = StudentClass::where("status", 1)->where("deleted", 0)->orderBy("id", "ASC")->get();
		$assignment = Assignment::find($id);
		$user = Auth::user();
		$role_id = $user->role_id;
		if($role_id==2){
			$added_by = $user->id;
			if($added_by != $assignment->added_by){
				\Session::flash('msg', 'You have not permitted to access that!');
				return redirect('/admin/assignments');
			}
			$allowedCourses = $user->allow_courses;
			$allowedCourses = explode(",", trim($allowedCourses));
			$courses = Courses::whereIn("id", $allowedCourses)->where("deleted",0)->orderBy("sort_id", "ASC")->get();
		}else{
			$courses = Courses::where("status",1)->where("deleted",0)->orderBy("sort_id", "ASC")->get();
		}

		return view('admin.assignment.edit',compact('classes','assignment','courses'));
	}

	public function update(Request $request, $id)
	{
		//dd($request->all());
		$validator = Validator::make($request->all(), [
			'course_id' => 'required',
			'title' => 'required',
			'image_file' => 'mimes:jpeg,jpg,png,gif,webp',
			'video_file' => 'mimes:mp4',
			'content' => 'required',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$assignment = Assignment::findOrFail($id);
			$image_file = $request->file('image_file');
			$video_file = $request->file('video_file');
			$another_file = $request->file('another_file');

			if($image_file){
				$destinationPath = public_path().'/upload/assignments/';
				$imageOriginalFile = $image_file->getClientOriginalName();
				$imageFilename = "assignment_".$id."_".time().$imageOriginalFile;
				$image_file->move($destinationPath, $imageFilename);
				//S3 Upload
				$s3 = AWS::createClient('s3');
				$s3->putObject(array(
				    'Bucket'     => env('AWS_BUCKET'),
				    'Key'        => "assignment_data/".$imageFilename,
				    'SourceFile' => $destinationPath.$imageFilename,
				));
				$imageUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $imageFilename;
				if(!empty($image_file) && file_exists( $destinationPath.$imageFilename )) {
					unlink( $destinationPath.$imageFilename );
				}
				if(!empty($assignment->image) && file_exists( $destinationPath.$assignment->image )) {
					unlink( $destinationPath.$assignment->image );
				}
				if(!empty($assignment->image)){
					$uploadedImageUrl = $assignment->image;
					$uploadedImageArr = explode("https://s3.ap-south-1.amazonaws.com/video.brainywoodindia.com/assignment_data/", $uploadedImageUrl);
					$imageName = $uploadedImageArr[1];
					$result = $s3->deleteObject(array(
				        'Bucket' => env('AWS_BUCKET'),
				        'Key'    => "assignment_data/".$imageName
				    ));
				}
			}else {
				$imageUrl = $assignment->image;
			}
			if($video_file){
				$destinationPath = public_path().'/upload/assignments/';
				$videoOriginalFile = $video_file->getClientOriginalName();
				//$videoFilename = rand(111,999).strtotime(date('Y-m-d-H:isa')).$videoOriginalFile;
				$videoFilename = "assignment_".$id."_".time()."_org.mp4";
				$video_file->move($destinationPath, $videoFilename);
				//S3 Upload
				$s3 = AWS::createClient('s3');
				$s3->putObject(array(
				    'Bucket'     => env('AWS_BUCKET'),
				    'Key'        => "assignment_data/".$videoFilename,
				    'SourceFile' => $destinationPath.$videoFilename,
				));
				$videoUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $videoFilename;
				if(!empty($video_file) && file_exists( $destinationPath.$videoFilename )) {
					unlink( $destinationPath.$videoFilename );
				}
				if(!empty($assignment->video) && file_exists( $destinationPath.$assignment->video )) {
					unlink( $destinationPath.$assignment->video );
				}
				if(!empty($assignment->video)){
					$uploadedVideoUrl = $assignment->video;
					$uploadedVideoArr = explode("https://s3.ap-south-1.amazonaws.com/video.brainywoodindia.com/assignment_data/", $uploadedVideoUrl);
					$videoName = $uploadedVideoArr[1];
					$result = $s3->deleteObject(array(
				        'Bucket' => env('AWS_BUCKET'),
				        'Key'    => "assignment_data/".$videoName
				    ));
				}
			}else {
				$videoUrl = $assignment->video;
			}
			
			if($video_file){
				if(!empty($assignment->video) && file_exists( public_path().'/upload/assignments/'.$assignment->video )) {
					unlink( public_path().'/upload/assignments/'.$assignment->video );
				}
			}
			if($another_file){
				$destinationPath = public_path().'/upload/assignments/';
				$otherOriginalFile = $another_file->getClientOriginalName();
				$otherFilename = "assignment_other_".$id."_".time().$otherOriginalFile;
				$another_file->move($destinationPath, $otherFilename);
				//S3 Upload
				$s3 = AWS::createClient('s3');
				$s3->putObject(array(
				    'Bucket'     => env('AWS_BUCKET'),
				    'Key'        => "assignment_data/".$otherFilename,
				    'SourceFile' => $destinationPath.$otherFilename,
				));
				$otherUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $otherFilename;
				if(!empty($another_file) && file_exists( $destinationPath.$otherFilename )) {
					unlink( $destinationPath.$otherFilename );
				}
				if(!empty($assignment->other_file) && file_exists( $destinationPath.$assignment->other_file )) {
					unlink( $destinationPath.$assignment->other_file );
				}
				if(!empty($assignment->other_file)){
					$uploadedOtherUrl = $assignment->other_file;
					$uploadedOtherArr = explode("https://s3.ap-south-1.amazonaws.com/video.brainywoodindia.com/assignment_data/", $uploadedOtherUrl);
					$otherName = $uploadedOtherArr[1];
					$result = $s3->deleteObject(array(
				        'Bucket' => env('AWS_BUCKET'),
				        'Key'    => "assignment_data/".$otherName
				    ));
				}
			}else {
				$otherUrl = $assignment->other_file;
			}
			$assignment->added_by = auth()->user()->id;
			$assignment->course_id = $request->get('course_id');
			$assignment->title = $request->get('title');
			$assignment->content = $request->get('content');
			$assignment->image = $imageUrl;
			$assignment->video = $videoUrl;
			$assignment->other_file = $otherUrl;
			$assignment->save();

			\Session::flash('msg', 'Assignment updated Successfully.');
			return redirect('/admin/assignments');
		}

	}

	public function delete($id)
	{
		$data = Assignment::findOrFail($id);
		$data->deleted=1;
		$data->update();

	    return redirect()->back();
	}

	public function updateStatus($id,$status)
	{
		$data = Assignment::findOrFail($id);
		$data->status=$status;
		$data->update();

		return redirect()->back();
	}

	public function sortUp($id,$sort_id)
	{
		$sort_id = $sort_id - 1;
		$record = Assignment::where("sort_id", $sort_id);
		$record->update(array("sort_id"=>$sort_id + 1));
		$record1 = Assignment::findOrFail($id);
		$record1->sort_id = $sort_id;
		$record1->save();

		\Session::flash('msg', 'Assignment Order Changed Successfully.');
	    return redirect()->back();
	}

	public function sortDown($id,$sort_id)
	{
		$sort_id = $sort_id + 1;
		$record = Assignment::where("sort_id", $sort_id);
		$record->update(array("sort_id"=>$sort_id - 1));
		$record1 = Assignment::findOrFail($id);
		$record1->sort_id = $sort_id;
		$record1->save();

		\Session::flash('msg', 'Assignment Order Changed Successfully.');
	    return redirect()->back();
	}

	public function imageremove($id)
	{
		$assignment = Assignment::findOrFail($id);
		if(file_exists( public_path().'/upload/assignments/'.$assignment->image )) {
			unlink( public_path().'/upload/assignments/'.$assignment->image );
		}
		//S3 Connection
		$s3 = AWS::createClient('s3');
		if(!empty($assignment->image)){
			$uploadedImageUrl = $assignment->image;
			$uploadedImageArr = explode("https://s3.ap-south-1.amazonaws.com/video.brainywoodindia.com/assignment_data/", $uploadedImageUrl);
			$imageName = $uploadedImageArr[1];
			$result = $s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => "assignment_data/".$imageName
		    ));
		}
		$assignment->image = NULL;
		$assignment->update();

		\Session::flash('msg', 'Assignment Image Removed Successfully.');
		return redirect()->back();
	}

	public function videoremove($id)
	{
		$assignment = Assignment::findOrFail($id);
		if(!empty($assignment->video) && file_exists( public_path().'/upload/assignments/'.$assignment->video )) {
			unlink( public_path().'/upload/assignments/'.$assignment->video );
		}
		//S3 Connection
		$s3 = AWS::createClient('s3');
		if(!empty($assignment->video)){
			$uploadedVideoUrl = $assignment->video;
			$uploadedVideoArr = explode("https://s3.ap-south-1.amazonaws.com/video.brainywoodindia.com/assignment_data/", $uploadedVideoUrl);
			$videoName = $uploadedVideoArr[1];
			$result = $s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => "assignment_data/".$videoName
		    ));
		}
		$assignment->video = NULL;
		$assignment->update();

		\Session::flash('msg', 'Assignment Video Removed Successfully.');
		return redirect()->back();
	}

	public function anotherfileremove($id)
	{
		$assignment = Assignment::findOrFail($id);
		if(file_exists( public_path().'/upload/assignments/'.$assignment->other_file )) {
			unlink( public_path().'/upload/assignments/'.$assignment->other_file );
		}
		//S3 Connection
		$s3 = AWS::createClient('s3');
		if(!empty($assignment->other_file)){
			$uploadedOtherUrl = $assignment->other_file;
			$uploadedOtherArr = explode("https://s3.ap-south-1.amazonaws.com/video.brainywoodindia.com/assignment_data/", $uploadedOtherUrl);
			$otherFileName = $uploadedOtherArr[1];
			$result = $s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => "assignment_data/".$otherFileName
		    ));
		}
		$assignment->other_file = NULL;
		$assignment->update();

		\Session::flash('msg', 'Assignment Other File Removed Successfully.');
		return redirect()->back();
	}

	public function getUserAssignments(Request $request, $assignment=NULL)
	{
		$assignment_id 		= $request->assignment_id;
		$user_id 			= $request->user_id;
		$assigned_to 		= $request->assigned_to;
		$teacher_assigned 	= isset($request->assigned) ? ($request->assigned=='not') ? 0 : 1 : '';
		$teacher_approved 	= $request->teacher_approved;
		//dd($teacher_assigned);
		$user = Auth::user();
		//dd($user);
		$role_id = $user->role_id;
		if($role_id==2){
			$assigned_to = $user->id;
			$allowedCourses = $user->allow_courses;
			$allowedCourses = explode(",", trim($allowedCourses));
			$record_data = Assignment::find($assignment);
			$course_id = $record_data->course_id;
			//dd($allowedCourses);
			if(!in_array($course_id, $allowedCourses)){
				\Session::flash('msg', 'You have not permitted to access that!');
				return redirect()->back();
			}
		}
		if(!empty($assignment)){
			/*if(isset($teacher_assigned) && $teacher_assigned==0){
				if(empty($teacher_assigned) && $teacher_assigned!=0){
					dd($teacher_assigned);
					$data = AssignmentSubmission::with('assignment','user')->where('assignment_id', $assignment)->where('user_status',1)->where('deleted',0)->orderBy('id','DESC')->get();
				}else{
					$data = AssignmentSubmission::with('assignment','user')->where('assignment_id', $assignment)->where('assigned_to', 0)->where('user_status',1)->where('deleted',0)->orderBy('id','DESC')->get();
				}
			}else{*/
				$data = AssignmentSubmission::with('assignment','user')
		                ->where('assignment_id', $assignment)
		                ->Where(function($query) use ($user_id) {
		                    if (isset($user_id) && !empty($user_id)) {
		                        $query->where('user_id', $user_id);
		                    }
		                })
		                ->Where(function($query) use ($assigned_to) {
		                    if (isset($assigned_to) && !empty($assigned_to)) {
		                        $query->where('assigned_to', $assigned_to);
		                    }
		                })
		                ->Where(function($query) use ($teacher_approved) {
		                    if (isset($teacher_approved) && !empty($teacher_approved)) {
		                        $query->where('teacher_approved', $teacher_approved);
		                    }
		                })
						->where('user_status',1)
						->where('deleted',0)
						->groupBy('user_id')
		            	->orderBy('id','DESC')->paginate(50);
		    //}
		}else{
			$data = AssignmentSubmission::with('assignment','user')
		                ->Where(function($query) use ($assignment_id) {
		                    if (isset($assignment_id) && !empty($assignment_id)) {
		                        $query->where('assignment_id', $assignment_id);
		                    }
		                })
		                ->Where(function($query) use ($user_id) {
		                    if (isset($user_id) && !empty($user_id)) {
		                        $query->where('user_id', $user_id);
		                    }
		                })
		                ->Where(function($query) use ($assigned_to) {
		                    if (isset($assigned_to) && !empty($assigned_to)) {
		                        $query->where('assigned_to', $assigned_to);
		                    }
		                })
		                ->Where(function($query) use ($teacher_assigned) {
		                    if (isset($teacher_assigned) && !empty($teacher_assigned)) {
		                        $query->where('assigned_to', '>', 0);
		                    }
		                })
		                ->Where(function($query) use ($teacher_approved) {
		                    if (isset($teacher_approved) && !empty($teacher_approved)) {
		                        $query->where('teacher_approved', $teacher_approved);
		                    }
		                })
						->where('user_status',1)
						->where('deleted',0)
						->groupBy('user_id')
		            	->OrderBy('id','DESC')->paginate(50);
		}
		$assignments = Assignment::Where('deleted',0)->get();
		$users = User::where("role_id", 3)->where("deleted", 0)->orderBy("name", "ASC")->get();
		$teachers = User::where("role_id", 2)->where("deleted", 0)->orderBy("name", "ASC")->get();
		//echo '<pre />'; print_r($data); die;
		return view('admin.assignment.submitted_assignments', compact('data','assignments','users','teachers'));
	}

	public function getTeacherAssigned(Request $request)
	{
		//echo "working here.."; die;
		//dd($request->all());
		$assignment_id 		= $request->assignment_id;
		$user_id 			= $request->user_id;
		$assigned_to 		= $request->assigned_to;
		$teacher_approved 	= ($request->teacher_approved != '') ? ($request->teacher_approved == 'not') ? 0 : 1 : '';
		//$teacher_approved 	= $request->teacher_approved;

		$user = Auth::user();
		//dd($user);
		$role_id = $user->role_id;
		if($role_id==2){
			$assigned_to = $user->id;
		}
		//dd($teacher_approved);
		$data = AssignmentSubmission::with('assignment','user')
	                ->where('assigned_to', $assigned_to)
	                ->Where(function($query) use ($assignment_id) {
	                    if (isset($assignment_id) && !empty($assignment_id)) {
	                        $query->where('assignment_id', $assignment_id);
	                    }
	                })
	                ->Where(function($query) use ($user_id) {
	                    if (isset($user_id) && !empty($user_id)) {
	                        $query->where('user_id', $user_id);
	                    }
	                })
	                ->Where(function($query) use ($teacher_approved) {
	                    if (isset($teacher_approved) && !empty($teacher_approved)) {
	                        $query->where('teacher_approved', $teacher_approved);
	                    }
	                })
					->where('user_status',1)
					->where('deleted',0)
					->groupBy('user_id','assignment_id')
	            	->OrderBy('id','DESC')->paginate(50);
		$assignments = Assignment::Where('deleted',0)->get();
		$users = User::where("role_id", 3)->where("deleted", 0)->orderBy("name", "ASC")->get();
		$teachers = User::where("role_id", 2)->where("deleted", 0)->orderBy("name", "ASC")->get();
		//echo '<pre />'; print_r($data); die;
		return view('admin.assignment.submitted_assignments', compact('data','assignments','users','teachers'));
	}

	public function submittedupdateStatus($id,$status)
	{
		$data = AssignmentSubmission::with('user')->findOrFail($id);
		$data->teacher_approved=$status;
		$data->update();
		if($status==1){
			//Send notification
			$userId = $data->user_id;
			$token = isset($data->user->deviceToken) ? $data->user->deviceToken : '';
			if ($token!='') {
				$title = 'BrainyWood';
				$click_action = 'Assignment';
				$module_id = $id;
				$message = 'Teacher remarked your submitted Assignment.';
				$this->helper->addNotification($userId,$message,$click_action,$module_id);
			}
		}

		return redirect()->back();
	}

    public function assigned_to_update(Request $request)
    {
		//dd($request->all());
    	$user_assignment_id = $request->user_assignment_id;
    	$assigned_to = $request->assigned_to;

    	$assignmentSubmit = AssignmentSubmission::findOrfail($user_assignment_id);
    	$assignmentSubmit->assigned_to = $assigned_to;
    	$assignmentSubmit->save();
		if($assignmentSubmit){
    		$assignToTeacher = AssignmentSubmission::where('assignment_id', $assignmentSubmit->assignment_id)->where('user_id', $assignmentSubmit->user_id)->update(['assigned_to' => $assigned_to]);
    		//echo 'done';
			\Session::flash('msg', 'Assignment assigned to teacher update successfully.');
		    $data         = array();
		    return response()->json([
		        'success' => true,
		        'data'    => $data,
		    ]);
		}else{
			\Session::flash('msg', 'Assignment assigned to teacher not successfully.');
		    $data         = array();
		    return response()->json([
		        'success' => true,
		        'data'    => $data,
		    ]);
		}
    }

    public function viewUserAssignment($id)
    {
		$record_data = AssignmentSubmission::with('assignment','user')->where('id', $id)->where('deleted',0)->first();
		$user_submitted = !empty($record_data) ? AssignmentSubmission::with('assignment','user')->where('assignment_id', $record_data->assignment_id)->where('user_id', $record_data->user_id)->where('deleted',0)->orderBy('id', 'ASC')->get() : [];
		//dd($user_submitted);
		$user = Auth::user();
		$role_id = $user->role_id;
		if($role_id==2){
			$assigned_to = $user->id;
			if($assigned_to != $record_data->assigned_to){
				\Session::flash('msg', 'You have not permitted to access that!');
				return redirect()->back();
			}
			$allowedCourses = $user->allow_courses;
			$allowedCourses = explode(",", trim($allowedCourses));
			$course_id = $record_data->assignment->course_id;
			if(!in_array($course_id, $allowedCourses)){
				\Session::flash('msg', 'You have not permitted to access that!');
				return redirect()->back();
			}
		}
		
		return view('admin.assignment.submitted_assignment_view', compact('record_data','user_submitted'));
    }

	public function teacher_remark(Request $request, $id)
	{
		//dd($request->all());
		if (!empty($request->file('teacher_images_file')) || !empty($request->file('teacher_videos_file')) || !empty($request->teacher_content) ) {
			$submittedAsignmnt = AssignmentSubmission::with('user')->findOrFail($id);
			$image_files = $request->file('teacher_images_file');
			$video_files = $request->file('teacher_videos_file');
			$another_files = $request->file('teacher_another_file');

			$imagesData = [];
			if($request->hasFile('teacher_images_file')){
				foreach($image_files as $image_file){
					if($image_file){
						$destinationPath = public_path().'/upload/assignments/';
						$imageOriginalFile = $image_file->getClientOriginalName();
						$imageFilename = "teacher_".$id."_".time().$imageOriginalFile;
						$image_file->move($destinationPath, $imageFilename);
						//S3 Upload
						$s3 = AWS::createClient('s3');
						$s3->putObject(array(
						    'Bucket'     => env('AWS_BUCKET'),
						    'Key'        => "assignment_data/".$imageFilename,
						    'SourceFile' => $destinationPath.$imageFilename,
						));
						$imagesData[] = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $imageFilename;
						if(!empty($image_file) && file_exists( $destinationPath.$imageFilename )) {
							unlink( $destinationPath.$imageFilename );
						}
						if(!empty($submittedAsignmnt->teacher_images)){
							$teacher_images_arr = !empty($submittedAsignmnt->teacher_images) ? explode(",", $submittedAsignmnt->teacher_images) : [];
							foreach($teacher_images_arr as $teacher_image){
								if(!empty($teacher_image) && file_exists( $destinationPath.$teacher_image )) {
									unlink( $destinationPath.$teacher_image );
								}
								$uploadedImageArr = explode("https://s3.ap-south-1.amazonaws.com/video.brainywoodindia.com/assignment_data/", $teacher_image);
								$imageName = $uploadedImageArr[1];
								$result = $s3->deleteObject(array(
							        'Bucket' => env('AWS_BUCKET'),
							        'Key'    => "assignment_data/".$imageName
							    ));
							}
						}
					}
				}
			}
			$videosData = [];
			if($request->hasFile('teacher_videos_file')){
				foreach($video_files as $video_file){
					if($video_file){
						$destinationPath = public_path().'/upload/assignments/';
						$videoOriginalFile = $video_file->getClientOriginalName();
						//$videoFilename = rand(111,999).strtotime(date('Y-m-d-H:isa')).$videoOriginalFile;
						$videoFilename = "teacher_".$id."_".time()."_org.mp4";
						$video_file->move($destinationPath, $videoFilename);
						//S3 Upload
						$s3 = AWS::createClient('s3');
						$s3->putObject(array(
						    'Bucket'     => env('AWS_BUCKET'),
						    'Key'        => "assignment_data/".$videoFilename,
						    'SourceFile' => $destinationPath.$videoFilename,
						));
						$videosData[] = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $videoFilename;
						if(!empty($video_file) && file_exists( $destinationPath.$videoFilename )) {
							unlink( $destinationPath.$videoFilename );
						}
						if(!empty($submittedAsignmnt->teacher_videos)){
							$teacher_videos_arr = !empty($submittedAsignmnt->teacher_videos) ? explode(",", $submittedAsignmnt->teacher_videos) : [];
							foreach($teacher_videos_arr as $teacher_video){
								if(!empty($teacher_video) && file_exists( $destinationPath.$teacher_video )) {
									unlink( $destinationPath.$teacher_video );
								}
								$uploadedVideoArr = explode("https://s3.ap-south-1.amazonaws.com/video.brainywoodindia.com/assignment_data/", $teacher_video);
								$videoName = $uploadedVideoArr[1];
								$result = $s3->deleteObject(array(
							        'Bucket' => env('AWS_BUCKET'),
							        'Key'    => "assignment_data/".$videoName
							    ));
							}
						}
					}
				}
			}
			$anotherFilesData = [];
			if($request->hasFile('teacher_another_file')){
				foreach($another_files as $another_file){
					if($another_file){
						$destinationPath = public_path().'/upload/assignments/';
						$anotherOriginalFile = $another_file->getClientOriginalName();
						$anotherFilename = "teacher_".$id."_".time().$anotherOriginalFile;
						$another_file->move($destinationPath, $anotherFilename);
						//S3 Upload
						$s3 = AWS::createClient('s3');
						$s3->putObject(array(
						    'Bucket'     => env('AWS_BUCKET'),
						    'Key'        => "assignment_data/".$anotherFilename,
						    'SourceFile' => $destinationPath.$anotherFilename,
						));
						$anotherFilesData[] = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $anotherFilename;
						if(!empty($another_file) && file_exists( $destinationPath.$anotherFilename )) {
							unlink( $destinationPath.$anotherFilename );
						}
						if(!empty($submittedAsignmnt->teacher_other_files)){
							$teacher_other_files_arr = !empty($submittedAsignmnt->teacher_other_files) ? explode(",", $submittedAsignmnt->teacher_other_files) : [];
							foreach($teacher_other_files_arr as $teacher_other_file){
								if(!empty($teacher_other_file) && file_exists( $destinationPath.$teacher_other_file )) {
									unlink( $destinationPath.$teacher_other_file );
								}
								$uploadedAnotherFileArr = explode("https://s3.ap-south-1.amazonaws.com/video.brainywoodindia.com/assignment_data/", $teacher_other_file);
								$anotherFileName = $uploadedAnotherFileArr[1];
								$result = $s3->deleteObject(array(
							        'Bucket' => env('AWS_BUCKET'),
							        'Key'    => "assignment_data/".$anotherFileName
							    ));
							}
						}
					}
				}
			}

			$submittedAsignmnt->teacher_images  = !empty($imagesData) ? implode(",", $imagesData) : $submittedAsignmnt->teacher_images;
			$submittedAsignmnt->teacher_videos  = !empty($videosData) ? implode(",", $videosData) : $submittedAsignmnt->teacher_videos;
			$submittedAsignmnt->teacher_other_files  = !empty($anotherFilesData) ? implode(",", $anotherFilesData) : $submittedAsignmnt->teacher_other_files;
			$submittedAsignmnt->teacher_content = $request->get('teacher_content');
			$submittedAsignmnt->teacher_approved = $request->get('teacher_approved');
			$submittedAsignmnt->save();
			//Send notification
			$userId = $submittedAsignmnt->user_id;
			$token = isset($submittedAsignmnt->user->deviceToken) ? $submittedAsignmnt->user->deviceToken : '';
			if ($token!='') {
				$title = 'BrainyWood';
				$click_action = 'Assignment';
				$module_id = $id;
				$message = 'Teacher reviewed your submitted Assignment.';
				$this->helper->addNotification($userId,$message,$click_action,$module_id);
			}

			\Session::flash('msg', 'User Assignment Remarked Successfully.');
			return redirect()->back();
		}

	}

	public function revertremove($id)
	{
		$submittedAsignmnt = AssignmentSubmission::findOrFail($id);
		$submittedAsignmnt->teacher_images = NULL;
		$submittedAsignmnt->teacher_videos = NULL;
		$submittedAsignmnt->teacher_content = NULL;
		$submittedAsignmnt->teacher_other_files = NULL;
		$submittedAsignmnt->update();
		
		\Session::flash('msg', 'Revert Removed Successfully.');
		return redirect()->back();
	}









	public function getassignmentbycourse(Request $request)
	{
		$courseId=$request->get('courseId');

		$getAssignments = Assignment::where('course_id',$courseId)->get();
		?>
		<option value="">--Select--</option>
		<?php
		foreach ($getAssignments as $key => $value) {
			?>
			<option value="<?php echo $value['id'] ?>"><?php echo $value['title'] ?></option>
			<?php
		}
	}

	public function awss3Data()
	{

		/*$url = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/';
		$images = [];
		$files = Storage::disk('s3')->files('mobile_app_data');
		foreach ($files as $file) {
			$images[] = [
				'name' => str_replace('mobile_app_data/', '', $file),
				'src' => $url . $file
			];
		}
		echo '<pre />'; print_r($images); die;*/
		$filePath = public_path().'/front/assets/img/web-logoed.jpg';
    	$fileName = "web-logoed.jpg";
		$s3 = AWS::createClient('s3');
		$s3->putObject(array(
		    'Bucket'     => env('AWS_BUCKET'),
		    'Key'        => "assignment_data/".$fileName,
		    'SourceFile' => $filePath,
		));
	}

}
