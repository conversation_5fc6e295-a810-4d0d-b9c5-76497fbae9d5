<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\ReferPoint;
use App\Models\UserPoint;
use App\Models\City;
use App\Models\State;
use App\Models\StudentClass;
use App\Models\User;
use DB;

class ReferPointController extends Controller
{
	public function index(){
	  $data = ReferPoint::orderBy("id", "desc");
	  $totalData = $data->count();
	  $data = $data->paginate(50);
	  return view('admin.refer_points.index', compact('data','totalData'));
	}
	public function create(){
		return view('admin.refer_points.create');
	}

	public function store(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'title' => 'required',
			'points' => 'required',
			'about' => 'required',
			'description' => 'required',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$referPoint = new ReferPoint();
			$referPoint->title = $request->get('title');
			$referPoint->points = $request->get('points');
			$referPoint->about = $request->get('about');
			$referPoint->description = $request->get('description');
			$referPoint->save();
			$referPointId=$referPoint->id;
			if($referPointId){
				$refer = ReferPoint::findOrFail($referPointId);
				$refer->sort_id = $referPointId;
				$refer->update();
			}
			
			\Session::flash('msg', 'Refer Point Added Successfully.');
			return back();
		}
	}

	public function show(ReferPoint $referPoint)
	{
		//
	}

	public function edit($id)
	{
		$referPoint = ReferPoint::find($id);
		return view('admin.refer_points.edit',compact('referPoint'));
	}

	public function update(Request $request, $id)
	{
		$validator = Validator::make($request->all(), [
			//'title' => 'required',
			'free_class' => 'required',
			'free_percantage' => 'required',
			// 'description' => 'required',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$referPoint = ReferPoint::findOrFail($id);
			//$referPoint->title = $request->get('title');
			$referPoint->free_class = $request->get('free_class');
			$referPoint->free_percantage = $request->get('free_percantage');
			// $referPoint->description = $request->get('description');
			$referPoint->save();

			\Session::flash('msg', 'Refer updated Successfully.');
			return redirect('/admin/refer_points');
		}

	}

	public function delete($id)
	{
		$data = ReferPoint::findOrFail($id);
		$data->delete();
		
		\Session::flash('msg', 'Refer Point deleted Successfully.');
	    return redirect()->back();
	}

	public function updateStatus($id,$status)
	{
		$data = ReferPoint::findOrFail($id);
		$data->status=$status;
		$data->update();

	    return redirect()->back();
	}

	public function sortUp($id,$sort_id)
	{
		$sort_id = $sort_id - 1;
		$data = ReferPoint::where("sort_id", $sort_id);
		//$data->sort_id = $sort_id + 1;
		$data->update(array("sort_id"=>$sort_id + 1));
		$data1 = ReferPoint::findOrFail($id);
		$data1->sort_id = $sort_id;
		$data1->save();

	    return redirect()->back();
	}

	public function sortDown($id,$sort_id)
	{
		$sort_id = $sort_id + 1;
		$data = ReferPoint::where("sort_id", $sort_id);
		//$data->sort_id = $sort_id - 1;
		$data->update(array("sort_id"=>$sort_id - 1));
		$data1 = ReferPoint::findOrFail($id);
		$data1->sort_id = $sort_id;
		$data1->save();

	    return redirect()->back();
	}

	public function leadershipBoard(Request $request)
	{
		$schools = User::select("school_college")->whereNotNull("school_college")->groupBy("school_college")->get();
		$classes = StudentClass::where('status',1)->where('deleted',0)->orderBy('id', 'ASC')->get();
		$states = State::orderBy('state', 'ASC')->get();
		$cities = City::orderBy('city', 'ASC')->get();

		$user_id = ($request->user_id) ? $request->user_id : '';
		$school = ($request->school) ? $request->school : '';
		$classId = ($request->class_id) ? $request->class_id : '';
		$state = ($request->state) ? $request->state : '';
		$city = ($request->city) ? $request->city : '';
		$points = ($request->points) ? $request->points : '';
		$from = ($request->from) ? $request->from : '';
		$to = ($request->to) ? $request->to.' 23:59:59' : date('Y-m-d H:i:s');
		$subscription = ($request->subscription) ? $request->subscription : '';
		
		if ($request->segment(3) == 'class_wise') {
			if (empty($classId) || $classId < 1) {
				return redirect()->route('admin.leadership.class_wise',['class_id'=>'1']);
			}
		}
		$data = UserPoint::select('users.id', 'users.name', 'users.image', 'users.city', 'users.class_id', DB::raw('SUM(points) as sum_points'))
				 ->join('users', 'users.id', '=', 'user_points.user_id')
				 ->Where(function($query) use ($user_id) {
					if (isset($user_id) && !empty($user_id)) {
						$query->where("users.id",$user_id);
					}
				 })
				 ->Where(function($query) use ($school) {
					if (isset($school) && !empty($school)) {
						$query->where("users.school_college",$school);
					}
				 })
                ->Where(function($query) use ($classId) {
                    if (isset($classId) && !empty($classId)) {
                        $query->where('users.class_id', $classId);
                    }
                })
				->Where(function($query) use ($state) {
					if (isset($state) && !empty($state)) {
						$query->where("users.state",$state);
					}
				})
				->Where(function($query) use ($city) {
					if (isset($city) && !empty($city)) {
						$query->where("users.city",$city);
					}
				})
				 ->Where(function($query) use ($points) {
					if (isset($points) && !empty($points)) {
						$query->where("user_points.points",">=",$points);
					}
				 })
				 ->Where(function($query) use ($from, $to) {
					if (isset($from) && !empty($from)) {
						$query->whereBetween("user_points.created_at",[$from, $to]);
					}
				 })
			    ->where('user_points.transaction_type', 502)
			    //->where('users.class_id', $classId)
			    ->groupBy('user_id')
			    ->orderBy('sum_points', 'DESC')
			    ->orderBy('name', 'ASC')

		// if (isset($subscription) && !empty($subscription)) {	 
		// 	if ($subscription=='paid') {
		// 		$data->whereHas('subscription_data', function($query) use ($subscription) {
		// 			$query->whereDate("end_date", ">=", date('Y-m-d'))->where("mode", "!=", "FREE");
		// 		});
		// 	} elseif ($subscription=='unpaid') {
		// 		$data->whereHas('subscription_data', function($query) use ($subscription) {
		// 			if (isset($subscription) && !empty($subscription)) {
		// 				$query->whereDate("end_date", "<", date('Y-m-d'));
		// 			}
		// 		});
		// 	} elseif ($subscription=='trial') {
		// 		$data->whereHas('subscription_data', function($query) use ($subscription) {
		// 			$query->whereDate("end_date", ">=", date('Y-m-d'))->where("mode", "FREE");
		// 		});
		// 	}
		// }
			    //->take(15)
			    ->get();
		//echo '<pre />'; print_r($data); die;
		$totalData = $data->count();
		//$data = $data->paginate(50);

		return view('admin.refer_points.leadership_board', compact('schools','classes','states','cities','data','totalData'));
	}

}
