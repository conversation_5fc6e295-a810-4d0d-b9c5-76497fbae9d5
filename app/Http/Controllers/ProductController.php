<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Http\Helper as Helper;
use App\Models\Product;
use App\Models\UserPoint;

class ProductController extends Controller
{
	protected $helper;

	public function __construct(Helper $helper)
	{
		$this->helper = $helper;
	}

	public function index(Request $request)
	{
		$status = $request->status;
		$data = Product::orderBy("sort_id", "ASC")
	                ->Where(function($query) use ($status) {
	                    if (isset($status) && !empty($status)) {
	                        $query->where('status', $status);
	                    }
	                });
		$totalData = $data->count();
		$data = $data->paginate(50);

		return view('admin.products.index', compact('data','totalData'));
	}

	public function create()
	{
		return view('admin.products.create');
	}

	public function store(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'title' => 'required',
			'points' => 'required',
			'image' => 'required',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$file = $request->file('image');
			$filename = NULL;
			$destinationPath = public_path().'/upload/products/';
			if($file){
				$originalFile = $file->getClientOriginalName();
				$filename = time().$originalFile;
				$file->move($destinationPath, $filename);
			}

			$product = new Product();
			$product->title = $request->input('title');
			$product->points = $request->input('points');
			$product->image = $filename;
			$product->description = $request->input('description');
			$product->save();
			$productId=$product->id;
			if($productId){
				$data = Product::findOrFail($productId);
				$data->sort_id = $productId;
				$data->update();
			}
			
			\Session::flash('msg', 'Product Added Successfully.');
			return back();
		}
	}

	public function show(Product $product)
	{
		//
	}

	public function edit($id)
	{
		$product = Product::find($id);

		return view('admin.products.edit',compact('product'));
	}

	public function update(Request $request, $id)
	{
		$validator = Validator::make($request->all(), [
			'title' => 'required',
			'points' => 'required',
			//'image' => 'required',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$product = Product::findOrFail($id);

			$file = $request->file('image');
			$destinationPath = public_path().'/upload/products/';
			if($file){
				$originalFile = $file->getClientOriginalName();
				$filename = time() . $originalFile;
				$file->move($destinationPath, $filename);
			}else{
			   $filename = $product->image;
			}
			
			$product->title = $request->input('title');
			$product->points = $request->input('points');
			$product->image = $filename;
			$product->description = $request->input('description');
			$product->save();

			\Session::flash('msg', 'Product updated Successfully.');
			return redirect('/admin/products');
		}
	}

	public function delete($id)
	{
		$data = Product::findOrFail($id);
		$data->delete();
		
		\Session::flash('msg', 'Product deleted Successfully.');
	    return redirect()->back();
	}

	public function updateStatus($id,$status)
	{
		$data = Product::findOrFail($id);
		$data->status=$status;
		$data->update();

	    return redirect()->back();
	}

	public function sortUp($id,$sort_id)
	{
		$sort_id = $sort_id - 1;
		$record = Product::where("sort_id", $sort_id);
		$record->update(array("sort_id"=>$sort_id + 1));
		$record1 = Product::findOrFail($id);
		$record1->sort_id = $sort_id;
		$record1->save();

		\Session::flash('msg', 'Product Order Changed Successfully.');
	    return redirect()->back();
	}

	public function sortDown($id,$sort_id)
	{
		$sort_id = $sort_id + 1;
		$record = Product::where("sort_id", $sort_id);
		$record->update(array("sort_id"=>$sort_id - 1));
		$record1 = Product::findOrFail($id);
		$record1->sort_id = $sort_id;
		$record1->save();

		\Session::flash('msg', 'Product Order Changed Successfully.');
	    return redirect()->back();
	}

	public function imageremove($id)
	{
		$product = Product::findOrFail($id);
		if(file_exists( public_path().'/upload/products/'.$product->image )) {
			unlink( public_path().'/upload/products/'.$product->image );
		}
		$product->image = NULL;
		$product->update();

		\Session::flash('msg', 'Product Image Removed Successfully.');
	    return redirect()->back();
	}

	public function orderHistory(Request $request)
	{
		$order_status = $request->status;
		$data = UserPoint::orderBy("id", "DESC")
					->where("transaction_type",501)
	                ->Where(function($query) use ($order_status) {
	                    if (isset($order_status) && !empty($order_status)) {
	                        $query->where('order_status', $order_status);
	                    }
	                })
					->paginate(50);
		return view('admin.products.order_history', compact('data'));
	}

	public function updateOrderStatus($id,$status)
	{
		$data = UserPoint::findOrFail($id);
		$data->order_status=$status;
		$data->update();

		if($status!=601){
			//Send notification
			$userId = $data->user_id;
			$token = isset($data->user_data->deviceToken) ? $data->user_data->deviceToken : '';
			if ($token!='') {
				$title = 'BrainyWood';
				$click_action = 'PointHistory';
				$module_id = $id;
				if($status==602){
					$message = 'Your Order Shipped Successfully, for further any enquiry contact to our support Team.';
				} elseif($status==603){
					$message = 'Your Order Delivered Successfully, for further any enquiry contact to our support Team.';
				}
				$this->helper->addNotification($userId,$message,$click_action,$module_id);
			}
		}

		\Session::flash('msg', 'Order Status Updated Successfully.');
	    return redirect()->back();
	}

	public function storeRemark(Request $request)
	{
		//dd($request->all());
		if (isset($request->remark_id)) {
			$remark_id = $request->remark_id;
			$comment = UserPoint::findOrFail($remark_id);
			$comment->remark = $request->get('remark');
			$comment->save();

			\Session::flash('msg', 'Comment Added Successfully.');
		}
		return redirect()->back();
	}

}
