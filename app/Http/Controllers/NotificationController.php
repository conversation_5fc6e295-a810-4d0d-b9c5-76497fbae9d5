<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Http\Helper as Helper;
use App\Models\Notification;
use App\Models\NotificationSend;
use App\Models\User;
use DB;

class NotificationController extends Controller
{
	protected $helper;

	public function __construct(Helper $helper)
	{
		$this->helper = $helper;
	}
	
	public function index(Request $request)
	{
		$user_id = ($request->user_id) ? $request->user_id : '';
		//$data = Notification::orderBy("id", "DESC")->limit(200)->get();
		$data = Notification::orderBy("id", "DESC")
			 ->Where(function($query) use ($user_id) {
				if (isset($user_id) && !empty($user_id)) {
					$query->where("user_id",$user_id);
				}
			 });
		$totalResult = $data->count();
		$data = $data->paginate(50);

		return view('admin.notifications.index', compact('data','totalResult'));
	}
	
	/**
	 * Show the form for creating a new resource.
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function create()
	{
		//$users = User::whereIn("role_id",array(2, 3))->where("status", 1)->get();
		$users = User::whereIn("role_id",array(3))->where("status", 1)->get();
		//$users = User::where("status", 1)->get()->pluck('id', 'name');

		return view('admin.notifications.create', compact('users'));
	}

	/**
	 * Store a newly created resource in storage.
	 *
	 * @param  \Illuminate\Http\Request $request
	 * @return \Illuminate\Http\Response
	 */
	public function store(Request $request)
	{
		//echo '<pre />'; print_r($request->all()); die;
		$validator = Validator::make($request->all(), [
			//'user_id' => 'required',
			'message' => 'required',
		]);

		if ($validator->fails()) {
			return back()
				->withErrors($validator)
				->withInput();
		} else { 
			//echo '<pre />'; print_r($request->all()); die;
			if ($request->hasFile('csv')) {
				//print_r(explode('.', $_FILES['csv']['name'])); die;
				if ($_FILES['csv']['error'] == 0) {
				    $name = $_FILES['csv']['name'];
				    //$ext = strtolower(end(explode('.', $_FILES['csv']['name'])));
				    $end = explode('.', $name);
				    $ext = strtolower($end[1]);
				    $type = $_FILES['csv']['type'];
				    $tmpName = $_FILES['csv']['tmp_name'];

				    // check the file is a csv
				    if ($ext === 'csv') {
				        if (($handle = fopen($tmpName, 'r')) !== FALSE) {
				            // necessary if a large csv file
				            set_time_limit(0);

				            $row = 0;
				            $insert_data = array();
				            while(($data = fgetcsv($handle, 1000, ',')) !== FALSE) {
				                // number of fields in the csv
				                $col_count = count($data);
				           
				                $csv[$row]['col1'] = $data[0];
				                $csv[$row]['col2'] = $data[1];
				                $csv[$row]['col3'] = $data[2];
				                
				                $num = $csv[$row]['col1'];
				                $requestby = $csv[$row]['col2'];
				                $other = $csv[$row]['col3'];
				                $userphone = $token = '';
				                if ($requestby!='') {
				    				$userdata = explode('-', $requestby);
				    				//print_r($userdata); //die;
				    				$username = $userdata[0];
				    				if (isset($userdata[1])) {
				    					$userphone = $userdata[1];
				    				}
				                }
				                if (is_numeric(trim($userphone))) {
				                	$user = User::where("phone", trim($userphone))->first();
									$userId = isset($user->id) ? $user->id : 0;
									$token = isset($user->deviceToken) ? $user->deviceToken : '';
				        			//echo 'NAME '.$username.' PHONE '.$userphone.' ID '.$userId.'<br>';
									$notification = new Notification();
									$notification->user_id = $userId;
									$notification->message = $request->get('message');
									$notification->save();
									$notificationId = $notification->id;

									if ($token!='') {
										$title = 'Guruathome';
									//	$this->helper->notificationsend($token, $title, $notification->message);//ak
									}
				                }
				  
				                // inc the row
				                $row++;
				            }//die;
				            fclose($handle);
				            
				            \Session::flash('msg', 'Notification Send Successfully.');
							return back();
				        }
				    } else {
				    	\Session::flash('msg', 'Please upload only csv file!');
						return back();
				    } 
				}

			} else {
				if ($request->get('condition_1') == 2) {
					//$users = User::select('id','deviceToken')->where('role_id', 3)->where('status', 1)->where('deleted', 0)->get();
					//echo '<pre />'; print_r($users); die;
					//foreach ($users as $user) {
						$notification = new Notification();
						$notification->user_id = 0; //$user->id;
						$notification->message = $request->get('message');
						$notification->save();
						$notificationId = $notification->id;

						/*$token = isset($user->deviceToken) ? $user->deviceToken : '';
						if ($token!='') {
							$title = 'Guruathome';
							$this->helper->notificationsend($token, $title, $notification->message);
						}*/
					//}

					\Session::flash('msg', 'Notification Send Successfully.');
					return back();
				} else {
					$user_id = ($request->get('user_id')) ? $request->get('user_id') : [];
					if (!empty($user_id)) {
						for ($i=0; $i<count($user_id); $i++){
							$notification = new Notification();
							$notification->user_id = $request->get('user_id')[$i];
							$notification->message = $request->get('message');
							$notification->save();
							$notificationId = $notification->id;

							$user = User::where("id", $notification->user_id)->first();
							$token = isset($user->deviceToken) ? $user->deviceToken : '';
							if ($token!='') {
								$title = 'Guruathome';
								//$this->helper->notificationsend($token, $title, $notification->message);//ak
							}
						}

						\Session::flash('msg', 'Notification Send Successfully.');
						return back();
					}
				}
			}
		}
	}

	/**
	 * Display the specified resource.
	 *
	 * @param  \App\Models\Notification $notifications
	 * @return \Illuminate\Http\Response
	 */
	public function show(Notification $notifications)
	{
		//
	}

	/**
	 * Show the form for editing the specified resource.
	 *
	 * @param  \App\Models\Notification $notifications
	 * @return \Illuminate\Http\Response
	 */
	public function edit(Notification $notifications, $id)
	{
		$data = Notification::findOrFail($id);
		$users = User::where("status", 1)->get();

		return view('admin.notifications.edit',compact('data','users'));
	}

	/**
	 * Update the specified resource in storage.
	 *
	 * @param  \Illuminate\Http\Request $request
	 * @param  \App\Models\Notification $notifications
	 * @return \Illuminate\Http\Response
	 */
	public function update(Request $request, Notification $notifications, $id)
	{
		$validator = Validator::make($request->all(), [
			'user_id' => 'required',
			'message' => 'required',
		]);

		if ($validator->fails()) {
			return back()
				->withErrors($validator)
				->withInput();
		} else {
			$notification = Notification::findOrFail($id);
			$notification->user_id = $request->get('user_id');
			$notification->message = $request->get('message');
			$notification->save();
			$notificationId = $notification->id;
			
			$user = User::where("id", $notification->user_id)->first();
			$token = isset($user->deviceToken) ? $user->deviceToken : '';
			if ($token!='') {
				$title = 'Guruathome';
			//	$this->helper->notificationsend($token, $title, $notification->message);//ak
			}

			\Session::flash('msg', 'Notification Updated Successfully.');
			return redirect('/admin/notifications');
		}

	}

	/**
	 * Remove the specified resource from storage.
	 *
	 * @param  \App\Models\Notification
	 * @return \Illuminate\Http\Response
	 */
	 public function delete($id)
	{
		$notification = Notification::findOrFail($id);
		$notification->delete();

		return redirect()->back();
	}

	public function updateStatus($id,$status)
	{
		$notification = Notification::findOrFail($id);
		$notification->status=$status;
		$notification->update();

		return redirect()->back();
	}
	
	public function viewSendNotification($id)
	{
		$data = NotificationSend::where("notification_id",$id)->orderBy("id", "DESC");
		$totalResult = $data->count();
		$data = $data->paginate(50);

		return view('admin.notifications.view_send_users', compact('data','totalResult'));
	}

}
