<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Models\Board;
use App\Models\LiveClassTimeTableDoc;
use App\Models\StudentClass;


class LiveClassTimeTableDocController extends Controller
{	
	public function index(Request $request)
	{
		$board_id	= ($request->board_id) ? $request->board_id : '';
		$class_id 	= ($request->class_id) ? $request->class_id : '';
		$from 	    = ($request->from) ? $request->from : '';
		$to     	= ($request->to) ? $request->to.' 23:59:59' : date('Y-m-d H:i:s');
		
		$boards = Board::orderBy("id", "ASC")->get();
		$classes = StudentClass::orderBy("id", "ASC")->get();

		$data = LiveClassTimeTableDoc::with("board_data", "class_data")
			 ->Where(function($query) use ($board_id) {
				if (isset($board_id) && !empty($board_id)) {
					$query->where("board_id", $board_id);
				}
			 })
			 ->Where(function($query) use ($class_id) {
				if (isset($class_id) && !empty($class_id)) {
					$query->where("class_id", $class_id);
				}
			 })
			 /*->Where(function($query) use ($class_id) {
				if (isset($class_id) && !empty($class_id)) {
					$query->whereRaw("find_in_set($class_id,class_ids)");
				}
			 })*/
			 ->Where(function($query) use ($from, $to) {
				if (isset($from) && !empty($from)) {
					//$query->whereBetween("created_at", [$from, $to]);
					$query->where("class_time", ">=", $from)->where("end_time", "<=", $to);
				}
			 })
			 ->orderBy("id", "DESC")->paginate(50);

		return view('admin.liveclass_timetable_doc.index', compact('boards','classes','data'));
	}
	
	public function create()
	{
		$boards = Board::orderBy("id", "ASC")->get();
		$classes = StudentClass::orderBy("id", "ASC")->get();

		return view('admin.liveclass_timetable_doc.create', compact('boards','classes'));
	}

	public function store(Request $request)
	{
		$validator = Validator::make($request->all(), [
			//'board_id' => 'required',
			'class_id' => 'required',
			'doc_file' => 'required',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			//echo '<pre />'; print_r($request->all()); die;
			$doc_file = $request->file('doc_file');
			$destinationPath = public_path().'/upload/liveclass_timetables/';
			if($doc_file){
				$docOriginalName = $doc_file->getClientOriginalName();
				$filename = time() . $docOriginalName;
				$doc_file->move($destinationPath, $filename);
			}
			
			$boardId = $request->get('board_id');
			$classId = $request->get('class_id');

			$data = LiveClassTimeTableDoc::where("board_id", $boardId)->where("class_id", $classId)->first();
			if (empty($data)) {
				$data = new LiveClassTimeTableDoc();
			}
			$data->board_id = $boardId;
			$data->class_id = $classId;
			$data->doc_file = $filename;
			$data->save();
			$dataId = $data->id;

			\Session::flash('msg', 'Live Class Time Table Doc Added Successfully.');
			return back();
		}
	}

	/**
	 * Display the specified resource.
	 *
	 * @param  \App\Models\LiveClassTimeTableDoc $liveclass_timetable_doc
	 * @return \Illuminate\Http\Response
	 */
	public function show(LiveClassTimeTableDoc $liveclass_timetable_doc)
	{
		//
	}

	public function edit($id)
	{
		$data = LiveClassTimeTableDoc::findOrFail($id);
		$boards = Board::orderBy("id", "ASC")->get();
		$classes = StudentClass::orderBy("id", "ASC")->get();

		return view('admin.liveclass_timetable_doc.edit',compact('data','boards','classes'));
	}

	public function update(Request $request, $id)
	{
		$validator = Validator::make($request->all(), [
			//'board_id' => 'required',
			'class_id' => 'required',
			//'doc_file' => 'required',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			//print_r($request->all());die;
			$data = LiveClassTimeTableDoc::findOrFail($id);

			$doc_file = $request->file('doc_file');
			
			$destinationPath = public_path() . '/upload/liveclass_timetables/';
			if($doc_file){
				$docOriginalName = $doc_file->getClientOriginalName();
				$filename = time() . $docOriginalName;
				$doc_file->move($destinationPath, $filename);
			}else{
				$filename = $data->doc_file;  
			}
			
			$data->board_id = $request->get('board_id');
			$data->class_id = $request->get('class_id');
			$data->doc_file = $filename;
			$data->save();
			$dataId = $data->id;

			\Session::flash('msg', 'Live Class Time Table Doc Updated Successfully.');
			return redirect('/admin/liveclass_timetable_doc');
		}

	}

	public function delete($id)
	{
		$data = LiveClassTimeTableDoc::findOrFail($id);
		$data->delete();

		\Session::flash('msg', 'Live Class Time Table Doc Deleted Successfully.');
		return redirect('/admin/liveclass_timetable_doc');
	}

	public function doc_file_remove($id)
	{
		$data = LiveClassTimeTableDoc::findOrFail($id);
		if(file_exists( public_path().'/upload/liveclass_timetables/'.$data->doc_file )) {
			unlink( public_path().'/upload/liveclass_timetables/'.$data->doc_file );
		}
		$data->doc_file = NULL;
		$data->update();

		\Session::flash('msg', 'Live Class Time Table Doc Removed Successfully.');
	    return redirect()->back();
	}

}
