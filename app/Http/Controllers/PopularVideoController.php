<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Popularvideo;
use App\Models\StudentClass;
use App\Models\VideoTemp;
use AWS;


class PopularVideoController extends Controller
{
	public function index(Request $request)
	{
		$class_type = ($request->class_type) ? $request->class_type : '';
		$class_id = ($request->class_id) ? $request->class_id : '';
		$classes = StudentClass::orderBy("id", "ASC")->get();
		$data = Popularvideo::orderBy('sort_id', 'ASC')
				 ->Where(function($query) use ($class_type) {
					if (isset($class_type) && !empty($class_type)) {
						$query->where("class_type", $class_type);
					}
				 })
				 ->Where(function($query) use ($class_id) {
					if (isset($class_id) && !empty($class_id)) {
						$query->whereRaw("find_in_set($class_id,class_ids)");
					}
				 })
				 ->where('deleted',0);
		$totalData = $data->count();
		$data = $data->paginate(50);

		return view('admin.popularvideos.index', compact('classes','data','totalData'));
	}
	
	public function create()
	{
		$classes = StudentClass::orderBy("id", "ASC")->get();
		
		return view('admin.popularvideos.create', compact('classes'));
	}

	public function store(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'name' => 'required',
			'image' => 'required',
			'video' => 'required|mimes:mp4',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$imagefilename = $videofilename = NULL;
			$file = $request->file('image');
			$video = $request->file('video');
			
			$destinationPath = public_path() . '/upload/popularvideos/';
			if($file){
				$imageOriginalName = $file->getClientOriginalName();
				$imagefilename = time() . $imageOriginalName;
				$file->move($destinationPath, $imagefilename);
			}
			if($video){
				$videoOriginalName = $video->getClientOriginalName();
				//$videofilename = strtotime(date('Y-m-d H:isa')).$videoOriginalName;
				$videofilename = time() . "_org.mp4";
				$video->move($destinationPath, $videofilename);
			}
			
			$popularvideo = new Popularvideo();
			$popularvideo->class_type = $request->get('class_type');
			$popularvideo->class_ids = !empty($request->class_ids) ? implode(",", $request->class_ids) : NULL;
			$popularvideo->name = $request->get('name');
			$popularvideo->video_thumb = $imagefilename;
			$popularvideo->video = $videofilename;
			$popularvideo->paid = ($request->get('paid')) ? $request->get('paid') : 0;
			$popularvideo->status = 1;
			$popularvideo->save();
			$popularvideoId = $popularvideo->id;
			if($popularvideoId){
				$popular = Popularvideo::findOrFail($popularvideoId);
				$popular->sort_id = $popularvideoId;
				$popular->save();
			}

			\Session::flash('msg', 'Popular Video Added Successfully.');
			return back();
		}
	}

	public function show(Popularvideo $popularvideos)
	{
		//
	}

	public function edit(Popularvideo $popularvideos, $id)
	{
		$classes = StudentClass::orderBy("id", "ASC")->get();
		$data = Popularvideo::findOrFail($id);

		return view('admin.popularvideos.edit',compact('classes','data'));
	}

	public function update(Request $request, Popularvideo $popularvideos, $id)
	{
		$s3 = AWS::createClient('s3');
		
		$validator = Validator::make($request->all(), [
			'name' => 'required',
			//'image' => 'required',
			'video' => 'mimes:mp4',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$popularvideo = Popularvideo::findOrFail($id);

			$file = $request->file('image');
			$video = $request->file('video');

			$destinationPath = public_path() . '/upload/popularvideos/';
			if($file){
				$imageOriginalName = $file->getClientOriginalName();
				$imagefilename = time() . $imageOriginalName;
				$file->move($destinationPath, $imagefilename);
			}else{
				$imagefilename = $popularvideo->video_thumb;
			}
			if($video){
				$videoOriginalName = $video->getClientOriginalName();
				//$videofilename = rand(111,999).strtotime(date('Y-m-d-H:isa')).$videoOriginalName;
				$videofilename = time() . "_org.mp4";
				$video->move($destinationPath, $videofilename);
			    $uploads3 = 0;
				$popularvideo->video_1 = $popularvideo->video_2 = $popularvideo->video_3 = $popularvideo->starttime = $popularvideo->endtime = NULL;
				$popularvideo->uploads3v1 = $popularvideo->uploads3v2 = $popularvideo->uploads3v3 = $popularvideo->processtatus = 0;
				
			}else{
				$videofilename = $popularvideo->video;
			    $uploads3 = $popularvideo->uploads3;
			}

			if($video){
				if(!empty($popularvideo->video) && file_exists( public_path().'/upload/popularvideos/'.$popularvideo->video )) {
					unlink( public_path().'/upload/popularvideos/'.$popularvideo->video );
				}
				if(!empty($popularvideo->video)){
					$videoName = $popularvideo->video;
					$s3->deleteObject(array(
				        'Bucket' => env('AWS_BUCKET'),
				        'Key'    => $videoName
				    ));
					$s3->deleteObject(array(
				        'Bucket' => env('AWS_BUCKET'),
				        'Key'    => "mobile_app_data/".$videoName
				    ));
				}
				$deleteVideoTemp = VideoTemp::where('popularvideoId',$id)->delete();
			}

			$popularvideo->class_type = $request->get('class_type');
			$popularvideo->class_ids = !empty($request->class_ids) ? implode(",", $request->class_ids) : NULL;
			$popularvideo->name = $request->get('name');
			$popularvideo->video_thumb = $imagefilename;
			$popularvideo->video = $videofilename;
			$popularvideo->paid = ($request->get('paid')) ? $request->get('paid') : 0;
			$popularvideo->uploads3 = $uploads3;
			$popularvideo->save();
			$popularvideoId = $popularvideo->id;

			\Session::flash('msg', 'Popular Video Updated Successfully.');
			return redirect('/admin/popularvideos');
		}

	}

	public function delete($id)
	{
		$popularvideo = Popularvideo::findOrFail($id);
		$popularvideo->deleted=1;
		$popularvideo->update();

		return redirect()->back();
	}

	public function updateStatus($id,$status)
	{
		$popularvideo = Popularvideo::findOrFail($id);
		$popularvideo->status=$status;
		$popularvideo->update();

		return redirect()->back();
	}

	public function sortUp($id,$sort_id)
	{
		$sort_id = $sort_id - 1;
		$popularvideo = Popularvideo::where("sort_id", $sort_id);
		//$popularvideo->sort_id = $sort_id + 1;
		$popularvideo->update(array("sort_id"=>$sort_id + 1));
		$popularvideo1 = Popularvideo::findOrFail($id);
		$popularvideo1->sort_id = $sort_id;
		$popularvideo1->save();

	    return redirect()->back();
	}

	public function sortDown($id,$sort_id)
	{
		$sort_id = $sort_id + 1;
		$popularvideo = Popularvideo::where("sort_id", $sort_id);
		//$popularvideo->sort_id = $sort_id - 1;
		$popularvideo->update(array("sort_id"=>$sort_id - 1));
		$popularvideo1 = Popularvideo::findOrFail($id);
		$popularvideo1->sort_id = $sort_id;
		$popularvideo1->save();

	    return redirect()->back();
	}

	public function convertVideo()
	{
		$data = VideoTemp::where('popularvideoId','!=',0)->orderBy('id', 'ASC')->get();
		
		return view('admin.popularvideos.convertVideo', compact('data'));
	}

	public function approveVideo($id)
	{
		$videoTemp = VideoTemp::where('id',$id)->first();
		$popularvideoId = $videoTemp->popularvideoId;
		$low_status = $videoTemp->low_status;
		$med_status = $videoTemp->med_status;
		$high_status = $videoTemp->high_status;
		$popularVideo = Popularvideo::findOrFail($popularvideoId);
		if ($low_status == 1) {
			$popularVideo->video_1 = $videoTemp->low_video;
		}
		if ($med_status == 1) {
			$popularVideo->video_2 = $videoTemp->med_video;
		}
		if ($high_status == 1) {
			$popularVideo->video_3 = $videoTemp->high_video;
		}
		$popularVideo->update();
		
		$delete = VideoTemp::where('id',$id)->delete();

		\Session::flash('msg', 'Converted Video Approved Successfully.');
		return redirect('/admin/popularvideos/convertVideo');
	}

	public function imgremove($id)
	{
		$popularvideo = Popularvideo::findOrFail($id);
		if(file_exists( public_path().'/upload/popularvideos/'.$popularvideo->video_thumb )) {
			unlink( public_path().'/upload/popularvideos/'.$popularvideo->video_thumb );
		}
		$popularvideo->video_thumb = NULL;
		$popularvideo->update();

		\Session::flash('msg', 'Popularvideo Image Removed Successfully.');
		return redirect()->back();
	}

	public function vidremove($id, $video_type)
	{
		$s3 = AWS::createClient('s3');
		
		$popularvideo = Popularvideo::findOrFail($id);
		if(!empty($popularvideo->video) && file_exists( public_path().'/upload/popularvideos/'.$popularvideo->video )) {
			unlink( public_path().'/upload/popularvideos/'.$popularvideo->video );
		}
		if(!empty($popularvideo->video)){
			$videoName = $popularvideo->video;
			$s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => $videoName
		    ));
			$s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => "mobile_app_data/".$videoName
		    ));
		}
		$popularvideo->video = NULL;
		$popularvideo->uploads3 = 0;
		if(!empty($popularvideo->video_1) && file_exists( public_path().'/upload/popularvideos/'.$popularvideo->video_1 ) && $popularvideo->video_1!='NA') {
			unlink( public_path().'/upload/popularvideos/'.$popularvideo->video_1 );
		}
		if(!empty($popularvideo->video_1)){
			$videoName1 = $popularvideo->video_1;
			$s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => "mobile_app_data/".$videoName1
		    ));
		}
		$popularvideo->video_1 = NULL;
		$popularvideo->uploads3v1 = 0;
		if(!empty($popularvideo->video_2) && file_exists( public_path().'/upload/popularvideos/'.$popularvideo->video_2 ) && $popularvideo->video_2!='NA') {
			unlink( public_path().'/upload/popularvideos/'.$popularvideo->video_2 );
		}
		if(!empty($popularvideo->video_2)){
			$videoName2 = $popularvideo->video_2;
			$s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => "mobile_app_data/".$videoName2
		    ));
		}
		$popularvideo->video_2 = NULL;
		$popularvideo->uploads3v2 = 0;
		if(!empty($popularvideo->video_3) && file_exists( public_path().'/upload/popularvideos/'.$popularvideo->video_3 ) && $popularvideo->video_3!='NA') {
			unlink( public_path().'/upload/popularvideos/'.$popularvideo->video_3 );
		}
		if(!empty($popularvideo->video_3)){
			$videoName3 = $popularvideo->video_3;
			$s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => "mobile_app_data/".$videoName3
		    ));
		}
		$popularvideo->video_3 = NULL;
		$popularvideo->uploads3v3 = 0;
		$deleteVideoTemp = VideoTemp::where('popularvideoId',$id)->delete();
		$popularvideo->processtatus = 0;
		$popularvideo->starttime = NULL;
		$popularvideo->endtime = NULL;
		$popularvideo->update();

		\Session::flash('msg', 'Popular Video Removed Successfully.');
		return redirect()->back();
	}

	public function updateishome(Request $request)
	{
		$id = $request->popVideoId;
		$data = Popularvideo::findOrFail($id);
		if($data->isHome==501){
			$data->isHome=502;
		}else{
			$data->isHome=501;
		}
		$data->update();
		$message = '<div class="alert alert-info"><a class="close" data-dismiss="alert">×</a><strong>Popularvideo IsHome Status Updated Successfully.</strong></div>';
	    return $message;
	}

}
