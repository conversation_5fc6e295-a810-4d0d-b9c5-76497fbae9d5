<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Http\Helper as Helper;
use App\Models\LiveClass;
use App\Models\LiveclassNotify;
use App\Models\StudentClass;
use App\Models\StudentClassSubject;
use App\Models\Subject;
use App\Models\User;
use App\Models\VideoTemp;
use AWS;


class LiveclassesController extends Controller
{
	protected $helper;

	public function __construct(Helper $helper)
	{
		$this->helper = $helper;
	}
	
	public function index(Request $request)
	{
		$added_by	= ($request->added_by) ? $request->added_by : '';
		$class_type = ($request->class_type) ? $request->class_type : '';
		$class_id 	= ($request->class_id) ? $request->class_id : '';
		$subject_id = ($request->subject_id) ? $request->subject_id : '';
		$from 	    = ($request->from) ? $request->from : '';
		$to     	= ($request->to) ? $request->to.' 23:59:59' : date('Y-m-d H:i:s');
		$status 	= isset($request->status) ? ($request->status=='active') ? 1 : 2 : '';
		
		$users = array();
		$classes = StudentClass::orderBy("id", "ASC")->get();
		if (!empty($class_id)) {
			$classSubject = StudentClassSubject::where("class_id", $class_id)->first();
			//dd($classSubject);
			//$subject_ids_arr = [];
			$i=0;
			$subject_ids = !empty($classSubject['subject_ids']) ? explode(",", $classSubject['subject_ids']) : [];
			//dd($subject_ids);
			/*foreach ($subject_ids as $key => $value) {
				$subject_ids_arr[]=$value;
				$i++;
			}
			array_unique($subject_ids_arr);*/
			$subjects = Subject::whereIn("id",$subject_ids)->orderBy("title", "ASC")->get();
		} else {
			$subjects = Subject::orderBy("title", "ASC")->get();
		}

		$user = Auth::user();
		//dd($user);
		$userId = $user->id;
		$role_id = $user->role_id;
		if($role_id==2){
			$data = LiveClass::where("added_by", $userId)->where("deleted", 0)->orderBy("id", "DESC");
			$totalResult = $data->count();
			$data = $data->paginate(50);
		}else{
			$users = User::where("role_id", "!=", 3)->where("deleted", 0)->orderBy("name", "ASC")->get();

			$data = LiveClass::orderBy("id", "DESC")
				 ->Where(function($query) use ($added_by) {
					if (isset($added_by) && !empty($added_by)) {
						$query->where("added_by", $added_by);
					}
				 })
				 ->Where(function($query) use ($class_type) {
					if (isset($class_type) && !empty($class_type)) {
						$query->where("class_type", $class_type);
					}
				 })
				 ->Where(function($query) use ($class_id) {
					if (isset($class_id) && !empty($class_id)) {
						$query->whereRaw("find_in_set($class_id,class_ids)");
					}
				 })
				 ->Where(function($query) use ($subject_id) {
					if (isset($subject_id) && !empty($subject_id)) {
						$query->where("subject_id", $subject_id);
					}
				 })
				 ->Where(function($query) use ($from, $to) {
					if (isset($from) && !empty($from)) {
						//$query->whereBetween("created_at", [$from, $to]);
						$query->where("class_time", ">=", $from)->where("end_time", "<=", $to);
					}
				 })
				 ->Where(function($query) use ($status) {
					if (isset($status) && !empty($status)) {
						$query->where("status", $status);
					}
				 })
				 ->where("deleted", 0);
			$totalResult = $data->count();
			$data = $data->paginate(50);
		}

		return view('admin.liveclasses.index', compact('role_id','users','classes','subjects','data','totalResult'));
	}
	
	public function create()
	{
		$user = Auth::user();
		$teachers = User::whereIn("role_id",array(1, 2))->where("status",1)->get();
		$classes = StudentClass::orderBy("id", "ASC")->get();

		return view('admin.liveclasses.create', compact('user','teachers','classes'));
	}

	public function store(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'added_by' => 'required',
			'title' => 'required',
			'subject_id' => 'required',
			'meeting_id' => 'required|regex:/^\S*$/u',
			'class_time' => 'required',
			'end_time' => 'required',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			//echo '<pre />'; print_r($request->all()); die;
			$file = $request->file('image');
			$destinationPath = public_path().'/upload/liveclasses/';
			if($file){
				$imageOriginalName = $file->getClientOriginalName();
				$filename=strtotime(date('Y-m-d-H:isa')).$imageOriginalName;
				$file->move($destinationPath, $filename);
			}
			$subject_id = $request->get('subject_id');
			$subject_data = Subject::select("title")->where("id",$subject_id)->first();
			$subject_title = !empty($subject_data) ? $subject_data->title : NULL;

			$liveClass = new LiveClass();
			$liveClass->added_by = $request->get('added_by');
			$liveClass->title = $request->get('title');
			$liveClass->class_type = $request->get('class_type');
			$liveClass->class_ids = !empty($request->class_ids) ? implode(",", $request->class_ids) : NULL;
			$liveClass->subject_id = $subject_id;
			$liveClass->subject_title = $subject_title;
			$liveClass->image = $filename;
			$liveClass->meeting_id = $request->get('meeting_id');
			$liveClass->pass_code = $request->get('pass_code');
			$liveClass->class_time = date('Y-m-d H:i:s', strtotime($request->get('class_time')));
			$liveClass->end_time = date('Y-m-d H:i:s', strtotime($request->get('end_time')));
			$liveClass->isFree = $request->get('free');
			$liveClass->status = 1;
			$user = User::where("id", $request->get('added_by'))->first();
			if($user->role_id==1){
				$liveClass->master_class = 1;
			}
			$liveClass->save();
			$liveClassId = $liveClass->id;

			\Session::flash('msg', 'Live Class Added Successfully.');
			return back();
		}
	}

	/**
	 * Display the specified resource.
	 *
	 * @param  \App\Models\LiveClass $liveclasses
	 * @return \Illuminate\Http\Response
	 */
	public function show(LiveClass $liveclasses)
	{
		//
	}

	public function edit(LiveClass $liveclasses, $id)
	{
		$user = Auth::user();
		$liveClass = LiveClass::findOrFail($id);
		$teachers = User::whereIn("role_id",array(1, 2))->where("status",1)->get();
		$classes = StudentClass::orderBy("id", "ASC")->get();
		$class_ids = !empty($liveClass->class_ids) ? explode(",", $liveClass->class_ids) : NULL;
		//$class_ids = $liveClass->class_ids;
		if (!empty($class_ids)) {
			$getClassSubjectData = StudentClassSubject::whereIn("class_id", $class_ids)->get();
			$subject_ids_arr = [];
			$i=0;
			foreach ($getClassSubjectData as $classSubject) {
				$subject_ids = !empty($classSubject['subject_ids']) ? explode(",", $classSubject['subject_ids']) : [];
				foreach ($subject_ids as $key => $value) {
					$subject_ids_arr[$i]=$value;
					$i++;
				}
			}
			array_unique($subject_ids_arr);
			$subjects = Subject::whereIn("id",$subject_ids_arr)->orderBy("title", "ASC")->get();
		} else {
			$subjects = Subject::orderBy("title", "ASC")->get();
		}

		return view('admin.liveclasses.edit',compact('user','liveClass','teachers','classes','subjects'));
	}

	public function update(Request $request, LiveClass $liveclasses, $id)
	{
		//dd($request->all());
		$s3 = AWS::createClient('s3');
		
		$validator = Validator::make($request->all(), [
			'added_by' => 'required',
			'title' => 'required',
			'subject_id' => 'required',
			'meeting_id' => 'required|regex:/^\S*$/u',
			'class_time' => 'required',
			'end_time' => 'required',
			'video' => 'mimes:mp4',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$liveClass = LiveClass::findOrFail($id);

			$file = $request->file('image');
			$video = $request->file('video');
			$pdf = $request->file('pdf');
			
			$destinationPath = public_path() . '/upload/liveclasses/';
			if($file){
				$imageOriginalName = $file->getClientOriginalName();
				$filename = time() . $imageOriginalName;
				$file->move($destinationPath, $filename);
			}else{
				$filename = $liveClass->image;  
			}
			if($video){
				$videoOriginalName = $video->getClientOriginalName();
				//$videofilename = strtotime(date('Y-m-d-H:isa')).$videoOriginalName;
				$videofilename = time() . "_org.mp4";
				$video->move($destinationPath, $videofilename);
				$uploads3 = 0;
				$liveClass->video_1 = $liveClass->video_2 = $liveClass->video_3 = $liveClass->starttime = $liveClass->endtime = NULL;
				$liveClass->uploads3v1 = $liveClass->uploads3v2 = $liveClass->uploads3v3 = $liveClass->processtatus = 0;
			}else{
				$videofilename = $liveClass->video;  
				$uploads3 = $liveClass->uploads3;
			}

			if($video){
				if(!empty($liveClass->video) && file_exists( public_path().'/upload/liveclasses/'.$liveClass->video )) {
					unlink( public_path().'/upload/liveclasses/'.$liveClass->video );
				}
				if(!empty($liveClass->video)){
					$videoName = $liveClass->video;
					$s3->deleteObject(array(
						'Bucket' => env('AWS_BUCKET'),
						'Key'    => $videoName
					));
					$s3->deleteObject(array(
						'Bucket' => env('AWS_BUCKET'),
						'Key'    => "mobile_app_data/".$videoName
					));
				}
				$deleteVideoTemp = VideoTemp::where('liveclassId',$id)->delete();
			}
			if($pdf){
				$pdfOriginalName = $pdf->getClientOriginalName();
				//$pdffilename = strtotime(date('Y-m-d-H:isa')).$pdfOriginalName;
				$pdffilename = time() . $pdfOriginalName;
				$pdf->move($destinationPath, $pdffilename);
			}else{
				$pdffilename = $liveClass->pdf;
			}

			$subject_id = $request->get('subject_id');
			$subject_data = Subject::select("title")->where("id",$subject_id)->first();
			$subject_title = !empty($subject_data) ? $subject_data->title : NULL;
			$liveClass->added_by = $request->get('added_by');
			$liveClass->title = $request->get('title');
			$liveClass->class_type = $request->get('class_type');
			$liveClass->class_ids = !empty($request->class_ids) ? implode(",", $request->class_ids) : NULL;
			$liveClass->subject_id = $subject_id;
			$liveClass->subject_title = $subject_title;
			$liveClass->image = $filename;
			//$liveClass->video = $request->get('video');
			$liveClass->video = $videofilename;
			$liveClass->pdf = $pdffilename;
			$liveClass->meeting_id = $request->get('meeting_id');
			$liveClass->pass_code = $request->get('pass_code');
			$liveClass->class_time = date('Y-m-d H:i:s', strtotime($request->get('class_time')));
			$liveClass->end_time = date('Y-m-d H:i:s', strtotime($request->get('end_time')));
			$liveClass->isFree = $request->get('free');
			$liveClass->uploads3 = $uploads3;
			//$liveClass->status = 1;
			$user = User::where("id", $request->get('added_by'))->first();
			if($user->role_id==1){
				$liveClass->master_class = 1;
			}
			$liveClass->save();
			$liveClassId = $liveClass->id;

			\Session::flash('msg', 'Live Class Updated Successfully.');
			return redirect('/admin/liveclasses');
		}

	}

	public function delete($id)
	{
		$liveClass = LiveClass::findOrFail($id);
		$liveClass->deleted=1;
		$liveClass->update();

		\Session::flash('msg', 'Live Class Deleted Successfully.');
		return redirect()->back();
	}

	public function updateStatus($id,$status)
	{
		$liveClass = LiveClass::findOrFail($id);
		$liveClass->status=$status;
		$liveClass->update();

		\Session::flash('msg', 'Live Class Status Updated Successfully.');
		return redirect()->back();
	}

	public function notify(Request $request)
	{
		$class_id 	= ($request->class_id) ? $request->class_id : '';
		$subject_id = ($request->subject_id) ? $request->subject_id : '';
		$liveClsId = ($request->live_class) ? $request->live_class : '';
		$from    = ($request->from) ? $request->from : '';
		$to      = ($request->to) ? $request->to.' 23:59:59' : date('Y-m-d H:i:s');

		$classes = StudentClass::orderBy("id", "ASC")->get();
		if (!empty($class_id)) {
			$classSubject = StudentClassSubject::where("class_id", $class_id)->first();
			$subject_ids = !empty($classSubject['subject_ids']) ? explode(",", $classSubject['subject_ids']) : [];
			$subjects = Subject::whereIn("id",$subject_ids)->orderBy("title", "ASC")->get();
		} else {
			$subjects = Subject::orderBy("title", "ASC")->get();
		}

		$liveClasses = LiveClass::where("deleted", 0)->orderBy("id", "DESC")->get();
		$ClassesData = LiveClass::orderBy("id", "DESC")
				 ->Where(function($query) use ($liveClsId) {
					if (isset($liveClsId) && !empty($liveClsId)) {
						$query->where("id", $liveClsId);
					}
				 })
				 ->Where(function($query) use ($class_id) {
					if (isset($class_id) && !empty($class_id)) {
						$query->whereRaw("find_in_set($class_id,class_ids)");
					}
				 })
				 ->Where(function($query) use ($subject_id) {
					if (isset($subject_id) && !empty($subject_id)) {
						$query->where("subject_id", $subject_id);
					}
				 })
				 ->Where(function($query) use ($from, $to) {
					if (isset($from) && !empty($from)) {
						//$query->whereBetween("created_at", [$from, $to]);
						$query->where("class_time", ">=", $from)->where("end_time", "<=", $to);
					} else {
						$query->where("end_time", ">=", $to);
					}
				 })
				 ->where("deleted", 0)->get();
		if (!empty($ClassesData)) {
			$liveClassIds = array();
			foreach($ClassesData as $livecls){
				$liveClassIds[] = $livecls->id;
			}
			$data = LiveclassNotify::whereIn('class_id', $liveClassIds)->where("status", 0);
			$totalResult = $data->count();
			$data = $data->paginate(50);
		} else {
			$data = LiveclassNotify::where("status", 0);
			$totalResult = $data->count();
			$data = $data->paginate(50);
		}

		return view('admin.liveclasses.notify', compact('classes','subjects','liveClasses','data','totalResult'));
	}

	public function sendLiveclass($id)
	{
		$liveClass = LiveclassNotify::findOrFail($id);
		
		$userId = $liveClass->user_id;
		$user = User::where("id", $userId)->first();
		$token = isset($user->deviceToken) ? $user->deviceToken : '';
		if ($token!='') {
			$title = 'BrainyWood';
			$message = 'Please check your Live class.';
			$click_action = 'Liveclass';
			$module_id = $id;
		//	$this->helper->notificationsend($token, $title, $message, $click_action, $module_id); // ak
		}

		$liveClass->status=1;
		$liveClass->update();

		\Session::flash('msg', 'Live Class Notification Sent Successfully.');
		return redirect('/admin/liveclasses/notify');
	}

	public function convertVideo()
	{
		$data = VideoTemp::where('liveclassId','!=',0)->orderBy('id', 'ASC')->get();
		
		return view('admin.liveclasses.convertVideo', compact('data'));
	}

	public function approveVideo($id)
	{
		$videoTemp = VideoTemp::where('id',$id)->first();
		$liveclassId = $videoTemp->liveclassId;
		$low_status = $videoTemp->low_status;
		$med_status = $videoTemp->med_status;
		$high_status = $videoTemp->high_status;
		$liveClass = LiveClass::findOrFail($liveclassId);
		if ($low_status == 1) {
			$liveClass->video_1 = $videoTemp->low_video;
		}
		if ($med_status == 1) {
			$liveClass->video_2 = $videoTemp->med_video;
		}
		if ($high_status == 1) {
			$liveClass->video_3 = $videoTemp->high_video;
		}
		$liveClass->update();
		
		$delete = VideoTemp::where('id',$id)->delete();

		\Session::flash('msg', 'Converted Video Approved Successfully.');
		return redirect('/admin/liveclasses/convertVideo');
	}

	public function imgremove($id)
	{
		$liveClass = LiveClass::findOrFail($id);
		if(file_exists( public_path().'/upload/liveclasses/'.$liveClass->image )) {
			unlink( public_path().'/upload/liveclasses/'.$liveClass->image );
		}
		$liveClass->image = NULL;
		$liveClass->update();

		\Session::flash('msg', 'LiveClass Image Removed Successfully.');
		return redirect()->back();
	}

	public function vidremove($id, $video_type)
	{
		$s3 = AWS::createClient('s3');
		
		$liveClass = LiveClass::findOrFail($id);
		if(!empty($liveClass->video) && file_exists( public_path().'/upload/liveclasses/'.$liveClass->video )) {
			unlink( public_path().'/upload/liveclasses/'.$liveClass->video );
		}
		if(!empty($liveClass->video)){
			$videoName = $liveClass->video;
			$s3->deleteObject(array(
				'Bucket' => env('AWS_BUCKET'),
				'Key'    => $videoName
			));
			$s3->deleteObject(array(
				'Bucket' => env('AWS_BUCKET'),
				'Key'    => "mobile_app_data/".$videoName
			));
		}
		$liveClass->video = NULL;
		$liveClass->uploads3 = 0;
		if(!empty($liveClass->video_1) && file_exists( public_path().'/upload/liveclasses/'.$liveClass->video_1 ) && $liveClass->video_1!='NA') {
			unlink( public_path().'/upload/liveclasses/'.$liveClass->video_1 );
		}
		if(!empty($liveClass->video_1)){
			$videoName1 = $liveClass->video_1;
			$s3->deleteObject(array(
				'Bucket' => env('AWS_BUCKET'),
				'Key'    => "mobile_app_data/".$videoName1
			));
		}
		$liveClass->video_1 = NULL;
		$liveClass->uploads3v1 = 0;
		if(!empty($liveClass->video_2) && file_exists( public_path().'/upload/liveclasses/'.$liveClass->video_2 ) && $liveClass->video_2!='NA') {
			unlink( public_path().'/upload/liveclasses/'.$liveClass->video_2 );
		}
		if(!empty($liveClass->video_2)){
			$videoName2 = $liveClass->video_2;
			$s3->deleteObject(array(
				'Bucket' => env('AWS_BUCKET'),
				'Key'    => "mobile_app_data/".$videoName2
			));
		}
		$liveClass->video_2 = NULL;
		$liveClass->uploads3v2 = 0;
		if(!empty($liveClass->video_3) && file_exists( public_path().'/upload/liveclasses/'.$liveClass->video_3 ) && $liveClass->video_3!='NA') {
			unlink( public_path().'/upload/liveclasses/'.$liveClass->video_3 );
		}
		if(!empty($liveClass->video_3)){
			$videoName3 = $liveClass->video_3;
			$s3->deleteObject(array(
				'Bucket' => env('AWS_BUCKET'),
				'Key'    => "mobile_app_data/".$videoName3
			));
		}
		$liveClass->video_3 = NULL;
		$liveClass->uploads3v3 = 0;
		$deleteVideoTemp = VideoTemp::where('liveclassId',$id)->delete();
		$liveClass->processtatus = 0;
		$liveClass->starttime = NULL;
		$liveClass->endtime = NULL;
		$liveClass->update();

		\Session::flash('msg', 'LiveClass Video Removed Successfully.');
		return redirect()->back();
	}

	public function pdfremove($id)
	{
		$liveClass = LiveClass::findOrFail($id);
		if(!empty($liveClass->pdf) && file_exists( public_path().'/upload/liveclasses/'.$liveClass->pdf )) {
			unlink( public_path().'/upload/liveclasses/'.$liveClass->pdf );
			$liveClass->pdf = NULL;
		}
		$liveClass->update();

		\Session::flash('msg', 'LiveClass Doc Removed Successfully.');
		return redirect()->back();
	}

	public function getSubjectList(Request $request)
	{
		//$class_ids = !empty($request->class_ids) ? implode(",", $request->class_ids) : NULL;
		$class_ids = $request->class_ids;
		//dd($class_ids);
		?>
		<option value="">--Select--</option>
		<?php
		if (!empty($class_ids)) {
			$getClassSubjectData = StudentClassSubject::whereIn("class_id", $class_ids)->get();
			$subject_ids_arr = [];
			$i=0;
			foreach ($getClassSubjectData as $classSubject) {
				$subject_ids = !empty($classSubject['subject_ids']) ? explode(",", $classSubject['subject_ids']) : [];
				foreach ($subject_ids as $key => $value) {
					$subject_ids_arr[$i]=$value;
					$i++;
				}
			}
			array_unique($subject_ids_arr);
			$getSubjectData = Subject::whereIn("id",$subject_ids_arr)->orderBy("title", "ASC")->get();
			foreach ($getSubjectData as $key => $value) {
				?>
				<option value="<?php echo $value['id']; ?>"><?php echo $value['title']; ?></option>
				<?php
			}
		}
	}

    public function getNotifyLiveclassesCsv(Request $request)
    {
		$class_id 	= ($request->class_id) ? $request->class_id : '';
		$subject_id = ($request->subject_id) ? $request->subject_id : '';
		$liveClsId 	= ($request->live_class) ? $request->live_class : '';
		$from 		= ($request->from) ? $request->from : '';
		$to 		= ($request->to) ? $request->to.' 23:59:59' : date('Y-m-d H:i:s');
		
		$ClassesData = LiveClass::orderBy("id", "DESC")
				 ->Where(function($query) use ($liveClsId) {
					if (isset($liveClsId) && !empty($liveClsId)) {
						$query->where("id", $liveClsId);
					}
				 })
				 ->Where(function($query) use ($class_id) {
					if (isset($class_id) && !empty($class_id)) {
						$query->whereRaw("find_in_set($class_id,class_ids)");
					}
				 })
				 ->Where(function($query) use ($subject_id) {
					if (isset($subject_id) && !empty($subject_id)) {
						$query->where("subject_id", $subject_id);
					}
				 })
				 ->Where(function($query) use ($from, $to) {
					if (isset($from) && !empty($from)) {
						//$query->whereBetween("created_at", [$from, $to]);
						$query->where("class_time", ">=", $from)->where("end_time", "<=", $to);
					} else {
						$query->where("end_time", ">=", $to);
					}
				 })
				 ->where("deleted", 0)->get();
		if (!empty($ClassesData)) {
			$liveClassIds = array();
			foreach($ClassesData as $livecls){
				$liveClassIds[] = $livecls->id;
			}
			$liveclsNotifyRequests = LiveclassNotify::whereIn('class_id', $liveClassIds)->where("status", 0)->groupBy("user_id");
		} else {
			$liveclsNotifyRequests = LiveclassNotify::where("status", 0)->groupBy("user_id");
		}
	    //dd($liveclsNotifyRequests->count());

	    $headers = array(
	        'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
	        'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
	        'Content-Disposition' => 'attachment; filename=abc.csv',
	        'Expires' => '0',
	        'Pragma' => 'public',
	    );

		$filename = "liveClasses_".date('d-M-Y').".csv";
		$handle = fopen($filename, 'w');
		fputcsv($handle, [
		    "#",
		    "UserName - Phone",
		    "Live Class Title",
		    "Start DateTime",
		    "End DateTime",
		]);

	    $i = 1;
		$liveclsNotifyRequests->chunk(100000, function ($data) use ($handle,$i) {
		    foreach ($data as $row) {
		        // Add a new row with data
		        fputcsv($handle, [
		            $i,
		            ($row->user_id>0) ? $row->user->name.' - '.$row->user->phone : '',
		            ($row->class_id>0) ? $row->liveclass->title : '',
		            ($row->class_id>0) ? date('d-M-y h:i A', strtotime($row->liveclass->class_time)) : '',
		            ($row->class_id>0) ? date('d-M-y h:i A', strtotime($row->liveclass->end_time)) : '',
		        ]);
	        	$i++;
		    }
		});

		fclose($handle);

		return response()->download($filename, "liveClasses_".date('d-M-Y').".csv", $headers);
	}

}
