<?php

namespace App\Http\Controllers\Admin;
use Illuminate\Pagination\LengthAwarePaginator;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use App\Http\Helper as Helper;
use Illuminate\Http\Request;
use App\Http\Requests\Admin\StoreUsersRequest;
use App\Http\Requests\Admin\UpdateUsersRequest;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Gate;
use Spatie\Permission\Models\Role;
use App\Models\ChatGroup;
use App\Models\City;
use App\Models\CouponCode;
use App\Models\Courses;
use App\Models\Franchise;
use App\Models\State;
use App\Models\StudentClass;
use App\Models\Payment;
use App\User;
use GuzzleHttp\Client;
use ZipArchive;
use Illuminate\Support\Facades\File;
use Exception;
use PDF;
use Illuminate\Support\Facades\URL;
use DateInterval;
use App\Models\UserPoint;
use App\Models\Subscription;
use App\Models\UserSubscription;
use Illuminate\Support\Facades\Validator;
use Auth;
use App\Models\Category;
use App\Models\SubCategory;
use App\Models\Subject;
use App\Models\Plan;
use App\Models\Profile_details;
use App\Models\TeacherAvailabiltyOfSlots;
use App\Models\TeacherAvailabiltyOfSlotsDetails;
use App\Models\Timemanage;
use Carbon\Carbon;
use DateTime;
use DateTimeZone;
use App\Models\TeachingDetails;
use App\Models\StudentApplyForDemo;
use App\Models\StudentBookingFrees;
use App\Models\Notification;
use App\Models\StudentContactSupportEnroll;
use App\Models\TeacherContactSupport;
use App\Models\Enroll;
use App\Models\SubAdminAssignRole;
use App\Models\PurchaseClass;
use App\Models\StudentEnrollSchedule;
use DB;
use Symfony\Component\HttpFoundation\StreamedResponse;
class UsersController extends Controller
{
	protected $helper;

	public function __construct(Helper $helper)
	{
		$this->helper = $helper;
	}
	
	/**
	 * Display a listing of User.
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function index()
	{
		if (! Gate::allows('users_manage')) {
			return abort(401);
		}

		//$users = User::all();
		//$users = User::whereIn("role_id",array(1, 2))->get();
		//$users = User::where("role_id",1)->where("deleted",0)->orderBy("id", "DESC")->get();
		//$users = User::where("role_id","!=",2)->where('role_id',"!=",3)->where('role_id',"!=",6)->where("deleted",0)->orderBy("id", "DESC");
		$students = '';
		$users = User::where("deleted",0)->orderBy("id", "DESC");
		$totalAdmins = $users->count();
		$users = $users->paginate(10);
		$filterUrl = url()->full();
		
		// $users = User::where("role_id","!=",2)->where("role_id","!=",3)->where("deleted",0)->orderBy("id", "DESC");
		// $totalAdmins = $users->count();
		// $users = $users->paginate(50);
		// $filterUrl = url()->full();
		return view('admin.users.index', compact('users','totalAdmins','filterUrl','students'));
	}
	public function teachers(Request $request)
	{

		if (! Gate::allows('users_manage')) {
			return abort(401);
		}
		$students ='';
		if(!empty($request->all())){
			$user_id = ($request->user_id) ? $request->user_id : '';
		 	$class_id  = isset($request->class_id)? $request->class_id :'';
		 	$category_id = isset($request->category_id) ? $request->category_id : '';
		 	$subject_id = isset($request->subject_id) ? $request->subject_id : '';
		 	$sub_category_id = isset($request->sub_category_id) ? $request->sub_category_id : '';
		 	$Profile_details =	Profile_details::select('user_id')->orderBy("id", "DESC")
			 ->Where(function($query) use ($class_id) {
				if (isset($class_id) && !empty($class_id)) {
					$query->where("select_class",$class_id);
				}
			 })
			 ->Where(function($query) use ($category_id) {
				if (isset($category_id) && !empty($category_id)) {
					$query->where("select_category",$category_id);
				}
			 })
			 ->Where(function($query) use ($subject_id) {
				if (isset($subject_id) && !empty($subject_id)) {
					$query->where("select_subject",$subject_id);
				}
			 })
			 ->Where(function($query) use ($sub_category_id) {
				if (isset($sub_category_id) && !empty($sub_category_id)) {
					$query->where("select_sub_category",$sub_category_id);
				}
			 })->get();
			 $user_id = array();
			 if(!empty($Profile_details))
			 {
			 	foreach ($Profile_details as $key => $value) {
			 		array_push($user_id,$value->user_id);
			 	}
			 }
			 // dd($Profile_details);
			 // if()
			$users = User::where(['role_id'=>2])->where('teacher_complete_profile_status',1)->where("deleted",0)->OrderBy("id","desc");
						 $totalTeachers = $users->count();
			$users = $users->paginate(10);
		}
		else
		{
			// if()
			$users = User::where(['role_id'=>2])->where('teacher_complete_profile_status',1)->where("deleted",0)->OrderBy("id","desc");
			$totalTeachers = $users->count();
			$users = $users->paginate(10);
		}
		
			// 			$users = User::orderBy("id", "DESC")
			// 			 ->Where(function($query) use ($user_id) {
			// 				if (isset($user_id) && !empty($user_id)) {
			// 					$query->where("id",$user_id);
			// 				}
			// 			 })
			// 			 ->where("role_id",2)->where("deleted",0);
			// //				$totalTeachers = $users->count();
			// 				$totalTeachers = isset($users) ?  $users->count() :0;
			// 				$users = $users->paginate(50);
		
            if(!empty($request->user_name)){
			$users = User::Where(function ($query) use($request) {
                $query->where('name', 'like',  '%' . $request->user_name .'%')->orwhere('email', 'like',  '%' . $request->user_name .'%');     
                })->paginate(10);
		}
		$totalStudents = $users->count();
		$filterUrl = url()->full();

		// $filterUrl = url()->full();
	
		$classes = StudentClass::where('status',1)->where('deleted',0)->orderBy('id', 'ASC')->get();
		$category = Category::where('status',1)->where('deleted',0)->orderBy('id', 'ASC')->get();
		$subject = Subject::where('status',1)->orderBy("id", "DESC")->get();
		$subCategory = SubCategory::where('status',1)->orderBy("id","DESC")->get();

		return view('admin.users.index', compact('users','totalTeachers','filterUrl','classes','category','subject','subCategory','students'));
	}

	public function teacherSupport(Request $request){

		if(!Gate::allows('supports'))
		{
			return abort(401);
		}
		$TeacherContactSupport = TeacherContactSupport::OrderBy("id","desc");
		$TeacherContactSupport = $TeacherContactSupport->paginate(10);

		if($TeacherContactSupport->all())
		{
			foreach ($TeacherContactSupport->all() as $key => $value) {
				$getStudentApplyForDemo =Enroll::where("id",$value->enroll_id)->first();

				if(!empty($getStudentApplyForDemo))
				{
					$getUser =User::where("id",$getStudentApplyForDemo->student_id)->first();
					$value->user_id = $getStudentApplyForDemo->student_id;
					$value->student_name = isset($getUser) ?$getUser->name :"";
					
					$getTeacher =User::where("id",$value->teacher_id)->first();
					$value->teacher_name = isset($getTeacher) ?$getTeacher->name :"";

					// if(!empty($getStudentApplyForDemo->teacher_availabilty_of_slots_details_id))
					// {
					// 	$getTeacherAvailabiltyOfSlotsDetails=TeacherAvailabiltyOfSlotsDetails::where("id",$getStudentApplyForDemo->teacher_availabilty_of_slots_details_id)->first();
					// //	dd($getTeacherAvailabiltyOfSlotsDetails);
					// 	if(!empty($getTeacherAvailabiltyOfSlotsDetails))
					// 	{
					// 		$start_time = $getTeacherAvailabiltyOfSlotsDetails->start_time;
					// 		$end_time = $getTeacherAvailabiltyOfSlotsDetails->end_time;
							
					// 		 $StudentBookingFrees=StudentBookingFrees::where("studentapplyfordemo_id",$getStudentApplyForDemo->id)
					// 		->where('select_time',$start_time)->where('select_end_time',$end_time)->first();
					// 		$value->class_date = date('d-m-Y',strtotime($StudentBookingFrees->select_date));
					// 		$value->class_start_time = $StudentBookingFrees->select_time;
					// 	}
					// }
				if($getStudentApplyForDemo->student_apply_for_demo_id !=0){
				   $demodata = StudentApplyForDemo::where('id',$getStudentApplyForDemo->student_apply_for_demo_id)->first();
				   if(!empty($demodata->class_id) && $demodata->class_id!=0 && $demodata->subject_id!=0){
							 $getStudentClass=StudentClass::where(["id"=>$demodata->class_id,'status'=>1,'deleted'=>0])->first();
							 $getSubjectClass=Subject::where(["id"=>$demodata->subject_id,'status'=>1,'deleted'=>0])->first();
                             if(!empty($getStudentClass) && (!empty($getSubjectClass))){
										$value->class_name = $getStudentClass->class_name;
										$value->subject_name = $getSubjectClass->title;
									}
								}
								else
								{
									$getCoursesClass=Courses::where(["id"=>$demodata->course_id,'status'=>1,'deleted'=>0])->first();
									$getCategoryClass=Category::where(["id"=>$demodata->category_id,'status'=>1,'deleted'=>0])->first();
									$value->course_name= isset($getCoursesClass->name) ? $getCoursesClass->name:'';
									$value->category_name= isset($getCategoryClass->name) ? $getCategoryClass->name:'';
								}
								}
								else{
									if(!empty($getStudentApplyForDemo->class_id) && $getStudentApplyForDemo->class_id!=0 && $getStudentApplyForDemo->subject_id!=0){
									$getStudentClass=StudentClass::where(["id"=>$getStudentApplyForDemo->class_id,'status'=>1,'deleted'=>0])->first();
									$getSubjectClass=Subject::where(["id"=>$getStudentApplyForDemo->subject_id,'status'=>1,'deleted'=>0])->first();

									if(!empty($getStudentClass) && (!empty($getSubjectClass)))
									{
										$value->class_name = isset($getStudentClass->class_name) ? $getStudentClass->class_name:'';
										$value->subject_name = isset($getSubjectClass->title) ? $getSubjectClass->title:'';
									}
								}
								else
								{
									$getCoursesClass=Courses::where(["id"=>$getStudentApplyForDemo->course_id,'status'=>1,'deleted'=>0])->first();
									$getCategoryClass=Category::where(["id"=>$getStudentApplyForDemo->category_id,'status'=>1,'deleted'=>0])->first();
									$value->course_name= isset($getCoursesClass->name) ? $getCoursesClass->name:'';
									$value->category_name= isset($getCategoryClass->name) ? $getCategoryClass->name:'';
								}
								}
				}
			}
		}

// dd($TeacherContactSupport);
				return view('admin.supports.teacherSupport',compact("TeacherContactSupport"));
	}

	public function studentSupport(Request $request){
		if(!Gate::allows('supports'))
		{
			return abort(401);
		}
		$getStudent = StudentContactSupportEnroll::orderBy("id","desc");
		$getStudent = $getStudent->paginate(10);
		//	dd($getStudent->all());
		if(!empty($getStudent->all()))
		{

			foreach ($getStudent->all() as $key => $value) {

				$student_name = User::where("id",$value->student_id)->first();
				$teacher_name = User::where("id",$value->teacher_id)->first();
				$value->student_name = isset($student_name->name) ? $student_name->name:"";
				$value->teacher_name = isset($teacher_name->name) ? $teacher_name->name:"";
				$getEnroll = StudentContactSupportEnroll::join('enrolls',"enrolls.id","=","student_contact_support_enroll.enroll_id")
				// ->join('studentapplyfordemo',"studentapplyfordemo.id","enrolls.student_apply_for_demo_id")
				->where("student_contact_support_enroll.enroll_id",$value->enroll_id)
				->first([

					'student_contact_support_enroll.id',
					'student_contact_support_enroll.student_id',
					'student_contact_support_enroll.teacher_id',
					'student_contact_support_enroll.message',
					'student_contact_support_enroll.created_at',
					'enrolls.student_apply_for_demo_id',
					'enrolls.no_of_class',
					'enrolls.fees',
					'enrolls.class_id',
					'enrolls.course_id',
					'enrolls.subject_id',
					'enrolls.category_id',
				]);
				// dd($getEnroll);
				// if(!empty($getEnroll->teacher_availabilty_of_slots_details_id))
				// {
				// 	$getTeacherAvailabiltyOfSlotsDetails=TeacherAvailabiltyOfSlotsDetails::where("id",$getEnroll->teacher_availabilty_of_slots_details_id)->first();
				// //	dd($getTeacherAvailabiltyOfSlotsDetails);
				// 	if(!empty($getTeacherAvailabiltyOfSlotsDetails))
				// 	{
				// 		$start_time = $getTeacherAvailabiltyOfSlotsDetails->start_time;
				// 		$end_time = $getTeacherAvailabiltyOfSlotsDetails->end_time;
						
				// 		 $StudentBookingFrees=StudentBookingFrees::where("studentapplyfordemo_id",$getEnroll->student_apply_for_demo_id)
				// 		->where('select_time',$start_time)->where('select_end_time',$end_time)->first();
				// 		$value->class_date = date('d-m-Y',strtotime($StudentBookingFrees->select_date));
				// 		$value->class_start_time = $StudentBookingFrees->select_time;

				// 	}
				// }
				if($getEnroll->student_apply_for_demo_id !=0){
					$demodata = StudentApplyForDemo::where('id',$getEnroll->student_apply_for_demo_id)->first();
					if(!empty($demodata->class_id) && $demodata->class_id!=0 && $demodata->subject_id!=0)
				{
					$getStudentClass=StudentClass::where(["id"=>$demodata->class_id,'status'=>1,'deleted'=>0])->first();
					$getSubjectClass=Subject::where(["id"=>$demodata->subject_id,'status'=>1,'deleted'=>0])->first();

					if(!empty($getStudentClass) && (!empty($getSubjectClass)))
					{
						$value->class_name = isset($getStudentClass->class_name) ? $getStudentClass->class_name:'';
						$value->subject_name = isset($getSubjectClass->title) ? $getSubjectClass->title:'';
					}
				}
				else
				{
					$getCoursesClass=Courses::where(["id"=>$demodata->course_id,'status'=>1,'deleted'=>0])->first();
					$getCategoryClass=Category::where(["id"=>$demodata->category_id,'status'=>1,'deleted'=>0])->first();
					$value->course_name= isset($getCoursesClass->name) ? $getCoursesClass->name:'';
					$value->category_name= isset($getCategoryClass->name) ? $getCategoryClass->name:'';
				}
				}else{
					// dd('ddd');
					if(!empty($getEnroll->class_id) && $getEnroll->class_id!=0 && $getEnroll->subject_id!=0)
				{
					$getStudentClass=StudentClass::where(["id"=>$getEnroll->class_id,'status'=>1,'deleted'=>0])->first();
					// dd($getStudentClass);
					$getSubjectClass=Subject::where(["id"=>$getEnroll->subject_id,'status'=>1,'deleted'=>0])->first();

					if(!empty($getStudentClass) && (!empty($getSubjectClass)))
					{
						$value->class_name = isset($getStudentClass->class_name) ? $getStudentClass->class_name:'';
						$value->subject_name = isset($getSubjectClass->title) ? $getSubjectClass->title:'';
					}
				}
				else
				{
					$getCoursesClass=Courses::where(["id"=>$getEnroll->course_id,'status'=>1,'deleted'=>0])->first();
					$getCategoryClass=Category::where(["id"=>$getEnroll->category_id,'status'=>1,'deleted'=>0])->first();
					$value->course_name= isset($getCoursesClass->name) ? $getCoursesClass->name:'';
					$value->category_name= isset($getCategoryClass->name) ? $getCategoryClass->name:'';
				}
				}
				

				
			}
		}
		
     // dd($getStudent);
		return view('admin.supports.studentSupport',compact("getStudent"));

	}


	function getTimezoneFullNameFallback($timezoneIdentifier) {

	    $timezonesAfrica = [
	    "Africa/Abidjan" => ["Standard" => "Greenwich Mean Time", "Daylight" => null],
	    "Africa/Accra" => ["Standard" => "Greenwich Mean Time", "Daylight" => null],
	    "Africa/Addis_Ababa" => ["Standard" => "East Africa Time", "Daylight" => null],
	    "Africa/Algiers" => ["Standard" => "Central European Time", "Daylight" => null],
	    "Africa/Cairo" => ["Standard" => "Eastern European Time", "Daylight" => "Eastern European Summer Time"],
	    "Africa/Casablanca" => ["Standard" => "Western European Time", "Daylight" => "Western European Summer Time"],
	    "Africa/Johannesburg" => ["Standard" => "South Africa Standard Time", "Daylight" => null],
	"America/Argentina/Buenos_Aires" => ["Standard" => "Argentina Time", "Daylight" => null],
	    "America/Chicago" => ["Standard" => "Central Standard Time", "Daylight" => "Central Daylight Time"],
	    "America/Denver" => ["Standard" => "Mountain Standard Time", "Daylight" => "Mountain Daylight Time"],
	    "America/Los_Angeles" => ["Standard" => "Pacific Standard Time", "Daylight" => "Pacific Daylight Time"],
	    "America/New_York" => ["Standard" => "Eastern Standard Time", "Daylight" => "Eastern Daylight Time"],
	    "America/Phoenix" => ["Standard" => "Mountain Standard Time", "Daylight" => null],
	    "America/Sao_Paulo" => ["Standard" => "Brasília Time", "Daylight" => null],
	"Asia/Calcutta" => ["Standard" => "India Standard Time", "Daylight" => null],
	    "Asia/Dubai" => ["Standard" => "Gulf Standard Time", "Daylight" => null],
	    "Asia/Hong_Kong" => ["Standard" => "Hong Kong Time", "Daylight" => null],
	    "Asia/Jakarta" => ["Standard" => "Western Indonesia Time", "Daylight" => null],
	    "Asia/Kabul" => ["Standard" => "Afghanistan Time", "Daylight" => null],
	    "Asia/Karachi" => ["Standard" => "Pakistan Standard Time", "Daylight" => null],
	    "Asia/Kathmandu" => ["Standard" => "Nepal Time", "Daylight" => null],
	    "Asia/Shanghai" => ["Standard" => "China Standard Time", "Daylight" => null],
	    "Asia/Singapore" => ["Standard" => "Singapore Standard Time", "Daylight" => null],
	    "Asia/Tokyo" => ["Standard" => "Japan Standard Time", "Daylight" => null],
	"Australia/Adelaide" => ["Standard" => "Australian Central Standard Time", "Daylight" => "Australian Central Daylight Time"],
	    "Australia/Brisbane" => ["Standard" => "Australian Eastern Standard Time", "Daylight" => null],
	    "Australia/Sydney" => ["Standard" => "Australian Eastern Standard Time", "Daylight" => "Australian Eastern Daylight Time"],
	"Europe/Berlin" => ["Standard" => "Central European Time", "Daylight" => "Central European Summer Time"],
	    "Europe/London" => ["Standard" => "Greenwich Mean Time", "Daylight" => "British Summer Time"],
	    "Europe/Moscow" => ["Standard" => "Moscow Standard Time", "Daylight" => null],
	"Pacific/Auckland" => ["Standard" => "New Zealand Standard Time", "Daylight" => "New Zealand Daylight Time"],
	    "Pacific/Fiji" => ["Standard" => "Fiji Time", "Daylight" => null],
	    "Pacific/Honolulu" => ["Standard" => "Hawaii-Aleutian Standard Time", "Daylight" => null],
	];
    
    return $timezonesAfrica[$timezoneIdentifier]['Standard'] ?? "Unknown Timezone";
}


	public function students_view($id){
		if (! Gate::allows('users_manage')) {
			return abort(401);
		}
		$roles = Role::get()->pluck('name', 'name');

		$user = User::findOrFail($id);
		if(!empty($user->refer_by)){
			$refer_by = User::select('name')->where('id',$user->refer_by)->first();
			$user->refer_by_name = $refer_by->name;
		}


		$timezoneIdentifier = $user->timezone;
		// $timez =  $this->getTimezoneFullNameFallback($timezoneIdentifier);
		// $user->timezone = $timez;


		



		// dd($user);
		return view('admin.users.view', compact('user', 'roles'));
	}

	public function students(Request $request){
		if (! Gate::allows('users_manage')) {
			return abort(401);
		}
		$students = User::where('role_id',6)->get();
		$allUsers = [];//User::where("role_id",3)->where("deleted",0)->orderBy("name", "ASC")->get();
		$country_codes = User::select("country_code")->whereNotNull("country_code")->groupBy("country_code")->get();
		$schools = User::select("school_college")->whereNotNull("school_college")->groupBy("school_college")->get();
		$classes = StudentClass::where('status',1)->where('deleted',0)->orderBy('id', 'ASC')->get();
		$states = State::orderBy('state', 'ASC')->get();
		//$cities = User::select("city")->where("city","!=","")->groupBy("city")->get();
		$cities = City::orderBy('city', 'ASC')->get();
		$subscriptions = Subscription::where('status',1)->where('deleted',0)->orderBy('name', 'ASC')->get();
		//$franchises ="";//ak Franchise::get();
		$franchises =User::where("status",1)->whereNotNull('refer_code')->where("deleted",0)->where("role_id",6)->get();

		//echo '<pre />'; print_r($schools); die;

		//print_r($request->all()); die;
		//$usersCount = User::where("role_id",3)->where("deleted",0)->count();
		
		$user_id = ($request->user_id) ? $request->user_id : '';
		$country_code = ($request->country_code) ? $request->country_code : '';
		$gender = ($request->gender) ? $request->gender : '';
		$school = ($request->school) ? $request->school : '';
		$class = ($request->class_id) ? $request->class_id : '';
		$state = ($request->state) ? $request->state : '';
		$city = ($request->city) ? $request->city : '';
		$plan_id = ($request->plan_id) ? $request->plan_id : '';
		$postal_code = ($request->postal_code) ? $request->postal_code : '';
		$refer_code = ($request->refer_code) ? $request->refer_code : '';
		$from = ($request->from) ? $request->from : '';
		$to = ($request->to) ? $request->to.' 23:59:59' : date('Y-m-d H:i:s');
		$subscription = ($request->subscription) ? $request->subscription : '';

		$users = User::orderBy("id", "DESC")
			 ->Where(function($query) use ($user_id) {
				if (isset($user_id) && !empty($user_id)) {
					$query->where("id",$user_id);
				}
			 })
			 ->Where(function($query) use ($country_code) {
				if (isset($country_code) && !empty($country_code)) {
					$query->where("country_code",$country_code);
				}
			 })
			 ->Where(function($query) use ($gender) {
				if (isset($gender) && !empty($gender)) {
					$query->where("gender",$gender);
				}
			 })
			 ->Where(function($query) use ($school) {
				if (isset($school) && !empty($school)) {
					$query->where("school_college",$school);
				}
			 })
			 ->Where(function($query) use ($class) {
				if (isset($class) && !empty($class)) {
					$query->where("class_id",$class);
				}
			 })
			 ->Where(function($query) use ($state) {
				if (isset($state) && !empty($state)) {
					$query->where("state",$state);
				}
			 })
			 ->Where(function($query) use ($city) {
				if (isset($city) && !empty($city)) {
					$query->where("city",$city);
				}
			 })
			 // ->whereHas('subscription_data', function($query) use ($plan) {
				// if (isset($plan) && !empty($plan)) {
				// 	$query->where("subscription_id",$plan);
				// }
			 // })
			 ->Where(function($query) use ($postal_code) {
				if (isset($postal_code) && !empty($postal_code)) {
					$query->where("postal_code",$postal_code);
				}
			 })
			 ->Where(function($query) use ($refer_code) {
				if (isset($refer_code) && !empty($refer_code)) {
					$query->where("refer_code",$refer_code);
				}
			 })
			 ->Where(function($query) use ($from, $to) {
				if (isset($from) && !empty($from)) {
					$query->whereBetween("created_at",[$from, $to]);
				}
			 })
			 ->where("role_id",6)->where("deleted",0);
		
		if (isset($plan_id) && !empty($plan_id)) {	 
			$users->whereHas('subscription_data', function($query) use ($plan_id) {
				if (isset($plan_id) && !empty($plan_id)) {
					$query->where("subscription_id",$plan_id);
				}
			});
		}
		if (isset($subscription) && !empty($subscription)) {	 
			if ($subscription=='paid') {
				$users->whereHas('subscription_data', function($query) use ($subscription) {
					$query->whereDate("end_date", ">=", date('Y-m-d'))->where("mode", "!=", "FREE");
				});
			/*} elseif ($subscription=='unpaid') {
				$users->whereHas('subscription_data', function($query) use ($subscription) {
					if (isset($subscription) && !empty($subscription)) {
						$query->whereDate("end_date", "<", date('Y-m-d'));
					}
				});*/
			} elseif ($subscription=='trial') {
				$users->whereHas('subscription_data', function($query) use ($subscription) {
					$query->whereDate("end_date", ">=", date('Y-m-d'))->where("mode", "FREE");
				});
			}
		}
		
		
		
		// dd($users);
		
		$users = $users->paginate(10);
		if(!empty($request->user_name)){
			$users = User::with('class_data')->Where(function ($query) use($request) {
                $query->where('name', 'like',  '%' . $request->user_name .'%')->orwhere('email', 'like',  '%' . $request->user_name .'%');     
                })->paginate(10);
		}
		$totalStudents = $users->count();
		$filterUrl = url()->full();

		return view('admin.users.index', compact('allUsers','country_codes','schools','classes','states','cities','subscriptions','franchises','users','totalStudents','filterUrl','students'));
	}
	public function teaching_details($id){
		$data = TeachingDetails::where(['user_id'=>$id])->OrderBy("id","desc");
		$data = $data->paginate(10);
		foreach($data as $datas){
			$userdata = User::where('id',$datas->user_id)->first();
			$datas->currency = $userdata->currency;
		}
		// echo "<pre>";
		// print_r($data);
		// die;
		// dd($data);
		return view('admin.teacher.teaching_details',compact("data"));
		}

	/**
	 * Show the form for creating new User.
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function create()
	{
		if (! Gate::allows('users_manage')) {
			return abort(401);
		}
		$roles = Role::get()->pluck('name', 'name');
		
		return view('admin.users.create', compact('roles'));
	}
	public function teacherCreate()
	{
		if (! Gate::allows('users_manage')) {
			return abort(401);
		}
		$roles = Role::get()->pluck('name', 'name');

		$country_codes = User::select("country_code")->whereNotNull("country_code")->groupBy("country_code")->get();
		$studentClasses = StudentClass::where('status',1)->where('deleted',0)->orderBy('id', 'ASC')->get();
		$states = State::orderBy('state', 'ASC')->get();
		$cities = City::orderBy('city', 'ASC')->get();
		$courses = Courses::orderBy('sort_id', 'ASC')->get();
		$groups = ChatGroup::orderBy('sort_id', 'ASC')->get();

		return view('admin.users.create', compact('roles','country_codes','studentClasses','states','cities','courses','groups'));
	}
	public function studentCreate()
	{
		if (! Gate::allows('users_manage')) {
			return abort(401);
		}
		$roles = Role::get()->pluck('name', 'name');

		$country_codes = User::select("country_code")->whereNotNull("country_code")->groupBy("country_code")->get();
		$studentClasses = StudentClass::where('status',1)->where('deleted',0)->orderBy('id', 'ASC')->get();
		$states = State::orderBy('state', 'ASC')->get();
		$cities = City::orderBy('city', 'ASC')->get();

		return view('admin.users.create', compact('roles','country_codes','studentClasses','states','cities'));
	}

	/**
	 * Store a newly created User in storage.
	 *
	 * @param  \App\Http\Requests\StoreUsersRequest  $request
	 * @return \Illuminate\Http\Response
	 */
	public function store(StoreUsersRequest $request)
	{
		if (! Gate::allows('users_manage')) {
			return abort(401);
		}
		$input = $request->all();
		//dd($input['email']);
		$subject ='Guruathome: Login Details';

		 $message ="Email Address: "  .$input['email']."</br>"  ;
 		 $message .="Password: "  .$input['password']."</br>"  ;
 		 $message .="Admin Panel Url : <a href='".url('/').'/login'."'>Click Here</a>";
 	
 		 
		$roles = $request->input('roles') ? $request->input('roles') : [];

		if (isset($roles[0])) {
			//echo $roles[0]; die;
			if ($roles[0]=='Student') {
				$role_id = 3;
			} else {
				$roles = Role::where('name',$roles[0])->first();
				$role_id = $roles->id;
			}
		} else {
			$role_id = 3;
		}

		if ($role_id==3) {
			if ($request->country_code == '91') {
				$validator = Validator::make($request->all(), [
					'phone' => 'required|digits:10',
					//'postal_code' => 'required|digits:6',
					//'parents_phone' => 'required|digits:10',
				]);
				if ($validator->fails()) {
					return back()->withErrors($validator)->withInput();
				}
			}
			$refer_code = $request->refer_code;
			$franchiseUserId = 0;
			/*//ak
			if (!empty($refer_code)) {
				$franchise = Franchise::where("refer_code", $refer_code)->first();
				if (!empty($franchise)) {
					$franchiseUserId = $franchise->user_id;
					$gender = $request->gender;
					$name = $request->name;
					$email = $request->email;
					$phone = $request->phone;
					$class_id = $request->class_id;
					$studentClass = StudentClass::where("id", $class_id)->first();
					$class_name = !empty($studentClass) ? $studentClass->class_name : 'NA';
					if ($gender=='Female'){
						$user_gender = 202;
					} elseif ($gender=='Non Binary'){
						$user_gender = 203;
					} else {
						$user_gender = 201;
					}
					$city = $request->city;
					$state_name = $request->state;
					$ch = curl_init();
					$url = config('constant.FMSLEADAPIURL');
					curl_setopt($ch, CURLOPT_URL,$url);
					curl_setopt($ch, CURLOPT_POST, true);
					curl_setopt($ch, CURLOPT_POSTFIELDS, "user_id=$franchiseUserId&full_name=$name&email_address=$email&mobile_number=$phone&class_name=$class_name&user_gender=$user_gender&city_name=$city&state_name=$state_name");
					curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
					$output = curl_exec ($ch);

					curl_close ($ch);

					$response = json_decode($output);
					//echo '<pre />'; print_r($response); die; // Show output
					if ($response->Status==false) {
						\Session::flash('msg', $response->Message);
						return redirect()->back();
					}
				} else {
					\Session::flash('msg', 'Refferal code not exists. Please try again!');
					return redirect()->back();
				}
			}
			*/
			$user = new User();
			$user->role_id = $role_id;
			$user->name = $request->name;
			$user->email = $request->email;
			$user->country_code = $request->country_code;
			$user->phone = $request->phone;
			$user->dob = date('Y-m-d',strtotime($request->get('dob')));
			$user->gender = $request->gender;
			$user->class_id = $request->class_id;
			$user->school_college = $request->school_college;
			$user->state = $request->state;
			$user->city = $request->city;
			$user->address = $request->address;
			$user->postal_code = $request->postal_code;
			$user->parents_phone = $request->parents_phone;
			$user->franchise_user_id = "";//ak$franchiseUserId;
			$user->refer_code = $request->refer_code;
			//$user->status = 1;
			if (!empty($request->password)) {
				$user->password = bcrypt($request->password);
				$user->userpass = $request->password;
			}
			$user->save();
			
			//Add 7 days free trial to new user
			$userId = $user->id;
			$today	  = date('Y-m-d');
			$userSubscription = UserSubscription::where("user_id", $userId)->orderBy('id', 'DESC')->first();
			if (empty($userSubscription)) {
				/*//ak
				$franchise = Franchise::where("refer_code", $user->refer_code)->where("refer_code", "!=", "")->first();
				if (!empty($franchise)) {
					$date = strtotime($today);
					if ($franchise->refer_code == 'IVR4LDY5') {
						$date = strtotime("+3 days", $date);
					} else {
						$date = strtotime("+7 days", $date);
					}
					$end_date = date('Y-m-d', $date);
					$data1 = array(
						'user_id'			=> $userId,
						'subscription_id'	=> 1,
						'start_date'		=> $today,
						'end_date'			=> $end_date,
						'mode'				=> 'FREE',
						'paymentStatus'		=> 1,
						'created_at'		=> date('Y-m-d H:i:s'),
					);
					$inserId = UserSubscription::insertGetId($data1);
				}*/

				//send coupon code to franchise student
				$checkCoupon = CouponCode::where('user_id', $userId)->where('condition_1', 1)->first();
				/*//ak
				if (empty($checkCoupon)) {
					$franchise = Franchise::where("refer_code", $user->refer_code)->where("refer_code", "!=", "")->first();
					if (!empty($franchise)) {
						$coupon = $this->helper->getcouponcode();
						$date = strtotime($today);
						$date = strtotime("+1 month", $date);
						$coupon_end_date = date('Y-m-d', $date);
						$couponcode = new CouponCode();
						$couponcode->coupon = $coupon;
						$couponcode->discount = 25;
						$couponcode->end_date = $coupon_end_date;
						$couponcode->description = 'Franchise user auto generated coupon code.';
						$couponcode->condition_1 = 1;
						$couponcode->user_id = $userId;
						$couponcode->subscription_id = 0;
						$couponcode->no_of_users = 0;
						$couponcode->save();
						$couponcodeId = $couponcode->id;
						
					//	$this->helper->sendEmail($user->email, 'BrainyWood: Coupon Code Discount', $data = array('userName' => $user->name, 'message' => '<p>Thank you for connecting at BrainyWood,</p><p>You have got successfully your Coupon Code: ' . $coupon . '</p><p>This Coupon code discount valid till: ' . $coupon_end_date . '</p>'));
					}
				}
				*/
			}

			\Session::flash('msg', 'Added Successfully.');
			//return redirect()->route('admin.students');
			return redirect(base64_decode($request->filterUrl));
		} else {
			$users = $request->all();

			unset($users['roles']);
			//$user['role_id']
			
			$user = User::create($users);
			//	dd($role_id);
				DB::table('sub_admin_role_assign')->insert(['role_id'=>$role_id, 'user_id'=>$user->id]);
				
		//$this->helper->sendmailtoemp($subject,$message,$request->email);

		//
			
			$update = User::where("id",$user->id)->update(array('role_id'=>$role_id, 'userpass'=>$request->password, 'status'=>1 ));
			$user->assignRole($roles);
			if ($role_id==2) {
				$allow_courses = !empty($request->allow_courses) ? implode(",", $request->allow_courses) : NULL;
				$allow_groups = !empty($request->allow_groups) ? implode(",", $request->allow_groups) : NULL;
				$update1 = User::where("id",$user->id)->update(array('allow_courses'=>$allow_courses, 'allow_groups'=>$allow_groups));

				\Session::flash('msg', 'Added Successfully.');
				return redirect()->route('admin.teachers');
			}
		}

		return redirect()->route('admin.users.index');
	}


	/**
	 * Show the form for editing User.
	 *
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function edit($id)
	{

		if (! Gate::allows('users_manage')) {
			return abort(401);
		}
		$roles = Role::get()->pluck('name', 'name');

		$user = User::findOrFail($id);

		return view('admin.users.edit', compact('user','roles'));
	}
	public function teacherEdit($id)
	{
		if (! Gate::allows('users_manage')) {
			return abort(401);
		}
		$roles = Role::get()->pluck('name', 'name');

		$user = User::findOrFail($id);
	//	dd($user->toArray());
		$country_codes = User::select("country_code")->whereNotNull("country_code")->groupBy("country_code")->get();
		$studentClasses = StudentClass::where('status',1)->where('deleted',0)->orderBy('id', 'ASC')->get();
		$states = State::orderBy('state', 'ASC')->get();
		$cities = City::orderBy('city', 'ASC')->get();
		$courses = Courses::orderBy('sort_id', 'ASC')->get();
		$groups = ChatGroup::orderBy('sort_id', 'ASC')->get();
		$stateName = $user->state;
		if(!empty($stateName)){
			$userState = State::where('state', $stateName)->first();
			$stateId = $userState->id;
			$cities = City::where('state_id', $stateId)->orderBy('city', 'ASC')->get();
		}

		return view('admin.users.edit', compact('user','roles','country_codes','studentClasses','states','cities','courses','groups'));
	}
	public function studentEdit($id)
	{
		if (! Gate::allows('users_manage')) {
			return abort(401);
		}
		$roles = Role::get()->pluck('name', 'name');

		$user = User::findOrFail($id);

		$country_codes = User::select("country_code")->whereNotNull("country_code")->groupBy("country_code")->get();
		$studentClasses = StudentClass::where('status',1)->where('deleted',0)->orderBy('id', 'ASC')->get();
		$states = State::orderBy('state', 'ASC')->get();
		$cities = City::orderBy('city', 'ASC')->get();
		$stateName = $user->state;
		if(!empty($stateName)){
			$userState = State::where('state', $stateName)->first();
			$stateId = $userState->id;
			$cities = City::where('state_id', $stateId)->orderBy('city', 'ASC')->get();
		}

		return view('admin.users.edit', compact('user','roles','country_codes','studentClasses','states','cities'));
	}

	/**
	 * Update User in storage.
	 *
	 * @param  \App\Http\Requests\UpdateUsersRequest  $request
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function update(UpdateUsersRequest $request, $id)
	{
		// dd($request->all());
		//die;
		if (! Gate::allows('users_manage')) {
			return abort(401);
		}
		//echo '<pre />'; print_r($request->all()); die;
		$user = User::findOrFail($id);
		$roles = $request->input('roles') ? $request->input('roles') : [];

		//echo '<pre />'; print_r($roles[0]); die;
		$dob = ($request->dob) ? date('Y-m-d H:i:s',strtotime($request->get('dob'))) : NULL;
		$gender = ($request->gender) ? $request->gender : NULL;
		$class_id = ($request->class_id) ? $request->class_id : NULL;
		$school_college = ($request->school_college) ? $request->school_college : NULL;
		$state = ($request->state) ? $request->state : NULL;
		$city = ($request->city) ? $request->city : NULL;
		$address = ($request->address) ? $request->address : NULL;
		$postal_code = ($request->postal_code) ? $request->postal_code : NULL;
		if (isset($roles[0]) && $roles[0]=='Student') {
			if ($request->country_code == '91') {
				$validator = Validator::make($request->all(), [
					'phone' => 'required|digits:10',
					//'postal_code' => 'required|digits:6',
					//'parents_phone' => 'required|digits:10',
				]);
				if ($validator->fails()) {
					return back()->withErrors($validator)->withInput();
				}
			}
			$refer_code = $request->refer_code;
			$franchiseUserId = $user->franchise_user_id;
			/*//ak
			if (!empty($refer_code) && $refer_code!=$user->refer_code) {
				$franchise = Franchise::where("refer_code", $refer_code)->first();
				if (!empty($franchise)) {
					$franchiseUserId = $franchise->user_id;
					$gender = $request->gender;
					$name = $request->name;
					$email = $request->email;
					$phone = $request->phone;
					$studentClass = StudentClass::where("id", $class_id)->first();
					$class_name = !empty($studentClass) ? $studentClass->class_name : 'NA';
					if ($gender=='Female'){
						$user_gender = 202;
					} elseif ($gender=='Non Binary'){
						$user_gender = 203;
					} else {
						$user_gender = 201;
					}
					$city = $request->city;
					$state_name = $request->state;
					
					$ch = curl_init();
					$url = config('constant.FMSLEADAPIURL');
					curl_setopt($ch, CURLOPT_URL,$url);
					curl_setopt($ch, CURLOPT_POST, true);
					curl_setopt($ch, CURLOPT_POSTFIELDS, "user_id=$franchiseUserId&full_name=$name&email_address=$email&mobile_number=$phone&class_name=$class_name&user_gender=$user_gender&city_name=$city&state_name=$state_name");
					curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
					$output = curl_exec ($ch);

					curl_close ($ch);

					$response = json_decode($output);
					//echo '<pre />'; print_r($response); die; // Show output
					if ($response->Status==false) {
						\Session::flash('error', $response->Message);
						return redirect()->back();
					}
				} else {
					\Session::flash('msg', 'Refferal code not exists. Please try again!');
					return redirect()->back();
				}
			}
			*/
			$user->name = $request->name;
			$user->email = $request->email;
			$user->country_code = $request->country_code;
			$user->phone = $request->phone;
			$user->dob = $dob;
			$user->gender = $gender;
			$user->class_id = $class_id;
			$user->school_college = $school_college;
			$user->state = $state;
			$user->city = $city;
			$user->address = $address;
			$user->postal_code = $postal_code;
			$user->parents_phone = $request->parents_phone;
			$user->franchise_user_id =""; //ak$franchiseUserId;
			$user->refer_code = $request->refer_code;
			if (!empty($request->password)) {
				$user->password = bcrypt($request->password);
				$user->userpass = $request->password;
			}
			$user->save();

			\Session::flash('msg', 'Updated Successfully.');
			//return redirect()->route('admin.students');
			return redirect(base64_decode($request->filterUrl));
		} else {
			$user->update($request->all());
			$update = User::where("id",$user->id)->update(array('userpass'=>$request->password));
			$roles = $request->input('roles') ? $request->input('roles') : [];
			$user->syncRoles($roles);
			if (isset($roles[0]) && $roles[0]=='Teacher') {
				$allow_courses = !empty($request->allow_courses) ? implode(",", $request->allow_courses) : [];
				$allow_groups = !empty($request->allow_groups) ? implode(",", $request->allow_groups) : [];
				$update1 = User::where("id",$user->id)->update(array('dob' => $dob, 'gender' => $gender, 'class_id' => $class_id, 'school_college' => $school_college, 'state' => $state, 'city' => $city, 'address' => $address, 'postal_code' => $postal_code, 'allow_courses' => $allow_courses, 'allow_groups' => $allow_groups ));

				\Session::flash('msg', 'Updated Successfully.');
				return redirect()->route('admin.teachers');
			} else {
				\Session::flash('msg', 'Updated Successfully.');
				return redirect()->route('admin.users.index');
			}
		}
	}

	/**
	 * Remove User from storage.
	 *
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function destroy($id)
	{
		if (! Gate::allows('users_manage')) {
			return abort(401);
		}
		$user = User::findOrFail($id);
		$user->deleted=1;
		$user->update();

		//return redirect()->route('admin.users.index');
		return redirect()->back();
	}

	/**
	 * Delete all selected User at once.
	 *
	 * @param Request $request
	 */
	public function massDestroy(Request $request)
	{
		if (! Gate::allows('users_manage')) {
			return abort(401);
		}
		if ($request->input('ids')) {
			$entries = User::whereIn('id', $request->input('ids'))->get();
			dd($entries);
			foreach ($entries as $entry) {
				//$entry->delete();
			}
		}
	}


	public function show($id){
		if (! Gate::allows('users_manage')) {
			return abort(401);
		}
		$roles = Role::get()->pluck('name', 'name');

		$user = User::findOrFail($id);

		return view('admin.users.view', compact('user', 'roles'));
	}

	public function updateStatus($id,$status)
	{
		$subject = "Account Activated";
		// $url = "https://guru.appicsoftwares.in/teacher/login";
		// $message = "Your account is activeted <br>".$url;
		
		$user = User::findOrFail($id);
		  // dd($user);
		$message = "<!doctypehtml><style>body,html{margin:0;padding:0;border:0}body{font-family:Arial,sans-serif;background-color:#f0f0f0}.container{max-width:600px;margin:0 auto;padding:20px;background-color:#fff;border-radius:5px;box-shadow:0 2px 5px rgba(0,0,0,.1)}.header{background-color:#007bff;color:#fff;padding:20px;border-top-left-radius:5px;border-top-right-radius:5px}.logo{text-align:center;padding-bottom:20px;padding-top:20px}.logo img{max-width:200px}.content{padding:20px;color:#333}h2{color:#007bff}p{font-size:16px;line-height:1.5}.button{display:inline-block;padding:10px 20px;background-color:#007bff;color:#fff;text-decoration:none;border-radius:5px}.button:hover{background-color:#0056b3}</style><div class=container><div class=logo><img alt='Guru At Home Logo'src=https://guru.appicsoftwares.in/static/media/logo.ee3dc851ce5164c5bcd3.png></div><div class=content><h2>Hi ".$user->name.", Your account is activated now.<br></h2><p>Please login through following link - <a class=button href=https://guru.appicsoftwares.in/teacher/login>Login</a></div></div>";
		// dd($user);
		if ($status=== 1 && $user->role_id === 2) {
			$userId = $id;
			//Add 7 days free trial to new user
			$today	  = date('Y-m-d');
			// $userSubscription = UserSubscription::where("user_id", $userId)->orderBy('id', 'DESC')->first();
			
		}
		$country = $user->dial_code;
		$template_name = "tutor_account_approved";
		$body_values = [$user->name];
		$user->status=$status;
		$user->update();
		if($status == 1){	
		   $this->helper->send_whatsapp_message($country,$user->phone,$template_name,$body_values);
		   $this->helper->sendmailtoemp($user->email, $subject, $message);
		}
		return redirect()->back();
	}

	public function deletedUsers()
	{
		if (! Gate::allows('users_manage')) {
			return abort(401);
		}

		$users = User::where("deleted",1)->get();

		return view('admin.users.deleted', compact('users'));
	}

	public function restoreUsers($id)
	{
		$user = User::findOrFail($id);
		$user->deleted=0;
		$user->update();

		\Session::flash('msg', 'User Re-stored Successfully.');
		return redirect()->back();
	}

	public function getCitiesByStateName(Request $request)
	{
		$state = $request->state;
		$stateData = State::where('state', $state)->first();
		$stateId = $stateData->id;
		$cities = City::where('state_id', $stateId)->orderBy('city', 'ASC')->get();
		$output = '<option value="">Select City</option>';
		foreach ($cities as $key => $value) {
			$output .= '<option value="'.$value->city.'">'.$value->city.'</option>';
		}
		echo $output;
	}

	public function studentAddTrial($id)
	{
		if (! Gate::allows('users_manage')) {
			return abort(401);
		}
		//$roles = Role::get()->pluck('name', 'name');

		$user = User::findOrFail($id);

		$today = date('Y-m-d');
		$date = strtotime($today);
		$date = strtotime("+7 days", $date);
		$end_date = date('Y-m-d', $date);

		return view('admin.users.trial', compact('user','end_date'));
	}
	public function studentTrialAdd(Request $request, $userId)
	{
		//dd($request->all());
		//Add free trial to the user
		$today	  = date('Y-m-d');
		$date = strtotime($today);
		$date = strtotime("+7 days", $date);
		$end_date = date('Y-m-d', $date);
		$end_date = ($request->end_date > $today) ? $request->end_date : $end_date;
		$userSubscription = UserSubscription::where("user_id", $userId)->orderBy('id', 'DESC')->first();
		if (empty($userSubscription)) {
			$data = array(
				'user_id'			=> $userId,
				'subscription_id'	=> 1,
				'start_date'		=> $today,
				'end_date'			=> $end_date,
				'mode'				=> 'FREE',
				'paymentStatus'		=> 1,
				'created_at'		=> date('Y-m-d H:i:s'),
			);
			$inserId = UserSubscription::insertGetId($data);
		} else {
			$data1 = array(
				'end_date'			=> $end_date,
				'mode'				=> 'FREE',
				'paymentStatus'		=> 1,
				'updated_at'		=> date('Y-m-d H:i:s'),
			);
			$update = UserSubscription::where("id", $userSubscription->id)->update($data1);
		}
		\Session::flash('msg', 'Subscription Package Added Successfully.');
		return redirect()->route('admin.students');
	}
	public function addTrial(Request $request)
	{
		if (empty($request->user_ids)) {
			return redirect()->back();
		}
		//dd($request->all());
		//Add free trial to the user
		$today	= date('Y-m-d');
		$date = strtotime($today);
		$date = strtotime("+7 days", $date);
		$end_date = date('Y-m-d', $date);
		$end_date = ($request->end_date > $today) ? $request->end_date : $end_date;
		$user_ids = !empty($request->user_ids) ? explode(",", $request->user_ids) : [];
		if (!empty($user_ids)) {
			foreach ($user_ids as $userId) {
				$userSubscription = UserSubscription::where("user_id", $userId)->where("end_date", ">=", $today)->where("paymentStatus", 1)->orderBy('id', 'DESC')->first();
				if (empty($userSubscription)) {
					$data = array(
						'user_id'			=> $userId,
						'subscription_id'	=> 1,
						'start_date'		=> $today,
						'end_date'			=> $end_date,
						'mode'				=> 'FREE',
						'paymentStatus'		=> 1,
						'created_at'		=> date('Y-m-d H:i:s'),
					);
					$inserId = UserSubscription::insertGetId($data);
				} else {
					/*$data1 = array(
						'end_date'			=> $end_date,
						'mode'				=> 'FREE',
						'paymentStatus'		=> 1,
						'updated_at'		=> date('Y-m-d H:i:s'),
					);
					$update = UserSubscription::where("id", $userSubscription->id)->update($data1);*/
				}
			}
			\Session::flash('msg', 'Subscription Package Added Successfully.');
		}
		//return redirect()->route('admin.students');
		return redirect(base64_decode($request->filterUrl));
	}

	public function user_list(Request $request)
	{
		$record_list  = User::Where('name', 'LIKE', "%".$request->keywords."%")
						->OrWhere('phone', 'LIKE', "%".$request->keywords."%")
						->OrWhere('email', 'LIKE', "%".$request->keywords."%")
						->where("role_id", 3)->OrderBy('name','ASC')->get();
		return response()->json($record_list);
	}

	public function teacher_list(Request $request)
	{
		$record_list  = User::Where('name', 'LIKE', "%".$request->keywords."%")
						->OrWhere('phone', 'LIKE', "%".$request->keywords."%")
						->OrWhere('email', 'LIKE', "%".$request->keywords."%")
						->where("role_id", 2)->OrderBy('name','ASC')->get();
		return response()->json($record_list);
	}

	public function studentPoints($id)
	{
		if (! Gate::allows('users_manage')) {
			return abort(401);
		}
		$user = User::findOrFail($id);
		if (empty($user)) {
			return redirect()->route('admin.students');
		}
		if (!empty($user->role_id != 3)) {
			return redirect()->route('admin.students');
		}
		$acheived = $user->user_points_credit->sum('points');
		$redeemed = $user->user_points_debit->sum('points');
		$acheived_data = UserPoint::where("user_id",$id)->where("transaction_type",502)->orderBy("id", "ASC")->get();
		$redeemed_data = UserPoint::where("user_id",$id)->where("transaction_type",501)->orderBy("id", "ASC")->get();

		return view('admin.users.student_points', compact('user','acheived','redeemed','acheived_data','redeemed_data'));
	}

	public function getUsersCsv(Request $request)
	{
		$user_id = ($request->user_id) ? $request->user_id : '';
		$country_code = ($request->country_code) ? $request->country_code : '';
		$gender = ($request->gender) ? $request->gender : '';
		$school = ($request->school) ? $request->school : '';
		$class = ($request->class_id) ? $request->class_id : '';
		$state = ($request->state) ? $request->state : '';
		$city = ($request->city) ? $request->city : '';
		$plan_id = ($request->plan_id) ? $request->plan_id : '';
		$postal_code = ($request->postal_code) ? $request->postal_code : '';
		$refer_code = ($request->refer_code) ? $request->refer_code : '';
		$from = ($request->from) ? $request->from : '';
		$to = ($request->to) ? $request->to.' 23:59:59' : date('Y-m-d H:i:s');
		$subscription = ($request->subscription) ? $request->subscription : '';

		$users = User::orderBy("id", "DESC")
			 ->Where(function($query) use ($user_id) {
				if (isset($user_id) && !empty($user_id)) {
					$query->where("id",$user_id);
				}
			 })
			 ->Where(function($query) use ($country_code) {
				if (isset($country_code) && !empty($country_code)) {
					$query->where("country_code",$country_code);
				}
			 })
			 ->Where(function($query) use ($gender) {
				if (isset($gender) && !empty($gender)) {
					$query->where("gender",$gender);
				}
			 })
			 ->Where(function($query) use ($school) {
				if (isset($school) && !empty($school)) {
					$query->where("school_college",$school);
				}
			 })
			 ->Where(function($query) use ($class) {
				if (isset($class) && !empty($class)) {
					$query->where("class_id",$class);
				}
			 })
			 ->Where(function($query) use ($state) {
				if (isset($state) && !empty($state)) {
					$query->where("state",$state);
				}
			 })
			 ->Where(function($query) use ($city) {
				if (isset($city) && !empty($city)) {
					$query->where("city",$city);
				}
			 })
			 ->Where(function($query) use ($postal_code) {
				if (isset($postal_code) && !empty($postal_code)) {
					$query->where("postal_code",$postal_code);
				}
			 })
			 ->Where(function($query) use ($refer_code) {
				if (isset($refer_code) && !empty($refer_code)) {
					$query->where("refer_code",$refer_code);
				}
			 })
			 ->Where(function($query) use ($from, $to) {
				if (isset($from) && !empty($from)) {
					$query->whereBetween("created_at",[$from, $to]);
				}
			 })
			 ->where("role_id",3)->where("deleted",0);
		
		if (isset($plan_id) && !empty($plan_id)) {	 
			$users->whereHas('subscription_data', function($query) use ($plan_id) {
				if (isset($plan_id) && !empty($plan_id)) {
					$query->where("subscription_id",$plan_id);
				}
			});
		}
		if (isset($subscription) && !empty($subscription)) {	 
			if ($subscription=='paid') {
				$users->whereHas('subscription_data', function($query) use ($subscription) {
					$query->whereDate("end_date", ">=", date('Y-m-d'))->where("mode", "!=", "FREE");
				});
			/*} elseif ($subscription=='unpaid') {
				$users->whereHas('subscription_data', function($query) use ($subscription) {
					if (isset($subscription) && !empty($subscription)) {
						$query->whereDate("end_date", "<", date('Y-m-d'));
					}
				});*/
			} elseif ($subscription=='trial') {
				$users->whereHas('subscription_data', function($query) use ($subscription) {
					$query->whereDate("end_date", ">=", date('Y-m-d'))->where("mode", "FREE");
				});
			}
		}
		//dd($users->count());

		$headers = array(
			'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
			'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
			'Content-Disposition' => 'attachment; filename=abc.csv',
			'Expires' => '0',
			'Pragma' => 'public',
		);

		$filename = "users_".date('d-M-Y').".csv";
		$handle = fopen($filename, 'w');
		fputcsv($handle, [
			"#",
			"UserName - Phone",
			"Email",
			"Gender",
			"School/College",
			"Class",
			"State",
			"City",
			"Postal Code",
			"Referred By",
			"Subscription PlanName",
		]);

		$i = 1;
		$users->chunk(100000, function ($data) use ($handle,$i) {
			foreach ($data as $row) {
				// Add a new row with data
				fputcsv($handle, [
					$i,
					$row->name.' - '.$row->phone,
					$row->email,
					$row->gender,
					$row->school_college,
					($row->class_id>0) ? $row->class_data->class_name : '',
					$row->state,
					$row->city,
					$row->postal_code,
					(!empty($row->franchise_data) && $row->franchise_data->franchise_user_id>0) ? $row->franchise_data->user_name.' - '.$row->refer_code : $row->refer_code,
					(!empty($row->subscription_data) && $row->subscription_data->subscription_id>0) ? $row->subscription_data->subscription->name : 'NA',
				]);
				$i++;
			}
		});

		fclose($handle);

		return response()->download($filename, "users_".date('d-M-Y').".csv", $headers);
	}

	public function teachers_request()
	{
	    $data = User::where(['role_id'=>2,'request_status'=>0]);
        $totalData = $data->count();
        $data = $data->paginate(10);
		return view('admin.teacher.teacher_request',compact('data','totalData'));
	}

	public function teacher_details(Request $request){
		if($request->all())
		{
		 	$class_id  = isset($request->class_id)? $request->class_id :'';
		 	$category_id = isset($request->category_id) ? $request->category_id : '';
		 	$subject_id = isset($request->subject_id) ? $request->subject_id : '';
		 	$sub_category_id = isset($request->sub_category_id) ? $request->sub_category_id : '';

		 	$Profile_details =	Profile_details::select('user_id')->orderBy("id", "DESC")
			 ->Where(function($query) use ($class_id) {
				if (isset($class_id) && !empty($class_id)) {
					$query->where("select_class",$class_id);
				}
			 })
			 ->Where(function($query) use ($category_id) {
				if (isset($category_id) && !empty($category_id)) {
					$query->where("select_category",$category_id);
				}
			 })
			 ->Where(function($query) use ($subject_id) {
				if (isset($subject_id) && !empty($subject_id)) {
					$query->where("select_subject",$subject_id);
				}
			 })
			 ->Where(function($query) use ($sub_category_id) {
				if (isset($sub_category_id) && !empty($sub_category_id)) {
					$query->where("select_sub_category",$sub_category_id);
				}
			 })->get();
			 $user_id = array();
			 if(!empty($Profile_details))
			 {
			 	foreach ($Profile_details as $key => $value) {
			 		array_push($user_id,$value->user_id);
			 	}
			 }
			 $data = User::where(['role_id'=>2,'request_status'=>1])->whereIn("id",$user_id);
			 $totalData = $data->count();
			$data = $data->paginate(10);
			
		}
		else
		{
			$data = User::where(['role_id'=>2,'request_status'=>1]);
			$totalData = $data->count();
			$data = $data->paginate(10);
		}
		
		$classes = StudentClass::where('status',1)->where('deleted',0)->orderBy('id', 'ASC')->get();
		$category = Category::where('status',1)->where('deleted',0)->orderBy('id', 'ASC')->get();
		$subject = Subject::where('status',1)->orderBy("id", "DESC")->get();
		$subCategory = SubCategory::where('status',1)->orderBy("id","DESC")->get();


		return view('admin.teacher.teacher_details',compact('data','totalData','classes','category','subject','subCategory'));
		
		
	}
	public function teacher_request_status(Request $request)
	{
		if($request->all())
		{
			$data = User::where(['id'=>$request->id])->update(['request_status'=>$request->request_status]);
			
			$emailCheck = User::select("email")->where("id",$request->id)->first();


			if($request->request_status==0)
			{
			//	$result = array("status"=>true,"message" =>"Teacher Request Is Pending");
				$status=200;
				$message="Teacher Request Is Pending";

			}
			else if($request->request_status==1)
			{
				//$result = array("status"=>true,"message" =>"Teacher Request Is Approved");
				$status=200;
				$message="Teacher Request Is Approved";
				$subject=" Your Requests ";
				if(!empty($emailCheck->email))
				{
					$this->helper->sendMail($emailCheck->email, $subject, $message);
				}
				

			}
			else if($request->request_status==2)
			{
				//$result = array("status"=>true,"message" =>"Teacher Request Is Rejected");
				$status=200;
				$message="Teacher Request Is Rejected";
			}
		}
		else
		{
			$status=400;
			$message="Something want wrong";

		}
		return response()->json(['statusCode' => $status, 'message' => $message]);
		
	}

	public function teacher_view_details($id){


		if($id!=0)
		{
			$data =User::where(['id'=>$id,"role_id"=>2]); //Profile_details::where(['user_id'=>$id,"user_role_id"=>2]);
			$totalData = $data->count();
			$data = $data->paginate(10);
		
			foreach($data as $pro)
			{
				$pro->dob = date('d M Y', strtotime($pro->dob));
//				$get_student_classes = StudentClass::select('class_name')->where("id",$pro->select_class)->first();
				$get_student_classes = StudentClass::select('class_name')->where("id",$pro->class_id)->first();

				$pro->class_name = isset($get_student_classes->class_name) ? $get_student_classes->class_name: '';

				//Category 
				$get_category = Category::select('name')->where("id",$pro->select_category)->first();
				$pro->category_name = isset($get_category->name) ? $get_category->name: '';

				//Sub Category 
				$get_sub_category = SubCategory::select('name')->where("id",$pro->select_sub_category)->first();
				$pro->sub_category_name = isset($get_sub_category->name) ? $get_sub_category->name: '';

				//Subject 
				$get_subject = Subject::select('title')->where('sub_category_id',$pro->select_sub_category)->where('category_id',$pro->select_category)->where("id",$pro->select_subject)->first();
				$pro->subject_name = isset($get_subject->title) ? $get_subject->title: '';
			}

			return view('admin.teacher.teacher_view_details',compact('data','totalData'));

		}
	}

	public function teacher_availabilty_of_slots(){
		if(Auth::user()->role_id==2)
		{
			$data =Auth::user();
			return view('admin.teacher.teacher_availabilty_of_slots',compact('data'));
		}

	}
	public function teacher_availabilty_of_slots_save(Request $request){
		if($request->all())
		{
			$data =[];
			$day = $request->day;
			$start_date = $request->start_date;
			$end_date = $request->end_date;
		
			if(!empty($day))
			{
				$ar = array();
				foreach($day as $k=> $d)
				{
					$TeacherAvailabiltyOfSlots = new TeacherAvailabiltyOfSlots();
					$TeacherAvailabiltyOfSlots->role_id =  $request->role_id;
					$TeacherAvailabiltyOfSlots->user_id =  $request->user_id;
					$TeacherAvailabiltyOfSlots->day =  $d;
					$TeacherAvailabiltyOfSlots->save();
					$inserted_Id = $TeacherAvailabiltyOfSlots->id;
						foreach ($start_date[$k] as $key => $value) {
							$endtime= isset($end_date[$k][$key])? $end_date[$k][$key] : '';
							$TeacherAvailabiltyOfSlotsDetails = new TeacherAvailabiltyOfSlotsDetails();
							$TeacherAvailabiltyOfSlotsDetails->teacher_availabilty_of_slots_id=$inserted_Id;
							$TeacherAvailabiltyOfSlotsDetails->start_time=isset($value) ? $value : '';
							$TeacherAvailabiltyOfSlotsDetails->end_time=$endtime;
							$TeacherAvailabiltyOfSlotsDetails->save();
					}
				}
			}
			return redirect('/admin/teacher_availabilty_of_slots_details');

		}
	
	}

	public function teacher_availabilty_of_slots_details(){
		if(!empty(Auth::user()->id))
		{
			$user_id = Auth::user()->id;
			$teacher_availabilty_of_slots_details = TeacherAvailabiltyOfSlots::select('id','user_id','role_id','day')->where("user_id",$user_id)->get();
			if(!empty($teacher_availabilty_of_slots_details))
			{
				foreach($teacher_availabilty_of_slots_details as $k)
				{
					$k->TeacherAvailabiltyOfSlotsDetails = $k->TeacherAvailabiltyOfSlotsDetailsData;
				
				}
			}
		}
		return view('admin.teacher.teacher_availabilty_of_slots_details',compact('teacher_availabilty_of_slots_details'));
	}

// 	public function teacher_view_availabilty_slot_details($id){
// 		if(!empty($id)){
// 			$user_id = $id;
// 			$teacher_availabilty_of_slots_details = TeacherAvailabiltyOfSlotsDetails::where("teacher_id",$user_id)->where('delete_time_status',0)->get();
// 			if(!empty($teacher_availabilty_of_slots_details)){
// 				foreach($teacher_availabilty_of_slots_details as $key=> $k){
// 			$teacher_slots_details = TeacherAvailabiltyOfSlots::select('day')->where("user_id",$user_id)->where('id',$k->teacher_availabilty_of_slots_id)->first();
// 			$k->slots_details = $teacher_slots_details;
// 					    // $k->TeacherAvailabiltyOfSlotsDetails = $k->TeacherAvailabiltyOfSlotsDetailsData;
// 					    // dd($k->TeacherAvailabiltyOfSlotsDetails[$key]['utc_select_time']);
// 					    // $utc = isset($k->TeacherAvailabiltyOfSlotsDetails[$key]['utc_select_time']) ? $k->TeacherAvailabiltyOfSlotsDetails[$key]['utc_select_time']:"";
// 					    // $utc1 = isset($k->TeacherAvailabiltyOfSlotsDetails[$key]['end_time']) ? $k->TeacherAvailabiltyOfSlotsDetails[$key]['end_time']:"";
// 				$utc = isset($k->utc_select_time) ? $k->utc_select_time:"";
// 				$utc1 = isset($k->utc_select_end_time) ? $k->utc_select_end_time:"";
// 			// echo "<pre>";print_r($utc);
// 	        // 			$dt = new DateTime($utc);
// 	        // 			// echo "<pre>";print_r($d);
// 					    // $tz = new DateTimeZone('Asia/Kolkata'); // or whatever zone you're after
//          //                $ist= $dt->setTimezone($tz);
//          //                $utctoist = $ist->format('Y-m-d H:i:s');
//          //                $IST = isset($k->utc_select_time) ? date('H:i:s', strtotime($utctoist)):"";
//          //                 dd($tz);

//          //                $dt1 = new DateTime($utc1);
// 					    // $tz1 = new DateTimeZone('Asia/Kolkata'); // or whatever zone you're after
//          //                $ist1= $dt1->setTimezone($tz1);
//          //                $utctoist1 = $ist1->format('Y-m-d H:i:s');
//          //                $IST1 = isset($k->utc_select_end_time) ? date('H:i:s', strtotime($utctoist1)):"";
//          //                $k->start_IST = isset($IST) ? $IST:"";
//          //                $k->end_IST = isset($IST1) ? $IST1 :"";

// 				if($utc !=""){
// 					date_default_timezone_set('UTC');
// 		             $LocalTime_start_time = new DateTime($utc);
// 		            $tz_start = new DateTimeZone('Asia/Kolkata' );
// 		            $LocalTime_start_time->setTimezone( $tz_start );
// 		            $start_date_time = (array) $LocalTime_start_time;
// 		            $StartDateTime = $start_date_time['date'];
// 		            $time_ist = isset($StartDateTime)?date('H:i:s', strtotime($StartDateTime)):"";
//              // dd($start_date_time['date']);
// 				}else{
// 					$time_ist = "";
// 				}
			
//             $k->start_IST = $time_ist;

//             if($utc1 !=""){
//             	date_default_timezone_set('UTC');
// 	            $LocalTime_start_time1 = new DateTime($utc1);
// 	            $tz_start1 = new DateTimeZone('Asia/Kolkata' );
// 	            $LocalTime_start_time1->setTimezone( $tz_start1 );
// 	            $start_date_time1= (array) $LocalTime_start_time1;
// 	            $StartDateTime1 = $start_date_time1['date'];
// 	            $time_ist1 = isset($StartDateTime1)?date('H:i:s', strtotime($StartDateTime1)):"";
// 	            // dd($select_time_ist);
	            
//             }else{
// 					$time_ist1="";
//             }
//             	$k->end_IST = $time_ist1;
// 			}
// 		 // dd($teacher_availabilty_of_slots_details);
// 		}
		

	
// 		return view('admin.teacher.teacher_view_availabilty_slot_details',compact('teacher_availabilty_of_slots_details'));

// 	}
// }



	public function teacher_view_availabilty_slot_details($id){
	  if(!empty($id)){
		 $user_id = $id;
		 $teacher_availabilty_of_slots_details = Timemanage::where("teacher_id",$user_id)->where('delete_time_status',0)->get();
		 if(!empty($teacher_availabilty_of_slots_details)){
			foreach($teacher_availabilty_of_slots_details as $key=> $k){
			$teacher_slots_details = TeacherAvailabiltyOfSlots::select('day')->where("user_id",$user_id)->where('id',$k->teacher_availabilty_of_slots_id)->first();
			// 2023-03-22T07:05:00.000Z
			// dd($teacher_slots_details);
			$k->slots_details = $teacher_slots_details;
			$utc_date = isset($k->date) ? $k->date:"";
			$utc='';
			if($k->start_time!=null){
				$utc = $utc_date."T".$k->start_time.".000Z";
				 // dd($utc);
			}
			// $utc = $utc_date."T".$k->start_time."000Z";//isset($k->start_time) ? $k->start_time:"";
			$utc1 = isset($k->end_time) ? $k->end_time:"";
			// dd($utc);
			if($utc !=""){
			   date_default_timezone_set('UTC');
		       $LocalTime_start_time = new DateTime($utc);
				// dd($LocalTime_start_time);
		       $tz_start = new DateTimeZone('Asia/Kolkata' );
               $LocalTime_start_time->setTimezone( $tz_start );
               $start_date_time = (array) $LocalTime_start_time;
               $StartDateTime = $start_date_time['date'];
               // dd($StartDateTime);
               $time_ist = isset($StartDateTime)?date('Y-m-d H:i:s', strtotime($StartDateTime)):"";
               $timestamp= date("Y-m-d", strtotime($time_ist));
               $time= date("H:i:s", strtotime($time_ist));
               // $timestamp = strtotime($utc_date);
                // dd($time_ist);
               $day = date('D', strtotime($timestamp));
               // dd($day);
				}else{
					$time = "";
				}

			$k->days = isset($day) ? $day:'';  
            $k->start_IST = $time;

            if($utc1 !=""){
            	date_default_timezone_set('UTC');
	            $LocalTime_start_time1 = new DateTime($utc1);
	            $tz_start1 = new DateTimeZone('Asia/Kolkata' );
	            $LocalTime_start_time1->setTimezone( $tz_start1 );
	            $start_date_time1= (array) $LocalTime_start_time1;
	            $StartDateTime1 = $start_date_time1['date'];
	            $time_ist1 = isset($StartDateTime1)?date('H:i:s', strtotime($StartDateTime1)):"";
	              // dd($LocalTime_start_time1);

            }else{
					$time_ist1="";
            }
            	$k->end_IST = $time_ist1;
			}
		     // dd($teacher_availabilty_of_slots_details);
		}
		return view('admin.teacher.teacher_view_availabilty_slot_details',compact('teacher_availabilty_of_slots_details','user_id'));

	}
}
	

  public function teacher_report($id){
  	// dd($request->all());
  	// $data = StudentApplyForDemo::join('enrolls', 'enrolls.student_apply_for_demo_id', '=', 'studentapplyfordemo.id')
   //                               ->join('student_enroll_schedule', 'student_enroll_schedule.enroll_id', '=', 'enrolls.id')
   //                               ->join('payments', 'payments.enroll_id', '=', 'enrolls.id') // Corrected table name
   //                               ->select('student_enroll_schedule.enroll_class_end_status', 'studentapplyfordemo.start_price_range', 'student_enroll_schedule.class_start_time', 'student_enroll_schedule.class_end_date', 'student_enroll_schedule.start_time', 'student_enroll_schedule.end_time', 'enrolls.*', 'payments.withdrowral_status', 'student_enroll_schedule.select_date', 'student_enroll_schedule.class_end_time', 'student_enroll_schedule.class_start_date','student_enroll_schedule.per_class_payment_status')
   //                               ->where('student_enroll_schedule.teacher_id', $id)
   //                               ->where('payments.payment_status', 'Successfully');

    $data = Enroll::join('student_enroll_schedule','student_enroll_schedule.enroll_id','=','enrolls.id')
    				->join('payments', 'payments.enroll_id', '=', 'enrolls.id')
    				->select('student_enroll_schedule.enroll_class_end_status', 'student_enroll_schedule.class_start_time', 'student_enroll_schedule.class_end_date', 'student_enroll_schedule.start_time', 'student_enroll_schedule.end_time', 'enrolls.*', 'payments.withdrowral_status', 'student_enroll_schedule.select_date', 'student_enroll_schedule.class_end_time', 'student_enroll_schedule.class_start_date','student_enroll_schedule.per_class_payment_status')
    				->where('student_enroll_schedule.teacher_id', $id)
                    ->where('payments.payment_status', 'Successfully');
  								    // ->Where('student_enroll_schedule.enroll_class_end_status',1);
  								    $totalData = $data->count();
        $data = $data->paginate(10);
    // dd($data);die;
        foreach($data as $da){
        	if($da->start_time=='hh-mm'){
        		$da->start_time = "0:0";
        	}
        	if($da->end_time=='hh-mm'){
        		$da->end_time = "0:0";
        	}
        	$utc_start_time = $da->start_time;
        	$utc_end_time = $da->end_time;
        	$class_stime = $da->class_start_time;
        	$class_etime = $da->class_end_time;

			date_default_timezone_set('UTC');
            $LocalTime_start_time = new DateTime($utc_start_time);
            $tz_start = new DateTimeZone('Asia/Kolkata' );
            $LocalTime_start_time->setTimezone( $tz_start );
            $start_date_time = (array) $LocalTime_start_time;
            $StartDateTime = $start_date_time['date'];
            $time_ist = date('g:i a', strtotime($StartDateTime));
            // dd($select_time_ist);
            $da->select_time_ist = $time_ist;

            $LocalTime_start_time1 = new DateTime($utc_end_time);
            $tz_start1 = new DateTimeZone('Asia/Kolkata' );
            $LocalTime_start_time1->setTimezone( $tz_start1 );
            $start_date_time1 = (array) $LocalTime_start_time1;
            $StartDateTime1 = $start_date_time1['date'];
            $time_ist1 = date('g:i a', strtotime($StartDateTime1));
            $da->select_end_time_ist = $time_ist1;

            // if($class_stime!=null){
            //    $LocalTime_start_time3 = new DateTime($class_stime);
            //    $tz_start3 = new DateTimeZone('Asia/Kolkata' );
            //    $LocalTime_start_time3->setTimezone( $tz_start3);
            //    $start_date_time3 = (array) $LocalTime_start_time3;
            //    $StartDateTime3 = $start_date_time3['date'];
            //    $time_ist3 = date('h:i:s a', strtotime($StartDateTime3));
            //    $da->stime = $time_ist3;
            // }else{
            // 	$time_ist3 = "";
            // }
           
            // if($class_etime!=null){
            //    $LocalTime_start_time4 = new DateTime($class_etime);
            //    $tz_start4 = new DateTimeZone('Asia/Kolkata' );
            //    $LocalTime_start_time4->setTimezone( $tz_start4);
            //    $start_date_time4 = (array) $LocalTime_start_time4;
            //    $StartDateTime4 = $start_date_time4['date'];
            //    $time_ist4 = date('h:i:s a', strtotime($StartDateTime4));
            //    $da->etime = $time_ist4;
            // }else{
            // 	$time_ist4 ="";
            // }
           
            // print_r($da->etime);
            $user = User::where('id',$da->student_id)->first();
            $cry = TeachingDetails::where('user_id',$da->teacher_id)->first();
            $da->student_name = $user->name;
            $da->currency = $cry->currency_symbol;
            $da->endtimeist = date('g:i a',strtotime($da->class_end_time));
            $da->starttimeist = date('g:i a',strtotime($da->class_start_time));
            $da->startdate = date('m/d/Y',strtotime($da->class_start_date));
                 

        }
  						   // dd($data);die;
  	return view('admin.teacher.teacher_report',compact('data','id'));
  }

  public function fees_update($id){
  	$fees = TeachingDetails::where('id',$id)->first();
  	return view('admin.teacher.fees_update',compact('fees'));
  }
  public function update_fees(Request $request){
  	$update = TeachingDetails::find($request->fees_id);
  	$update->fees = $request->fees;
  	$update->save();
  	 \Session::flash('msg', 'Teaching Fee updated.');
            return back();
  }

  public function fees_index(){
  	$data = TeachingDetails::orderBy('id', 'desc');
  	$totalData = $data->count();
    $data = $data->paginate(10);
    // dd($data);die;
    $class_name = '';
    $subject_name='';
    foreach ($data as $value) {
    	// dd($value->class_or_category_id);
    	$user = User::where('id',$value->user_id)->first();
    	$value->teacher_name = isset($user->name) ? $user->name:'';
    	if($value->type == 1){
    		$class_name = StudentClass::where('id',$value->class_or_category_id)->first();
    		$subject_name = Subject::where('id',$value->subject_or_course_id)->first();
    		$value->c_name = isset($class_name->class_name) ? $class_name->class_name:'';
    		$value->s_name = isset($subject_name->title) ? $subject_name->title:'';
    	}else{
    	  $class_name = Category::where('id',$value->class_or_category_id)->first();
    	  $subject_name = Courses::where('id',$value->subject_or_course_id)->first();
    	  $value->c_name = isset($class_name->name) ? $class_name->name:'';
    	  $value->s_name = isset($subject_name->name) ? $subject_name->name:'';
    	}
    }
    // dd($data);
  	return view('admin.teacher.fees_index',compact('data'));
  }


  public function teacher_report_csv(Request $request){
  	// dd($request->all());
  	$teacherId = $request->teacher_id;
    $totalData = 20;
    $fromDate = $request->start_date;
    $toDate = $request->end_date;
    // $orderArray = StudentApplyForDemo::join('enrolls','enrolls.student_apply_for_demo_id','=','studentapplyfordemo.id')
  		// 							->join('student_enroll_schedule','student_enroll_schedule.enroll_id','=','enrolls.id')
  		// 							->join('payments','payments.enroll_id','=','enrolls.id')
  		// 						    ->select('student_enroll_schedule.enroll_class_end_status','studentapplyfordemo.start_price_range','student_enroll_schedule.class_start_time','student_enroll_schedule.class_end_date','student_enroll_schedule.start_time','student_enroll_schedule.end_time','payments.withdrowral_status','enrolls.fees','student_enroll_schedule.select_date','student_enroll_schedule.class_start_date','student_enroll_schedule.class_end_time','enrolls.student_id','student_enroll_schedule.per_class_payment_status')
  		// 						    ->where('payments.payment_status','Successfully')
  		// 						    ->where('enrolls.teacher_id',$teacherId)
  		// 						    ->whereBetween('student_enroll_schedule.select_date',array($fromDate,$toDate))->get();
  								     // dd($orderArray);
  								    $orderArray = Enroll::join('student_enroll_schedule','student_enroll_schedule.enroll_id','=','enrolls.id')
  									->join('payments','payments.enroll_id','=','enrolls.id')
  								    ->select('student_enroll_schedule.enroll_class_end_status','student_enroll_schedule.class_start_time','student_enroll_schedule.class_end_date','student_enroll_schedule.start_time','student_enroll_schedule.end_time','payments.withdrowral_status','enrolls.fees','student_enroll_schedule.select_date','student_enroll_schedule.class_start_date','student_enroll_schedule.class_end_time','enrolls.student_id','student_enroll_schedule.per_class_payment_status')
  								    ->where('payments.payment_status','Successfully')
  								    ->where('enrolls.teacher_id',$teacherId)
  								    ->whereBetween('student_enroll_schedule.select_date',array($fromDate,$toDate))->get();
  								     // dd($orderArray);
  								    $headers = array(
	        'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
	        'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
	        'Content-Disposition' => 'attachment; filename=abc.csv',
	        'Expires' => '0',
	        'Pragma' => 'public',
	    );
  								   
    $filename = "users_".date('d-M-Y').".csv";
    // $url ="export";
    $handle = fopen($filename, 'w');
    // $fp = fopen($url . $filename, 'a+');
 // dd($fp);
    // $orderArray= $this->home->get_bookingLocation($id);
    foreach ($orderArray as $key => $val) {
    	    $utc_start_time = $val->start_time;
        	$utc_end_time = $val->end_time;

        	$class_stime = $val->class_start_time;
        	$class_etime = $val->class_end_time;

			date_default_timezone_set('UTC');
            $LocalTime_start_time = new DateTime($utc_start_time);
            $tz_start = new DateTimeZone('Asia/Kolkata' );
            $LocalTime_start_time->setTimezone( $tz_start );
            $start_date_time = (array) $LocalTime_start_time;
            $StartDateTime = $start_date_time['date'];
            $time_ist = date('g:i a', strtotime($StartDateTime));

            $LocalTime_start_time1 = new DateTime($utc_end_time);
            $tz_start1 = new DateTimeZone('Asia/Kolkata' );
            $LocalTime_start_time1->setTimezone( $tz_start1 );
            $start_date_time1 = (array) $LocalTime_start_time1;
            $StartDateTime1 = $start_date_time1['date'];
            $time_ist1 = date('g:i a', strtotime($StartDateTime1));

           
            $time_ist2 = date('g:i a', strtotime($class_stime));
            $time_ist3 = date('g:i a', strtotime($class_etime));
            if(!empty($val->class_start_date)){
               $startdate =date('m/d/Y',strtotime($val->class_start_date));
            }else{
              $startdate = "N/A";
            }
    	    $user = User::where('id',$val->student_id)->first();
    	// dd($user);die;
    	$student_name = isset($user->name) ? $user->name:"";
    	if($val->per_class_payment_status == NULL){
    		$status = "Pending";
    	}if($val->per_class_payment_status==1){
    		$status = "Approved";
    	}if($val->per_class_payment_status==3){
    		$status = "Reject";
    	}

      $jsonArray['data'][] = array(
      	'student_id'=>$student_name,
        'select_date' =>  $val['select_date'],
        'start_time' => $time_ist,//$val['start_time'],
        'end_time' => $time_ist1,//$val['end_time'],
        'class_start_date' => $startdate,
        'class_start_time' =>  $time_ist2,
        'class_end_time' => $time_ist3,
        // 'class_end_date' => $val['class_end_date'],
        'fees' =>  $val['fees'],
        'withdrowral_status' => $status,
      );
    }

    
    fputcsv($handle, array("Student Name","Schedule Date", "Schedule Start Time", "Schedule End Time", "Actual Start Date", "Actual Start Time", "Actual End Time","Class Fees", "Withdrowral Status"));
    foreach ($jsonArray['data'] as $fields) {
      fputcsv($handle, $fields);
    }
    fclose($handle);
     // $headers=header("Location:" . $url . date('His') . 'test.csv');
    // dd($url);
  return response()->download($filename, "users_".date('d-M-Y').".csv", $headers);
    exit; 

	}

	public function freeCredited(Request $request){
		// dd($request->all());
		$user = User::where('id',$request->user_id)->first();
		$previouscredit = isset($user->free_class) ? $user->free_class:0;
		$aftercredit = $previouscredit+$request->free_creadits;
	    $free_credit = User::where('id',$request->user_id)->update(['free_class'=>$aftercredit]);
	    $fcmtoken = User::where('id',$request->user_id)->first();
		 $this->notificationsendd($fcmtoken->deviceToken, "Free credit", "You have got 1 free session in super credits");
                             $notification = new Notification();
                             $notification->user_id = $request->user_id;
                             $notification->teacher_id = null;
                             $notification->title = "Credits";
                             $notification->message = "You have got 1 free session in super credits";
                             $notification->status = 1;
                             $notification->save();
		 \Session::flash('msg', 'Free Class Creadits Added Successfully.');
            return back();
	}

	public function invoice(Request $request){
		if($request->user_name){
			$user = User::where('name', 'like',  '%' . $request->user_name .'%')->pluck('id');
			$data = PurchaseClass::whereNotNull('invoice_pdf')->whereIn('student_id',$user);
		}else{
			$data = PurchaseClass::whereNotNull('invoice_pdf')->orderBy('id', 'desc');
		}
		// $data = PurchaseClass::orderBy('id','desc');

		$total = $data->count();
		$data = $data->paginate(10);
		foreach($data as $datas){
			$user = User::select('name','email')->where('id',$datas->student_id)->first();
			$datas->user_name = isset($user->name) ? $user->name:'';
			$datas->user_email = isset($user->email) ? $user->email:'';
		}


		// dd($data);
		return view('admin.invoice.invoice',compact('data','total'));
	}


// public function downloadData(Request $request) {
//     $startDate = $request->input('start_date');
//     $endDate = $request->input('end_date');
//     $data = PurchaseClass::whereBetween('created_at', [$startDate, $endDate])->get();
//     $pdf = PDF::loadView('email.	invoices.pdf', compact('data'));
//      return $pdf->download('invoices.pdf');

// }

// 	 public function downloadData(Request $request)
// {
//     // Fetch invoice file names based on the date range
//     $invoiceFileNames = PurchaseClass::whereBetween('created_at', [$request->start_date, $request->end_date])
//         ->pluck('invoice_pdf')
//         ->toArray();

//     // Loop through each invoice file name and force download
//     foreach ($invoiceFileNames as $fileName) {
//         $filePath = public_path('invoice/' . $fileName);

//         // Check if the file exists
//         if (file_exists($filePath)) {
//             // Set headers for file download
//             header('Content-Type: application/pdf');
//             header('Content-disposition: attachment; filename=' . basename($filePath));
//             header('Content-Length: ' . filesize($filePath));

//             // Output the file content directly
//             readfile($filePath);
//         } else {
//             // File not found, handle the error or provide a message to the user
//             // For example, you can redirect the user back with a message
//             return redirect()->back()->with('error', 'File not found.');
//         }
//     }

//     // Terminate script execution to prevent any further output
//     exit;
// }


public function downloadData(Request $request)
{
	// $start_date = date('Y-m-d', strtotime('+1 day', strtotime($request->start_date)));
	$time = " 00:00:00";
	$endtime = " 23:59:59";
	$start_date = $request->start_date.$time;
	$end_date = $request->end_date.$endtime;
    // $end_date = date('Y-m-d', strtotime('+1 day', strtotime($request->end_date)));
    if ($end_date <= $start_date) {
        // return response()->json(['message' => 'End date must be after start date.'], 400);
		return redirect()->back()->with('error', 'End date must be after start date.');
    }
	// dd($end_date);
	// dd($request->all());
    // Fetch invoice file names based on the date range
  //   if($request->user_name){
		// 	$user = User::where('name', 'like',  '%' . $request->user_name .'%')->pluck('id');
		// 	$data = PurchaseClass::whereIn('student_id',$user);
		// }else{
		// 	$data = PurchaseClass::orderBy('id', 'desc');
		// }
		// $data = PurchaseClass::orderBy('id','desc');
		// $data = $data->paginate(10);
  //   if(!empty($request->name)){
		// 	$user = User::where('name', 'like',  '%' . $request->name .'%')->orWhere('email',$request->name)->pluck('id');
		// 	$invoiceFileNames = PurchaseClass::whereIn('student_id',$user)->pluck('invoice_pdf')->toArray();
		// 	// dd($invoiceFileNames);
		// }else{
  //   	 $invoiceFileNames = PurchaseClass::whereBetween('created_at', [$request->start_date, $request->end_date])
  //       ->pluck('invoice_pdf')
  //       ->toArray();
  //   }
	if (!empty($request->name)) {
    $user = User::where('name', 'like', '%' . $request->name . '%')->orWhere('email', $request->name)->pluck('id');
    $invoiceFileNames = PurchaseClass::whereIn('student_id', $user)
                                    ->whereNotNull('invoice_pdf') // Filter out rows with null invoice_pdf
                                    ->pluck('invoice_pdf')
                                    ->toArray();
} else {
    $invoiceFileNames = PurchaseClass::whereBetween('created_at', [$start_date, $end_date])
                                    ->whereNotNull('invoice_pdf') // Filter out rows with null invoice_pdf
                                    ->pluck('invoice_pdf')
                                    ->toArray();
                                   // dd($invoiceFileNames);
}

   	if (empty($invoiceFileNames)) {
        // return response()->json(['message' => 'No data found for the given criteria.'], 404);
        return redirect()->back()->with('error', 'No data found for the given criteria.');
    }

    // Create a new zip archive
    $zipFileName = 'invoices_' . now()->format('Y-m-d_H-i-s') . '.zip';
    $zipFilePath = public_path($zipFileName);
    $zip = new ZipArchive;

    if ($zip->open($zipFilePath, ZipArchive::CREATE | ZipArchive::OVERWRITE) === true) {
        // Loop through each invoice file name and add it to the zip archive
        foreach ($invoiceFileNames as $fileName) {
            $filePath = public_path('invoice/' . $fileName);

            // Check if the file exists
            if (file_exists($filePath)) {
                // Add the file to the zip archive
                $zip->addFile($filePath, $fileName);
            } else {
                // Log or handle the error if the file is not found
                // For example, you can skip adding this file to the zip archive
            }
        }

        // Close the zip archive
        $zip->close();

        // Read the zip file contents
        $zipFileContents = file_get_contents($zipFilePath);

        // Delete the temporary zip file
        unlink($zipFilePath);

        // Set headers for file download and serve the zip file contents
        return response($zipFileContents)
            ->header('Content-Type', 'application/zip')
            ->header('Content-disposition', 'attachment; filename=' . $zipFileName);
    } else {
        // Handle the case when the zip archive could not be created
        return redirect()->back()->with('error', 'Failed to create zip archive.');
    }
}





	public function notificationsendd($deviceToken, $title, $body){
        $url = "https://fcm.googleapis.com/fcm/send";
        $deviceToken = $deviceToken;
        $serverKey="AAAAHospWg8:APA91bEO2t5wUNyU8kwdzd8z_UoimyZl638DALOgOnmvGzJhNVGrru-NToZX_tKPbLpqoo9TyBIrUqQS8gwjO1Mrr2yC1-u7pTeGCId4EqjY-zdoblGVWQseNa2zrPFWehILlK9UTjy0";
        $title = $title;
        $body = $body;
        // $type = $type;
        $notification = array('title' => $title, 'body' => $body,"click_action" =>"https://guru.appic.tech", 'sound' => 'default', 'badge' => '1');
        $arrayToSend = array('to' => $deviceToken, 'notification' => $notification, 'priority' => 'high');
        $json = json_encode($arrayToSend);
       // dd($json);
        $headers = array();
        $headers[] = 'Content-Type: application/json';
        $headers[] = 'Authorization: key=' . $serverKey;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $json);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        //Send the request
        $result = curl_exec($ch);
        curl_close($ch);
       // dd($result); 

    }

public function incomplate_class(Request $request)
{
$incompleteSchedulesQuery = StudentEnrollSchedule::query()
    ->where('enroll_class_end_status', 1);
// dd($incompleteSchedulesQuery);
if ($request->user_name) {
    $searchTerm = $request->user_name;

    $teachers = User::where('name', 'like', '%' . $searchTerm . '%')
                      ->where('role_id',2)
                     ->pluck('id');

    $students = User::where('name', 'like', '%' . $searchTerm . '%')
                      ->where('role_id', 6)
                     ->pluck('id');

    $userIds = $teachers->merge($students);

    $incompleteSchedulesQuery->whereIn('student_id', $userIds)
                            ->orWhereIn('teacher_id', $userIds)
                            ->where('enroll_class_end_status', 1)
                            ->orderBy('select_date', 'desc'); // Replace 'your_column_name' with the actual column name
}

$incompleteSchedules = $incompleteSchedulesQuery->get();
 // dd($incompleteSchedules);
    // $incomplate = StudentEnrollSchedule::query(); // Use the query builder
    $data = [];

    foreach ($incompleteSchedulesQuery->get() as $key => $value) {
    $start_time = new DateTime($value->class_start_time);
    $end_time = new DateTime($value->class_end_time);
    $interval = $start_time->diff($end_time);

    // Extract the total minutes from the DateInterval object
    $total_class_time_minutes = ($interval->h * 50) + $interval->i;
    $user_name = User::where('id', $value->student_id)->first();
    $teacher_name = User::where('id', $value->teacher_id)->first();
    $value->student_name = isset($user_name->name) ? $user_name->name : '';
    $value->teachername = isset($teacher_name->name) ? $teacher_name->name : '';
    $value->t_duration = isset($total_class_time_minutes) ? $total_class_time_minutes : '0';
    $enroll = Enroll::where('id', $value->enroll_id)->first();
    $value->fees = isset($enroll->fees) ? $enroll->fees : 0;
  
    if ($total_class_time_minutes < 50) {
        $data[] = $value;
    }
}


    // $currentPage = LengthAwarePaginator::resolveCurrentPage();
    // $perPage = 10;
    // $currentPageItems = array_slice($data, ($currentPage - 1) * $perPage, $perPage);
    // $paginatedData = new LengthAwarePaginator($currentPageItems, count($data), $perPage, $currentPage);
      // dd($data);
    return view('admin.teacher_payment.incomplate_class', compact('data'));
}
    public function classStatus($id, $status){
    $user = StudentEnrollSchedule::find($id);

    if (!$user) {
        // Handle the case where the user is not found
        return redirect()->back()->withErrors('User not found.');
    }

    // Update the admin_payment_status
    $updated = StudentEnrollSchedule::where('id', $id)->update(['admin_payment_status' => $status]);

    // If the admin_payment_status is updated to 1, add fees to the teacher's wallet
    if ($status == 1) {
        $feesdata = Enroll::find($user->enroll_id);
        $userdata = User::find($user->teacher_id);

        if ($feesdata && $userdata) {
            $total_wallet_amount = $userdata->total_wallet_amount + $feesdata->fees;
            User::where('id', $userdata->id)->update(['total_wallet_amount' => $total_wallet_amount]);
        } else {
            // Handle the case where the feesdata or userdata is not found
            return redirect()->back()->withErrors('Fees data or user data not found.');
        }
    }

    // Redirect back to the previous page
    return redirect()->back();
}

	public function downloadCSVUser(Request $request)
    {
    // $endDate = $request->input('end_date');
    $time = " 00:00:00";
    $endtime = " 23:59:59";
    $startDate = $request->input('start_date').$time;
    $endDate = $request->input('end_date').$endtime;
    // $startDate = date('Y-m-d', strtotime('+1 day', strtotime($request->start_date)));
    // $endDate = date('Y-m-d', strtotime('+1 day', strtotime($request->end_date)));
    	if($request->name_student){
    	   // $users = User::where('role_id',6)->get();
    		$users = User::where('name',$request->name_student)->OrWhere('email',$request->name_student)->get();

    	}else{
    		$users = User::whereBetween('created_at', [$startDate, $endDate])->where('role_id',6)->get();
    	}

        $headers = array(
            "Content-type" => "text/csv",
            "Content-Disposition" => "attachment; filename=users.csv",
            "Pragma" => "no-cache",
            "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
            "Expires" => "0"
        );

        $callback = function () use ($users) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['ID', 'Name', 'Email','Parent Name','Country code','Country','Phone','Address','Refer Code','Timezone','Currency','status',"created at time"]);

            foreach ($users as $user) {
            	$phone = $user->phone;
            	$created_at = date('Y-m-d', strtotime($user->created_at));
            	if($user->status == 0){
            		$status = "Inactive";
            	}else{
            		$status = "Active";
            	}
                fputcsv($file, [$user->id, $user->name, $user->email, $user->parent_name,$user->dial_code, $user->country_flag, $phone, $user->address,$user->refer_code, $user->timezone,$user->currency,$status,$created_at]);
            }
            fclose($file);
        };

        return new StreamedResponse($callback, 200, $headers);
    }

    public function downloadCSVTeacher(Request $request)
    {
    	$time = " 00:00:00";
    	$endtime = " 23:59:59";
        $startDate = $request->input('start_date') .$time;
        $endDate = $request->input('end_date') .$endtime;
        // $startDate = date('Y-m-d', strtotime('+1 day', strtotime($request->start_date)));
       // $endDate = date('Y-m-d', strtotime('+1 day', strtotime($request->end_date)));
    	// dd($endDate);
      if(!empty($request->name_email)){
    	$users = User::where('name',$request->name_email)->OrWhere('email',$request->name_email)->get();
    	 // dd($users);
    }else{
    	 // dd('edd');
      $users = User::whereBetween('created_at', [$startDate, $endDate])->where('role_id',2)->get();
    }
     
        $headers = array(
            "Content-type" => "text/csv",
            "Content-Disposition" => "attachment; filename=teachers.csv",
            "Pragma" => "no-cache",
            "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
            "Expires" => "0"
        );

        $callback = function () use ($users) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['ID', 'Name', 'Email','Country code','Country','Phone','DOB','Address','Timezone','Teacher Experience','Qualification','Currency','status',"created Date"]);

            foreach ($users as $user) {
            	$created_at = date('Y-m-d', strtotime($user->created_at));
            	if($user->status == 0){
            		$status = "Inactive";
            	}else{
            		$status = "Active";
            	}
                fputcsv($file, [$user->id, $user->name, $user->email,$user->dial_code, $user->country_flag, $user->phone, $user->dob, $user->address, $user->timezone, $user->teacher_exp,$user->highest_qualification,$user->currency,$status,$created_at]);
            }

            fclose($file);
        };

        return new StreamedResponse($callback, 200, $headers);
    }

    public function book_class(Request $request){
    	$students = User::where('role_id',6)->get();
    	$teachers = User::where('role_id',2)->get();
    	$class = StudentClass::where('status',1)->get();
    	$subjects = Subject::where('status',1)->get();
    	$category = Category::where('status',1)->get();
    	$courses = Courses::where('status',1)->get();
    	$plans = Plan::where('status',1)->get();
    	return view('admin.student_purchase_class.book_ragular_class',compact('students','teachers','class','category','courses','subjects','plans'));
    }

    public function select_plan(Request $request){

    	if($request->class_id_n_category == 1){
    	   $plans = Plan::where('subject_id',$request->subject_id)->where('status',1)->get();
    	}else{
    		$plans = Plan::where('course_id',$request->subject_id)->where('status',1)->get();
    	}
    	return response()->json(['plans' => $plans]);
    }
    public function getTeacherAvailability($teacherId, $date)
    {

       $AddDate = date('Y-m-d', strtotime('+1 day', strtotime($date)));
       $subDate = date('Y-m-d', strtotime('-1 day', strtotime($date)));
        

    // Retrieve student enroll schedule
       $bookconvertedTimeSlots = [];
    $book_SlotData = StudentEnrollSchedule::join('enrolls', 'enrolls.id', '=', 'student_enroll_schedule.enroll_id')
        ->select('student_enroll_schedule.start_time as utc_select_time', 'student_enroll_schedule.end_time as utc_select_end_time',
            'enrolls.teacher_id', 'student_enroll_schedule.id', 'enrolls.id as enroll_id', 'student_enroll_schedule.select_date', 'enrolls.student_apply_for_demo_id','student_enroll_schedule.created_at')
        ->where('enrolls.teacher_id', $teacherId)
        ->whereBetween('student_enroll_schedule.select_date', [$subDate, $AddDate])
        ->get();

    // Retrieve student booking frees for each schedule
    foreach ($book_SlotData as $key1 => $value1) {
        $value1->book_Slot = StudentBookingFrees::join('studentapplyfordemo', 'studentapplyfordemo.id', '=', 'student_booking_frees.studentapplyfordemo_id')
            ->select('student_booking_frees.select_date', 'student_booking_frees.utc_select_time as demo_utc_select_time', 'student_booking_frees.utc_select_end_time as demo_utc_select_end_time',
                'studentapplyfordemo.teacher_assign_id', 'student_booking_frees.id', 'studentapplyfordemo.id as studentapplyfordemo_id')
            ->where('studentapplyfordemo.teacher_assign_id', $teacherId)
            ->first();

        $book_SlotData[$key1]->day = date('D', strtotime($value1->select_date));
        // $timeutc = $value1->select_date.'T'.$value1->utc_select_time.'000000Z';
        $startDateTimeUTC = Carbon::parse($value1->select_date . ' ' . $value1->utc_select_time, 'UTC');
        $startDateTimeUTC->setTimezone('Asia/Kolkata');
        $bookconvertedTimeSlots[] = [
        // 'day' => $dayNumber,
        'utc_select_time' => $startDateTimeUTC->format('Y-m-d H:i:s'),
        // 'end_time' => $endDateTimeUTC->format('Y-m-d H:i:s')
    ];
    $value1->indiabooktme = $bookconvertedTimeSlots;
    }
// dd($book_SlotData);
    // Retrieve teacher availability slots and related data
   $get_TeacherAvailabiltyOfSlots = TeacherAvailabiltyOfSlots::where("user_id", $teacherId)->get();

if ($get_TeacherAvailabiltyOfSlots->isNotEmpty()) {
   foreach ($get_TeacherAvailabiltyOfSlots as $val) {
    $utc_time = Timemanage::select('start_time', 'end_time', 'date', 'teacher_availabilty_of_slots_id')
        ->where('teacher_availabilty_of_slots_id', $val->id)
        ->where('delete_time_status', 0)
        ->get();

    if ($utc_time->isNotEmpty() && !is_null($utc_time->first()->start_time)) { // Convert only if time is not null
        // Iterate over each UTC time slot and convert only the start time to Indian time zone
        $convertedTimeSlots = [];
        foreach ($utc_time as $slot) {
            $startDateTimeUTC = Carbon::parse($slot->date . ' ' . $slot->start_time, 'UTC');
            $startDateTimeUTC->setTimezone('Asia/Kolkata');

            $endDateTimeUTC = Carbon::parse($slot->date . ' ' . $slot->end_time, 'UTC');
            $endDateTimeUTC->setTimezone('Asia/Kolkata');

            // Get the numeric representation of the day of the week (0 for Sunday, 1 for Monday, etc.)
            $dayNumber = $startDateTimeUTC->format('w');

            $convertedTimeSlots[] = [
                'day' => $dayNumber,
                'start_time' => $startDateTimeUTC->format('Y-m-d H:i:s'),
                'end_time' => $endDateTimeUTC->format('Y-m-d H:i:s')
            ];


        }
        	$val->utctime = $convertedTimeSlots;
       
    } else {
        // For slots with NULL time values, keep them as they are
        $val->utctime = $utc_time->toArray();
    }
}


// dd($filteredTimeSlots);

}


 // dd($get_TeacherAvailabiltyOfSlots);
    // return response(["statusCode" => 200, "message" => "Teacher book slot", "data" => $book_SlotData, 'get_TeacherAvailabiltyOfSlots' => $get_TeacherAvailabiltyOfSlots]);
    return response()->json(['availability' => $get_TeacherAvailabiltyOfSlots,"data"=>$book_SlotData]);
    }


    public function book_new_ragulr_class(Request $request){
    	 // dd($request->all());
    	if(!empty($request->class_id)){
    	  $teacher_fess = TeachingDetails::where('type',1)->where("user_id",$request->teacher_id)->where('class_or_category_id',$request->class_id)->where('subject_or_course_id',$request->subject_id)->first();
    	}else{
    		$teacher_fess = TeachingDetails::where('type',2)->where("user_id",$request->teacher_id)->where('class_or_category_id',$request->category_id)->where('subject_or_course_id',$request->course_id)->first();
    	}
    	$classDates = $request->class_date;
        $classTimes = $request->class_time;
        $no_of_class = $request->no_of_class;
    	$enroolldata = new Enroll();
    	$enroolldata->student_id = $request->student_id;
    	$enroolldata->teacher_id = $request->teacher_id;
    	$enroolldata->no_of_class = $no_of_class;
    	$enroolldata->plan_id = $request->plan_id;
    	$enroolldata->fees = isset($teacher_fess->fees) ? $teacher_fess->fees:0;
    	if(!empty($request->class_id) && !empty($request->subject_id)){
    	$enroolldata->class_id = isset($request->class_id) ? $request->class_id:0;
    	$enroolldata->subject_id = isset($request->subject_id) ? $request->subject_id:0;
    	$enroolldata->category_id = 0;
    	$enroolldata->course_id = 0;
    	}else{
    	  $enroolldata->course_id = $request->course_id;
    	  $enroolldata->category_id = $request->category_id;
    	  $enroolldata->class_id = 0;
    	  $enroolldata->subject_id = 0;
        }
    	  $enroolldata->class_created_by = 'Admin';
    	  // dd($enroolldata);
    	  $enroolldata->save();

    	  $PurchaseClass = new PurchaseClass();
    	  $PurchaseClass->enroll_id = $enroolldata->id;
    	  $PurchaseClass->student_id = $request->student_id;
    	  $PurchaseClass->teacher_id = $request->teacher_id;
    	  $PurchaseClass->coupon_code = null;
    	  $PurchaseClass->gst_amount = 0;
    	  $PurchaseClass->actual_amount = 0;
    	  $PurchaseClass->discount_amount = 0;
    	  $PurchaseClass->amount = 0;
    	  $PurchaseClass->actual_price = 0;
    	  $PurchaseClass->amount_to_pay = 0;
    	  $PurchaseClass->currency = null;
    	  $PurchaseClass->per_session_amount = 0;
    	  $PurchaseClass->save();

    	  // $payment = new Payment();
    	  // $payments->enroll_id = $PurchaseClass->enroll_id;
    	  // $payments->student_id = $request->student_id;
    	  // $payments->teacher_id = $request->teacher_id;
    	  // $payments->purchase_class_id = $PurchaseClass->id;
    	  // $payments->transation_id = 0;
    	  // $payments->payment_status = "Successfully";
    	  // $payments->save();
    	  $payment = Payment::create(array(
			 	'enroll_id' =>$PurchaseClass->enroll_id,
			 	'student_id'=>$request->student_id,
			 	"teacher_id"=>$request->teacher_id,
			 	'purchase_class_id'=>$PurchaseClass->id,
			 	'transation_id'=>0,
			 	'payment_status'=>"Successfully"
			 ));
			  
    	 
    	  $localTimeZone = 'Asia/Kolkata';
    	  $class_link = User::where('id',$request->teacher_id)->first();
           for ($i = 0; $i < count($classDates); $i++) {
			    $scheduledata = new StudentEnrollSchedule();
			    $scheduledata->enroll_id = $enroolldata->id;
			    $scheduledata->zoom_link = isset($class_link->class_url) ? $class_link->class_url:'';
			    $date = date_create_from_format('Y-m-d', $classDates[$i]);
			    $time = date_create_from_format('H:i:s', $classTimes[$i]);
			    if ($date !== false && $time !== false) {
			        $datetime = new DateTime($classDates[$i].' '.$classTimes[$i], new DateTimeZone($localTimeZone));
			        $datetime->setTimezone(new DateTimeZone('UTC'));
			        $startDateUTC = $datetime->format('Y-m-d');
			        $startTimeUTC = $datetime->format('H:i:s');
			        $endTimeUTC = $datetime->add(new DateInterval('PT1H'))->format('H:i:s');
			        $scheduledata->end_time = $endTimeUTC;
			        $scheduledata->enroll_sorting_date = $startDateUTC.'T'.$endTimeUTC.':000Z';
			    } else {
			        $startDateUTC = "yy-mm-dd";
			        $startTimeUTC = "hh-mm";
			        $scheduledata->end_time = "hh-mm";
			    }

    $scheduledata->select_date = $startDateUTC;
    $scheduledata->start_time = $startTimeUTC;

    $scheduledata->student_id = $enroolldata->student_id;
    $scheduledata->teacher_id = $enroolldata->teacher_id;
    $scheduledata->save();
}

    	\Session::flash('msg', 'Class Added Successfully.');
            return back();
}


public function convert_timeZone(Request $request) {
    $teacher = User::find($request->teacherId);
    $student = User::find($request->student_id);
    $selectedTime = Carbon::parse($request->selectedDate . ' ' . $request->selectedTime, 'Asia/Kolkata');
    $dateTimeUtc = $selectedTime->setTimezone('UTC');
    $teacherTimeZone = new DateTimeZone($teacher->timezone);
    $convertedTime = $dateTimeUtc->setTimezone($teacherTimeZone)->format('m/d/Y h:i:s A');
    $studentTimeZone = new DateTimeZone($student->timezone);
    $convertedTime1 = $dateTimeUtc->setTimezone($studentTimeZone)->format('m/d/Y h:i:s A');
    return response()->json(['convertedTime' => $convertedTime, 'studentTimeZone' => $convertedTime1]);
}

	public function studentdetails(Request $request){
		 $student = User::find($request->id);
		 return response()->json(['student' => $student]);
	}
	public function teacherdetails(Request $request){
		
		 $student = User::find($request->id);
		 return response()->json(['teacher' => $student]);
	}

	public function select_classes_subject(Request $request){
    	
    	$teaching_details = TeachingDetails::where('user_id',$request['teacherId'])->where('class_or_category_id',$request['class_id'])->get();
    	foreach ($teaching_details as $key => $value) {
    		  if($request->class_id_n_category == 1){
    			 $value->subject = Subject::where('id',$value->subject_or_course_id)->first();
    		  }else{
    		  	$value->subject = Courses::where('id',$value->subject_or_course_id)->first();
    		  }
    	}
    	// dd($teaching_details);
    	return response()->json(['teaching_details' => $teaching_details]);
    }
}
