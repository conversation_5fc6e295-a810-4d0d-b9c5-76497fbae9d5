<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Payment;
use App\Models\PurchaseClass;
use App\Models\Enroll;
use App\Models\StudentClass;
use App\Models\Subject;
use App\Models\Category;
use App\Models\Courses;
use App\Models\Freecreadit;
use App\Models\StudentEnrollSchedule;
use App\Models\StudentDemoClassFeedback;
use App\Models\StudentApplyForDemo;
use Validator;
use DateTime;
use DateTimeZone;
class WithdrowralController extends Controller
{
	/**
	 * Display a listing of the resource.
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function index(Request $request){
// 	  if ($request->user_name) {
//           $users = User::where('name', 'like', '%' . $request->user_name . '%')->get();
//           $userIds = $users->pluck('id')->toArray();
//           $data = Payment::whereIn('teacher_id', $userIds)->where('payment_status', 'Pending');
//         } else {
//            $data = Payment::orderBy("id", "DESC")->orWhere("withdrowral_status", 1)
//                    ->orWhere("withdrowral_status", 2);
// }
if ($request->user_name) {
    $users = User::where('name', 'like', '%' . $request->user_name . '%')->get();
    $userIds = $users->pluck('id')->toArray();
    $data = Payment::whereIn('teacher_id', $userIds)
        // ->where('payment_status', 'Pending')
        ->where('withdrowral_amount', '>', 0.0); // Add this condition
} else {
    $data = Payment::orderBy("id", "DESC")
        // ->where('payment_status', 'Pending')
        ->where('withdrowral_amount', '>', 0.0); // Add this condition
}


		// $data = Payment::orderBy("id", "DESC")->orwhere("withdrowral_status", 1)
		// 	             ->orwhere("withdrowral_status", 2);
		$totalResult = $data->count();
		$data = $data->paginate(10);
		if(!empty($data)){
			foreach ($data as $key => $value) {
				if($value->teacher_id!=0){
					$get_user= User::where("id",$value->teacher_id)->first()->name;
					$value->teacher_name = isset($get_user) ? $get_user:'';
				}
			}
		}
		return view('admin.withdrowral.index', compact('data'));
	}

	public function withdrowral_Approved_pending(Request $request){
	  $withdrowral_id = $request->withdrowral_id;
	  $withdrowral_status = $request->withdrowral_status;
 	  $withdrowral_amount = $request->withdrowral_amount;
	  $getPayment = Payment::find($withdrowral_id);
	  $teacher_id = $getPayment->teacher_id;	
	  $getuser = User::find($teacher_id);
	  $total_wallet_amount = $getuser->total_wallet_amount;		
	  $final_amount =0;
	  if($total_wallet_amount>=$withdrowral_amount){
		 $final_amount = $total_wallet_amount -$withdrowral_amount;
		}
		 $getuser->total_wallet_amount = $final_amount;	
		 $getuser->save();
		 $file = $request->file('iamge');
 	     $destinationPath = public_path().'/withdrowralImage/';
	     $filename="";
         if($file){
            $originalFile = $file->getClientOriginalName();
            $filename .= strtotime(date('Y-m-d-H:isa')).$originalFile;
            $file->move($destinationPath, $filename);
        }
        $getPayment->withdrowral_status = $request->withdrowral_status;
        if($request->withdrowral_status==1){
        	$getPayment->admin_status = 'Pending';
        }
        if($request->withdrowral_status==2){
        	$getPayment->admin_status = 'Approved';
        }
        if($request->withdrowral_status==3){
        	$getPayment->admin_status = 'Decline';
        }
		$getPayment->image = $filename;
		$getPayment->withdrowral_time = date('Y-m-d-H:isa');

		$start_date = $getPayment->withdrowral_from_date;
        $end_date = $getPayment->withdrowral_to_date;

    // Retrieve student enroll schedule
    $book_SlotData = StudentEnrollSchedule::where('admin_payment_status',1)->where('teacher_id',$teacher_id)->whereBetween('select_date', [$start_date, $end_date])
        ->update(['per_class_payment_status' => 1]);

		$getPayment->save();
		$msg = "";
		if($request->withdrowral_status==2){
			$msg.="withdrowral Amount Approved Successfully";
		}
		else{
			$msg.="withdrowral Amount Pending Successfully";
		}
        \Session::flash('msg',$msg);
            return back();
	}

	public function teacher_payment_details(Request $request){
		if($request->user_name){
			$user = User::where('name', 'like',  '%' . $request->user_name .'%')->pluck('id');
			$data = Payment::whereIn('teacher_id',$user);
		}else{
			$data = Payment::orderBy('id', 'desc');
		}
	  // $data = Payment::orderBy("id", "DESC");
	  $totalResult = $data->count();
	  $data = $data->paginate(10);
	  if(!empty($data)){
		  foreach ($data as $key => $value) {
			  if($value->teacher_id!=0){
				  $get_user= User::where("id",$value->teacher_id)->first();
				  $value->name = isset($get_user->name) ? $get_user->name:"";
				  $get_student= User::where("id",$value->student_id)->first();
				  $value->s_name = isset($get_student->name) ? $get_student->name:"";
				  $value->teacher_name = isset($get_user->name) ? $get_user->name:'';
				  $enroll_data = Enroll::where('id',$value->enroll_id)->first();
				  $value->no_of_session = isset($enroll_data->no_of_class) ? $enroll_data->no_of_class:"";
			    }
			}
		}
	  // dd($data);
		return view('admin.teacher_payment.index', compact('data'));
	}

	public function student_purchase_class(Request $request){
		if($request->user_name){
			$user = User::where('name', 'like',  '%' . $request->user_name .'%')->pluck('id')->toArray();
			// dd($user);
			$data = PurchaseClass::whereIn("student_id", $user)->orWhereIn('teacher_id',$user);
			// dd($data);
		}else{
			$data = PurchaseClass::orderBy('id', 'desc');
		}
		// echo 111;die; 
	  if(!empty($request->enroll_id)){
	  	 $enroll_data = Enroll::where('id',$request->enroll_id)->first();
	  	 $user = User::where('id',$enroll_data->student_id)->first();
	  	 if(!empty($enroll_data->complete_class)){
	  	 	 $free_class = $user->free_class+$enroll_data->complete_class;
	  	 	}else{
	  	 	  $free_class = $user->free_class+$enroll_data->no_of_class;
	  	 	}
	  	
		 $cancel_class = Enroll::where('id',$request->enroll_id)->update(['admin_cancel_status'=>1]);
		 $update = User::where('id',$enroll_data->student_id)->update(['free_class'=>$free_class]);
	  }

	  	// $data = PurchaseClass::orderBy("id", "DESC");
	    $totalResult = $data->count();
	    $data = $data->paginate(40);
	  if(!empty($data)){
		  foreach ($data as $key => $value) {
			  if($value->teacher_id!=0){
			  	  $enroll = Enroll::where('id',$value->enroll_id)->first();
				  $get_user= User::where("id",$value->teacher_id)->first();
				  
				  $get_student= User::where("id",$value->student_id)->first();
				  $value->teacher_name = isset($get_user->name) ? $get_user->name:'';
				  
				  $value->student_name = isset($get_student->name) ? $get_student->name:'';
				  $value->student_email = isset($get_student->email) ? $get_student->email:'';
				  $value->no_of_class = isset($enroll->no_of_class) ? $enroll->no_of_class:'';
				  $value->complete_class = isset($enroll->complete_class) ? $enroll->complete_class:'';
				  $value->admin_cancel_status = isset($enroll->admin_cancel_status) ? $enroll->admin_cancel_status:0;
			    }
			} 
		}
	  // }
	     // dd($data);
		return view('admin.student_purchase_class.index', compact('data'));
	}

	public function fetchFeedback($enrollId) {
		// dd($enrollId);
    $data = StudentDemoClassFeedback::where('enroll_id', $enrollId)->first();

    return response()->json($data);
}



	

	public function delete($id){
		$delete = Enroll::where('id',$id)->update(['delete_status'=>1]);
		// return redirect('admin/student_purchase_class'.'/'.$delete->subject_id);
       return back()->with('message', 'Delete Successfully');

	}
	


	public function student_classes(Request $request,$student_id){
		// dd($student_id);
		// $userId = $student_id;
		$teaacherData  = User::where('role_id',2)->get();
		$currentDate =date('Y-m-d');
		$teacher_available_slot=[];
		$data =Enroll::join('student_enroll_schedule','student_enroll_schedule.enroll_id',"=",'enrolls.id')
		->join('users',"users.id","=","enrolls.teacher_id")
		// ->join('teacher_availabilty_of_slots_details',"teacher_availabilty_of_slots_details.id","=","student_enroll_schedule.teacher_availabilty_of_slots_details_id")
		// ->join('studentapplyfordemo',"studentapplyfordemo.id","=","enrolls.student_apply_for_demo_id")
		->where("student_enroll_schedule.enroll_id",$student_id)
		// ->where("enrolls.student_id",$student_id)
		->where("enrolls.status",0)
		->where('enrolls.delete_status','<>',1)
		// ->where('student_enroll_schedule.select_date',">=",$currentDate)
		->select([
			 'enrolls.class_id',
			'enrolls.subject_id',
			'enrolls.course_id',
			'enrolls.category_id',
			// 'studentapplyfordemo.course_id',
			'student_enroll_schedule.enroll_class_end_status',
			'student_enroll_schedule.id as student_enroll_schedule_id',
			'student_enroll_schedule.cancel_status',
			'users.name as teacher_name',
			'users.class_url as class_url',
			// 'student_enroll_schedule.teacher_availabilty_of_slots_details_id',
				'enrolls.id as enroll_id',
				'enrolls.student_id as user_id',
				'enrolls.student_apply_for_demo_id',
				'enrolls.no_of_class',
				'enrolls.status as enroll_status',
				'student_enroll_schedule.id as student_enroll_schedule_id',
				'student_enroll_schedule.type',
				'student_enroll_schedule.start_time',
				'student_enroll_schedule.end_time',
				'student_enroll_schedule.select_date',
				'student_enroll_schedule.teacher_id',
				// 'teacher_availabilty_of_slots_details.zoom_link'
		])->get();
		       // dd($data);
		$dataarry=[];
		if(!empty($data)){
			foreach($data as $value){
				if($value->student_apply_for_demo_id !=0) {
					// dd('ravi');
					$demodata = StudentApplyForDemo::where('id',$value->student_apply_for_demo_id)->first();
					$value->student_name= 	User::where("id",$value->user_id)->first()->name;
					$value->student_enroll_schedule_id = $value->student_enroll_schedule_id;
				   if($demodata->class_id!=0){
					   // dd('raci');
					   $value->class_name= 	StudentClass::where("id",$demodata->class_id)->first()->class_name;
					   // dd($value->class_name);
					   $value->subject_name= 	Subject::where("id",$demodata->subject_id)->first()->title;
					   
				   }
				   if($demodata->course_id!=0){
					$value->category_name= 	Category::where("id",$demodata->category_id)->first()->name;
					$value->course_name= 	Courses::where("id",$demodata->course_id)->first()->name;

				   }
				}else{
					$value->student_name= 	User::where("id",$value->user_id)->first()->name;
					$value->student_enroll_schedule_id = $value->student_enroll_schedule_id;
				   if($value->class_id!=0){
					   $value->class_name= 	StudentClass::where("id",$value->class_id)->first()->class_name;
					   $value->subject_name= 	Subject::where("id",$value->subject_id)->first()->title;
					   
				   }
				   if($value->course_id!=0){
					$value->category_name= 	Category::where("id",$value->category_id)->first()->name;
					$value->course_name= 	Courses::where("id",$value->course_id)->first()->name;

				   }
				}
				
				    $value->zoome_class_link = isset($value->zoom_link) ? $value->zoom_link :$value->class_url;
				    // $value->enroll_id = isset($value->enroll_id) ? $value->enroll_id :$value->enroll_id;
				    // $value->student_id = isset($value->student_id) ? $value->student_id :$value->student_id;
				    // $value->teacher_id = isset($value->teacher_id) ? $value->teacher_id :$value->teacher_id;
				    // $value->class_id = isset($value->class_id) ? $value->class_id :$value->class_id;
				    // $value->subject_id; = isset($value->subject_id) ? $value->subject_id :$value->subject_id;
				    $dataarry['enroll_id'] = $value->enroll_id;
				    $dataarry['student_id'] = $value->user_id;
				    $dataarry['teacher_id'] = $value->teacher_id;
				    $dataarry['class_id'] = $value->class_id;
				    $dataarry['subject_id'] = $value->subject_id;

				    if($value->start_time== 'hh-mm' || $value->end_time =='hh-mm'){
				    	$value->start_time = '00:00';
				    	$value->end_time = '00:00';
				    }


				    $utc_start_time = $value->start_time;
					date_default_timezone_set('UTC');
		            $LocalTime_start_time = new DateTime($utc_start_time);
		            $tz_start = new DateTimeZone('Asia/Kolkata' );
		            $LocalTime_start_time->setTimezone( $tz_start );
		            $start_date_time = (array) $LocalTime_start_time;
		            $StartDateTime = $start_date_time['date'];
		            $time_ist = date('h:i:s a', strtotime($StartDateTime));
		            $value->start_time_ist = $time_ist;
		            // dd($time_ist);


		             $utc_end_time = $value->end_time;
					date_default_timezone_set('UTC');
		            $LocalTime_start_time1 = new DateTime($utc_end_time);
		            $tz_start1 = new DateTimeZone('Asia/Kolkata' );
		            $LocalTime_start_time1->setTimezone( $tz_start1 );
		            $start_date_time1 = (array) $LocalTime_start_time1;
		            $StartDateTime1 = $start_date_time1['date'];
		            $time_ist1 = date('h:i:s a', strtotime($StartDateTime1));
		            $value->end_time_ist = $time_ist1;
		            $feedbck= StudentDemoClassFeedback::where("enroll_id",$value->student_enroll_schedule_id)->first();
				    if(!empty($feedbck)){
				  	 $value->feed_back = "true";
				    }else{
				  	 $value->feed_back = "false";
				    }
			}
		      // dd($data);
			return view('admin.student_purchase_class.class_details', compact('data','dataarry','teaacherData'));
			// return response()->json(array("statusCode"=>200,'message'=>'',"data"=>$getEnroll));
		}else{
			$message = "Will Show the failure message";
			return response()->json(array("statusCode"=>400,'message'=>$message));
		}
	}

	// public function class_add_byadmin(Request $request){
	// 	 $data = Subject::where('deleted',0)->where("status",1)->get();
	// 	 $classDetails = StudentClass::where('deleted',0)->where("status",1)->get();
	// 	return view('admin.student_purchase_class.class_add',compact('data','classDetails'));
	// }

    public function get_subject(Request $request){
      $class_id = $request->class_id;
      if(isset($class_id)){
         $data = Subject::where('deleted',0)->where("status",1)->where('class_id',$class_id)->get();
         if($data){
            $result = array('status'=>true, 'message'=>'fatch data succesfully', 'data'=>$data);
         }else{
            $result = array('status'=>false, 'message'=>'Something went wrong');
         }
        }else{
          $result = array('status'=>false, 'message'=>'Select Class');
        }
        echo json_encode($result);
    }

    public function class_add_byadmin(Request $request){
    	// dd($request->all());die;
        $validator = Validator::make($request->all(), [
        'free_creadits'=>'required'
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        } else {
        		$dataarry = json_decode($request->dataarry);
        		// dd($dataarry->enroll_id);
            $Class = new Freecreadit();
            $Class->student_id = $dataarry->student_id;
            $Class->teacher_id = $dataarry->teacher_id;
             $Class->admin_teacher_assign_id = $request->admin_teacher_assign_id;
            $Class->class_id =   $dataarry->class_id;
            $Class->enroll_id =  $dataarry->enroll_id;
            $Class->subject_id = $dataarry->subject_id;
            $Class->free_creadits = $request->free_creadits;
            $Class->save();
            \Session::flash('msg', 'Class Added Successfully.');
            return back();
        }
    }
}
