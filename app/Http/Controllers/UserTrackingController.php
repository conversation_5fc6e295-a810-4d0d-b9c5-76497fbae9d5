<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Validator;
use App\Http\Helper as Helper;
use App\Models\Assignment;
use App\Models\AssignmentSubmission;
use App\Models\Chapter;
use App\Models\ChatGroupUser;
use App\Models\City;
use App\Models\ContinueStudy;
use App\Models\Courses;
use App\Models\Franchise;
use App\Models\Lession;
use App\Models\PostEntry;
use App\Models\Quiz;
use App\Models\QuestionAsk;
use App\Models\QuestionAnswer;
use App\Models\State;
use App\Models\StudentClass;
use App\Models\StudentExam;
use App\Models\Subscription;
use App\Models\User;
use App\Models\UserSubscription;
use App\Models\UserTracking;
use App\Models\LoginSession;
use Auth;
use DB;
use Carbon\Carbon;


class UserTrackingController extends Controller
{
	protected $helper;

	public function __construct(Helper $helper)
	{
		$this->helper = $helper;
	}

	public function index()
	{
		$today = date('Y-m-d');
		$users = User::where("role_id",3)->where("deleted",0)->orderBy('id', 'DESC')->get();
		$totalStudents = $users->count();
		$paidUsers = $freeUsers = $trialUsers = 0;
		if (!empty($users)) {
			foreach ($users as $user) {
				$subscription = UserSubscription::where("user_id", $user->id)->where("end_date", ">=", $today)->where("paymentStatus", 1)->orderBy('id', 'DESC')->first();
				$userSubscription = !empty($subscription) ? 1 : 0;
				$subscriptionMode = !empty($subscription) ? $subscription->mode : 'FREE';
				if($userSubscription==1 && $subscriptionMode!='FREE') {
					$paidUsers++;
				}
				if($userSubscription==0) {
					$freeUsers++;
				}
				if($userSubscription==1 && $subscriptionMode=='FREE') {
					$trialUsers++;
				}
			}
		}

		return view('admin.tracking.index', compact('totalStudents','paidUsers','freeUsers','trialUsers'));
	}

	public function users_list(Request $request)
	{
		if (! Gate::allows('tracking')) {
			return abort(401);
		}
		if(!isset($request->overview) && !isset($request->days)) {
			return redirect()->route('admin.tracking.users_list', ['overview'=>'all']);
		}
		$schools = User::select("school_college")->where("school_college","!=","")->groupBy("school_college")->get();
		$classes = StudentClass::where('status',1)->where('deleted',0)->orderBy('id', 'ASC')->get();
		$states = State::orderBy('state', 'ASC')->get();
		$cities = City::orderBy('city', 'ASC')->get();
		$subscriptions = Subscription::where('status',1)->where('deleted',0)->orderBy('name', 'ASC')->get();
		$franchises = Franchise::get();

		$today = date('Y-m-d');
		$paidUserIds = $freeUserIds = $trialUserIds = [];
		$users = User::where("role_id",3)->where("deleted",0)->orderBy('id', 'DESC')->get();
		if (!empty($users)) {
			foreach ($users as $user) {
				$subscription = UserSubscription::where("user_id", $user->id)->where("end_date", ">=", $today)->where("paymentStatus", 1)->orderBy('id', 'DESC')->first();
				$userSubscription = !empty($subscription) ? 1 : 0;
				$subscriptionMode = !empty($subscription) ? $subscription->mode : 'FREE';
				// if($userSubscription==1 && $subscriptionMode!='FREE') {
				// 	$paidUserIds[] = $user->id;
				// }
				if($userSubscription==0) {
					$freeUserIds[] = $user->id;
				}
				// if($userSubscription==1 && $subscriptionMode=='FREE') {
				// 	$trialUserIds[] = $user->id;
				// }
			}
		}

		$user_id = ($request->user_id) ? $request->user_id : '';
		$gender = ($request->gender) ? $request->gender : '';
		$school = ($request->school) ? $request->school : '';
		$class = ($request->class) ? $request->class : '';
		$state = ($request->state) ? $request->state : '';
		$city = ($request->city) ? $request->city : '';
		$plan_id = ($request->plan_id) ? $request->plan_id : '';
		$refer_code = ($request->refer_code) ? $request->refer_code : '';
		$from = ($request->from) ? $request->from : '';
		$to = ($request->to) ? $request->to.' 23:59:59' : date('Y-m-d H:i:s');
		$subscription = ($request->overview) ? $request->overview : '';

		$users = User::orderBy("id", "DESC")
			 ->Where(function($query) use ($user_id) {
				if (isset($user_id) && !empty($user_id)) {
					$query->where("id",$user_id);
				}
			 })
			 ->Where(function($query) use ($gender) {
				if (isset($gender) && !empty($gender)) {
					$query->where("gender",$gender);
				}
			 })
			 ->Where(function($query) use ($school) {
				if (isset($school) && !empty($school)) {
					$query->where("school_college",$school);
				}
			 })
			 ->Where(function($query) use ($class) {
				if (isset($class) && !empty($class)) {
					$query->where("class_id",$class);
				}
			 })
			 ->Where(function($query) use ($state) {
				if (isset($state) && !empty($state)) {
					$query->where("state",$state);
				}
			 })
			 ->Where(function($query) use ($city) {
				if (isset($city) && !empty($city)) {
					$query->where("city",$city);
				}
			 })
			 ->Where(function($query) use ($refer_code) {
				if (isset($refer_code) && !empty($refer_code)) {
					$query->where("refer_code",$refer_code);
				}
			 })
			 ->Where(function($query) use ($from, $to) {
				if (isset($from) && !empty($from)) {
					$query->whereBetween("created_at",[$from, $to]);
				}
			 })
			 ->where("role_id",3)->where("deleted",0);
		
		if (isset($plan_id) && !empty($plan_id)) {	 
			$users->whereHas('subscription_data', function($query) use ($plan_id) {
				if (isset($plan_id) && !empty($plan_id)) {
					$query->where("subscription_id",$plan_id);
				}
			});
		}
		if (isset($subscription) && !empty($subscription)) {	 
			if ($subscription=='paid') {
				$users->whereHas('subscription_data', function($query) use ($subscription) {
					$query->whereDate("end_date", ">=", date('Y-m-d'))->where("mode", "!=", "FREE");
				});
			} elseif ($subscription=='free') {
				$users->Where(function($query) use ($freeUserIds) {
					if (isset($freeUserIds) && !empty($freeUserIds)) {
						$query->whereIn("id",$freeUserIds);
					}
				});
			} elseif ($subscription=='trial') {
				$users->whereHas('subscription_data', function($query) use ($subscription) {
					$query->whereDate("end_date", ">=", date('Y-m-d'))->where("mode", "FREE");
				});
			}
		}
		$lastDate = '';
		if (!empty($request->days) && $request->days>0) {
			$date = strtotime($today);
			$date = strtotime("-".$request->days." days", $date);
			$lastDate = date('Y-m-d', $date);
			$users->Where(function($query) use ($lastDate) {
				if (isset($lastDate) && !empty($lastDate)) {
					$query->where("updated_at", "<=", $lastDate);
				}
			});
		}
		
		$totalResult = $users->count();
		$users = $users->paginate(50);
		
		/*if (!empty($request->overview) && $request->overview=='all') {
			$users = $this->helper->getStudentsList($gender, $school, $class, $state, $city, $refer_code, $from, $to);
		} elseif (!empty($request->overview) && $request->overview=='paid') {
			$users = $this->helper->getStudentsList($gender, $school, $class, $state, $city, $refer_code, $from, $to, '', $paidUserIds);
		} elseif (!empty($request->overview) && $request->overview=='free') {
			$users = $this->helper->getStudentsList($gender, $school, $class, $state, $city, $refer_code, $from, $to, '', $freeUserIds);
		} elseif (!empty($request->overview) && $request->overview=='trial') {
			$users = $this->helper->getStudentsList($gender, $school, $class, $state, $city, $refer_code, $from, $to, '', $trialUserIds);
		} else {
			if (!empty($request->days) && $request->days>0) {
				$date = strtotime($today);
				$date = strtotime("-".$request->days." days", $date);
				$lastDate = date('Y-m-d', $date);
				//$users = User::where("role_id",3)->where("status",1)->where("deleted",0)->where("updated_at", "<=", $lastDate)->orderBy('id', 'DESC')->get();
				$users = $this->helper->getStudentsList($gender, $school, $class, $state, $city, $refer_code, $from, $to, $lastDate);
			}
		}
		//$totalUsers = $users->count();*/

		return view('admin.tracking.users_list', compact('schools','classes','states','cities','subscriptions','franchises','users','totalResult'));
	}

	public function users_wise(Request $request)
	{
		if (! Gate::allows('tracking')) {
			return abort(401);
		}

		$today = date('Y-m-d');
		$paidUserIds = $freeUserIds = $trialUserIds = [];
		$allUsers = User::where("role_id",3)->where("deleted",0)->orderBy('id', 'DESC')->get();
		if (!empty($allUsers)) {
			foreach ($allUsers as $user) {
				$subscription = UserSubscription::where("user_id", $user->id)->where("end_date", ">=", $today)->where("paymentStatus", 1)->orderBy('id', 'DESC')->first();
				$userSubscription = !empty($subscription) ? 1 : 0;
				$subscriptionMode = !empty($subscription) ? $subscription->mode : 'FREE';
				// if($userSubscription==1 && $subscriptionMode!='FREE') {
				// 	$paidUserIds[] = $user->id;
				// }
				if($userSubscription==0) {
					$freeUserIds[] = $user->id;
				}
				// if($userSubscription==1 && $subscriptionMode=='FREE') {
				// 	$trialUserIds[] = $user->id;
				// }
			}
		}

		$schools = User::select("school_college")->where("school_college","!=","")->groupBy("school_college")->get();
		$classes = StudentClass::where('status',1)->where('deleted',0)->orderBy('id', 'ASC')->get();
		$states = State::orderBy('state', 'ASC')->get();
		$cities = City::orderBy('city', 'ASC')->get();
		$franchises = Franchise::get();

		$user_id = ($request->user_id) ? $request->user_id : '';
		$gender = ($request->gender) ? $request->gender : '';
		$school = ($request->school) ? $request->school : '';
		$class = ($request->class) ? $request->class : '';
		$state = ($request->state) ? $request->state : '';
		$city = ($request->city) ? $request->city : '';
		$refer_code = ($request->refer_code) ? $request->refer_code : '';
		$from = ($request->from) ? $request->from : '';
		$to = ($request->to) ? $request->to.' 23:59:59' : date('Y-m-d H:i:s');
		$subscription = ($request->subscription) ? $request->subscription : '';

		$users = User::orderBy("id", "DESC")
				 ->Where(function($query) use ($user_id) {
					if (isset($user_id) && !empty($user_id)) {
						$query->where("id",$user_id);
					}
				 })
				 ->Where(function($query) use ($gender) {
					if (isset($gender) && !empty($gender)) {
						$query->where("gender",$gender);
					}
				 })
				 ->Where(function($query) use ($school) {
					if (isset($school) && !empty($school)) {
						$query->where("school_college",$school);
					}
				 })
				 ->Where(function($query) use ($class) {
					if (isset($class) && !empty($class)) {
						$query->where("class_id",$class);
					}
				 })
				 ->Where(function($query) use ($state) {
					if (isset($state) && !empty($state)) {
						$query->where("state",$state);
					}
				 })
				 ->Where(function($query) use ($city) {
					if (isset($city) && !empty($city)) {
						$query->where("city",$city);
					}
				 })
				 ->Where(function($query) use ($refer_code) {
					if (isset($refer_code) && !empty($refer_code)) {
						$query->where("refer_code",$refer_code);
					}
				 })
				 ->Where(function($query) use ($from, $to) {
					if (isset($from) && !empty($from)) {
						$query->whereBetween("created_at",[$from, $to]);
					}
				 })
			 	 ->where("role_id",3)->where("deleted",0);
			if (isset($subscription) && !empty($subscription)) {	 
				if ($subscription=='paid') {
					$users->whereHas('subscription_data', function($query) use ($subscription) {
						$query->whereDate("end_date", ">=", date('Y-m-d'))->where("mode", "!=", "FREE");
					});
				} elseif ($subscription=='free') {
					$users->Where(function($query) use ($freeUserIds) {
						if (isset($freeUserIds) && !empty($freeUserIds)) {
							$query->whereIn("id",$freeUserIds);
						}
					});
				} elseif ($subscription=='trial') {
					$users->whereHas('subscription_data', function($query) use ($subscription) {
						$query->whereDate("end_date", ">=", date('Y-m-d'))->where("mode", "FREE");
					});
				}
			}
		
		$totalResult = $users->count();
		$users = $users->paginate(50);
		//dd($users);

		return view('admin.tracking.users_wise_list', compact('schools','classes','states','cities','franchises','users'));
	}

	public function users_details(Request $request, $userId)
	{
		if (! Gate::allows('tracking')) {
			return abort(401);
		}
		
		$user = User::where("id",$userId)->first();
		$totalHours = $this->helper->getTrackingTime('All',[0=>$userId]);
		$totalSessions = LoginSession::where('user_id',$userId)->count();

		//Days Coloumn Chart Data
		$from = $to = '';
		$today = date('Y-m-d');
		$days_arr = [];
		if (isset($from) && !empty($from)) {
			$startTime = strtotime( $from );
			$endTime = strtotime( $to );
			// Loop between timestamps, 24 hours at a time
			for ( $i = $startTime; $i <= $endTime; $i = $i + 86400 ) {
				$thisDate = date( 'Y-m-d', $i ); // 2010-05-01, 2010-05-02, etc
				$data['day'] = date('j M l',strtotime($thisDate));
				$data['used_time'] = $this->helper->getDateWiseTime($userId, $thisDate);
				if ($today >= $thisDate) {
					array_push($days_arr, $data);
				}
			}
		} else {
			$date = date('F Y');//Current Month Year
			$data['day'] = date('j M l',strtotime(date('Y-m-01')));
			$data['used_time'] = $this->helper->getDateWiseTime($userId, date('Y-m-01'));
			while (strtotime($date) < strtotime(date('Y-m') . '-' . date('t', strtotime($date)))) {
				$date = date("Y-m-d", strtotime("+1 day", strtotime($date)));//Adds 1 day onto current date
				$day_num = date('j', strtotime($date));//Day number
				if ($day_num==2) {
					array_push($days_arr, $data);
				}
				$data['day'] = date('j M l',strtotime($date));
				$data['used_time'] = $this->helper->getDateWiseTime($userId, $date);
				if ($today >= $date) {
					array_push($days_arr, $data);
				}
			}
		}
		//Module Pie Chart Data
		$data_arr = [];
		$modules = UserTracking::select("module")->where("module","!=","")->groupBy("module")->get();
		if (!empty($modules)) {
			foreach ($modules as $module) {
				$moduledata['module_name'] = ucwords(str_replace("_", " ", strtolower($module->module)));
				$moduledata['module_time'] = number_format($this->helper->getModuleWiseTime([0=>$userId], $module->module, $from, $to)/60,2);
				array_push($data_arr, $moduledata);
			}
		}
		$last3days = $last7days = $last10days = $last20days = $last30days = 0;
		$last3days = $this->helper->getTrackingTimeDaysWise($userId, 3);
		$last7days = $this->helper->getTrackingTimeDaysWise($userId, 7);
		$last10days = $this->helper->getTrackingTimeDaysWise($userId, 10);
		$last20days = $this->helper->getTrackingTimeDaysWise($userId, 20);
		$last30days = $this->helper->getTrackingTimeDaysWise($userId, 30);
		//dd($data_arr);

		$module = ($request->module) ? $request->module : '';
		$device_type = ($request->device_type) ? $request->device_type : '';
		$from = ($request->from) ? $request->from : '';
		$to = ($request->to) ? $request->to.' 23:59:59' : date('Y-m-d H:i:s');

		//Days Line Chart Data
		$linechart_arr = [];
		if (isset($from) && !empty($from)) {
			$startTime = strtotime( $from );
			$endTime = strtotime( $to );
			// Loop between timestamps, 24 hours at a time
			for ( $i = $startTime; $i <= $endTime; $i = $i + 86400 ) {
				$thisDate = date( 'Y-m-d', $i ); // 2010-05-01, 2010-05-02, etc
				$linechart_data['day'] = date('j M l',strtotime($thisDate));
				$linechart_data['used_time'] = $this->helper->getDateWiseTime($userId, $thisDate, $module, $device_type);
				if ($today >= $thisDate) {
					array_push($linechart_arr, $linechart_data);
				}
			}
		} else {
			$date = date('F Y');//Current Month Year
			$linechart_data['day'] = date('j M l',strtotime(date('Y-m-01')));
			$linechart_data['used_time'] = $this->helper->getDateWiseTime($userId, date('Y-m-01'), $module, $device_type);
			while (strtotime($date) < strtotime(date('Y-m') . '-' . date('t', strtotime($date)))) {
				$date = date("Y-m-d", strtotime("+1 day", strtotime($date)));//Adds 1 day onto current date
				$day_num = date('j', strtotime($date));//Day number
				if ($day_num==2) {
					array_push($linechart_arr, $linechart_data);
				}
				$linechart_data['day'] = date('j M l',strtotime($date));
				$linechart_data['used_time'] = $this->helper->getDateWiseTime($userId, $date, $module, $device_type);
				if ($today >= $date) {
					array_push($linechart_arr, $linechart_data);
				}
			}
		}

		$userTopCourses = [];
		//$userTopCourses = ContinueStudy::where('user_id',$userId)->where('course_id','>',0)->get();
		$courseComplete = ContinueStudy::select("course_id", DB::raw('SUM(video_total_time) as total_time') , DB::raw('SUM(video_viewed_time) as total_viewed'))
			->where('user_id',$userId)->where('course_id','>',0)
            ->groupBy('course_id')
            ->get();
		foreach ($courseComplete as $key => $val) {
			$courseData['course_name'] = !empty($val->course_data) ? $val->course_data->name : '';
			$courseData['duration'] = round($val['total_viewed'] / 60000);
			$courseData['progress'] = ($val['total_viewed'] > 0) ? round((($val['total_viewed'] / $val['total_time']) * 100),2) : 0;
			array_push($userTopCourses, $courseData);
		}
		$progress = array_column($userTopCourses, 'progress');
		array_multisort($progress, SORT_ASC, $userTopCourses);
		
		$questionAsked = QuestionAsk::where('user_id',$userId)->where("status",1)->count();
		$questionAnswered = QuestionAnswer::where('user_id',$userId)->where("status",1)->count();
		$chatGroupsJoined = ChatGroupUser::where('user_id',$userId)->where("block_status",0)->count();
		$postEntries = PostEntry::where('user_id',$userId)->where("status",1)->get();
		$assignmentSubmitted = AssignmentSubmission::where('user_id',$userId)->where("user_status",1)->get();
		$quizCertificates = StudentExam::where('user_id',$userId)->where("is_complete",1)->whereNotNull("certificate")->get();
		$userExams = StudentExam::where('user_id',$userId)->where("is_complete",1)->orderBy("id","DESC")->get();
		
		/*$userTracking = UserTracking::orderBy('id', 'DESC')
			 ->where("user_id",$userId)
			 ->Where(function($query) use ($module) {
				if (isset($module) && !empty($module)) {
					$query->where("module",$module);
				}
			 })
			 ->Where(function($query) use ($device_type) {
				if (isset($device_type) && !empty($device_type)) {
					$query->where("device_type",$device_type);
				}
			 })
			 ->Where(function($query) use ($from, $to) {
				  if (isset($from) && !empty($from)) {
						$query->whereBetween("created_at",[$from, $to]);
				  }
			 })->paginate(50);*/

		return view('admin.tracking.users_details', compact('user','totalHours','totalSessions','days_arr','data_arr','last3days','last7days','last10days','last20days','last30days','linechart_arr','userTopCourses','questionAsked','questionAnswered','chatGroupsJoined','postEntries','assignmentSubmitted','quizCertificates','userExams'));
	}

	public function hours_wise()
	{
		$totalHours = $paidUserHours = $freeUserHours = $trialUserHours = 0;
		$totalHours = $this->helper->getTrackingTime('All')/60;
		$paidUserHours = $this->helper->getTrackingTime('Paid')/60;
		$freeUserHours = $this->helper->getTrackingTime('Free')/60;
		$trialUserHours = $this->helper->getTrackingTime('Trial')/60;
		/*$today = date('Y-m-d');
		$users = User::where("role_id",3)->where("isLogin", 1)->where("status",1)->where("deleted",0)->orderBy('id', 'DESC')->get();
		if (!empty($users)) {
			foreach ($users as $user) {
				$subscription = UserSubscription::where("user_id", $user->id)->where("end_date", ">=", $today)->where("paymentStatus", 1)->orderBy('id', 'DESC')->first();
				$userSubscription = !empty($subscription) ? 1 : 0;
				$subscriptionMode = !empty($subscription) ? $subscription->mode : 'FREE';
				if($userSubscription==1 && $subscriptionMode!='FREE') {
					$paidUserIds[] = $user->id;
					$paidUserHours = $this->helper->getTrackingTime($paidUserIds)/60;
				}
				if($userSubscription==0) {
					$freeUserIds[] = $user->id;
					$freeUserHours = $this->helper->getTrackingTime($freeUserIds)/60;
				}
				if($userSubscription==1 && $subscriptionMode=='FREE') {
					$trialUserIds[] = $user->id;
					$trialUserHours = $this->helper->getTrackingTime($trialUserIds)/60;
				}
			}
		}*/

		return view('admin.tracking.hours_wise', compact('totalHours','paidUserHours','freeUserHours','trialUserHours'));
	}

	public function hours_wise_users_list(Request $request)
	{
		if (! Gate::allows('tracking')) {
			return abort(401);
		}
		/*$to = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', '2021-12-11 10:20:33');
		$from = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', '2021-12-11 10:50:56');
		$diff_in_hours = $to->diffInHours($from);
		print_r($diff_in_hours);die;*/

		$schools = User::select("school_college")->where("school_college","!=","")->groupBy("school_college")->get();
		$classes = StudentClass::where('status',1)->where('deleted',0)->orderBy('id', 'ASC')->get();
		$states = State::orderBy('state', 'ASC')->get();
		$cities = City::orderBy('city', 'ASC')->get();
		$franchises = Franchise::get();

		$overview = ($request->overview) ? ucfirst($request->overview) : 'All';

		$user_id = ($request->user_id) ? $request->user_id : '';
		$gender = ($request->gender) ? $request->gender : '';
		$school = ($request->school) ? $request->school : '';
		$class = ($request->class) ? $request->class : '';
		$state = ($request->state) ? $request->state : '';
		$city = ($request->city) ? $request->city : '';
		$plan_id = ($request->plan_id) ? $request->plan_id : '';
		$refer_code = ($request->refer_code) ? $request->refer_code : '';
		$from = ($request->from) ? $request->from : '';
		$to = ($request->to) ? $request->to.' 23:59:59' : date('Y-m-d H:i:s');
		$subscription = ($request->overview) ? $request->overview : '';
		$month = ($request->month) ? $request->month : '';

		/*$users = User::where("role_id",3)->where("status",1)->where("deleted",0)->orderBy('id', 'DESC')->get();
		$today = date('Y-m-d');
		$paidUserIds = $freeUserIds = $trialUserIds = [];*/
		/*$time_start = microtime(true); 

		//sample script
		for($i=0; $i<1000; $i++){
		 //do anything
		}

		$time_end = microtime(true);
		$execution_time = ($time_end - $time_start);
		echo '<b>Total Execution Time:</b> '.number_format($execution_time, 10).'seconds'; die;*/
		/*$totalUsers = $users->count();
		if (!empty($users)) {
			foreach ($users as $user) {
				$subscription = UserSubscription::where("user_id", $user->id)->where("end_date", ">=", $today)->where("paymentStatus", 1)->orderBy('id', 'DESC')->first();
				$userSubscription = !empty($subscription) ? 1 : 0;
				$subscriptionMode = !empty($subscription) ? $subscription->mode : 'FREE';
				if($userSubscription==1 && $subscriptionMode!='FREE') {
					$paidUserIds[] = $user->id;
				}
				if($userSubscription==0) {
					$freeUserIds[] = $user->id;
				}
				if($userSubscription==1 && $subscriptionMode=='FREE') {
					$trialUserIds[] = $user->id;
				}
			}
		}*/
		$userTracking = $users = $userIds = $allUserIds = [];
		$totalHours = $onWebsite = $onAndroid = $onIos = $totalSessions = 0;
		if ($overview!='All') {
			$userTracking = UserTracking::select('user_id')
				 ->Where(function($query) use ($from, $to) {
					if (isset($from) && !empty($from)) {
						$query->whereBetween("created_at",[$from, $to]);
					}
				 })
				 ->Where(function($query) use ($month) {
					if (isset($month) && !empty($month)) {
						$query->whereMonth('created_at', '=', $month);
					}
				 })->where('subscription_status',$overview)->whereNotNull('out_time')->orderBy('id', 'DESC')->groupBy('user_id')->get();
		} else {
			$userTracking = UserTracking::select('user_id')
				 ->Where(function($query) use ($from, $to) {
					if (isset($from) && !empty($from)) {
						$query->whereBetween("created_at",[$from, $to]);
					}
				 })
				 ->Where(function($query) use ($month) {
					if (isset($month) && !empty($month)) {
						$query->whereMonth('created_at', '=', $month);
					}
				 })->whereNotNull('out_time')->orderBy('id', 'DESC')->groupBy('user_id')->get();
		}
		if (!empty($userTracking)) {
			foreach ($userTracking as $tracking) {
				$userIds[] = $tracking->user_id;
			}
			$users = User::whereIn('id',$userIds)
				 ->Where(function($query) use ($user_id) {
					if (isset($user_id) && !empty($user_id)) {
						$query->where("id",$user_id);
					}
				 })
				 ->Where(function($query) use ($gender) {
					if (isset($gender) && !empty($gender)) {
						$query->where("gender",$gender);
					}
				 })
				 ->Where(function($query) use ($school) {
					if (isset($school) && !empty($school)) {
						$query->where("school_college",$school);
					}
				 })
				 ->Where(function($query) use ($class) {
					if (isset($class) && !empty($class)) {
						$query->where("class_id",$class);
					}
				 })
				 ->Where(function($query) use ($state) {
					if (isset($state) && !empty($state)) {
						$query->where("state",$state);
					}
				 })
				 ->Where(function($query) use ($city) {
					if (isset($city) && !empty($city)) {
						$query->where("city",$city);
					}
				 })
				 ->Where(function($query) use ($refer_code) {
					if (isset($refer_code) && !empty($refer_code)) {
						$query->where("refer_code",$refer_code);
					}
				 })
				 ->where("role_id",3)->where("deleted",0);
			
			if (isset($plan_id) && !empty($plan_id)) {	 
				$users->whereHas('subscription_data', function($query) use ($plan_id) {
					if (isset($plan_id) && !empty($plan_id)) {
						$query->where("subscription_id",$plan_id);
					}
				});
			}
			$allUsers = $users->orderBy('id', 'DESC')->get();
			foreach ($allUsers as $allUser) {
				$allUserIds[] = $allUser->id;
			}
			$users = $users->orderBy('id', 'DESC')->paginate(50);
			if (!empty($allUserIds)) {
				//dd($allUserIds);
				$totalHours = $this->helper->getTrackingTime($overview,$allUserIds)/60;
				$onWebsite = $this->helper->getTrackingTimeUser($overview,'website',$allUserIds);
				$onAndroid = $this->helper->getTrackingTimeUser($overview,'android',$allUserIds);
				$onIos = $this->helper->getTrackingTimeUser($overview,'ios',$allUserIds);
				$totalSessions = LoginSession::whereIn('user_id',$allUserIds)->count();
			}
		}
		/*if (!empty($request->overview) && $request->overview=='all') {
			$onWebsite = $this->helper->getTrackingTimeUser('all','website');
			$onAndroid = $this->helper->getTrackingTimeUser('all','android');
			$onIos = $this->helper->getTrackingTimeUser('all','ios');
			//$userSessions = UserTracking::select('created_at',DB::raw('COUNT(created_at) as count'))->where('module','home')->groupBy('created_at')->get();
		} elseif (!empty($request->overview) && $request->overview=='paid') {
			//$users = User::whereIn('id',$paidUserIds)->orderBy('id', 'DESC')->get();
			//$totalHours = $this->helper->getTrackingTime($paidUserIds)/60;
			$onWebsite = $this->helper->getTrackingTimeUser($paidUserIds,'website');
			$onAndroid = $this->helper->getTrackingTimeUser($paidUserIds,'android');
			$onIos = $this->helper->getTrackingTimeUser($paidUserIds,'ios');
			//$userSessions = LoginSession::whereIn('user_id',$paidUserIds)->get();
		} elseif (!empty($request->overview) && $request->overview=='free') {
			//$users = User::whereIn('id',$freeUserIds)->orderBy('id', 'DESC')->get();
			//$totalHours = $this->helper->getTrackingTime($freeUserIds)/60;
			$onWebsite = $this->helper->getTrackingTimeUser($freeUserIds,'website');
			$onAndroid = $this->helper->getTrackingTimeUser($freeUserIds,'android');
			$onIos = $this->helper->getTrackingTimeUser($freeUserIds,'ios');
			//$userSessions = LoginSession::whereIn('user_id',$freeUserIds)->get();
		} elseif (!empty($request->overview) && $request->overview=='trial') {
			//$users = User::whereIn('id',$trialUserIds)->orderBy('id', 'DESC')->get();
			//$totalHours = $this->helper->getTrackingTime($trialUserIds)/60;
			$onWebsite = $this->helper->getTrackingTimeUser($trialUserIds,'website');
			$onAndroid = $this->helper->getTrackingTimeUser($trialUserIds,'android');
			$onIos = $this->helper->getTrackingTimeUser($trialUserIds,'ios');
			//$userSessions = LoginSession::whereIn('user_id',$trialUserIds)->get();
		} else {
			$users = $userSessions = [];
			$onWebsite = $onAndroid = $onIos = 0;
		}
		$totalSessions = $userSessions->count();*/
		//$totalUsers = $users->count();
		//dd($users);
		//dd($totalUsers);

		return view('admin.tracking.hours_wise_users_list', compact('schools','classes','states','cities','franchises','totalHours','onWebsite','onAndroid','onIos','users','totalSessions'));
	}

	public function hours_wise_user_view(Request $request)
	{
		if (! Gate::allows('tracking')) {
			return abort(401);
		}

		$userId = $request->id;
		$user = User::findOrfail($userId);
		$totalHours = $this->helper->getTrackingTime('All',[0=>$user->id]);
		$onWebsite = $this->helper->getTrackingTimeUser($user->id,'website');
		$onAndroid = $this->helper->getTrackingTimeUser($user->id,'android');
		$onIos = $this->helper->getTrackingTimeUser($user->id,'ios');
		$totalSessions = LoginSession::where('user_id',$userId)->count();
		//$userSessions = UserTracking::where('user_id',$userId)->groupBy('created_at')->orderBy('id', 'DESC')->get();
		/*$userSessions = UserTracking::select('created_at',DB::raw('COUNT(created_at) as count'))
			->where('user_id',$userId)
			->where('module','home')
			->groupBy('created_at')
			->get();*/
		//$totalSessions = $userSessions->count();
		/*$userSessions = UserTracking::select('created_at',DB::raw('COUNT(created_at) as count'))
			->where('user_id',$userId)
			->get()
			->groupBy(function($date) {
				return Carbon::parse($date->created_at)->format('Y-m-d'); // grouping by years
				//return Carbon::parse($date->created_at)->format('m'); // grouping by months
			});
		$totalSessions = 0;
		foreach ($userSessions as $userSesn) {
			foreach ($userSesn as $userSess) {
				//echo '<pre />'; print_r($userSess);
				$totalSessions += $userSess->count;
			}
		}//die;*/
		//dd($user);
		//dd($totalHours);

		return view('admin.tracking.hours_wise_user_view', compact('user','totalHours','onWebsite','onAndroid','onIos','totalSessions'));
	}

	public function days_wise(Request $request)
	{
		$status = ($request->status) ? $request->status : '';
		$from = ($request->from) ? $request->from : '';
		$to = ($request->to) ? $request->to.' 23:59:59' : date('Y-m-d H:i:s');
		$month = ($request->month) ? $request->month : '';
		
		//if ($month == '') {
			$allUsersMonday      = $this->helper->getTrackingDaywise('monday', $status, $from, $to, $month)/60;
			$allUsersTuesday     = $this->helper->getTrackingDaywise('tuesday', $status, $from, $to, $month)/60;
			$allUsersWednesday   = $this->helper->getTrackingDaywise('wednesday', $status, $from, $to, $month)/60;
			$allUsersThursday    = $this->helper->getTrackingDaywise('thursday', $status, $from, $to, $month)/60;
			$allUsersFriday      = $this->helper->getTrackingDaywise('friday', $status, $from, $to, $month)/60;
			$allUsersSaturday    = $this->helper->getTrackingDaywise('saturday', $status, $from, $to, $month)/60;
			$allUsersSunday      = $this->helper->getTrackingDaywise('sunday', $status, $from, $to, $month)/60;

			$paidUsersMonday     = $this->helper->getTrackingDaywise('monday', $status, $from, $to, $month, 'Paid')/60;
			$paidUsersTuesday    = $this->helper->getTrackingDaywise('tuesday', $status, $from, $to, $month, 'Paid')/60;
			$paidUsersWednesday  = $this->helper->getTrackingDaywise('wednesday', $status, $from, $to, $month, 'Paid')/60;
			$paidUsersThursday   = $this->helper->getTrackingDaywise('thursday', $status, $from, $to, $month, 'Paid')/60;
			$paidUsersFriday     = $this->helper->getTrackingDaywise('friday', $status, $from, $to, $month, 'Paid')/60;
			$paidUsersSaturday   = $this->helper->getTrackingDaywise('saturday', $status, $from, $to, $month, 'Paid')/60;
			$paidUsersSunday     = $this->helper->getTrackingDaywise('sunday', $status, $from, $to, $month, 'Paid')/60;

			$freeUsersMonday     = $this->helper->getTrackingDaywise('monday', $status, $from, $to, $month, 'Free')/60;
			$freeUsersTuesday    = $this->helper->getTrackingDaywise('tuesday', $status, $from, $to, $month, 'Free')/60;
			$freeUsersWednesday  = $this->helper->getTrackingDaywise('wednesday', $status, $from, $to, $month, 'Free')/60;
			$freeUsersThursday   = $this->helper->getTrackingDaywise('thursday', $status, $from, $to, $month, 'Free')/60;
			$freeUsersFriday     = $this->helper->getTrackingDaywise('friday', $status, $from, $to, $month, 'Free')/60;
			$freeUsersSaturday   = $this->helper->getTrackingDaywise('saturday', $status, $from, $to, $month, 'Free')/60;
			$freeUsersSunday     = $this->helper->getTrackingDaywise('sunday', $status, $from, $to, $month, 'Free')/60;

			$trialUsersMonday    = $this->helper->getTrackingDaywise('monday', $status, $from, $to, $month, 'Trial')/60;
			$trialUsersTuesday   = $this->helper->getTrackingDaywise('tuesday', $status, $from, $to, $month, 'Trial')/60;
			$trialUsersWednesday = $this->helper->getTrackingDaywise('wednesday', $status, $from, $to, $month, 'Trial')/60;
			$trialUsersThursday  = $this->helper->getTrackingDaywise('thursday', $status, $from, $to, $month, 'Trial')/60;
			$trialUsersFriday    = $this->helper->getTrackingDaywise('friday', $status, $from, $to, $month, 'Trial')/60;
			$trialUsersSaturday  = $this->helper->getTrackingDaywise('saturday', $status, $from, $to, $month, 'Trial')/60;
			$trialUsersSunday    = $this->helper->getTrackingDaywise('sunday', $status, $from, $to, $month, 'Trial')/60;
		/*} else {
			$allUsersMonday      = $this->helper->getTrackingDaywise('monday')/60;
			$allUsersTuesday     = $this->helper->getTrackingDaywise('tuesday')/60;
			$allUsersWednesday   = $this->helper->getTrackingDaywise('wednesday')/60;
			$allUsersThursday    = $this->helper->getTrackingDaywise('thursday')/60;
			$allUsersFriday      = $this->helper->getTrackingDaywise('friday')/60;
			$allUsersSaturday    = $this->helper->getTrackingDaywise('saturday')/60;
			$allUsersSunday      = $this->helper->getTrackingDaywise('sunday')/60;

			$paidUsersMonday     = $this->helper->getTrackingDaywise('monday', '', 'Paid')/60;
			$paidUsersTuesday    = $this->helper->getTrackingDaywise('tuesday', '', 'Paid')/60;
			$paidUsersWednesday  = $this->helper->getTrackingDaywise('wednesday', '', 'Paid')/60;
			$paidUsersThursday   = $this->helper->getTrackingDaywise('thursday', '', 'Paid')/60;
			$paidUsersFriday     = $this->helper->getTrackingDaywise('friday', '', 'Paid')/60;
			$paidUsersSaturday   = $this->helper->getTrackingDaywise('saturday', '', 'Paid')/60;
			$paidUsersSunday     = $this->helper->getTrackingDaywise('sunday', '', 'Paid')/60;

			$freeUsersMonday     = $this->helper->getTrackingDaywise('monday', '', 'Free')/60;
			$freeUsersTuesday    = $this->helper->getTrackingDaywise('tuesday', '', 'Free')/60;
			$freeUsersWednesday  = $this->helper->getTrackingDaywise('wednesday', '', 'Free')/60;
			$freeUsersThursday   = $this->helper->getTrackingDaywise('thursday', '', 'Free')/60;
			$freeUsersFriday     = $this->helper->getTrackingDaywise('friday', '', 'Free')/60;
			$freeUsersSaturday   = $this->helper->getTrackingDaywise('saturday', '', 'Free')/60;
			$freeUsersSunday     = $this->helper->getTrackingDaywise('sunday', '', 'Free')/60;

			$trialUsersMonday    = $this->helper->getTrackingDaywise('monday', '', 'Trial')/60;
			$trialUsersTuesday   = $this->helper->getTrackingDaywise('tuesday', '', 'Trial')/60;
			$trialUsersWednesday = $this->helper->getTrackingDaywise('wednesday', '', 'Trial')/60;
			$trialUsersThursday  = $this->helper->getTrackingDaywise('thursday', '', 'Trial')/60;
			$trialUsersFriday    = $this->helper->getTrackingDaywise('friday', '', 'Trial')/60;
			$trialUsersSaturday  = $this->helper->getTrackingDaywise('saturday', '', 'Trial')/60;
			$trialUsersSunday    = $this->helper->getTrackingDaywise('sunday', '', 'Trial')/60;
		}*/

		$data = compact('allUsersMonday','allUsersTuesday','allUsersWednesday','allUsersThursday','allUsersFriday','allUsersSaturday','allUsersSunday','paidUsersMonday','paidUsersTuesday','paidUsersWednesday','paidUsersThursday','paidUsersFriday','paidUsersSaturday','paidUsersSunday','freeUsersMonday','freeUsersTuesday','freeUsersWednesday','freeUsersThursday','freeUsersFriday','freeUsersSaturday','freeUsersSunday','trialUsersMonday','trialUsersTuesday','trialUsersWednesday','trialUsersThursday','trialUsersFriday','trialUsersSaturday','trialUsersSunday');
		return view('admin.tracking.days_wise', $data);
	}

	public function days_wise_users_list(Request $request, $overview='all', $day='monday')
	{
		if (! Gate::allows('tracking')) {
			return abort(401);
		}

		if ($day=='monday') {
			$betweenDay = 0;
		} elseif ($day=='tuesday') {
			$betweenDay = 1;
		} elseif ($day=='wednesday') {
			$betweenDay = 2;
		} elseif ($day=='thursday') {
			$betweenDay = 3;
		} elseif ($day=='friday') {
			$betweenDay = 4;
		} elseif ($day=='saturday') {
			$betweenDay = 5;
		} elseif ($day=='sunday') {
			$betweenDay = 6;
		} else {
			$betweenDay = 0;
		}

		$month = ($request->month) ? $request->month : '';
		$userId = $request->user;
		if (!empty($day)) {
			$userTracking = UserTracking::orderBy('id', 'DESC')
				 ->Where(function($query) use ($overview) {
					if (isset($overview) && !empty($overview) && $overview!='all') {
						$query->where("subscription_status",$overview);
					}
				 })
				 ->Where(function($query) use ($userId) {
					if (isset($userId) && !empty($userId)) {
						$query->where("user_id",$userId);
					}
				 })
				 ->Where(function($query) use ($month) {
					if (isset($month) && !empty($month)) {
						$query->whereMonth("created_at", "=", $month);
					}
				 })
				 ->whereRaw('WEEKDAY(in_time) = '.$betweenDay)->whereNotNull('out_time');
			$allTrackingData = $userTracking->get();
			$totalStudents = $userTracking->count();
			$userTracking = $userTracking->paginate(50);
		}

		$day = ucfirst($day);
		//$today = date('Y-m-d');
		//$paidUserIds = $freeUserIds = $trialUserIds = [];
		$users = User::where("role_id",3)->where("status",1)->where("deleted",0)->orderBy('id', 'DESC')->get();
		/*if (!empty($users)) {
			foreach ($users as $user) {
				$subscription = UserSubscription::where("user_id", $user->id)->where("end_date", ">=", $today)->where("paymentStatus", 1)->orderBy('id', 'DESC')->first();
				$userSubscription = !empty($subscription) ? 1 : 0;
				$subscriptionMode = !empty($subscription) ? $subscription->mode : 'FREE';
				if($userSubscription==1 && $subscriptionMode!='FREE') {
					$paidUserIds[] = $user->id;
				}
				if($userSubscription==0) {
					$freeUserIds[] = $user->id;
				}
				if($userSubscription==1 && $subscriptionMode=='FREE') {
					$trialUserIds[] = $user->id;
				}
			}
		}
		if ($overview=='paid') {
			$userTracking = UserTracking::orderBy('id', 'DESC')
				 ->Where(function($query) use ($userId) {
					  if (isset($userId) && !empty($userId)) {
							$query->where("user_id",$userId);
					  }
				 })
				 ->Where(function($query) use ($month) {
					if (isset($month) && !empty($month)) {
						$query->whereMonth("created_at", "=", $month);
					}
				 })
				 ->whereIn('user_id',$paidUserIds)->get();
		} elseif ($overview=='free') {
			$userTracking = UserTracking::orderBy('id', 'DESC')
				 ->Where(function($query) use ($userId) {
					  if (isset($userId) && !empty($userId)) {
							$query->where("user_id",$userId);
					  }
				 })
				 ->Where(function($query) use ($month) {
					if (isset($month) && !empty($month)) {
						$query->whereMonth("created_at", "=", $month);
					}
				 })
				 ->whereIn('user_id',$freeUserIds)->get();
		} elseif ($overview=='trial') {
			$userTracking = UserTracking::orderBy('id', 'DESC')
				 ->Where(function($query) use ($userId) {
					  if (isset($userId) && !empty($userId)) {
							$query->where("user_id",$userId);
					  }
				 })
				 ->Where(function($query) use ($month) {
					if (isset($month) && !empty($month)) {
						$query->whereMonth("created_at", "=", $month);
					}
				 })
				 ->whereIn('user_id',$trialUserIds)->get();
		} else {
			$userTracking = UserTracking::orderBy('id', 'DESC')
				 ->Where(function($query) use ($userId) {
					if (isset($userId) && !empty($userId)) {
						$query->where("user_id",$userId);
					}
				 })
				 ->Where(function($query) use ($month) {
					if (isset($month) && !empty($month)) {
						$query->whereMonth("created_at", "=", $month);
					}
				 })->get();
		}*/
		$onAll = $onAndroid = $onWebsite = $onIos = 0;
		//$onAll = $this->helper->getTrackingDaywise('monday');
		if (!empty($allTrackingData)) {
			foreach ($allTrackingData as $tracking) {
				if (!empty($tracking->out_time) && $tracking->out_time!='') {
					if (Carbon::parse($tracking->in_time)->format('l')==ucfirst($day)) {
						$onAll += $this->helper->getTrackingTimeRow($tracking->id);
						if ($tracking->device_type=='android') {
							$onAndroid += $this->helper->getTrackingTimeRow($tracking->id);
						}
						if ($tracking->device_type=='website') {
							$onWebsite += $this->helper->getTrackingTimeRow($tracking->id);
						}
						if ($tracking->device_type=='ios') {
							$onIos += $this->helper->getTrackingTimeRow($tracking->id);
						}
					}
				}
			}
			$onAll = number_format(($onAll/60),2);
			$onAndroid = number_format(($onAndroid/60),2);
			$onWebsite = number_format(($onWebsite/60),2);
			$onIos = number_format(($onIos/60),2);
		}

		return view('admin.tracking.days_wise_users_list', compact('overview','day','users','userTracking','onAll','onAndroid','onWebsite','onIos'));
	}

	public function days_wise_chart(Request $request)
	{
		if (! Gate::allows('tracking')) {
			return abort(401);
		}
		if(!isset($request->overview) && !isset($request->days)) {
			return redirect()->route('admin.tracking.days_wise_chart', ['overview'=>'all']);
		}

		$schools = User::select("school_college")->where("school_college","!=","")->groupBy("school_college")->get();
		$classes = StudentClass::where('status',1)->where('deleted',0)->orderBy('id', 'ASC')->get();
		$states = State::orderBy('state', 'ASC')->get();
		$cities = City::orderBy('city', 'ASC')->get();

		$gender = ($request->gender) ? $request->gender : '';
		$school = ($request->school) ? $request->school : '';
		$class = ($request->class) ? $request->class : '';
		$state = ($request->state) ? $request->state : '';
		$city = ($request->city) ? $request->city : '';
		$refer_code = ($request->refer_code) ? $request->refer_code : '';
		$from = ($request->from) ? $request->from : '';
		$to = ($request->to) ? $request->to.' 23:59:59' : date('Y-m-d H:i:s');

		$today = date('Y-m-d');
		$paidUserIds = $freeUserIds = $trialUserIds = $users = $userIds = $userTracking = [];
		$totalUsers = 0;
		$allUsers = User::where("role_id",3)->where("status",1)->where("deleted",0)->orderBy('id', 'DESC')->get();
		if (!empty($allUsers)) {
			foreach ($allUsers as $user) {
				$subscription = UserSubscription::where("user_id", $user->id)->where("end_date", ">=", $today)->where("paymentStatus", 1)->orderBy('id', 'DESC')->first();
				$userSubscription = !empty($subscription) ? 1 : 0;
				$subscriptionMode = !empty($subscription) ? $subscription->mode : 'FREE';
				if($userSubscription==1 && $subscriptionMode!='FREE') {
					$paidUserIds[] = $user->id;
				}
				if($userSubscription==0) {
					$freeUserIds[] = $user->id;
				}
				if($userSubscription==1 && $subscriptionMode=='FREE') {
					$trialUserIds[] = $user->id;
				}
			}
		}
		if (!empty($request->overview) && $request->overview=='all') {
			$users = $this->helper->getStudentsList($gender, $school, $class, $state, $city, $refer_code, '', '');
		} elseif (!empty($request->overview) && $request->overview=='paid' && !empty($paidUserIds)) {
			$users = $this->helper->getStudentsList($gender, $school, $class, $state, $city, $refer_code, '', '', '', $paidUserIds);
		} elseif (!empty($request->overview) && $request->overview=='free' && !empty($freeUserIds)) {
			$users = $this->helper->getStudentsList($gender, $school, $class, $state, $city, $refer_code, '', '', '', $freeUserIds);
		} elseif (!empty($request->overview) && $request->overview=='trial' && !empty($trialUserIds)) {
			$users = $this->helper->getStudentsList($gender, $school, $class, $state, $city, $refer_code, '', '', '', $trialUserIds);
		} else {
			if (!empty($request->days) && $request->days>0) {
				$date = strtotime($today);
				$date = strtotime("-".$request->days." days", $date);
				$lastDate = date('Y-m-d', $date);
				$users = $this->helper->getStudentsList($gender, $school, $class, $state, $city, $refer_code, '', '', $lastDate);
			}
		}
		$days_arr = [];
		if (!empty($users)) {
			$totalUsers = $users->count();
			foreach ($users as $user) {
				$userIds[] = $user->id;
			}
			//Graph Data
			if (isset($from) && !empty($from)) {
				$startTime = strtotime( $from );
				$endTime = strtotime( $to );
				// Loop between timestamps, 24 hours at a time
				for ( $i = $startTime; $i <= $endTime; $i = $i + 86400 ) {
					$thisDate = date( 'Y-m-d', $i ); // 2010-05-01, 2010-05-02, etc
					$data['day'] = date('j M l',strtotime($thisDate));
					$data['user'] = UserTracking::distinct()->whereIn('user_id',$userIds)->whereDate('created_at',$thisDate)->orderBy('id', 'DESC')->count('user_id');
					if ($today >= $thisDate) {
						array_push($days_arr, $data);
					}
				}
			} else {
				$date = date('F Y');//Current Month Year
				$data['day'] = date('j M l',strtotime(date('Y-m-01')));
				$data['user'] = UserTracking::distinct()->whereIn('user_id',$userIds)->whereDate('created_at',date('Y-m-01'))->orderBy('id', 'DESC')->count('user_id');
				while (strtotime($date) < strtotime(date('Y-m') . '-' . date('t', strtotime($date)))) {
					$date = date("Y-m-d", strtotime("+1 day", strtotime($date)));//Adds 1 day onto current date
					$day_num = date('j', strtotime($date));//Day number
					if ($day_num==2) {
						array_push($days_arr, $data);
					}
					$data['day'] = date('j M l',strtotime($date));
					$data['user'] = UserTracking::distinct()->whereIn('user_id',$userIds)->whereDate('created_at',$date)->orderBy('id', 'DESC')->count('user_id');
					if ($today >= $date) {
						array_push($days_arr, $data);
					}
				}
			}
		}

		return view('admin.tracking.days_wise_chart', compact('schools','classes','states','cities','users','totalUsers','days_arr'));
	}

	public function day_hours_wise_chart(Request $request)
	{
		if (! Gate::allows('tracking')) {
			return abort(401);
		}
		if(!isset($request->overview) && !isset($request->days)) {
			return redirect()->route('admin.tracking.day_hours_wise_chart', ['overview'=>'all']);
		}

		$schools = User::select("school_college")->where("school_college","!=","")->groupBy("school_college")->get();
		$classes = StudentClass::where('status',1)->where('deleted',0)->orderBy('id', 'ASC')->get();
		$states = State::orderBy('state', 'ASC')->get();
		$cities = City::orderBy('city', 'ASC')->get();

		$gender = ($request->gender) ? $request->gender : '';
		$school = ($request->school) ? $request->school : '';
		$class = ($request->class) ? $request->class : '';
		$state = ($request->state) ? $request->state : '';
		$city = ($request->city) ? $request->city : '';
		$refer_code = ($request->refer_code) ? $request->refer_code : '';
		$from = ($request->from) ? $request->from.' 00:00:00' : date('Y-m-d').' 00:00:00';
		$to = ($request->from) ? $request->from.' 23:59:59' : date('Y-m-d H:i:s');

		$today = date('Y-m-d');
		$paidUserIds = $freeUserIds = $trialUserIds = $users = $userIds = $userTracking = [];
		$totalUsers = 0;
		$allUsers = User::where("role_id",3)->where("status",1)->where("deleted",0)->orderBy('id', 'DESC')->get();
		if (!empty($allUsers)) {
			foreach ($allUsers as $user) {
				$subscription = UserSubscription::where("user_id", $user->id)->where("end_date", ">=", $today)->where("paymentStatus", 1)->orderBy('id', 'DESC')->first();
				$userSubscription = !empty($subscription) ? 1 : 0;
				$subscriptionMode = !empty($subscription) ? $subscription->mode : 'FREE';
				if($userSubscription==1 && $subscriptionMode!='FREE') {
					$paidUserIds[] = $user->id;
				}
				if($userSubscription==0) {
					$freeUserIds[] = $user->id;
				}
				if($userSubscription==1 && $subscriptionMode=='FREE') {
					$trialUserIds[] = $user->id;
				}
			}
		}
		if (!empty($request->overview) && $request->overview=='all') {
			$users = $this->helper->getStudentsList($gender, $school, $class, $state, $city, $refer_code, '', '');
		} elseif (!empty($request->overview) && $request->overview=='paid' && !empty($paidUserIds)) {
			$users = $this->helper->getStudentsList($gender, $school, $class, $state, $city, $refer_code, '', '', '', $paidUserIds);
		} elseif (!empty($request->overview) && $request->overview=='free' && !empty($freeUserIds)) {
			$users = $this->helper->getStudentsList($gender, $school, $class, $state, $city, $refer_code, '', '', '', $freeUserIds);
		} elseif (!empty($request->overview) && $request->overview=='trial' && !empty($trialUserIds)) {
			$users = $this->helper->getStudentsList($gender, $school, $class, $state, $city, $refer_code, '', '', '', $trialUserIds);
		} else {
			if (!empty($request->days) && $request->days>0) {
				$date = strtotime($today);
				$date = strtotime("-".$request->days." days", $date);
				$lastDate = date('Y-m-d', $date);
				$users = $this->helper->getStudentsList($gender, $school, $class, $state, $city, $refer_code, '', '', $lastDate);
			}
		}
		$days_arr = [];
		if (!empty($users)) {
			$totalUsers = $users->count();
			foreach ($users as $user) {
				$userIds[] = $user->id;
			}
			//Graph Data
			if (isset($from) && !empty($from)) {
				$startTime = strtotime( $from );
				$endTime = strtotime( $to );
				// Loop between timestamps, 24 hours at a time
				for ( $i = 0; $i < $endTime - $startTime; $i += 3600 ) {
					$thisDate = $from; //date( 'Y-m-d', $i ); // 2010-05-01, 2010-05-02, etc
					$fromTime = date('H:i', $startTime + $i);
					$toTime = strtotime("+60 minutes", strtotime($fromTime));
					$toTime = date('H:i', $toTime);
					$data['day'] = $fromTime.' - '.$toTime; //date('j M l',strtotime($thisDate));
					$data['user'] = UserTracking::distinct()->whereIn('user_id',$userIds)->whereDate('created_at',$thisDate)->whereTime('created_at', '>=', $fromTime)->whereTime('created_at', '<=', $toTime)->orderBy('id', 'DESC')->count('user_id');
					//if ($today >= $thisDate) {
						array_push($days_arr, $data);
					//}
				}
				//dd($days_arr);
			}
		}

		return view('admin.tracking.day_hours_wise_chart', compact('schools','classes','states','cities','users','totalUsers','days_arr'));
	}

	public function module_wise_chart(Request $request)
	{
		if (! Gate::allows('tracking')) {
			return abort(401);
		}
		if(!isset($request->overview) && !isset($request->days)) {
			return redirect()->route('admin.tracking.module_wise_chart', ['overview'=>'all']);
		}

		$schools = User::select("school_college")->where("school_college","!=","")->groupBy("school_college")->get();
		$classes = StudentClass::where('status',1)->where('deleted',0)->orderBy('id', 'ASC')->get();
		$states = State::orderBy('state', 'ASC')->get();
		$cities = City::orderBy('city', 'ASC')->get();

		$today = date('Y-m-d');
		$paidUserIds = $freeUserIds = $trialUserIds = $users = $userIds = $userTracking = [];
		$totalUsers = 0;
		$allUsers = User::select('id')->where("role_id",3)->where("status",1)->where("deleted",0)->orderBy('id', 'DESC')->get();
		if (!empty($allUsers)) {
			foreach ($allUsers as $user) {
				$subscription = UserSubscription::where("user_id", $user->id)->where("end_date", ">=", $today)->where("paymentStatus", 1)->orderBy('id', 'DESC')->first();
				$userSubscription = !empty($subscription) ? 1 : 0;
				$subscriptionMode = !empty($subscription) ? $subscription->mode : 'FREE';
				/*if($userSubscription==1 && $subscriptionMode!='FREE') {
					$paidUserIds[] = $user->id;
				}*/
				if($userSubscription==0) {
					$freeUserIds[] = $user->id;
				}
				/*if($userSubscription==1 && $subscriptionMode=='FREE') {
					$trialUserIds[] = $user->id;
				}*/
			}
		}

		$user_id = ($request->user_id) ? $request->user_id : '';
		$gender = ($request->gender) ? $request->gender : '';
		$school = ($request->school) ? $request->school : '';
		$class = ($request->class) ? $request->class : '';
		$state = ($request->state) ? $request->state : '';
		$city = ($request->city) ? $request->city : '';
		$refer_code = ($request->refer_code) ? $request->refer_code : '';
		$from = ($request->from) ? $request->from : '';
		$to = ($request->to) ? $request->to.' 23:59:59' : date('Y-m-d H:i:s');
		$subscription = ($request->overview) ? $request->overview : '';

		$users = User::select('id')->orderBy('id', 'DESC')
			 ->Where(function($query) use ($user_id) {
				if (isset($user_id) && !empty($user_id)) {
					$query->where("id",$user_id);
				}
			 })
			 ->Where(function($query) use ($gender) {
				if (isset($gender) && !empty($gender)) {
					$query->where("gender",$gender);
				}
			 })
			 ->Where(function($query) use ($school) {
				if (isset($school) && !empty($school)) {
					$query->where("school_college",$school);
				}
			 })
			 ->Where(function($query) use ($class) {
				if (isset($class) && !empty($class)) {
					$query->where("class_id",$class);
				}
			 })
			 ->Where(function($query) use ($state) {
				if (isset($state) && !empty($state)) {
					$query->where("state",$state);
				}
			 })
			 ->Where(function($query) use ($city) {
				if (isset($city) && !empty($city)) {
					$query->where("city",$city);
				}
			 })
			 ->Where(function($query) use ($refer_code) {
				if (isset($refer_code) && !empty($refer_code)) {
					$query->where("refer_code",$refer_code);
				}
			 })
			 ->where("role_id",3)->where("deleted",0);

		if (isset($subscription) && !empty($subscription)) {	 
			if ($subscription=='paid') {
				$users->whereHas('subscription_data', function($query) use ($subscription) {
					$query->whereDate("end_date", ">=", date('Y-m-d'))->where("mode", "!=", "FREE");
				});
			} elseif ($subscription=='free') {
				$users->Where(function($query) use ($freeUserIds) {
					if (isset($freeUserIds) && !empty($freeUserIds)) {
						$query->whereIn("id",$freeUserIds);
					}
				});
			} elseif ($subscription=='trial') {
				$users->whereHas('subscription_data', function($query) use ($subscription) {
					$query->whereDate("end_date", ">=", date('Y-m-d'))->where("mode", "FREE");
				});
			}
		}
		$users = $users->get();

		/*if (!empty($request->overview) && $request->overview=='all') {
			$users = $this->helper->getStudentsList($gender, $school, $class, $state, $city, $refer_code, '', '');
		} elseif (!empty($request->overview) && $request->overview=='paid' && !empty($paidUserIds)) {
			$users = $this->helper->getStudentsList($gender, $school, $class, $state, $city, $refer_code, '', '', '', $paidUserIds);
		} elseif (!empty($request->overview) && $request->overview=='free' && !empty($freeUserIds)) {
			$users = $this->helper->getStudentsList($gender, $school, $class, $state, $city, $refer_code, '', '', '', $freeUserIds);
		} elseif (!empty($request->overview) && $request->overview=='trial' && !empty($trialUserIds)) {
			$users = $this->helper->getStudentsList($gender, $school, $class, $state, $city, $refer_code, '', '', '', $trialUserIds);
		} else {
			if (!empty($request->days) && $request->days>0) {
				$date = strtotime($today);
				$date = strtotime("-".$request->days." days", $date);
				$lastDate = date('Y-m-d', $date);
				$users = $this->helper->getStudentsList($gender, $school, $class, $state, $city, $refer_code, '', '', $lastDate);
			}
		}*/
		if (!empty($users)) {
			$totalUsers = $users->count();
			foreach ($users as $user) {
				$userIds[] = $user->id;
			}
			//Graph Data
			$modules = UserTracking::select("module")->where("module","!=","")->groupBy("module")->get();
			$data_arr = [];
			/*if (isset($from) && !empty($from)) {
				foreach ($modules as $module) {
					$data['module'] = $module->module;
					$data['userCount'] = UserTracking::distinct()->whereIn('user_id',$userIds)->where('module',$module->module)->whereBetween('created_at',[$from, $to])->orderBy('id', 'DESC')->count('user_id');
					array_push($data_arr, $data);
				}
			} else {*/
				foreach ($modules as $module) {
					$data['module'] = ucwords(str_replace("_", " ", strtolower($module->module)));
					$data['userCount'] = UserTracking::distinct()->whereIn('user_id',$userIds)->where('module',$module->module)
						 ->Where(function($query) use ($from, $to) {
							if (isset($from) && !empty($from)) {
								$query->whereBetween("created_at",[$from, $to]);
							}
						 })->orderBy('id', 'DESC')->count('user_id');
					$data['module_time'] = $this->helper->getModuleWiseTime($userIds, $module->module, $from, $to);
					array_push($data_arr, $data);
				}
			//}
		}

		return view('admin.tracking.module_wise_chart', compact('schools','classes','states','cities','users','totalUsers','data_arr'));
	}

	public function top_modules(Request $request, $module='course', $watchedType='screen')
	{
		$schools = User::select("school_college")->where("school_college","!=","")->groupBy("school_college")->get();
		$states = State::orderBy('state', 'ASC')->get();
		$cities = City::orderBy('city', 'ASC')->get();
		$courses = Courses::where('deleted', 0)->orderBy('sort_id', 'ASC')->get();
		$lessons = Lession::where('deleted', 0)->orderBy('sort_id', 'ASC')->get();

		$user_type = ($request->user_type) ? ucfirst($request->user_type) : 'All';
		$school = ($request->school) ? $request->school : '';
		$state = ($request->state) ? $request->state : '';
		$city = ($request->city) ? $request->city : '';
		$courseId = ($request->course) ? $request->course : 0;
		$lessonId = ($request->lesson) ? $request->lesson : 0;
		if ($courseId == 0) {
			if ($lessonId > 0) {
				$getLesson = Lession::select('courseId')->where("id", $lessonId)->first();
				$courseId = $getLesson->courseId;
			}
		}
		$from = ($request->from) ? $request->from : '';
		$to = ($request->to) ? $request->to.' 23:59:59' : date('Y-m-d H:i:s');
		$moduleIds = [];
		$userIds = [];
		$users = User::orderBy('id', 'DESC')
			 ->Where(function($query) use ($school) {
				if (isset($school) && !empty($school)) {
					$query->where("school_college",$school);
				}
			 })
			 ->Where(function($query) use ($state) {
				if (isset($state) && !empty($state)) {
					$query->where("state",$state);
				}
			 })
			 ->Where(function($query) use ($city) {
				if (isset($city) && !empty($city)) {
					$query->where("city",$city);
				}
			 })
			 ->where("role_id",3)->where("deleted",0)->get();
		if (!empty($users)) {
			foreach ($users as $user) {
				$userIds[] = $user->id;
			}
		}

		if ($watchedType!='video') {
			$watchedType = '';
			if ($lessonId > 0) {
				$getTopics = Chapter::select('id')->where("courseId", $courseId)->where("lessionId", $lessonId)->where('deleted', 0)->orderBy("id", "DESC")->get();
				if (!empty($getTopics)) {
					foreach ($getTopics as $topic) {
						$moduleIds[] = $topic->id;
					}
					$topModulesData = UserTracking::select('id','module_id',DB::raw('COUNT(module_id) as count'))
						 ->Where(function($query) use ($user_type) {
							if (isset($user_type) && !empty($user_type) && $user_type!='All') {
								$query->where("subscription_status",$user_type);
							}
						 })
						 ->Where(function($query) use ($userIds, $school, $state, $city) {
							if (isset($userIds) && !empty($userIds) && !empty($school) || !empty($state) || !empty($city)) {
								$query->whereIn("user_id",$userIds);
							}
						 })
						 ->Where(function($query) use ($from, $to) {
							if (isset($from) && !empty($from)) {
								$query->whereBetween("created_at",[$from, $to]);
							}
						 })
						 ->whereIn('module_id',$moduleIds)->where('module',$module)->where('module_id','>','0')->where('watch_type','!=','video')->groupBy('module_id')->orderBy('count', 'DESC')->get();
				}
			} elseif ($courseId > 0) {
				$getLessons = Lession::select('id')->where("courseId", $courseId)->where('deleted', 0)->orderBy("id", "DESC")->get();
				if (!empty($getLessons)) {
					foreach ($getLessons as $lesson) {
						$moduleIds[] = $lesson->id;
					}
					$topModulesData = UserTracking::select('id','module_id',DB::raw('COUNT(module_id) as count'))
						 ->Where(function($query) use ($user_type) {
							if (isset($user_type) && !empty($user_type) && $user_type!='All') {
								$query->where("subscription_status",$user_type);
							}
						 })
						 ->Where(function($query) use ($userIds, $school, $state, $city) {
							if (isset($userIds) && !empty($userIds) && !empty($school) || !empty($state) || !empty($city)) {
								$query->whereIn("user_id",$userIds);
							}
						 })
						 ->Where(function($query) use ($from, $to) {
							if (isset($from) && !empty($from)) {
								$query->whereBetween("created_at",[$from, $to]);
							}
						 })
						 ->whereIn('module_id',$moduleIds)->where('module',$module)->where('module_id','>','0')->where('watch_type','!=','video')->groupBy('module_id')->orderBy('count', 'DESC')->get();
				}
			} else {
				$topModulesData = UserTracking::select('id','module_id',DB::raw('COUNT(module_id) as count'))
					 ->Where(function($query) use ($user_type) {
						if (isset($user_type) && !empty($user_type) && $user_type!='All') {
							$query->where("subscription_status",$user_type);
						}
					 })
					 ->Where(function($query) use ($userIds, $school, $state, $city) {
						if (isset($userIds) && !empty($userIds) && !empty($school) || !empty($state) || !empty($city)) {
							$query->whereIn("user_id",$userIds);
						}
					 })
					 ->Where(function($query) use ($from, $to) {
						if (isset($from) && !empty($from)) {
							$query->whereBetween("created_at",[$from, $to]);
						}
					 })
					 ->where('module',$module)->where('module_id','>','0')->where('watch_type','!=','video')->groupBy('module_id')->orderBy('count', 'DESC')->get();
			}
		} else {
			if ($lessonId > 0) {
				$getTopics = Chapter::select('id')->where("courseId", $courseId)->where("lessionId", $lessonId)->where('deleted', 0)->orderBy("id", "DESC")->get();
				if (!empty($getTopics)) {
					foreach ($getTopics as $topic) {
						$moduleIds[] = $topic->id;
					}
					$topModulesData = UserTracking::select('id','module_id',DB::raw('COUNT(module_id) as count'))
						 ->Where(function($query) use ($user_type) {
							if (isset($user_type) && !empty($user_type) && $user_type!='All') {
								$query->where("subscription_status",$user_type);
							}
						 })
						 ->Where(function($query) use ($userIds, $school, $state, $city) {
							if (isset($userIds) && !empty($userIds) && !empty($school) || !empty($state) || !empty($city)) {
								$query->whereIn("user_id",$userIds);
							}
						 })
						 ->Where(function($query) use ($from, $to) {
							if (isset($from) && !empty($from)) {
								$query->whereBetween("created_at",[$from, $to]);
							}
						 })
						 ->whereIn('module_id',$moduleIds)->where('module',$module)->where('module_id','>','0')->where('watch_type','video')->groupBy('module_id')->orderBy('count', 'DESC')->get();
				}
			} elseif ($courseId > 0) {
				$getLessons = Lession::select('id')->where("courseId", $courseId)->where('deleted', 0)->orderBy("id", "DESC")->get();
				if (!empty($getLessons)) {
					foreach ($getLessons as $lesson) {
						$moduleIds[] = $lesson->id;
					}
					$topModulesData = UserTracking::select('id','module_id',DB::raw('COUNT(module_id) as count'))
						 ->Where(function($query) use ($user_type) {
							if (isset($user_type) && !empty($user_type) && $user_type!='All') {
								$query->where("subscription_status",$user_type);
							}
						 })
						 ->Where(function($query) use ($userIds, $school, $state, $city) {
							if (isset($userIds) && !empty($userIds) && !empty($school) || !empty($state) || !empty($city)) {
								$query->whereIn("user_id",$userIds);
							}
						 })
						 ->Where(function($query) use ($from, $to) {
							if (isset($from) && !empty($from)) {
								$query->whereBetween("created_at",[$from, $to]);
							}
						 })
						 ->whereIn('module_id',$moduleIds)->where('module',$module)->where('module_id','>','0')->where('watch_type','video')->groupBy('module_id')->orderBy('count', 'DESC')->get();
				}
			} else {
				$topModulesData = UserTracking::select('id','module_id',DB::raw('COUNT(module_id) as count'))
					 ->Where(function($query) use ($user_type) {
						if (isset($user_type) && !empty($user_type) && $user_type!='All') {
							$query->where("subscription_status",$user_type);
						}
					 })
					 ->Where(function($query) use ($userIds, $school, $state, $city) {
						if (isset($userIds) && !empty($userIds) && !empty($school) || !empty($state) || !empty($city)) {
							$query->whereIn("user_id",$userIds);
						}
					 })
					 ->Where(function($query) use ($from, $to) {
						if (isset($from) && !empty($from)) {
							$query->whereBetween("created_at",[$from, $to]);
						}
					 })
					 ->where('module',$module)->where('module_id','>','0')->where('watch_type','video')->groupBy('module_id')->orderBy('count', 'DESC')->get();
			}
		}
		/*if (!empty($topModulesData)) {
			dd($topModulesData);
		}*/

		return view('admin.tracking.top_modules', compact('schools','states','cities','courses','lessons','module','watchedType','topModulesData'));
	}

	public function top_quiz(Request $request)
	{
		$courses = Courses::where('deleted', 0)->orderBy('sort_id', 'ASC')->get();
		$lessons = Lession::where('deleted', 0)->orderBy('sort_id', 'ASC')->get();
		$topics  = Chapter::where('deleted', 0)->orderBy('sort_id', 'ASC')->get();
		$courseId = ($request->course) ? $request->course : 0;
		$lessonId = ($request->lesson) ? $request->lesson : 0;
		$topicId = ($request->topic) ? $request->topic : 0;
		$from = ($request->from) ? $request->from : '';
		$to = ($request->to) ? $request->to.' 23:59:59' : date('Y-m-d H:i:s');

		$quizIds = [];
		if ($courseId > 0) {
			$getQuiz = Quiz::select('id')->where("courseId", $courseId)->where("lessionId", $lessonId)->where("topicId", $topicId)->where('deleted', 0)->orderBy("id", "DESC")->get();
			if (!empty($getQuiz)) {
				foreach ($getQuiz as $quiz) {
					$quizIds[] = $quiz->id;
				}
			}
		}
		if ($topicId!=0 || $lessonId!=0 || $courseId!=0) {
			$topQuizData = UserTracking::select('id','module_id',DB::raw('COUNT(module_id) as count'))
				 ->Where(function($query) use ($from, $to) {
					if (isset($from) && !empty($from)) {
						$query->whereBetween("created_at",[$from, $to]);
					}
				 })
				 ->whereIn('module_id',$quizIds)->where('module','quiz')->where('module_id','>','0')->where('watch_type','!=','video')->groupBy('module_id')->orderBy('count', 'DESC')->get();
		} else {
			$topQuizData = UserTracking::select('id','module_id',DB::raw('COUNT(module_id) as count'))
				 ->Where(function($query) use ($from, $to) {
					if (isset($from) && !empty($from)) {
						$query->whereBetween("created_at",[$from, $to]);
					}
				 })
				 ->where('module','quiz')->where('module_id','>','0')->where('watch_type','!=','video')->groupBy('module_id')->orderBy('count', 'DESC')->get();
		}

		return view('admin.tracking.top_quiz', compact('courses','lessons','topics','topQuizData'));
	}

	public function top_assignment(Request $request)
	{
		$courses = Courses::where('deleted',0)->orderBy('sort_id', 'ASC')->get();
		$courseId = ($request->course) ? $request->course : 0;
		$from = ($request->from) ? $request->from : '';
		$to = ($request->to) ? $request->to.' 23:59:59' : date('Y-m-d H:i:s');

		$assignmentIds = [];
		if ($courseId > 0) {
			$assignments = Assignment::select('id')->where("course_id", $courseId)->where('deleted', 0)->orderBy('id', 'DESC')->get();
			if (!empty($assignments)) {
				foreach ($assignments as $assignment) {
					$assignmentIds[] = $assignment->id;
				}
			}
			$topAssignmentData = UserTracking::select('id','module_id',DB::raw('COUNT(module_id) as count'))
				 ->Where(function($query) use ($from, $to) {
					if (isset($from) && !empty($from)) {
						$query->whereBetween("created_at",[$from, $to]);
					}
				 })
				 ->whereIn('module_id',$assignmentIds)->where('module','assignment')->where('module_id','>','0')->where('watch_type','!=','video')->groupBy('module_id')->orderBy('count', 'DESC')->get();
		} else {
			$topAssignmentData = UserTracking::select('id','module_id',DB::raw('COUNT(module_id) as count'))
				 ->Where(function($query) use ($from, $to) {
					if (isset($from) && !empty($from)) {
						$query->whereBetween("created_at",[$from, $to]);
					}
				 })
				 ->where('module','assignment')->where('module_id','>','0')->where('watch_type','!=','video')->groupBy('module_id')->orderBy('count', 'DESC')->get();
		}

		return view('admin.tracking.top_assignment', compact('courses','topAssignmentData'));
	}

	public function loggedin()
	{
		$userLoggedIn = User::where("role_id",3)->where("isLogin",1)->where("status",1)->where("deleted",0)->count();
		$webLoggedIn = User::where("role_id",3)->where("deviceType","website")->where("isLogin",1)->where("status",1)->where("deleted",0)->count();
		$androidLoggedIn = User::where("role_id",3)->where("deviceType","android")->where("isLogin",1)->where("status",1)->where("deleted",0)->count();
		$iosLoggedIn = User::where("role_id",3)->where("deviceType","ios")->where("isLogin",1)->where("status",1)->where("deleted",0)->count();

		return view('admin.tracking.loggedin', compact('userLoggedIn','webLoggedIn','androidLoggedIn','iosLoggedIn'));
	}

}