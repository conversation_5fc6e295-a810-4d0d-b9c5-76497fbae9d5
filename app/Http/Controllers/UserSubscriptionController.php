<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\CouponCode;
use App\Models\Franchise;
use App\Models\StudentClass;
use App\Models\Subscription;
use App\Models\UserSubscription;
use App\Models\User;
include public_path().'/razorpay-php/Razorpay.php';
use Razorpay\Api\Api;


class UserSubscriptionController extends Controller
{
	/**
	 * Display a listing of the resource.
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function index(Request $request)
	{
		//get paid amount from RazorPay
		/*$userSubscriptions = UserSubscription::where("txn_id", "!=", "")->where("amount", 0)->orderBy('id', 'DESC')->get();
		foreach ($userSubscriptions as $key => $value) {
			$api = new Api(env('RAZOR_KEY'), env('RAZOR_SECRET'));
			$payment = $api->payment->fetch($value['txn_id']);
			if (isset($payment)) {
				$paymentStatus = isset($payment['status']) ? $payment['status'] : 'failed';
				if ($paymentStatus!='failed') {
					$amount = isset($payment['amount']) ? $payment['amount'] : 0;
					$subPrice = $amount / 100;
					$update = UserSubscription::where("id", $value['id'])->update(array("amount"=>$subPrice));
				}
			}
		}*/
		//echo '<pre />'; print_r($payment['amount']); die;
		$users = User::where("role_id", 3)->where("deleted", 0)->orderBy("name", "ASC")->get();
		$classes = StudentClass::where('status',1)->where('deleted',0)->orderBy('id', 'ASC')->get();
		$coupons = CouponCode::where("deleted", 0)->get();
		$subscriptions = Subscription::where("deleted", 0)->get();

		$userId = ($request->user) ? $request->user : '';
		$class  = ($request->class_id) ? $request->class_id : '';
		$plan 	= ($request->plan) ? $request->plan : '';
		$coupon = ($request->coupon) ? $request->coupon : '';
		$from   = ($request->from) ? $request->from : '';
		$to     = ($request->to) ? $request->to.' 23:59:59' : date('Y-m-d H:i:s');
		$remaining_days = ($request->remaining_days) ? (int)$request->remaining_days : '';
		$mode   = ($request->mode) ? $request->mode : '';
		$subscription = ($request->subscription) ? $request->subscription : '';

		$end_date = '';
		if (!empty($remaining_days)) {
			$today = date('Y-m-d');
			$date = strtotime($today);
			$date = strtotime("+".$remaining_days." days", $date);
			$end_date = date('Y-m-d', $date);
		}

		$data = UserSubscription::with("user")->orderBy("id", "DESC")
			 ->Where(function($query) use ($userId) {
				if (isset($userId) && !empty($userId)) {
					$query->where("user_id", $userId);
				}
			 })
			 ->whereHas("user", function($query) use ($class) {
				if (isset($class) && !empty($class)) {
					$query->where("class_id", $class);
				}
			 })
			 ->Where(function($query) use ($plan) {
				if (isset($plan) && !empty($plan)) {
					$query->where("subscription_id", $plan);
				}
			 })
			 ->Where(function($query) use ($coupon) {
				if (isset($coupon) && !empty($coupon)) {
					$query->where("coupon_code", $coupon);
				}
			 })
			 ->Where(function($query) use ($from, $to) {
				if (isset($from) && !empty($from)) {
					$query->whereBetween("start_date", [$from, $to]);
				}
			 })
			 ->Where(function($query) use ($end_date) {
				if (isset($end_date) && !empty($end_date)) {
					$query->where("end_date", "<=", $end_date);
				}
			 })
			 ->Where(function($query) use ($mode) {
				if (isset($mode) && !empty($mode)) {
					$query->where("mode", $mode);
				}
			 })
			 ->Where(function($query) use ($subscription) {
				if (isset($subscription) && !empty($subscription)) {
					if ($subscription=='trial') {
						$query->where("mode", "FREE");
					} elseif ($subscription=='paid') {
						$query->where("end_date", ">=", date('Y-m-d'))->where("mode", "!=", "FREE");
					} elseif ($subscription=='expired') {
						$query->where("end_date", "<", date('Y-m-d'));
					}
				} else {
					//$query->where("mode", "!=", "FREE");
				}
			 })
			 ->where("paymentStatus", 1);
		$totalResult = $data->count();
		$data = $data->paginate(50);
		$filterUrl = url()->full();

		return view('admin.usersubscriptions.index', compact('users','classes','coupons','subscriptions','data','totalResult','filterUrl'));
	}
	
	/**
	 * Show the form for creating a new resource.
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function create()
	{
		$users = User::where("role_id", 3)->where("status", 1)->where("deleted", 0)->get();
		$subscriptions = Subscription::where("status", 1)->where('deleted',0)->get();

		return view('admin.usersubscriptions.create', compact('users','subscriptions'));
	}

	/**
	 * Store a newly created resource in storage.
	 *
	 * @param  \Illuminate\Http\Request $request
	 * @return \Illuminate\Http\Response
	 */
	public function store(Request $request)
	{
		//echo '<pre />'; print_r($request->all()); die;
		$validator = Validator::make($request->all(), [
			'user_id' => 'required',
			'subscription_id' => 'required',
		]);

		if ($validator->fails()) {
			return back()
				->withErrors($validator)
				->withInput();
		}else { 
			//echo '<pre />'; print_r($request->all()); die;
			$subscriptionId = $request->get('subscription_id');
			$getSubscription = Subscription::where("id", $subscriptionId)->first();
			$month = !empty($getSubscription->month) ? $getSubscription->month : 0;
			$today = date('Y-m-d');
			$end_date = date('Y-m-d', strtotime('+'.$month.' months'));
			$userSubscription = new UserSubscription();
			$userSubscription->user_id = $request->get('user_id');
			$userSubscription->subscription_id = $request->get('subscription_id');
			$userSubscription->start_date = $today;
			$userSubscription->end_date = $end_date;
			$userSubscription->mode = 'Admin';
			$userSubscription->paymentStatus = 1;
			$userSubscription->save();
			$userSubscriptionId = $userSubscription->id;

			\Session::flash('msg', 'User Subscription Added Successfully.');
			//return back();
			return redirect(base64_decode($request->filterUrl));
		}
	}

	/**
	 * Display the specified resource.
	 *
	 * @param  \App\Models\UserSubscription $usersubscriptions
	 * @return \Illuminate\Http\Response
	 */
	public function show(UserSubscription $usersubscriptions)
	{
		//
	}

	/**
	 * Show the form for editing the specified resource.
	 *
	 * @param  \App\Models\UserSubscription $usersubscriptions
	 * @return \Illuminate\Http\Response
	 */
	public function edit(UserSubscription $usersubscriptions, $id)
	{
		$data = UserSubscription::findOrFail($id);
		$users = User::where("role_id", 3)->where("status", 1)->where("deleted", 0)->get();
		$subscriptions = Subscription::where("status", 1)->where('deleted',0)->get();

		return view('admin.usersubscriptions.edit',compact('data','users','subscriptions'));
	}

	/**
	 * Update the specified resource in storage.
	 *
	 * @param  \Illuminate\Http\Request $request
	 * @param  \App\Models\UserSubscription $usersubscriptions
	 * @return \Illuminate\Http\Response
	 */
	public function update(Request $request, UserSubscription $usersubscriptions, $id)
	{
		$validator = Validator::make($request->all(), [
			'user_id' => 'required',
			'subscription_id' => 'required',
			'start_date' => 'required',
			'end_date' => 'required',
		]);

		if ($validator->fails()) {
			return back()
				->withErrors($validator)
				->withInput();
		} else {
			$userSubscription = UserSubscription::findOrFail($id);
			$userSubscription->user_id = $request->get('user_id');
			$userSubscription->subscription_id = $request->get('subscription_id');
			$userSubscription->start_date = date('Y-m-d',strtotime($request->get('start_date')));
			$userSubscription->end_date = date('Y-m-d',strtotime($request->get('end_date')));
			$userSubscription->paymentStatus = 1;
			$userSubscription->save();
			$userSubscriptionId = $userSubscription->id;

			\Session::flash('msg', 'User Subscription Updated Successfully.');
			//return redirect('/admin/usersubscriptions');
			return redirect(base64_decode($request->filterUrl));
		}

	}

	public function updateStatus($id,$status)
	{
		try
		{
			\DB::beginTransaction();
			$userSubscription = UserSubscription::findOrFail($id);
			//dd($userSubscription);
			if ($status==1) {
				$userId = $userSubscription->user_id;
				$paidAmount = $userSubscription->amount;
				$checkUserSubscription = UserSubscription::where("user_id", $userId)->where("mode", "!=", "FREE")->count();
				$user = User::where("id", $userId)->first();
				if (!empty($user->refer_code) && $user->refer_code!='') {
					$franchise = Franchise::where("refer_code", $user->refer_code)->first();
					if (isset($franchise)) {
						$franchise_user_id = $franchise->user_id;
						if ($checkUserSubscription == 1) {
							//if ($paidAmount >= 1500) {
								//echo $franchise->user_id; die;
								//Franchise Student Taken First Time Plan
								$ch = curl_init();
								$url = "https://brainywoodindia.com/public/franchisemanagement/api/sale";
								curl_setopt($ch, CURLOPT_URL,$url);
								curl_setopt($ch, CURLOPT_POST, true);
								curl_setopt($ch, CURLOPT_POSTFIELDS, "user_id=$franchise_user_id&student_name=$user->name&email_address=$user->email&mobile_number=$user->phone&mobile_number=$user->phone&sale_amount=$paidAmount");
								curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
								$output = curl_exec ($ch);

								curl_close ($ch);

								$response = json_decode($output);
								/*echo $response->ResponseCode;
								echo 'sale'. '<pre />'; print_r($response); die; // Show output*/
								if ($response->ResponseCode==400) {
									\Session::flash('msg', $response->Message.' can not update now!');
									return redirect('/admin/usersubscriptions');
								}
								/*$returnFUserId = $response->Data->user_id;
								$returnFRefCode = $response->Data->ref_code;
								$update = User::where("id", $userId)->update(array("franchise_user_id"=>$returnFUserId,"refer_code"=>$returnFRefCode));*/
							// } else {
							// 	\Session::flash('msg', 'Amount is low, can not update it!');
							// 	return redirect('/admin/usersubscriptions');
							// }
						} else {
							//Franchise Student Renewal The Plan
							$ch = curl_init();
							$url = "https://brainywoodindia.com/public/franchisemanagement/api/renewal";
							curl_setopt($ch, CURLOPT_URL,$url);
							curl_setopt($ch, CURLOPT_POST, true);
							curl_setopt($ch, CURLOPT_POSTFIELDS, "user_id=$franchise_user_id&student_name=$user->name&sale_amount=$paidAmount");
							curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
							$output = curl_exec ($ch);

							curl_close ($ch);

							$response = json_decode($output);
							//echo 'renewal'. '<pre />'; print_r($response); die; // Show output
							if ($response->ResponseCode==400) {
								\Session::flash('msg', $response->Message.' can not update now!');
								//\Session::flash('msg', 'Something went wrong, can not update it!');
								return redirect('/admin/usersubscriptions');
							}
						}
					}
				}
			}
			$userSubscription->commission_status=$status;
			$userSubscription->update();
			\DB::commit();
		} catch (Exception $exception) {
			\DB::rollback();
			return back()->withError($exception->getMessage());
		}

		return redirect('/admin/usersubscriptions');
	}

	/**
	 * Remove the specified resource from storage.
	 *
	 * @param  \App\Models\UserSubscription
	 * @return \Illuminate\Http\Response
	 */
	public function delete($id)
	{
		$userSubscription = UserSubscription::findOrFail($id);
		$userSubscription->deleted=1;
		$userSubscription->update();

		return redirect('/admin/usersubscriptions');
	}

    public function getUserSubscriptionsCsv(Request $request)
    {
		$userId = ($request->user) ? $request->user : '';
		$class  = ($request->class_id) ? $request->class_id : '';
		$plan 	= ($request->plan) ? $request->plan : '';
		$coupon = ($request->coupon) ? $request->coupon : '';
		$from   = ($request->from) ? $request->from : '';
		$to     = ($request->to) ? $request->to.' 23:59:59' : date('Y-m-d H:i:s');
		$remaining_days = ($request->remaining_days) ? (int)$request->remaining_days : '';
		$mode   = ($request->mode) ? $request->mode : '';
		$subscription = ($request->subscription) ? $request->subscription : '';

		$end_date = '';
		if (!empty($remaining_days)) {
			$today = date('Y-m-d');
			$date = strtotime($today);
			$date = strtotime("+".$remaining_days." days", $date);
			$end_date = date('Y-m-d', $date);
		}

		$userSubscriptions = UserSubscription::with("user")->orderBy("id", "DESC")
			 ->Where(function($query) use ($userId) {
				if (isset($userId) && !empty($userId)) {
					$query->where("user_id", $userId);
				}
			 })
			 ->whereHas("user", function($query) use ($class) {
				if (isset($class) && !empty($class)) {
					$query->where("class_id", $class);
				}
			 })
			 ->Where(function($query) use ($plan) {
				if (isset($plan) && !empty($plan)) {
					$query->where("subscription_id", $plan);
				}
			 })
			 ->Where(function($query) use ($coupon) {
				if (isset($coupon) && !empty($coupon)) {
					$query->where("coupon_code", $coupon);
				}
			 })
			 ->Where(function($query) use ($from, $to) {
				if (isset($from) && !empty($from)) {
					$query->whereBetween("start_date", [$from, $to]);
				}
			 })
			 ->Where(function($query) use ($end_date) {
				if (isset($end_date) && !empty($end_date)) {
					$query->where("end_date", "<=", $end_date);
				}
			 })
			 ->Where(function($query) use ($mode) {
				if (isset($mode) && !empty($mode)) {
					$query->where("mode", $mode);
				}
			 })
			 ->Where(function($query) use ($subscription) {
				if (isset($subscription) && !empty($subscription)) {
					if ($subscription=='trial') {
						$query->where("mode", "FREE");
					} elseif ($subscription=='paid') {
						$query->where("end_date", ">=", date('Y-m-d'))->where("mode", "!=", "FREE");
					} elseif ($subscription=='expired') {
						$query->where("end_date", "<", date('Y-m-d'));
					}
				} else {
					//$query->where("mode", "!=", "FREE");
				}
			 })
			 ->where("paymentStatus", 1)->groupBy("user_id");
	    //dd($userSubscriptions->count());

	    $headers = array(
	        'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
	        'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
	        'Content-Disposition' => 'attachment; filename=abc.csv',
	        'Expires' => '0',
	        'Pragma' => 'public',
	    );

		$filename = "userSubscriptions_".date('d-M-Y').".csv";
		$handle = fopen($filename, 'w');
		fputcsv($handle, [
		    "#",
		    "UserName - Phone",
		    "User ClassName",
		    "Referred By",
		    "Plan Name",
		    "Month",
		    "Used Coupon Code",
		    "Start Date",
		    "End Date",
		    "Remaining Days",
		    "Payment Mode",
		]);

	    $i = 1;
		$userSubscriptions->chunk(100000, function ($data) use ($handle,$i) {
		    foreach ($data as $row) {
		    	$remaining_days = 0;
            	if(strtotime($row->end_date) > strtotime(date('Y-m-d'))){
            		$remaining_days = round((strtotime($row->end_date) - strtotime(date('Y-m-d'))) / (60 * 60 * 24));
            	}
		        // Add a new row with data
		        fputcsv($handle, [
		            $i,
		            ($row->user_id>0) ? $row->user->name.' - '.$row->user->phone : '',
		            ($row->user->class_id>0) ? $row->user->class_data->class_name : '',
		            ($row->user->franchise_user_id>0) ? $row->user->franchise_data->user_name.' - '.$row->user->franchise_data->refer_code : '',
		            ($row->subscription_id>0) ? $row->subscription->name : '',
		            ($row->subscription_id>0) ? $row->subscription->month : '',
		            $row->coupon_code,
		            date('d-M-y', strtotime($row->start_date)),
					date('d-M-y', strtotime($row->end_date)),
					$remaining_days,
					$row->mode,
		        ]);
	        	$i++;
		    }
		});

		fclose($handle);

		return response()->download($filename, "userSubscriptions_".date('d-M-Y').".csv", $headers);
	}

}
