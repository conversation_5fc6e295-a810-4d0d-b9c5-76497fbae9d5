<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Http\Helper as Helper;
use App\Models\Chapter;
use App\Models\Courses;
use App\Models\Lession;
use App\Models\Notification;
use App\Models\QuestionAsk;
use App\Models\QuestionAnswer;
use App\Models\User;
use App\Models\UserPoint;
use DB;

class QuestionAnswerController extends Controller
{
	protected $helper;

	public function __construct(Helper $helper)
	{
		$this->helper = $helper;
	}
	
	public function index(Request $request)
	{
		$user = Auth::user();
		//dd($user);
		$userId = $user->id;
		$role_id = $user->role_id;

		$course	= ($request->course) ? $request->course : '';
		$lesson = ($request->lesson) ? $request->lesson : '';
		$topic 	= ($request->topic) ? $request->topic : '';
		$from 	= ($request->from) ? $request->from : '';
		$to     = ($request->to) ? $request->to.' 23:59:59' : date('Y-m-d H:i:s');
		$status = isset($request->status) ? ($request->status=='active') ? 1 : 2 : '';

		$data = QuestionAsk::orderBy("id", "DESC")
				 ->Where(function($query) use ($course) {
					if (isset($course) && !empty($course)) {
						$query->where("course_id", $course);
					}
				 })
				 ->Where(function($query) use ($lesson) {
					if (isset($lesson) && !empty($lesson)) {
						$query->where("lession_id", $lesson);
					}
				 })
				 ->Where(function($query) use ($topic) {
					if (isset($topic) && !empty($topic)) {
						$query->where("topic_id", $topic);
					}
				 })
				 ->Where(function($query) use ($from, $to) {
					if (isset($from) && !empty($from)) {
						$query->whereBetween("created_at", [$from, $to]);
					}
				 })
				 ->Where(function($query) use ($status) {
					if (isset($status) && !empty($status)) {
						$query->where("status", $status);
					}
				 })
				 ->where("deleted", 0)->paginate(50);

		if($role_id==2){
			$allowedCourses = $user->allow_courses;
			$allowedCourses = explode(",", trim($allowedCourses));
			$data = QuestionAsk::whereIn("course_id", $allowedCourses)->where("course_id", "!=", 0)->where("deleted",0)->orderBy("id", "DESC")->paginate(50);
		}
		$totalQuestions = count($data);
		$totalAnswers = QuestionAnswer::where("status",1)->count();
		$admin_role_id = 1;
		$teacher_role_id = 2;
		$adminGivenAnswers = QuestionAnswer::with('user')
			->whereHas("user", function($query) use ($admin_role_id) {
				if (isset($admin_role_id) && !empty($admin_role_id)) {
					$query->where("role_id", "!=", 2)->where("role_id", "!=", 3);
				}
			 })->where("status",1)->count();
		$teacherGivenAnswers = QuestionAnswer::with('user')
			->whereHas("user", function($query) use ($teacher_role_id) {
				if (isset($teacher_role_id) && !empty($teacher_role_id)) {
					$query->where("role_id", $teacher_role_id);
				}
			 })->where("status",1)->count();
		if($role_id==2){
			$allowedCourses = $user->allow_courses;
			$allowedCourses = explode(",", trim($allowedCourses));
			$courses = Courses::whereIn("id", $allowedCourses)->where("status",1)->where("deleted",0)->orderBy("sort_id", "ASC")->get();
			$lessions = Lession::whereIn("courseId", $allowedCourses)->where("status",1)->where("deleted",0)->orderBy("sort_id", "ASC")->get();
			$topics = Chapter::whereIn("courseId", $allowedCourses)->where("status",1)->where("deleted",0)->orderBy("sort_id", "ASC")->get();
		}else{
			$courses = Courses::where("status",1)->where("deleted",0)->orderBy("sort_id", "ASC")->get();
			$lessions = Lession::where("status",1)->where("deleted",0)->orderBy("sort_id", "ASC")->get();
			$topics = Chapter::where("status",1)->where("deleted",0)->orderBy("sort_id", "ASC")->get();
		}
		/*$allowedCourses = explode(",", trim($allowedCourses));
		DB::enableQueryLog();
		dd(DB::getQueryLog(Courses::whereIn("id", $allowedCourses)->where("status",1)->where("deleted",0)->orderBy("sort_id", "ASC")->get()));*/
		//dd($courses);
		//echo '<pre />'; print_r($courses); die;

		return view('admin.questionanswers.index', compact('data','totalQuestions','totalAnswers','adminGivenAnswers','teacherGivenAnswers','courses','lessions','topics'));
	}
	
	public function create()
	{
		return view('admin.questionanswers.create');
	}

	public function store(Request $request)
	{
		//echo '<pre />'; print_r($request->all()); die;
		$validator = Validator::make($request->all(), [
			'name' => 'required',
			'video' => 'required',
		]);

		if ($validator->fails()) {
			return back()
				->withErrors($validator)
				->withInput();
		}else { 
			$file = $request->file('video');
			if($file){
				$destinationPath = public_path().'/upload/questionask/';
				$originalFile = $file->getClientOriginalName();
				$filename=strtotime(date('Y-m-d H:isa')).$originalFile;
				$file->move($destinationPath, $filename);
			}
			
			$queAsk = new QuestionAsk();
			$queAsk->name = $request->get('name');
			$queAsk->video = $filename;
			$queAsk->status = 1;
			$queAsk->save();
			$queAskId = $queAsk->id;

			\Session::flash('msg', 'Question Answer Added Successfully.');
			return back();
		}
	}

	public function show(QuestionAsk $questionanswers, $id)
	{
		$data = QuestionAsk::findOrFail($id);
		$answers = QuestionAnswer::where("ques_id", $id)->get();

		return view('admin.questionanswers.view',compact('data','answers'));
	}

	public function edit(QuestionAsk $questionanswers, $id)
	{
		$data = QuestionAsk::findOrFail($id);
		$answers = QuestionAnswer::where("ques_id", $id)->get();

		return view('admin.questionanswers.edit',compact('data','answers'));
	}

	public function update(Request $request, QuestionAsk $questionanswers, $id)
	{
		//echo '<pre />'; print_r($request->all()); die;
		$answers = $request->ans_id;
		if(isset($answers)){
			foreach($answers as $key => $answer){
				$queAns = QuestionAnswer::findOrFail($answer);

				$file = isset($request->file('image')[$key]) ? $request->file('image')[$key] : '';
				if($file){
					$destinationPath = public_path().'/upload/questionask/';
					$originalFile = $file->getClientOriginalName();
					$filename = rand(111,999).strtotime(date('Y-m-d-H:isa')).$originalFile;
					$file->move($destinationPath, $filename);
				}else{
					$filename = $queAns->image;
				}

				$queAns->answer = $request->get('answer')[$key];
				$queAns->image = $filename;
				$queAns->status = 1;
				$queAns->save();
			}
		}

		$queAsk = QuestionAsk::findOrFail($id);

		$file = $request->file('image');
		if($file){
			$destinationPath = public_path().'/upload/questionask/';
			$originalFile = $file->getClientOriginalName();
			$filename = rand(111,999).strtotime(date('Y-m-d-H:isa')).$originalFile;
			$file->move($destinationPath, $filename);
		}else{
			$filename=$queAsk->image;
		}

		$queAsk->question = $request->get('question');
		$queAsk->image = $filename;
		//$queAsk->status = 1;
		$queAsk->save();
		$queAskId = $queAsk->id;

		if(!empty($request->adm_answer) || !empty($request->file('adm_image'))){
			if (auth()->user()->role_id==1) {
				$expert = 1;
			} elseif (auth()->user()->role_id==2) {
				$expert = 2;
			} else {
				$expert = 0;
			}
			
			$queAnswer = new QuestionAnswer();

			$file = $request->file('adm_image');
			if($file){
				$destinationPath = public_path().'/upload/questionask/';
				$originalFile = $file->getClientOriginalName();
				$filename = rand(111,999).strtotime(date('Y-m-d-H:isa')).$originalFile;
				$file->move($destinationPath, $filename);
			}else{
				$filename=$queAnswer->image;
			}

			$queAnswer->user_id = auth()->user()->id;
			$queAnswer->ques_id = $id;
			$queAnswer->answer = $request->get('adm_answer');
			$queAnswer->image = $filename;
			$queAnswer->expert = $expert;
			$queAnswer->status = 1;
			$queAnswer->save();
			$queAnswerId = $queAnswer->id;

			$userId = auth()->user()->id;
			$ques = QuestionAsk::where("id", $id)->first();
			$msg = auth()->user()->name.', Answered a question '.$ques->question.' in Q&A, check it now.';
			if($ques->user_id>0){
				$notify = new Notification();
				$notify->user_id = $ques->user_id;
				$notify->message = $msg;
				$notify->save();
			
				$token = isset($ques->user->deviceToken) ? $ques->user->deviceToken : '';
				if ($token!='') {
					$title = 'BrainyWood';
					$click_action = 'QA';
					$module_id = $id;
				//	$this->helper->notificationsend($token, $title, $msg, $click_action, $module_id);//ak
				}
			}
		}

		\Session::flash('msg', 'Question Answer Updated Successfully.');
		return redirect('/admin/questionanswers');
	}

	public function delete($id)
	{
		$queAsk = QuestionAsk::findOrFail($id);
		$queAsk->deleted=1;
		$queAsk->update();

		return redirect()->back();
	}

	public function updateStatus($id,$status)
	{
		$queAsk = QuestionAsk::findOrFail($id);
		$queAsk->status=$status;
		$queAsk->update();

		//Refer point on Approved Question
		if ($status==1) {
			$userId = $queAsk->user_id;
			$userPoint = UserPoint::where("user_id", $userId)->where("module_id", $id)->where("refer_point_id", 68)->first();
			if (empty($userPoint)) {
				$this->helper->addUserReferPoints($userId,68,$id);
			}
		}

		return redirect()->back();
	}

	public function deleteAnswer($id)
	{
		$delete = QuestionAnswer::where("id",$id)->delete();

		\Session::flash('msg', 'Answer Deleted Successfully.');
		return back();
	}

	public function getLessionsBycourse(Request $request)
	{
		$courseId = $request->courseId;
		$lessions = Lession::where("courseId", $courseId)->where("status", 1)->where('deleted', 0)->orderBy('sort_id', 'ASC')->get();
		$output = '<option>Select Lesson</option>';
		foreach ($lessions as $key => $value) {
			$output .= '<option value="'.$value->id.'">'.$value->name.'</option>';
		}
		echo $output;
	}

	public function getTopicsByLession(Request $request)
	{
		$courseId = $request->courseId;
		$lessionId = $request->lessionId;
		$chapters = Chapter::where("courseId", $courseId)->where("lessionId", $lessionId)->where("status", 1)->where('deleted', 0)->orderBy('sort_id', 'ASC')->get();
		$output = '<option>Select Topic</option>';
		foreach ($chapters as $key => $value) {
			$output .= '<option value="'.$value->id.'">'.$value->name.'</option>';
		}
		echo $output;
	}

}
