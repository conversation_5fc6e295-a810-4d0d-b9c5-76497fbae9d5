<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Http\Helper as Helper;
use App\Models\BadWord;

class BadWordController extends Controller
{
	protected $helper;

	public function __construct(Helper $helper)
	{
		$this->helper = $helper;
	}

	public function index()
	{
		$data = BadWord::orderBy("id", "DESC")->paginate(50);
		return view('admin.badword.index', compact('data'));
	}

	public function create()
	{
		return view('admin.badword.create');
	}

	public function store(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'bad_word' => 'required',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$badWord = new BadWord();
			$badWord->bad_word = $request->get('bad_word');
			$badWord->save();
			
			\Session::flash('msg', 'Bad Word Added Successfully.');
			return back();
		}
	}

	public function show(BadWord $badWord)
	{
		//
	}

	public function edit($id)
	{
		$badWord = BadWord::find($id);
		return view('admin.badword.edit',compact('badWord'));
	}

	public function update(Request $request, $id)
	{
		$validator = Validator::make($request->all(), [
			'bad_word' => 'required',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$badWord = BadWord::findOrFail($id);
			$badWord->bad_word = $request->get('bad_word');
			$badWord->save();

			\Session::flash('msg', 'Bad Word updated Successfully.');
			return redirect('/admin/badwords');
		}

	}

	public function delete($id)
	{
		$data = BadWord::findOrFail($id);
		$data->delete();
		
		\Session::flash('msg', 'Bad Word deleted Successfully.');
	    return redirect('/admin/badwords');
	}

}
