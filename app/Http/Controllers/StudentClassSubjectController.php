<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\StudentClassSubject;
use App\Models\StudentClass;
use App\Models\Subject;

class StudentClassSubjectController extends Controller
{
	public function index()
	{
		$data = StudentClassSubject::orderBy("id", "DESC")->paginate(50);
		return view('admin.student_class_subject.index', compact('data'));
	}

	public function create()
	{
		$classes = StudentClass::orderBy("id", "ASC")->get();
		$subjects = Subject::orderBy("title", "ASC")->get();

		return view('admin.student_class_subject.create',compact('classes','subjects'));
	}

	public function store(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'class_id' => 'required|unique:student_class_subjects,class_id',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$class_subject = new StudentClassSubject();
			$class_subject->class_id = $request->input('class_id');
			$subject_ids = !empty($request->subject_ids) ? implode(",", $request->subject_ids) : NULL;
			$class_subject->subject_ids = $subject_ids;
			$class_subject->save();
			
			\Session::flash('msg', 'Student Class Subject Added Successfully.');
			return back();
		}
	}

	public function show(StudentClassSubject $class_subject)
	{
		//
	}

	public function edit($id)
	{
		$class_subject = StudentClassSubject::find($id);
		$classes = StudentClass::orderBy("id", "ASC")->get();
		$subjects = Subject::orderBy("title", "ASC")->get();

		return view('admin.student_class_subject.edit',compact('class_subject','classes','subjects'));
	}

	public function update(Request $request, $id)
	{
		$validator = Validator::make($request->all(), [
			'class_id' => 'required|unique:student_class_subjects,class_id,'.$id,
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$class_subject = StudentClassSubject::findOrFail($id);
			$class_subject->class_id = $request->input('class_id');
			$subject_ids = !empty($request->subject_ids) ? implode(",", $request->subject_ids) : NULL;
			$class_subject->subject_ids = $subject_ids;
			$class_subject->save();

			\Session::flash('msg', 'Student Class Subject updated Successfully.');
			return redirect('/admin/class_subjects');
		}
	}

	public function delete($id)
	{
		$data = StudentClassSubject::findOrFail($id);
		$data->delete();
		
		\Session::flash('msg', 'Student Class Subject deleted Successfully.');
	    return redirect('/admin/class_subjects');
	}

	public function updateStatus($id,$status)
	{
		$data = StudentClassSubject::findOrFail($id);
		$data->status=$status;
		$data->update();

	    return redirect()->back();
	}

}
