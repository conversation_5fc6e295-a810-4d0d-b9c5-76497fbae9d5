<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use App\Models\Category;
use Validator;
use App\Models\StudentClass;
class CategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(){
      $data = Category::where('deleted',0)->orderBy('id', 'DESC');
      $totalData = $data->count();
      $data = $data->paginate(10);
      if(!empty($data)){
         foreach($data as $d){
                 if($d->class_id!=0){
                    $d->class_name = isset($d->class->class_name)? $d->class->class_name:"";
                }else{
                  $d->class_name="-";
                }  
            }
        }
        $classDetails = StudentClass::where('deleted',0)->where("status",1)->get();
        
        return view('admin.category.index',compact('data','totalData'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
       // dd(Category::all());
                $classDetails = StudentClass::where('deleted',0)->where("status",1)->get();
        return view('admin.category.create',compact('classDetails'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // dd($request->all());
        $validator = Validator::make($request->all(), [
        'name' => 'required|unique:categories',
        'image' => 'required',
        'class_id'=>'required',
        // 'description'=>'required'
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        } else {

        $file = $request->file('image');

        $destinationPath = public_path().'/category/';
     
        if($file){
            $originalFile = $file->getClientOriginalName();
            $filename = strtotime(date('Y-m-d-H:isa')).$originalFile;
            $file->move($destinationPath, $filename);
        }


            $Category = new Category();
            $Category->name = $request->get('name');
            $Category->image = $filename;
            $Category->description = $request->description;
            $Category->class_id = $request->class_id;
            $Category->save();
            $categoryId=$Category->id;
            if($categoryId){
                $Category = Category::findOrFail($categoryId);
                $Category->sort_id = $categoryId;
                $Category->update();
            }
            \Session::flash('msg', 'Category Added Successfully.');
            return back();
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
                $classDetails = StudentClass::where('deleted',0)->where("status",1)->get();
        $category = Category::findOrFail($id);
        return view('admin.category.edit',compact('category','classDetails'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {

           $validator = Validator::make($request->all(), [
            'name' => 'required',
            // 'description' => 'required',
            'class_id'=>'required',
           
        ]);
        if ($validator->fails()) {
          
            return back()->withErrors($validator)->withInput();
        } else {
            $Category = Category::findOrFail($id);
            $file = $request->file('image');
          
            $destinationPath = public_path().'/category/';
            if($file){
                $originalFile = $file->getClientOriginalName();
                $filename = time() . $originalFile;
                $file->move($destinationPath, $filename);
            }else{
               $filename=$Category->image;  
            }
            $Category->name = $request->get('name');
            $Category->image = $filename;
             $Category->description = $request->description;
            $Category->class_id = $request->class_id;
            $Category->save();
            \Session::flash('msg', 'Category Updated Successfully.');
            return redirect('/admin/category');
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function imgremove($id)
    {
        $category = Category::findOrFail($id);
        if(file_exists( public_path().'/category/'.$category->image )) {
            unlink( public_path().'/category/'.$category->image );
        }
        $category->image = NULL;
        $category->update();

        \Session::flash('msg', 'Category Image Removed Successfully.');
        return redirect()->back();
    }
    public function delete($id){
        $category = Category::findOrFail($id);
      
        $category->deleted = 1;
        $category->update();

        // $data = Category::findOrFail($id);
        // $data->deleted=1;
        // $data->update();
        
        \Session::flash('msg', 'Category Deleted Successfully.');
        
        return redirect()->back();
    }
    public function sortUp($id,$sort_id)
	{
		$sort_id = $sort_id - 1;
		$SubCategory = Category::where("sort_id", $sort_id);
		$SubCategory->update(array("sort_id"=>$sort_id + 1));
		$SubCategory = Category::findOrFail($id);
		$SubCategory->sort_id = $sort_id;
		$SubCategory->save();
	    return redirect()->back();
	}

	public function sortDown($id,$sort_id)
	{
		$sort_id = $sort_id + 1;
		$SubCategory = Category::where("sort_id", $sort_id);
		$SubCategory->update(array("sort_id"=>$sort_id - 1));
		$SubCategory = Category::findOrFail($id);
		$SubCategory->sort_id = $sort_id;
		$SubCategory->save();
	    return redirect()->back();
	}

   public function updateStatus($id,$status)
    {
        $data = Category::findOrFail($id);
        $data->status=$status;
        $data->update();

        return redirect()->back();
    }


}
