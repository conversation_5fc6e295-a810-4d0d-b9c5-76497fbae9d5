<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Board;

class BoardController extends Controller
{
	public function index()
	{
		$data = Board::orderBy("id", "DESC")->get();
		return view('admin.board.index', compact('data'));
	}

	public function create()
	{
		return view('admin.board.create');
	}

	public function store(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'title' => 'required|unique:boards,title',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$board = new Board();
			$board->title = $request->input('title');
			$board->status = 2;
			$board->save();
			
			\Session::flash('msg', 'Board Added Successfully.');
			return back();
		}
	}

	public function show(Board $board)
	{
		//
	}

	public function edit($id)
	{
		$board = Board::find($id);

		return view('admin.board.edit',compact('board'));
	}

	public function update(Request $request, $id)
	{
		$validator = Validator::make($request->all(), [
			'title' => 'required|unique:boards,title,'.$id,
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$board = Board::findOrFail($id);
			$board->title = $request->input('title');
			$board->save();

			\Session::flash('msg', 'Board updated Successfully.');
			return redirect('/admin/boards');
		}
	}

	public function delete($id)
	{
		$data = Board::findOrFail($id);
		$data->delete();
		
		\Session::flash('msg', 'Board deleted Successfully.');
	    return redirect('/admin/boards');
	}

	public function updateStatus($id,$status)
	{
		$data = Board::findOrFail($id);
		$data->status=$status;
		$data->update();

	    return redirect()->back();
	}

}
