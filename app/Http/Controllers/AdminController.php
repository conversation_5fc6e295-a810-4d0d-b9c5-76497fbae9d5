<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Helper as Helper;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Models\City;
use App\Models\State;
use App\Models\User;
use App\Models\Profile_details;
use App\Models\Category;
use App\Models\StudentClass;
use App\Models\SubCategory;
use App\Models\Subject;


class AdminController extends Controller
{

protected $helper;

    public function __construct(Helper $helper)
    {
        $this->helper = $helper;
    }

    /**
     * email tempplate load
     */
    public function adminEmail(){
        return view('admin.notification_email');
    }

    /**
     * Show user profile
     */
    public  function viewProfile()
    {


        $data = User::with('permissions')->findOrFail(Auth::id());
        $userRoles = $data->roles()->get(); 
    //dd($userRoles);
      //  dd($data);die;
        $timezone_offset = $this->helper->timezone();

       // dd($timezone_offset);
        return view('admin.profile',compact('data','timezone_offset'));
    }

    /**
     * update user profile update
     */
    public  function updateProfile(Request $request){

        $data = User::findOrFail(Auth::id());
        
        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'email' => 'required|email|unique:users,email,'.$data->id,
            'phone' => 'required|numeric|min:10|unique:users,phone,'.$data->id,
        ]);
        if ($validator->fails()) {
            return back()
                ->withErrors($validator)
                ->withInput();
        } else {

            $data->name = $request->input('name');
            $data->email = $request->input('email');
            $data->phone = $request->input('phone');
          //  $data->state = $request->input('state');
           // $data->city = $request->input('city');
            $data->address = $request->input('address');
            $data->timezone = $request->input('timezone_offset');
            //$data->postal_code = $request->input('postal_code');
            $data->percentage_commission = $request->input('percentage_commission');
            $data->save();
            $select_class =$request->select_class;
            $select_category =$request->select_category;
            $select_sub_category =$request->select_sub_category;
            $select_subject =$request->select_subject;
            $fees_per_class = $request->fees_per_class;
         


            if($data->role_id==2)
            {
                Profile_details::where('user_role_id',$data->role_id)->where('user_id',$data->id)->delete();
                if(!empty($select_class))
                {
                    foreach($select_class as $k =>$val)
                    {
                        echo $select_category[$k];
                        Profile_details::create(
                        array(
                            'user_role_id'=>$data->role_id,
                            'user_id'=>$data->id,
                            'select_class'=>$select_class[$k],
                            'select_category'=>$select_category[$k],
                            'select_sub_category'=>$select_sub_category[$k],
                            'select_subject'=>$select_subject[$k],
                            'fees_per_class'=>$fees_per_class[$k]
                        ));
                    }
                }
            }
            return redirect()->action('AdminController@viewProfile');
        }
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
