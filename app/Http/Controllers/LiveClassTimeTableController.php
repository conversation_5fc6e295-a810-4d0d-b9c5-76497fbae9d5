<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\LiveClassTimeTable;
use App\Models\StudentClass;
use App\Models\StudentClassSubject;
use App\Models\Subject;

class LiveClassTimeTableController extends Controller
{
	public function index(Request $request)
	{
		$class_id 	= $request->class_id;
		$subject_id = $request->subject_id;
		$status 	= $request->status;
		$from 		= ($request->from) ? $request->from : '';
		$to 		= ($request->to) ? $request->to.' 23:59:59' : date('Y-m-d').' 23:59:59';
		$data = LiveClassTimeTable::with('class_data','subject_data')
	                ->Where(function($query) use ($class_id) {
	                    if (isset($class_id) && !empty($class_id)) {
	                        $query->where('class_id', $class_id);
	                    }
	                })
	                ->Where(function($query) use ($subject_id) {
	                    if (isset($subject_id) && !empty($subject_id)) {
	                        $query->where('subject_id', $subject_id);
	                    }
	                })
	                ->Where(function($query) use ($status) {
	                    if (isset($status) && !empty($status)) {
	                        $query->where('status', $status);
	                    }
	                })
					->Where(function($query) use ($from, $to) {
						if (isset($from) && !empty($from)) {
							//$query->whereBetween("created_at", [$from, $to]);
							$query->where("start_time", ">=", $from)->where("end_time", "<=", $to);
						}
					})
					->orderBy("id", "DESC")->paginate(50);
		
		$classes = StudentClass::orderBy("id", "ASC")->get();
		if (!empty($class_id)) {
			$getClassSubjectData = StudentClassSubject::where("class_id", $class_id)->first();
			$subject_ids_arr = [];
			$i=0;
			$subject_ids = !empty($getClassSubjectData->subject_ids) ? explode(",", $getClassSubjectData->subject_ids) : [];
			foreach ($subject_ids as $key => $value) {
				$subject_ids_arr[$i]=$value;
				$i++;
			}
			array_unique($subject_ids_arr);
			$subjects = Subject::whereIn("id",$subject_ids_arr)->orderBy("title", "ASC")->get();
		} else {
			$subjects = Subject::orderBy("title", "ASC")->get();
		}
		
		return view('admin.liveclass_timetable.index', compact('data','classes','subjects'));
	}

	public function create()
	{
		$classes = StudentClass::orderBy("id", "ASC")->get();

		return view('admin.liveclass_timetable.create',compact('classes'));
	}

	public function store(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'class_id' => 'required',
			'subject_id' => 'required',
			'title' => 'required',
			'start_time' => 'required',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$liveclass_timetable = new LiveClassTimeTable();
			$liveclass_timetable->class_id = $request->input('class_id');
			$liveclass_timetable->subject_id = $request->input('subject_id');
			$liveclass_timetable->title = $request->input('title');
			$liveclass_timetable->start_time = date('Y-m-d H:i:s', strtotime($request->input('start_time')));
			$liveclass_timetable->end_time = date('Y-m-d H:i:s', strtotime($request->input('end_time')));
			$liveclass_timetable->status = 2;
			$liveclass_timetable->save();
			$liveclass_timetableId=$liveclass_timetable->id;
			
			\Session::flash('msg', 'Live Class Time Table Added Successfully.');
			return back();
		}
	}

	public function show(LiveClassTimeTable $liveclass_timetable)
	{
		//
	}

	public function edit($id)
	{
		$classes = StudentClass::orderBy("id", "ASC")->get();
		$liveclass_timetable = LiveClassTimeTable::find($id);
		$class_id = $liveclass_timetable->class_id;
		if (!empty($class_id)) {
			$getClassSubjectData = StudentClassSubject::where("class_id", $class_id)->first();
			$subject_ids_arr = [];
			$i=0;
			$subject_ids = !empty($getClassSubjectData->subject_ids) ? explode(",", $getClassSubjectData->subject_ids) : [];
			foreach ($subject_ids as $key => $value) {
				$subject_ids_arr[$i]=$value;
				$i++;
			}
			array_unique($subject_ids_arr);
			$subjects = Subject::whereIn("id",$subject_ids_arr)->orderBy("title", "ASC")->get();
		} else {
			$subjects = Subject::orderBy("title", "ASC")->get();
		}

		return view('admin.liveclass_timetable.edit',compact('liveclass_timetable','classes','subjects'));
	}

	public function update(Request $request, $id)
	{
		$validator = Validator::make($request->all(), [
			'class_id' => 'required',
			'subject_id' => 'required',
			'title' => 'required',
			'start_time' => 'required',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$liveclass_timetable = LiveClassTimeTable::findOrFail($id);
			$liveclass_timetable->class_id = $request->input('class_id');
			$liveclass_timetable->subject_id = $request->input('subject_id');
			$liveclass_timetable->title = $request->input('title');
			$liveclass_timetable->start_time = date('Y-m-d H:i:s', strtotime($request->input('start_time')));
			$liveclass_timetable->end_time = date('Y-m-d H:i:s', strtotime($request->input('end_time')));
			$liveclass_timetable->save();

			\Session::flash('msg', 'Live Class Time Table Updated Successfully.');
			return redirect('/admin/liveclass_timetable');
		}
	}

	public function delete($id)
	{
		$data = LiveClassTimeTable::findOrFail($id);
		$data->delete();
		
		\Session::flash('msg', 'Live Class Time Table Deleted Successfully.');
	    return redirect('/admin/liveclass_timetable');
	}

	public function updateStatus($id,$status)
	{
		$data = LiveClassTimeTable::findOrFail($id);
		$data->status=$status;
		$data->update();

	    return redirect()->back();
	}

	public function getSubjectList(Request $request)
	{
		$class_id = $request->class_id;
		?>
		<option value="0">--Select--</option>
		<?php
		if (!empty($class_id)) {
			$getClassSubjectData = StudentClassSubject::where("class_id", $class_id)->first();
			$subject_ids_arr = [];
			$i=0;
			$subject_ids = !empty($getClassSubjectData->subject_ids) ? explode(",", $getClassSubjectData->subject_ids) : [];
			foreach ($subject_ids as $key => $value) {
				$subject_ids_arr[$i]=$value;
				$i++;
			}
			array_unique($subject_ids_arr);
			$getSubjectData = Subject::whereIn("id",$subject_ids_arr)->orderBy("title", "ASC")->get();
			foreach ($getSubjectData as $key => $value) {
				?>
				<option value="<?php echo $value['id']; ?>"><?php echo $value['title']; ?></option>
				<?php
			}
		}
	}

}
