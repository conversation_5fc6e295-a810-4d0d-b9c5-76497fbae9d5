<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Courses;
use App\Coursefeature;
use App\Coursefeq;
use App\VideoTemp;
use AWS;


class CoursesController extends Controller
{
	/**
	 * Display a listing of the resource.
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function index()
	{
		$course = Courses::where('sort_id',0)->orderBy('sort_id', 'ASC')->first();
		if($course){
			$Course = Courses::findOrFail($course->id);
			$Course->sort_id = $course->id;
			$Course->update();
		}
		$data = Courses::where('deleted',0)->orderBy('sort_id', 'ASC')->get();

		return view('admin.courses.index', compact('data'));
	}

	public function test(){

		$data = Courses::find(5)->get();

		foreach ($data as $col){
			echo $col->collage;
		}
	}
	/**
	 * Show the form for creating a new resource.
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function create()
	{
		return view('admin.courses.create');
	}

	/**
	 * Store a newly created resource in storage.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @return \Illuminate\Http\Response
	 */
	public function store(Request $request)
	{ 
		$s3 = AWS::createClient('s3');

		//echo '<pre />'; print_r($request->all()); die;
		$validator = Validator::make($request->all(), [
			'name' => 'required',
			'video' => 'required|mimes:mp4',
		]);

		if ($validator->fails()) {
			return back()
				->withErrors($validator)
				->withInput();
		} else {
			$file = $request->file('image');
			$video = $request->file('video');
			$thumb = $request->file('thumb');
			$pdf = $request->file('pdf');
			$filename = $filenamevideo = $filenamepdf = '';
			if($file){
				$destinationPath = public_path().'/course/';
				$originalFile = $file->getClientOriginalName();
				$filename=strtotime(date('Y-m-d-H:isa')).$originalFile;
				$file->move($destinationPath, $filename);
			}
			if($video){
				$destinationPathvideo = public_path().'/course/';
				$originalFilevideo = $video->getClientOriginalName();
				//$filenamevideo=rand(111,999).strtotime(date('Y-m-d-H:isa')).$originalFilevideo;
				$filenamevideo = time() . "_org.mp4";
				$video->move($destinationPathvideo, $filenamevideo);
			}
			if($pdf){
				$destinationPathpdf = public_path().'/course/';
				$originalFilepdf = $pdf->getClientOriginalName();
				$filenamepdf=rand(111,999).strtotime(date('Y-m-d-H:isa')).$originalFilepdf;
				$pdf->move($destinationPathpdf, $filenamepdf);
			}
				
			$Courses = new Courses();
			$Courses->name = $request->get('name');
			$Courses->overview = $request->get('overview');
			$Courses->video_duration = $request->get('video_duration');
			$Courses->image = $filename;
			$Courses->video = $filenamevideo;
			$Courses->pdf = $filenamepdf;
			$Courses->isFree = $request->get('free');
			$Courses->save();
			$courseId=$Courses->id;
			if($courseId){
				$Course = Courses::findOrFail($courseId);
				$Course->sort_id = $courseId;
				$Course->update();
			}
		    
			$feature = ($request->get('featu')) ? $request->get('featu') : [];
			for ($i=0; $i<count($feature); $i++){
				$Coursefeature=new Coursefeature();
				$Coursefeature->courseId =$courseId;    
				$Coursefeature->feature =$request->input('featu')[$i];    
				$Coursefeature->save(); 
			}
			$faqTitle = ($request->get('faqTitle')) ? $request->get('faqTitle') : [];
			for ($i=0; $i<count($faqTitle); $i++){
				$Coursefeq=new Coursefeq();
				$Coursefeq->courseId =$courseId;    
				$Coursefeq->title =$request->input('faqTitle')[$i];
				$Coursefeq->contant =$request->input('faqcontant')[$i];    
				$Coursefeq->save(); 
			}

			/*Video Convert & Upload on S3 Start*/
			$course = Courses::where("id",$courseId)->first();
			if(!empty($course->video)){
				$localurl = "/usr/bin/ffmpeg";

				if ($course->processtatus==1) {
					$starttime = @$course->starttime;
					if (!empty($starttime)) {
						$hourdiff = round((strtotime(date("Y-m-d H:i:s")) - strtotime($starttime)) / 3600, 1);
						if ($hourdiff > 2) {
							//@mail("<EMAIL>", "BrainyWood Course Video Stuck", "video Id" . $course1->id);
							$update = Courses::where("id",$course->id)->update(array('endtime' => date("Y-m-d H:i:s"), 'processtatus' => 3));
						}
						\Session::flash('msg', 'Course underprocess!');
						return back();
					}
				}

				$foldername = public_path().'/course/';
				$orgurl = $foldername . $course->video;
				
				if (empty($course->video_1)) {
					$name1 = $course->id . "_240.mp4";
					$nameV1 = $course->id . '_course_' . time() . "_240.mp4";

					$videourl = "temp/" .  $name1;
					$videopath1 = $foldername . $nameV1;
					$update = Courses::where("id",$course->id)->update(array('video_1' => 'NA', 'starttime' => date("Y-m-d H:i:s"), 'processtatus' => 1));

					$command1 = $localurl . " -i " . $orgurl . "  -aspect 4:3 -s 320x240 " . $videopath1;
					$process = exec($command1, $result);
					//echo $command1;

					$update = Courses::where("id",$course->id)->update(array('video_1' => $nameV1, 'endtime' => date("Y-m-d H:i:s"), 'processtatus' => 2));
				}
				if (empty($course->video_2)) {
					$name1 = $course->id . "_480.mp4";
					$nameV2 = $course->id . '_course_' . time() . "_480.mp4";

					$videourl = "temp/" .  $name1;
					$videopath2 = $foldername . $nameV2;
					$update = Courses::where("id",$course->id)->update(array('video_2' => 'NA', 'starttime' => date("Y-m-d H:i:s"), 'processtatus' => 1));

					$command2 = $localurl . " -i " . $orgurl . "  -aspect 4:3 -s 480x360 " . $videopath2;
					$process = exec($command2, $result);
					//echo $command2;

					$update = Courses::where("id",$course->id)->update(array('video_2' => $nameV2, 'endtime' => date("Y-m-d H:i:s"), 'processtatus' => 2));
				}
				if (empty($course->video_3)) {
					$name1 = $course->id . "_720.mp4";
					$nameV3 = $course->id . '_course_' . time() . "_720.mp4";

					$videourl = "temp/" .  $name1;
					$videopath3 = $foldername . $nameV3;
					$update = Courses::where("id",$course->id)->update(array('video_3' => 'NA', 'starttime' => date("Y-m-d H:i:s"), 'processtatus' => 1));

					$command3 = $localurl . " -i " . $orgurl . "  -aspect 16:9 -s 960x540 " . $videopath3;
					$process = exec($command3, $result);
					//echo $command3;

					$update = Courses::where("id",$course->id)->update(array('video_3' => $nameV3, 'endtime' => date("Y-m-d H:i:s"), 'processtatus' => 2));
				}

				//S3 Upload
				$newvideo = "course_".$course->id."_".time()."_org.mp4";
				$s3->putObject(array(
				    'Bucket'     => env('AWS_BUCKET'),
				    'Key'        => $newvideo,
				    'SourceFile' => $orgurl,
				));
				$s3->putObject(array(
				    'Bucket'     => env('AWS_BUCKET'),
				    'Key'        => "mobile_app_data/".$newvideo,
				    'SourceFile' => $orgurl,
				));
		        $newvideo1 = "course_".$course->id."_".time()."_v1.mp4";
		        $s3->putObject([
		            'Bucket' => env('AWS_BUCKET'),
		            'Key'    =>"mobile_app_data/".$newvideo1,
		            'SourceFile' => $videopath1,

		        ]);
		        $newvideo2 = "course_".$course->id."_".time()."_v2.mp4";
		        $s3->putObject([
		            'Bucket' => env('AWS_BUCKET'),
		            'Key'    =>"mobile_app_data/".$newvideo2,
		            'SourceFile' => $videopath2,

		        ]);
		        $newvideo3 = "course_".$course->id."_".time()."_v3.mp4";
		        $s3->putObject([
		            'Bucket' => env('AWS_BUCKET'),
		            'Key'    =>"mobile_app_data/".$newvideo3,
		            'SourceFile' => $videopath3,

		        ]);
				if(!empty($video) && file_exists( $orgurl )) {
					unlink( $orgurl );
				}
				if(!empty($video) && file_exists( $videopath1 )) {
					unlink( $videopath1 );
				}
				if(!empty($video) && file_exists( $videopath2 )) {
					unlink( $videopath2 );
				}
				if(!empty($video) && file_exists( $videopath3 )) {
					unlink( $videopath3 );
				}
				$update = Courses::where("id",$course->id)->update(["uploads3" => 1, "video" => $newvideo, "video_1" => $newvideo1, "video_2" => $newvideo2, "video_3" => $newvideo3]);
			}
			/*Video Convert & Upload on S3 End*/
			
			\Session::flash('msg', 'Course Added Successfully.');
			return back();
		}
	}

	/**
	 * Display the specified resource.
	 *
	 * @param  \App\Courses  $courses
	 * @return \Illuminate\Http\Response
	 */
	public function show(Courses $courses)
	{
		//
	}

	/**
	 * Show the form for editing the specified resource.
	 *
	 * @param  \App\Courses  $courses
	 * @return \Illuminate\Http\Response
	 */
	public function edit(Courses $courses, $id)
	{
		$Coursefeq = Coursefeq::where('courseId',$id)->get();
		$Coursefeature = Coursefeature::where('courseId',$id)->get();
	  
		$course = Courses::findOrFail($id);
		//$course = Courses::with('course_features_data','course_faqs_data')->findOrFail($id);
		//dd($course);
		return view('admin.courses.edit',compact('course','Coursefeq','Coursefeature'));
	}

	/**
	 * Update the specified resource in storage.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @param  \App\Courses  $courses
	 * @return \Illuminate\Http\Response
	 */
	public function update(Request $request, Courses $courses, $id)
	{
		$s3 = AWS::createClient('s3');
		
		$validator = Validator::make($request->all(), [
			'name' => 'required',
			'video' => 'mimes:mp4',
		]);

		if ($validator->fails()) {
			return back()
				->withErrors($validator)
				->withInput();
		} else {

			$Courses = Courses::findOrFail($id);

			$file = $request->file('image');
			$video = $request->file('video');
			$thumb = $request->file('thumb');
			$pdf = $request->file('pdf');

			if($file){
				$destinationPath = public_path().'/course/';
				$originalFile = $file->getClientOriginalName();
				$filename=strtotime(date('Y-m-d-H:isa')).$originalFile;
				$file->move($destinationPath, $filename);
			}else{
			   $filename=$Courses->image;  
			}

			if($video){
				$destinationPathvideo = public_path().'/course/';
				$originalFilevideo = $video->getClientOriginalName();
				//$filenamevideo=rand(111,999).strtotime(date('Y-m-d-H:isa')).$originalFilevideo;
				$filenamevideo = time() . "_org.mp4";
				$video->move($destinationPathvideo, $filenamevideo);
			}else{
			   $filenamevideo=$Courses->video;  
			}
			if($pdf){
				$destinationPathpdf = public_path().'/course/';
				$originalFilepdf = $pdf->getClientOriginalName();
				$filenamepdf=rand(111,999).strtotime(date('Y-m-d-H:isa')).$originalFilepdf;
				$pdf->move($destinationPathpdf, $filenamepdf);
			}else{
				$filenamepdf=$Courses->pdf;
			}
			
			if($video){
				if(!empty($Courses->video) && file_exists( public_path().'/course/'.$Courses->video )) {
					unlink( public_path().'/course/'.$Courses->video );
				}
				if(!empty($Courses->video_1) && file_exists( public_path().'/course/'.$Courses->video_1 ) && $Courses->video_1!='NA') {
					unlink( public_path().'/course/'.$Courses->video_1 );
				}
				if(!empty($Courses->video_2) && file_exists( public_path().'/course/'.$Courses->video_2 ) && $Courses->video_2!='NA') {
					unlink( public_path().'/course/'.$Courses->video_2 );
				}
				if(!empty($Courses->video_3) && file_exists( public_path().'/course/'.$Courses->video_3 ) && $Courses->video_3!='NA') {
					unlink( public_path().'/course/'.$Courses->video_3 );
				}
				if(!empty($Courses->video)){
					$videoName = $Courses->video;
					$s3->deleteObject(array(
				        'Bucket' => env('AWS_BUCKET'),
				        'Key'    => $videoName
				    ));
					$s3->deleteObject(array(
				        'Bucket' => env('AWS_BUCKET'),
				        'Key'    => "mobile_app_data/".$videoName
				    ));
				}
				if(!empty($Courses->video_1)){
					$videoName1 = $Courses->video_1;
					$s3->deleteObject(array(
				        'Bucket' => env('AWS_BUCKET'),
				        'Key'    => "mobile_app_data/".$videoName1
				    ));
				}
				if(!empty($Courses->video_2)){
					$videoName2 = $Courses->video_2;
					$s3->deleteObject(array(
				        'Bucket' => env('AWS_BUCKET'),
				        'Key'    => "mobile_app_data/".$videoName2
				    ));
				}
				if(!empty($Courses->video_3)){
					$videoName3 = $Courses->video_3;
					$s3->deleteObject(array(
				        'Bucket' => env('AWS_BUCKET'),
				        'Key'    => "mobile_app_data/".$videoName3
				    ));
				}
				$deleteVideoTemp = VideoTemp::where('courseId',$id)->where('lessionId',0)->where('topicId',0)->delete();
				$Courses->video_1 = NULL;
				$Courses->video_2 = NULL;
				$Courses->video_3 = NULL;
				$Courses->processtatus = 0;
				$Courses->starttime = NULL;
				$Courses->endtime = NULL;
				$Courses->uploads3 = 0;
			}
			$Courses->name = $request->get('name');
			$Courses->overview = $request->get('overview');
			$Courses->video_duration = $request->get('video_duration');
			$Courses->image = $filename;
			$Courses->video = $filenamevideo;
			$Courses->pdf = $filenamepdf;
			$Courses->isFree = $request->get('free');
			$courseId=$Courses->save();
			$courseId=$id;
		   
			Coursefeature::where('courseId',$id)->delete();
			Coursefeq::where('courseId',$id)->delete();
			$feature = ($request->get('featu')) ? $request->get('featu') : [];
			for ($i=0; $i<count($feature); $i++){
				$Coursefeature=new Coursefeature();
				$Coursefeature->courseId =$courseId;    
				$Coursefeature->feature =$request->input('featu')[$i];    
				$Coursefeature->save(); 
			}
			$faqTitle = ($request->get('faqTitle')) ? $request->get('faqTitle') : [];
			for ($i=0; $i<count($faqTitle); $i++){
				$Coursefeq=new Coursefeq();
				$Coursefeq->courseId =$courseId;    
				$Coursefeq->title =$request->input('faqTitle')[$i];
				$Coursefeq->contant =$request->input('faqcontant')[$i];    
				$Coursefeq->save(); 
			}
			
			//$Courses->update();

			/*Video Convert & Upload on S3 Start*/
			if($video){
				$localurl = "/usr/bin/ffmpeg";

				$course = Courses::where("id",$courseId)->first();
				if ($course->processtatus==1) {
					$starttime = @$course->starttime;
					if (!empty($starttime)) {
						$hourdiff = round((strtotime(date("Y-m-d H:i:s")) - strtotime($starttime)) / 3600, 1);
						if ($hourdiff > 2) {
							//@mail("<EMAIL>", "BrainyWood Course Video Stuck", "video Id" . $course->id);
							$update = Courses::where("id",$course->id)->update(array('endtime' => date("Y-m-d H:i:s"), 'processtatus' => 3));
						}
						\Session::flash('msg', 'Course underprocess!');
						return back();
					}
				}

				$foldername = public_path().'/course/';
				$orgurl = $foldername . $course->video;
				
				if (empty($course->video_1)) {
					$name1 = $course->id . "_240.mp4";
					$nameV1 = $course->id . '_course_' . time() . "_240.mp4";

					$videourl = "temp/" .  $name1;
					$videopath1 = $foldername . $nameV1;
					$update = Courses::where("id",$course->id)->update(array('video_1' => 'NA', 'starttime' => date("Y-m-d H:i:s"), 'processtatus' => 1));

					$command1 = $localurl . " -i " . $orgurl . "  -aspect 4:3 -s 320x240 " . $videopath1;
					$process = exec($command1, $result);
					//echo $command1; die;
					echo "working..";
					$update = Courses::where("id",$course->id)->update(array('video_1' => $nameV1, 'endtime' => date("Y-m-d H:i:s"), 'processtatus' => 2));
				}
				if (empty($course->video_2)) {
					$name1 = $course->id . "_480.mp4";
					$nameV2 = $course->id . '_course_' . time() . "_480.mp4";

					$videourl = "temp/" .  $name1;
					$videopath2 = $foldername . $nameV2;
					$update = Courses::where("id",$course->id)->update(array('video_2' => 'NA', 'starttime' => date("Y-m-d H:i:s"), 'processtatus' => 1));

					$command2 = $localurl . " -i " . $orgurl . "  -aspect 4:3 -s 480x360 " . $videopath2;
					$process = exec($command2, $result);
					//echo $command2;

					$update = Courses::where("id",$course->id)->update(array('video_2' => $nameV2, 'endtime' => date("Y-m-d H:i:s"), 'processtatus' => 2));
				}
				if (empty($course->video_3)) {
					$name1 = $course->id . "_720.mp4";
					$nameV3 = $course->id . '_course_' . time() . "_720.mp4";

					$videourl = "temp/" .  $name1;
					$videopath3 = $foldername . $nameV3;
					$update = Courses::where("id",$course->id)->update(array('video_3' => 'NA', 'starttime' => date("Y-m-d H:i:s"), 'processtatus' => 1));

					$command3 = $localurl . " -i " . $orgurl . "  -aspect 16:9 -s 960x540 " . $videopath3;
					$process = exec($command3, $result);
					//echo $command3;

					$update = Courses::where("id",$course->id)->update(array('video_3' => $nameV3, 'endtime' => date("Y-m-d H:i:s"), 'processtatus' => 2));
				}

				//S3 Upload
				$newvideo = "course_".$course->id."_".time()."_org.mp4";
				$s3->putObject(array(
				    'Bucket'     => env('AWS_BUCKET'),
				    'Key'        => $newvideo,
				    'SourceFile' => $orgurl,
				));
				$s3->putObject(array(
				    'Bucket'     => env('AWS_BUCKET'),
				    'Key'        => "mobile_app_data/".$newvideo,
				    'SourceFile' => $orgurl,
				));
		        $newvideo1 = "course_".$course->id."_".time()."_v1.mp4";
		        $s3->putObject([
		            'Bucket' => env('AWS_BUCKET'),
		            'Key'    =>"mobile_app_data/".$newvideo1,
		            'SourceFile' => $videopath1,
		        ]);
		        $newvideo2 = "course_".$course->id."_".time()."_v2.mp4";
		        $s3->putObject([
		            'Bucket' => env('AWS_BUCKET'),
		            'Key'    =>"mobile_app_data/".$newvideo2,
		            'SourceFile' => $videopath2,
		        ]);
		        $newvideo3 = "course_".$course->id."_".time()."_v3.mp4";
		        $s3->putObject([
		            'Bucket' => env('AWS_BUCKET'),
		            'Key'    =>"mobile_app_data/".$newvideo3,
		            'SourceFile' => $videopath3,
		        ]);
				if(!empty($video) && file_exists( $orgurl )) {
					unlink( $orgurl );
				}
				if(!empty($video) && file_exists( $videopath1 )) {
					unlink( $videopath1 );
				}
				if(!empty($video) && file_exists( $videopath2 )) {
					unlink( $videopath2 );
				}
				if(!empty($video) && file_exists( $videopath3 )) {
					unlink( $videopath3 );
				}
				$update = Courses::where("id",$course->id)->update(["uploads3" => 1, "video" => $newvideo, "video_1" => $newvideo1, "video_2" => $newvideo2, "video_3" => $newvideo3]);
			}
			/*Video Convert & Upload on S3 End*/

			\Session::flash('msg', 'Course Updated Successfully.');
			return redirect('/admin/courses');
		}

	}

	/**
	 * Remove the specified resource from storage.
	 *
	 * @param  \App\Courses  $courses
	 * @return \Illuminate\Http\Response
	 */
	public function delete($id)
	{
		$user = Courses::findOrFail($id);
		$user->deleted=1;
		$user->update();

	   return redirect('/admin/courses');
	}

	public function updateStatus($id,$status)
	{
		//echo "string";die;
		$user = Courses::findOrFail($id);
		$user->status=$status;
		$user->update();

	   return redirect('/admin/courses');
	}

	public function sortUp($id,$sort_id)
	{
		$sort_id = $sort_id - 1;
		$course = Courses::where("sort_id", $sort_id);
		//$course->sort_id = $sort_id + 1;
		$course->update(array("sort_id"=>$sort_id + 1));
		$course1 = Courses::findOrFail($id);
		$course1->sort_id = $sort_id;
		$course1->save();

	   return redirect('/admin/courses');
	}

	public function sortDown($id,$sort_id)
	{
		$sort_id = $sort_id + 1;
		$course = Courses::where("sort_id", $sort_id);
		//$course->sort_id = $sort_id - 1;
		$course->update(array("sort_id"=>$sort_id - 1));
		$course1 = Courses::findOrFail($id);
		$course1->sort_id = $sort_id;
		$course1->save();

	   return redirect('/admin/courses');
	}

	public function convertVideo()
	{
		$data = VideoTemp::where('courseId','!=',0)->where('lessionId',0)->where('topicId',0)->orderBy('id', 'ASC')->get();
		
		return view('admin.courses.convertVideo', compact('data'));
	}

	public function approveVideo($id)
	{
		$videoTemp = VideoTemp::where('id',$id)->first();
		$courseId = $videoTemp->courseId;
		$low_status = $videoTemp->low_status;
		$med_status = $videoTemp->med_status;
		$high_status = $videoTemp->high_status;
		$course = Courses::findOrFail($courseId);
		if ($low_status == 1) {
			$course->video_1 = $videoTemp->low_video;
		}
		if ($med_status == 1) {
			$course->video_2 = $videoTemp->med_video;
		}
		if ($high_status == 1) {
			$course->video_3 = $videoTemp->high_video;
		}
		$course->update();
		
		$delete = VideoTemp::where('id',$id)->delete();

		\Session::flash('msg', 'Converted Video Approved Successfully.');
	    return redirect('/admin/courses/convertVideo');
	}

	public function imgremove($id)
	{
		$course = Courses::findOrFail($id);
		if(file_exists( public_path().'/course/'.$course->image )) {
			unlink( public_path().'/course/'.$course->image );
		}
		$course->image = NULL;
		$course->update();

		\Session::flash('msg', 'Course Image Removed Successfully.');
	    return redirect()->back();
	}

	public function vidremove($id)
	{
		$s3 = AWS::createClient('s3');
		$course = Courses::findOrFail($id);
		if(!empty($course->video) && file_exists( public_path().'/course/'.$course->video )) {
			unlink( public_path().'/course/'.$course->video );
		}
		if(!empty($course->video_1) && file_exists( public_path().'/course/'.$course->video_1 ) && $course->video_1!='NA') {
			unlink( public_path().'/course/'.$course->video_1 );
		}
		if(!empty($course->video_2) && file_exists( public_path().'/course/'.$course->video_2 ) && $course->video_2!='NA') {
			unlink( public_path().'/course/'.$course->video_2 );
		}
		if(!empty($course->video_3) && file_exists( public_path().'/course/'.$course->video_3 ) && $course->video_3!='NA') {
			unlink( public_path().'/course/'.$course->video_3 );
		}
		if(!empty($course->video)){
			$videoName = $course->video;
			$s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => $videoName
		    ));
			$s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => "mobile_app_data/".$videoName
		    ));
		}
		if(!empty($course->video_1)){
			$videoName1 = $course->video_1;
			$s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => "mobile_app_data/".$videoName1
		    ));
		}
		if(!empty($course->video_2)){
			$videoName2 = $course->video_2;
			$s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => "mobile_app_data/".$videoName2
		    ));
		}
		if(!empty($course->video_3)){
			$videoName3 = $course->video_3;
			$s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => "mobile_app_data/".$videoName3
		    ));
		}
		$deleteVideoTemp = VideoTemp::where('courseId',$id)->where('lessionId',0)->where('topicId',0)->delete();
		$course->video = NULL;
		$course->video_1 = NULL;
		$course->video_2 = NULL;
		$course->video_3 = NULL;
		$course->processtatus = 0;
		$course->starttime = NULL;
		$course->endtime = NULL;
		$course->uploads3 = 0;
		$course->update();

		\Session::flash('msg', 'Course Video Removed Successfully.');
	    return redirect()->back();
	}

	public function pdfremove($id)
	{
		$course = Courses::findOrFail($id);
		if(file_exists( public_path().'/course/'.$course->pdf )) {
			unlink( public_path().'/course/'.$course->pdf );
		}
		$course->pdf = NULL;
		$course->update();

		\Session::flash('msg', 'Course PDF Removed Successfully.');
	    return redirect()->back();
	}

}