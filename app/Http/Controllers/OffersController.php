<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Offers;
use Validator;
class OffersController extends Controller
{
	
	public function index(Request $request)
	{
		$data = Offers::where('deleted',0)->orderBy("id", "DESC");
		$totalResult = $data->count();
		$data = $data->paginate(10);
		return view('admin.offers.index', compact('data'));
	}

	public function create()
	{
		$users = User::where('role_id',6)->get();
		return view('admin.offers.create',compact('users'));
	}

	public function store(Request $request){
		if($request->all())
		{
		 $validator = Validator::make($request->all(), [
        	'title' => 'required',
        	'coupon_code' => 'required|unique:offers.coupon_code',
        	'description' => 'required',
        	'start_valid_date'=>'required',
        	'end_valid_date'=>'required',
        	'coupon_code'=>'required',
        	'discount_percentage'=>'required'
        ]);
		 if ($validator->fails()) {
	            return back()->withErrors($validator)->withInput();
	        } else {

				$checkOfferCode = offers::where('coupon_code',$request->coupon_code)->first();
		     	if(!is_null($checkOfferCode))
		     	{
					\Session::flash('error', 'Coupon Code Already Exist');
					return redirect()->back();	
		     	}
		     	else
		     	{
		     		$offers = new Offers();
		     		$offers->title = $request->title;
		     		// $offers->currency = implode(",",$request->currency);
		     		$offers->min_classes = $request->minimum_class;
	     			$offers->description = $request->description;
	     			$offers->coupon_code = $request->coupon_code;
	     			$offers->no_of_users = $request->all_user;
	     			if($request->no_of_users!=NULL && $request->no_of_users!=""){
	     				$offers->no_of_users = implode(",", $request->no_of_users);
	     			}
	     			
	     			$offers->start_valid_date = $request->start_valid_date;
	     			$offers->end_valid_date = $request->end_valid_date;
	     			$offers->discount_percentage = $request->discount_percentage;
	     			$offers->status = 1;
		     		if($offers->save())
		     		{
						\Session::flash('msg', 'Offers Added Successfully.');
						return redirect('admin/offers');
		     		}
		     		else
		     		{
		     			\Session::flash('msg', 'Status Added Failed.');
						return redirect()->back();
		     		}
		        }	
		    }
		}
	}
	public function edit($id){

		$offers = Offers::find($id);
		$users = User::where('role_id',6)->where('status',1)->get();
		$userarr = explode(",", $offers->no_of_users);
		$currencyarr = explode(",", $offers->currency);
		 // dd($currencyarr);
		return view("admin.offers.edit",compact('offers','users','userarr','currencyarr'));
	}

	public function updateStatus($id,$status)
	{
		$Offers= Offers::findOrFail($id);
		$Offers->status = $status;
		$Offers->update();
		\Session::flash('msg', 'Status Updated Successfully.');
		return redirect('admin/offers');
	}

    public function update(Request $request, $id)
    {
    // dd($request->all());
    	$validator = Validator::make($request->all(), [
           'title' => 'required',
        	'coupon_code' => 'required',
        	'description' => 'required',
        	// 'no_of_users'=>'required',
        	'start_valid_date'=>'required',
        	'end_valid_date'=>'required',
        	'coupon_code'=>'required',
        	'discount_percentage'=>'required'
        ]);
        if ($validator->fails()) {
          
            return back()->withErrors($validator)->withInput();
        } else {

	     	$offers = Offers::findOrFail($id);
	     	$checkOfferCode = offers::where('coupon_code',$request->coupon_code)->where("id","!=",$id)->first();
	     	if(!is_null($checkOfferCode))
	     	{
				\Session::flash('error', 'Coupon Code Already Exist');
				return redirect()->back();	
	     	}
	     	else
	     	{
				$offers->title = isset($request->title) ?$request->title : $offers->title;
				$offers->discount_percentage = isset($request->discount_percentage) ?$request->discount_percentage :$offers->discount_percentage;
				$offers->min_classes = isset($request->min_classes) ?$request->min_classes :$offers->min_classes;
				if(isset($request->currency)){
					$offers->currency = isset($request->currency) ?implode(",",$request->currency) :$offers->currency;
				}
				
				$offers->description = isset($request->description) ?$request->description :$offers->description;
				$offers->coupon_code = isset($request->coupon_code) ?$request->coupon_code : $offers->coupon_code;

				$offers->no_of_users = isset($request->all_user) ?$request->all_user :$offers->all_user;
				if(isset($request->no_of_users)){
				$offers->no_of_users = implode(",",$request->no_of_users);
			}

		
				
				$offers->start_valid_date = isset($request->start_valid_date) ?$request->start_valid_date :$offers->start_valid_date;
				$offers->end_valid_date = isset($request->end_valid_date) ?$request->end_valid_date :$offers->end_valid_date;
				// dd($offers);/
				if($offers->save()){
					\Session::flash('msg', 'Offers Updated Successfully.');
					return redirect('admin/offers');		
				}
			}
		}
    }


    
}
