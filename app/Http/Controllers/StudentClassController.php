<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\StudentClass;
use App\Models\Role;

use Auth;

use Image;
class StudentClassController extends Controller
{
	/**
	 * Display a listing of the resource.
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function index()
	{
		$user = auth()->user();

$userRoles = $user->roles()->get(); 
	// dd($userRoles);
		//dd($user->can());
		$data = StudentClass::where('deleted',0)->orderBy('id', 'desc')->paginate(10);
		if(!empty($data)){
			foreach ($data as $key) {

				$key->image = !empty($key->image) ?  url('/public/').$key->image :'';
			}
		}
		//print_r($data);die;

		return view('admin.student_classes.index', compact('data'));
	}
	public function getclass(Request $request){
		$data = StudentClass::where('deleted',0)->orderBy('id', 'ASC')->get();
		if(!empty($data))
		{
			$result = array("status"=>true,"data"=>$data);
		}
		else
		{
			$result = array("status"=>false,"data"=>"");
		}
		echo json_encode($result);
	}

	/**
	 * Show the form for creating a new resource.
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function create()
	{
		return view('admin.student_classes.create');
	}

	/**
	 * Store a newly created resource in storage.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @return \Illuminate\Http\Response
	 */
	public function store(Request $request)
	{
		// print_r($request->all());die;
		$validator = Validator::make($request->all(), [
			'class_name' => 'required',
			'image' => 'required',
			// 'description' => 'required',
		]);

		if ($validator->fails()) {
			return back()
				->withErrors($validator)
				->withInput();
		}else { 
			//print_r($request->all());die;
			
			$stClass = new StudentClass();
			$stClass->class_name = $request->get('class_name');
			$fileimage = "";
             $cate_url = '';
             if ($request->hasfile('image')) {
                $file_image = $request->file('image');
                $fileimage = rand(00000, 99999) . date('d').".". $file_image->getClientOriginalExtension();
                $destination = public_path("upload/class");
                $file_image->move($destination, $fileimage);
                $cate_url = '/upload/class' . '/' . $fileimage;
                }

			$stClass->status = 1;
			$stClass->image= $cate_url;
			$stClass->description = $request->description;
			$stClass->save();
			$stClassId=$stClass->id;
			
			\Session::flash('msg', 'Student Class Added Successfully.');
			return redirect('admin/classes');
		}
	}

	/**
	 * Display the specified resource.
	 *
	 * @param  \App\Models\StudentClass $student_classes
	 * @return \Illuminate\Http\Response
	 */
	public function show(StudentClass $student_classes)
	{
		//
	}

	/**
	 * Show the form for editing the specified resource.
	 *
	 * @param  \App\Models\StudentClass $student_classes
	 * @return \Illuminate\Http\Response
	 */
	public function edit(StudentClass $student_classes, $id)
	{
		$stClass = StudentClass::findOrFail($id);
		$stClass->image = url('/').$stClass->image;
		return view('admin.student_classes.edit',compact('stClass'));
	}

	/**
	 * Update the specified resource in storage.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @param  \App\Models\StudentClass $student_classes
	 * @return \Illuminate\Http\Response
	 */
	public function update(Request $request, StudentClass $student_classes, $id)
	{
		
		$validator = Validator::make($request->all(), [
			'class_name' => 'required',
			// 'description' => 'required',
		]);

		if ($validator->fails()) {
			return back()
				->withErrors($validator)
				->withInput();
		}else {
			$stClass = StudentClass::findOrFail($id);
			$stClass->class_name = $request->get('class_name');
			$fileimage = "";
             $cate_url = '';

             if ($request->hasfile('image')) {

             	 if(file_exists(url('/').$stClass->image))
             	
                {
                    unlink(url('/').$stClass->image);
                }
	                $file_image = $request->file('image');
	                $fileimage = rand(00000, 99999) . date('d').".". $file_image->getClientOriginalExtension();
	                $destination = public_path("upload/class");
	                $file_image->move($destination, $fileimage);
	                $cate_url = '/upload/class' . '/' . $fileimage;

                }
                else
                {
                	$cate_url = $stClass->image;
                }
                $stClass->image = $cate_url;
            	$stClass->description = $request->description;
				$stClass->status = 1;
				$stClass->save();
				$stClassId=$id;
			
			\Session::flash('msg', 'Student Class Updated Successfully.');
			return redirect('/admin/classes');
		}

	}

	/**
	 * Remove the specified resource from storage.
	 *
	 * @param  \App\Models\StudentClass $student_classes
	 * @return \Illuminate\Http\Response
	 */
	 public function delete($id)
	{
		$stClass = StudentClass::findOrFail($id);
		$stClass->deleted=1;
		$stClass->update();
			\Session::flash('msg', 'Class deleted Successfully.');
	   return redirect('/admin/classes');
	}

	public function updateStatus($id,$status)
	{
		//echo "string";die;
		$stClass = StudentClass::findOrFail($id);
		$stClass->status=$status;
		$stClass->update();

	   return redirect('/admin/classes');
	}

}