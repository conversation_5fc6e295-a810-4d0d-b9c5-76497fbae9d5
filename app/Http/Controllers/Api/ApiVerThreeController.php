<?php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\UserCollection;
use App\Http\Helper as Helper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Mail;
use App\Mail\SendMail;
use App\Models\AboutUs;
use App\Models\Assignment;
use App\Models\AssignmentSubmission;
use App\Models\Banner;
use App\Models\Chapter;
use App\Models\ChatGroup;
use App\Models\ChatGroupUser;
use App\Models\ChatMessage;
use App\Models\City;
use App\Models\CommunityCategory;
use App\Models\CommunityPost;
use App\Models\Contactus;
use App\Models\ContinueStudy;
use App\Models\CouponCode;
use App\Models\Coursefeature;
use App\Models\Coursefeq;
use App\Models\Courses;
use App\Models\Franchise;
use App\Models\Lession;
use App\Models\LiveClass;
use App\Models\LiveclassNotify;
use App\Models\LiveClassTimeTable;
use App\Models\LiveClassTimeTableDoc;
use App\Models\Notification;
use App\Models\Page;
use App\Models\PinMessage;
use App\Models\Popularvideo;
use App\Models\Portfolio;
use App\Models\PostEntry;
use App\Models\PostEntryComment;
use App\Models\PostEntryLike;
use App\Models\QuestionAnswer;
use App\Models\QuestionAnswerLike;
use App\Models\QuestionAsk;
use App\Models\Quiz;
use App\Models\Quizoption;
use App\Models\Quizquestions;
use App\Models\RatingMessage;
use App\Models\RatingType;
use App\Models\RatingUser;
use App\Models\State;
use App\Models\StudentClass;
use App\Models\StudentClassSubject;
use App\Models\Subject;
use App\Models\StudentExam;
use App\Models\StudentExamAnswer;
use App\Models\Subscription;
use App\Models\User;
use App\Models\UserSubscription;
use App\Models\UserTracking;
use App\Models\UserTemp;
use Image;
/*use DateTime;
date_default_timezone_set('Asia/Kolkata');*/
use AWS;
use DB;
use Carbon\Carbon;
use PDF;
include public_path().'/razorpay-php/Razorpay.php';
use Razorpay\Api\Api;


class ApiVerThreeController extends Controller
{
	protected $helper;

	public function __construct(Helper $helper)
	{
		$this->helper = $helper;
	}

	public function generateRandomString($length = 50)
	{
		$characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
		$charactersLength = strlen($characters);
		$randomString = '';
		for ($i = 0; $i < $length; $i++) {
			$randomString .= $characters[rand(0, $charactersLength - 1)];
		}
		return $randomString;
	}

	public function convertTimeinMiliseconds($hour,$minute,$seconds)
	{
		$sec_to_milli = $seconds * 1000;            //seconds to milliseconds
		$min_to_milli = $minute * 60 * 1000;        //minutes to milliseconds
		$hrs_to_milli = $hour * 60 * 60 * 1000;     //hours to milliseconds

		$milliseconds = $hrs_to_milli + $min_to_milli + $sec_to_milli;

		return $milliseconds;
	}

	public function getStudentClass()
	{
		$studentClasses = StudentClass::where('status',1)->where('deleted',0)->orderBy('id', 'ASC')->get();
		$classdata = array();
		foreach ($studentClasses as $key => $val) {
			$classdata[$key]['id'] = $val['id'];
			$classdata[$key]['class_name'] = $val['class_name'];
		}
		$message = "Get All Student Classes List.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("student_classes" => $classdata)]);
	}

	public function getStudentSubject(Request $request)
	{
		$class_id = ($request->class_id) ? $request->class_id : '';
		$subjectdata = array();
		if (!empty($class_id)) {
			$classSubject = StudentClassSubject::where('class_id', $class_id)->first();
			if (!empty($classSubject)) {
				$subject_ids = !empty($classSubject->subject_ids) ? explode(",", $classSubject->subject_ids) : [];
				foreach ($subject_ids as $subject_id) {
					$subject = Subject::where("id",$subject_id)->first();
					$subjectdetail['id'] = $subject['id'];
					$subjectdetail['title'] = $subject['title'];
					array_push($subjectdata, $subjectdetail);
				}
				/*$studentSubjects = Subject::whereIn("id",$subject_ids)->where("status", 1)->orderBy("title", "ASC")->get();
				foreach ($studentSubjects as $key => $val) {
					$subjectdata[$key]['id'] = $val['id'];
					$subjectdata[$key]['title'] = $val['title'];
				}*/
			}
		} else {
			$studentSubjects = Subject::where('status', 1)->orderBy('title', 'ASC')->get();
			foreach ($studentSubjects as $key => $val) {
				$subjectdata[$key]['id'] = $val['id'];
				$subjectdata[$key]['title'] = $val['title'];
			}
		}
		$message = "Get All Student Subjects List.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("student_subjects" => $subjectdata)]);
	}

	public function getStates()
	{
		$states = State::orderBy('state', 'ASC')->get();
		$statedata = array();
		foreach ($states as $key => $val) {
			$statedata[$key]['id'] = $val['id'];
			$statedata[$key]['state_name'] = $val['state'];
		}
		$message = "Get All States List.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("states" => $statedata)]);
	}

	public function getCities()
	{
		$cities = City::orderBy('city', 'ASC')->get();
		$citydata = array();
		foreach ($cities as $key => $val) {
			$citydata[$key]['city_name'] = $val->city;
		}
		$message = "Get All Cities List.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("cities" => $citydata)]);
	}
	public function getCitiesByState(Request $request)
	{
		$stateId = $request->stateId;
		$cities = City::where("state_id", $stateId)->orderBy('city', 'ASC')->get();
		$citydata = array();
		if (!empty($cities)) {
			foreach ($cities as $key => $value) {
				$citydata[$key]['city_name']  = $value->city;
			}
			$message = "Cities List by State.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("cities" => $citydata)]);
		} else {
			$message = "Cities List not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("cities" => "")]);
		}
	}

	public function getuserDetail($userid)
	{
		$user = User::where('id', '=', $userid)->first();
		if ($user->role_id == 1) {
			$role = "Administrator";
		}else if ($user->role_id == 2) {
			$role = "Teacher";
		}else{
			$role = "User";
		}
		$studentClass = StudentClass::where("id", $user->class_id)->first();
		$class_name = !empty($studentClass) ? $studentClass->class_name : 'NA';
		//$features = UserFeature::where("user_id", $userid)->get();
		$featureArray = array();
		if (!empty($features)) {
			foreach ($features as $val) {
				$featureArray[] = array("name" => $val->name);
			}
		}
		$data = array(
			'userId' => ($user->id) ? $user->id : '',
			//'role_id' => ($role) ? $role : '',
			'name' => ($user->name) ? $user->name : '',
			'email' => ($user->email) ? $user->email : '',
			'phone' => ($user->phone) ? $user->phone : '',
			'gender' => ($user->gender) ? $user->gender : '',
			'dob' => ($user->dob) ? date('d/m/Y', strtotime($user->dob)) : '',
			'class_name' => $class_name,
			'school_college' => ($user->school_college) ? $user->school_college : '',
			'state' => ($user->state) ? $user->state : '',
			'city' => ($user->city) ? $user->city : '',
			'image' => ($user->image) ? asset('upload/profile') . '/' . $user->image : '',
			'api_token' => ($user->api_token) ? $user->api_token : '',
			'deviceToken' => ($user->deviceToken) ? $user->deviceToken : '',
			'otp_match' => ($user->otp_match) ? $user->otp_match : '',
			//'features' => $featureArray,
		);
		return $data;
	}
	
	public function login(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'email' => 'required',
			'password' => 'required',
			'deviceToken' => 'required',
		]);

		if($validator->fails()){
			$msg = $validator->messages()->first();
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}
		//dd($request->all());
		$email		 = !empty($_REQUEST['email']) ? trim($_REQUEST['email']) : '';
		//$mobile	 = !empty($_REQUEST['mobile']) ? trim($_REQUEST['mobile']) : '';
		$password    = !empty($_REQUEST['password']) ? trim($_REQUEST['password']) : '';
		$deviceToken = !empty($_REQUEST['deviceToken']) ? trim($_REQUEST['deviceToken']) : '';

		if (!empty($email) && !empty($password)) {
			if (is_numeric($email)) {
				$checkUser = User::where("phone", $email)->first();
				if (!empty($checkUser)) {
					if (Hash::check($password, $checkUser->password)) {
						if (Auth::attempt(['phone' => $email, 'password' => $password])) {
							$user = Auth::user();
							if($user->role_id != 3){
								$msg = "You are not allowed to login here!";
								return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
							}
							if($user->status != 1){
								$msg = "Your account not activated, Please contact to Team!";
								return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
							}
							$otp = rand(1111, 9999); //'5555';
							$user->generateToken();
							$userss = User::find($user->id);
							$userss->deviceToken = $deviceToken;
							$userss->otp_match = $otp;
							$userss->save();
						/*ak	$this->helper->sms($user->phone, $otp);
							$this->helper->sendEmail($user->email, 'Guruathome: Verify OTP', $data = array('userName' => $user->name, 'message' => '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: ' . $otp . '</p>'));
*/
							$code = 200;
							$msg = 'Login successfully.';
							$returndata = $user;
							$returndata['image'] = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
							$returndata['current_otp'] = $otp;
						}else{
							$code = 400;
							$msg = 'Please enter correct mobile no or password!';
							$returndata = array("id" => "");
						}
					}else{
						$code = 400;
						$msg = 'Please enter correct password!';
						$returndata = array("id" => "");
					}
				}else{
					$code = 400;
					$msg = 'Please enter correct mobile no!';
					$returndata = array("id" => "");
				}
			} else {
				$checkUser = User::where("email", $email)->first();
				if (!empty($checkUser)) {
					if (Hash::check($password, $checkUser->password)) {
						if(Auth::attempt(['email' => $email, 'password' => $password])){
							$user = Auth::user();
							if($user->role_id != 3){
								$msg = "You are not allowed to login here!";
								return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
							}
							if($user->status != 1){
								$msg = "Your account not activated, Please contact to Team!";
								return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
							}
							$otp = '5555'; //rand(1111, 9999);
							//$user['token'] =  $user->createToken('MyApp')->accessToken;
							$user->generateToken();
							$userss = User::find($user->id);
							$userss->deviceToken = $deviceToken;
							$userss->otp_match = $otp;
							$userss->save();
							//$this->helper->sms($user->phone, $otp);
							//$this->helper->sendEmail($user->email, 'Guruathome: Verify OTP', $data = array('userName' => $user->name, 'message' => '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: ' . $otp . '</p>'));

							$code = 200;
							$msg = 'Login successfully.';
							$returndata = $user;
							$returndata['image'] = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
							$returndata['current_otp'] = $otp;
						}else{
							$code = 400;
							$msg = 'Please enter correct email or password!';
							$returndata = array("id" => "");
						}
					}else{
						$code = 400;
						$msg = 'Please enter correct password!';
						$returndata = array("id" => "");
					}
				}else{
					$code = 400;
					$msg = 'Please enter correct email!';
					$returndata = array("id" => "");
				}
			}
		}else{
			$code = 400;
			$msg = 'Please enter correct mobile no or email!';
			$returndata = array("id" => "");
		}
		return response()->json(['statusCode' => $code, 'message' => $msg, 'data' => $returndata]);
	}

	public function register(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'name' => 'required|regex:/^[\pL\s\-]+$/u',
			'email' => 'required|email|unique:users,email',
			'phone' => 'required|numeric|unique:users,phone',
			'class_name' => 'required',
			'city' => 'required',
			'password' => 'required|min:6',
			'confirm_password' => 'required|min:6|max:20|same:password',
		]);

		if($validator->fails()){
			$msg = $validator->messages()->first();
			if($msg=='The email has already been taken.'){
				$msg = 'Email id already exists. Please login directly.';
			}
			if($msg=='The phone has already been taken.'){
				$msg = 'Phone number already exists. Please login directly.';
			}
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}

		$name 				= ucwords($request->input('name'));
		$email 				= $request->input('email');
		$phone 				= $request->input('phone');
		$password 			= $request->input('password');
		$confirm_password 	= $request->input('confirm_password');
		$gender 			= $request->input('gender');
		$class_name 		= $request->input('class_name');
		$refer_code			= $request->input('refer_code');
		$city 				= $request->input('city');
		$deviceToken 		= $request->input('deviceToken');

		$cities = City::where('city', $city)->first();
		$state_id = $cities->state_id;
		$state = '';
		if ($state_id != '' && $state_id > 0) {
			$states = State::where('id', $state_id)->first();
			$state = $states->state;
		}

		$msg = '';
		if (!empty($name)  && !empty($email) && !empty($phone) && !empty($password)) {
			if ($password != $confirm_password) {
				$msg = "Password and Confirm password not matched!";
				return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
			}
			$usercheck = User::where('email', $email)->first();
			if (!empty($usercheck)) {
				$msg = "Email id already exists. Please login directly.";
				return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
			} else {
				$phonecheck = User::where('phone', $phone)->first();
				if (!empty($phonecheck)) {
					$msg = "Phone number already exists. Please login directly.";
					return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
				} else {
					$franchiseUserId = 0;
					if (!empty($refer_code)) {
						$franchise = Franchise::where("refer_code", $refer_code)->first();
						if (!empty($franchise)) {
							$franchiseUserId = $franchise->user_id;
							if ($gender=='Female'){
								$user_gender = 202;
							} elseif ($gender=='Non Binary'){
								$user_gender = 203;
							} else {
								$user_gender = 201;
							}
							$ch = curl_init();
							$url = config('constant.FMSLEADAPIURL');
							curl_setopt($ch, CURLOPT_URL,$url);
							curl_setopt($ch, CURLOPT_POST, true);
							curl_setopt($ch, CURLOPT_POSTFIELDS, "user_id=$franchiseUserId&full_name=$name&email_address=$email&mobile_number=$phone&class_name=$class_name&user_gender=$user_gender&city_name=$city&state_name=$state");
							curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
							$output = curl_exec ($ch);

							curl_close ($ch);

							$response = json_decode($output);
							//echo '<pre />'; print_r($response); die; // Show output
							if ($response->Status==false) {
								$msg = $response->Message;
								return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
							}
						} else {
							$msg = "Referral code not exists. Please try again!";
							return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
						}
					}

					$imagess = '';
					if (isset($_FILES['image']['name']) && $_FILES['image']['name']!='') {
						$profileimagename = $_FILES['image']['name'];
						$tmpimage1 = $_FILES['image']['tmp_name'];
						$newprofileImage = rand(00000, 99999) . date('d') . $profileimagename;
						$location = "upload/profile/";
						move_uploaded_file($tmpimage1, $location . $newprofileImage);
						$url = 'upload/profile/' . $newprofileImage;
						$img = Image::make($url)->resize(200, 200);
						$imagess =  $img->basename;
					}
					$otp = rand(1111, 9999);
					$data = array(
						'role_id' 	=> 3,
						'name' 		=> $name,
						'email' 	=> $email,
						'phone' 	=> $phone,
						'password' 	=> bcrypt($password),
						'userpass' 	=> $password,
						//'dob'     => $dob,
						'gender' 	=> $gender,
						'class_name' => $class_name,
						//'school_college' => $school_college,
						//'image'   => $imagess,
						'state' 	=> $state,
						'city' 		=> $city,
						'franchise_user_id' => $franchiseUserId,
						'refer_code' 	=> $refer_code,
						'remember_token' => Str::random(60),
						'api_token' => Str::random(60),
						'devicetoken' => $deviceToken,
						'otp_match' => $otp,
						'status'    => 1,
						//'isDevice'=>'App',
						'created_at' => date('Y-m-d H:i:s'),
					);
					$userId = User::insertGetId($data);
					$msg = 'User Registration Completed Successfully.';
					$this->helper->addNotification($userId,$msg);
					/*
					//ak
					$this->helper->sms($phone, $otp);
					$this->helper->sendEmail($email, 'Guruathome: Verify your account', $data = array('userName' => $name, 'message' => '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: ' . $otp . '</p>'));
					*/
					$returndata = array();
					$returndata = $this->getuserDetail($userId);
					$returndata['otp'] = $otp;

					$user = User::where("id", $userId)->first();
					//Add 7 days free trial to new user
					$today	  = date('Y-m-d');
					$userSubscription = UserSubscription::where("user_id", $userId)->orderBy('id', 'DESC')->first();
					if (empty($userSubscription)) {
						$franchise = Franchise::where("refer_code", $user->refer_code)->where("refer_code", "!=", "")->first();
						if (!empty($franchise)) {
							$date = strtotime($today);
							if ($franchise->refer_code == 'IVR4LDY5') {
								$date = strtotime("+3 days", $date);
							} else {
								$date = strtotime("+7 days", $date);
							}
							$end_date = date('Y-m-d', $date);
							$data1 = array(
								'user_id'			=> $userId,
								'subscription_id'	=> 1,
								'start_date'		=> $today,
								'end_date'			=> $end_date,
								'mode'				=> 'FREE',
								'paymentStatus'		=> 1,
								'created_at'		=> date('Y-m-d H:i:s'),
							);
							$inserId = UserSubscription::insertGetId($data1);
						}

						//send coupon code to franchise student
						$checkCoupon = CouponCode::where('user_id', $userId)->where('condition_1', 1)->first();
						if (empty($checkCoupon)) {
							$franchise = Franchise::where("refer_code", $user->refer_code)->where("refer_code", "!=", "")->first();
							if (!empty($franchise)) {
								$coupon = $this->helper->getcouponcode();
								$date = strtotime($today);
								$date = strtotime("+1 month", $date);
								$coupon_end_date = date('Y-m-d', $date);
								$couponcode = new CouponCode();
								$couponcode->coupon = $coupon;
								$couponcode->discount = 25;
								$couponcode->end_date = $coupon_end_date;
								$couponcode->description = 'Franchise user auto generated coupon code.';
								$couponcode->condition_1 = 1;
								$couponcode->user_id = $userId;
								$couponcode->subscription_id = 1;
								$couponcode->no_of_users = 0;
								$couponcode->save();
								$couponcodeId = $couponcode->id;
								
								//ak $this->helper->sendEmail($user->email, 'Guruathome: Coupon Code Discount', $data = array('userName' => $user->name, 'message' => '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your Coupon Code: ' . $coupon . '</p><p>This Coupon code discount valid till: ' . $coupon_end_date . '</p>'));
							}
						}
					}

					return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => $returndata]);
				}
			}
		} else {
			$msg = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}
	}
	public function otpMatch(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'userId' => 'required|numeric',
			'otp' => 'required|numeric',
		]);

		if($validator->fails()){
			$msg = $validator->messages()->first();
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}
		$userId = $request->input('userId');
		$otp = $request->input('otp');
		if (empty($otp) || empty($userId)) {
			return response()->json(['statusCode' => 400, 'message' => 'Wrong parameter Passed!', 'data' => array("id" => "")]);
		}
		$user = User::where('otp_match', $otp)->where("id", $userId)->first();
		if ($user) {
			User::where('id', $userId)->update(array('status' => 1));

			//Add 7 days free trial to new user
			$today	  = date('Y-m-d');
			$userSubscription = UserSubscription::where("user_id", $userId)->orderBy('id', 'DESC')->first();
			if (empty($userSubscription)) {
				$franchise = Franchise::where("refer_code", $user->refer_code)->where("refer_code", "!=", "")->first();
				if (!empty($franchise)) {
					$date = strtotime($today);
					if ($franchise->refer_code == 'IVR4LDY5') {
						$date = strtotime("+3 days", $date);
					} else {
						$date = strtotime("+7 days", $date);
					}
					$end_date = date('Y-m-d', $date);
					$data1 = array(
						'user_id'			=> $userId,
						'subscription_id'	=> 1,
						'start_date'		=> $today,
						'end_date'			=> $end_date,
						'mode'				=> 'FREE',
						'paymentStatus'		=> 1,
						'created_at'		=> date('Y-m-d H:i:s'),
					);
					$inserId = UserSubscription::insertGetId($data1);
				}

				//send coupon code to franchise student
				$checkCoupon = CouponCode::where('user_id', $userId)->where('condition_1', 1)->first();
				if (empty($checkCoupon)) {
					$franchise = Franchise::where("refer_code", $user->refer_code)->first();
					if (!empty($franchise)) {
						$coupon = $this->helper->getcouponcode();
						$date = strtotime($today);
						$date = strtotime("+1 month", $date);
						$coupon_end_date = date('Y-m-d', $date);
						$couponcode = new CouponCode();
						$couponcode->coupon = $coupon;
						$couponcode->discount = 25;
						$couponcode->end_date = $coupon_end_date;
						$couponcode->description = 'Franchise user auto generated coupon code.';
						$couponcode->condition_1 = 1;
						$couponcode->user_id = $userId;
						$couponcode->subscription_id = 1;
						$couponcode->no_of_users = 0;
						$couponcode->save();
						$couponcodeId = $couponcode->id;
						
					//ak	$this->helper->sendEmail($user->email, 'Guruathome: Coupon Code Discount', $data = array('userName' => $user->name, 'message' => '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your Coupon Code: ' . $coupon . '</p><p>This Coupon code discount valid till: ' . $coupon_end_date . '</p>'));
					}
				}
				
			//ak	$this->helper->smsWithTemplate($user->phone, 'AfterVerificationAccount', '+************', '<EMAIL>');
			}
			
			$returndata = array();
			$returndata = $this->getuserDetail($user->id);
			return response()->json(['statusCode' => 200, 'message' => 'Account Verified Successfully.', 'data' => $returndata]);
		} else {
			return response()->json(['statusCode' => 400, 'message' => 'otp does not match!', 'data' =>  array("id" => "")]);
		}
	}
	public function resendOtp(Request $request)
	{
		$userId = $request->input('userId');
		if (!empty($userId)) {
			$checkUser = User::where('id', $userId)->first();
			if ($checkUser) {
				$otpnumber = rand(1111, 9999);
				$phone = $checkUser->phone;
				$update = User::where('id', $userId)->update(['otp_match' => $otpnumber]);
				if ($update) {
					$returndata['otp'] = $otpnumber;
					$msg = 'Verification Otp Send, Please Check.';
					/*
					//ak
					$this->helper->sms($phone, $otpnumber);
					$this->helper->sendEmail($checkUser->email, 'Guruathome: Verify OTP', $data = array('userName' => $checkUser->name, 'message' => '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: ' . $otpnumber . '</p>'));
					*/
					
					return response()->json(['statusCode' => 200, 'message' => $msg, "data" => $returndata]);
				} else {
					return response()->json(['statusCode' => 400, 'message' => 'Somthing Went Wrong!', 'data' => array("otp" => "")]);
				}
			} else {
				return response()->json(['statusCode' => 400, 'message' => 'Invaild User Id!', 'data' => array("otp" => "")]);
			}
		} else {
			return response()->json(['statusCode' => 400, 'message' => 'Wrong Paramenter Passed!', 'data' => array("otp" => "")]);
		}
	}

	public function registerNew(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'email'			=> 'required|email|unique:users,email',
			'country_code' 	=> 'required',
			'phone'			=> 'required|numeric|unique:users,phone',
		]);

		if($validator->fails()){
			$msg = $validator->messages()->first();
			if($msg=='The email has already been taken.'){
				$msg = 'Email id already exists. Please login directly.';
			}
			if($msg=='The phone has already been taken.'){
				$msg = 'Phone number already exists. Please login directly.';
			}
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "", "otp" => "")]);
		}

		$email 			= $request->input('email');
		$country_code 	= trim($request->input('country_code'),"+");
		$country_flag 	= strtoupper($request->input('country_flag'));
		$phone 			= $request->input('phone');

		$msg = '';
		if (!empty($email) && !empty($country_code) && !empty($phone)) {
			$otp = rand(1111, 9999);
			$request['otp'] = $otp;
			$userTemp = UserTemp::where("email", $email)->where("country_code", $country_code)->where("phone", $phone)->first();
			if (empty($userTemp)) {
				$userTemp = UserTemp::where("phone", $phone)->first();
				if (empty($userTemp)) {
					$userTemp = UserTemp::where("email", $email)->first();
					if (empty($userTemp)) {
						$userTemp = new UserTemp();
					}
				}
			}
			$userTemp->fill($request->all());
			$userTemp->save();
			$userId = $userTemp->id;
			$msg = 'OTP sent on your mobile OR email, Please check and verify your account first.';
/*
//ak
			$this->helper->sms($country_code.$phone, $otp);
			$this->helper->sendEmail($email, 'Guruathome: Verify your account', $data = array('userName' => 'User', 'message' => '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: ' . $otp . '</p>'));
*/


			return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => array("id" => $userId, "otp" => $otp)]);
		} else {
			$msg = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "", "otp" => "")]);
		}
	}
	public function verifyNewAccount(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'id' => 'required|numeric',
			'otp' => 'required|numeric',
		]);

		if($validator->fails()){
			$msg = $validator->messages()->first();
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}
		$id = $request->input('id');
		$otp = $request->input('otp');
		$deviceToken = $request->input('deviceToken');
		if (empty($id) || empty($otp)) {
			return response()->json(['statusCode' => 400, 'message' => 'Wrong parameter Passed!', 'data' => array("id" => "")]);
		}
		$userTemp = UserTemp::where("id", $id)->where('otp', $otp)->first();
		if ($userTemp) {
			$user = User::where("email", $userTemp->email)->where('phone', $userTemp->phone)->first();
			if (empty($user)) {
				$data = array(
					'role_id'		=> 3,
					'email' 		=> $userTemp->email,
					'country_code' 	=> $userTemp->country_code,
					'country_flag' 	=> $userTemp->country_flag,
					'phone' 		=> $userTemp->phone,
					'otp_match' 	=> $userTemp->otp,
					'remember_token'=> Str::random(60),
					'api_token' 	=> Str::random(60),
					'devicetoken' 	=> $deviceToken,
					'status'    	=> 1,
					'created_at' 	=> date('Y-m-d H:i:s'),
				);
				$userId = User::insertGetId($data);
			} else {
				$userId = $user->id;
			}
			$msg = 'Please complete your profile first.';
			//$userTemp->delete();
			
			$returndata = array();
			$returndata = $this->getuserDetail($userId);
			return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => $returndata]);
		} else {
			$user = User::where("id", $id)->where('otp_match', $otp)->first();
			if ($user) {
				$userId = $user->id;
				User::where('id', $userId)->update(array('status' => 1));
				$msg = 'Account Verified Successfully.';
				
				$returndata = array();
				$returndata = $this->getuserDetail($userId);
				return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => $returndata]);
			} else {
				$msg = 'OTP does not matched!';
				return response()->json(['statusCode' => 400, 'message' => $msg, 'data' =>  array("id" => "")]);
			}
		}
	}
	public function saveFirstProfile(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'userId' => 'required|numeric',
			'name' => 'required|regex:/^[\pL\s\-]+$/u',
			'class_id' => 'required|numeric',
			'password' => 'required|min:6',
			//'confirm_password' => 'required|min:6|max:20|same:password',
		]);

		if($validator->fails()){
			$msg = $validator->messages()->first();
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}

		$userId 			= $request->input('userId');
		$name 				= ucwords($request->input('name'));
		$class_id 			= $request->input('class_id');
		$password 			= $request->input('password');
		//$confirm_password 	= $request->input('confirm_password');
		$refer_code			= $request->input('refer_code');
		$deviceToken 		= $request->input('deviceToken');

		$msg = '';
		if (!empty($name)  && !empty($class_id) && !empty($password)) {
			$user = User::findOrFail($userId);
			$studentClass = StudentClass::where("id", $class_id)->first();
			$class_name = !empty($studentClass) ? $studentClass->class_name : 'NA';
			$gender = $user->gender;
			$email = $user->email;
			$phone = $user->phone;
			$city = !empty($user->city) ? $user->city : 'NA';
			$state = !empty($user->state) ? $user->state : 'NA';
			$franchiseUserId = 0;
			if (!empty($refer_code)) {
				$franchise = Franchise::where("refer_code", $refer_code)->first();
				if (!empty($franchise)) {
					$franchiseUserId = $franchise->user_id;
					if ($gender=='Female'){
						$user_gender = 202;
					} elseif ($gender=='Non Binary'){
						$user_gender = 203;
					} else {
						$user_gender = 201;
					}
					$ch = curl_init();
					$url = config('constant.FMSLEADAPIURL');
					curl_setopt($ch, CURLOPT_URL,$url);
					curl_setopt($ch, CURLOPT_POST, true);
					curl_setopt($ch, CURLOPT_POSTFIELDS, "user_id=$franchiseUserId&full_name=$name&email_address=$email&mobile_number=$phone&class_name=$class_name&user_gender=$user_gender&city_name=$city&state_name=$state");
					curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
					$output = curl_exec ($ch);

					curl_close ($ch);

					$response = json_decode($output);
					//echo '<pre />'; print_r($response); die; // Show output
					if ($response->Status==false) {
						$msg = $response->Message;
						return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
					}
				} else {
					$msg = "Referral code not exists. Please try again!";
					return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
				}
			}

			$data = array(
				'name'				=> $name,
				'password' 			=> bcrypt($password),
				'userpass' 			=> $password,
				'class_id'			=> $class_id,
				'franchise_user_id' => $franchiseUserId,
				'refer_code' 		=> $refer_code,
				//'remember_token' 	=> Str::random(60),
				//'api_token' 		=> Str::random(60),
				'devicetoken' 		=> $deviceToken,
			);
			$updateUser = $user->update($data);
			$msg = 'User Registration Completed Successfully.';
			$this->helper->addNotification($userId,$msg);
			$returndata = array();
			$returndata = $this->getuserDetail($userId);

			//Add 7 days free trial to new user
			$today	  = date('Y-m-d');
			$userSubscription = UserSubscription::where("user_id", $userId)->orderBy('id', 'DESC')->first();
			if (empty($userSubscription)) {
				$franchise = Franchise::where("refer_code", $user->refer_code)->where("refer_code", "!=", "")->first();
				if (!empty($franchise)) {
					$date = strtotime($today);
					if ($franchise->refer_code == 'IVR4LDY5') {
						$date = strtotime("+3 days", $date);
					} else {
						$date = strtotime("+7 days", $date);
					}
					$end_date = date('Y-m-d', $date);
					$data1 = array(
						'user_id'			=> $userId,
						'subscription_id'	=> 1,
						'start_date'		=> $today,
						'end_date'			=> $end_date,
						'mode'				=> 'FREE',
						'paymentStatus'		=> 1,
						'created_at'		=> date('Y-m-d H:i:s'),
					);
					$inserId = UserSubscription::insertGetId($data1);
				}

				//send coupon code to franchise student
				$checkCoupon = CouponCode::where('user_id', $userId)->where('condition_1', 1)->first();
				if (empty($checkCoupon)) {
					$franchise = Franchise::where("refer_code", $user->refer_code)->where("refer_code", "!=", "")->first();
					if (!empty($franchise)) {
						$coupon = $this->helper->getcouponcode();
						$date = strtotime($today);
						$date = strtotime("+1 month", $date);
						$coupon_end_date = date('Y-m-d', $date);
						$couponcode = new CouponCode();
						$couponcode->coupon = $coupon;
						$couponcode->discount = 25;
						$couponcode->end_date = $coupon_end_date;
						$couponcode->description = 'Franchise user auto generated coupon code.';
						$couponcode->condition_1 = 1;
						$couponcode->user_id = $userId;
						$couponcode->subscription_id = 1;
						$couponcode->no_of_users = 0;
						$couponcode->save();
						$couponcodeId = $couponcode->id;
						
						//ak $this->helper->sendEmail($user->email, 'Guruathome: Coupon Code Discount', $data = array('userName' => $user->name, 'message' => '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your Coupon Code: ' . $coupon . '</p><p>This Coupon code discount valid till: ' . $coupon_end_date . '</p>'));
					}
				}
			}

			return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => $returndata]);
		} else {
			$msg = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}
	}
	
	public function loginNew(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'email' => 'required',
			'password' => 'required',
			'deviceToken' => 'required',
		]);

		if($validator->fails()){
			$msg = $validator->messages()->first();
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}
		//dd($request->all());
		$country_code	= !empty($request->input('country_code')) ? trim($request->input('country_code'),"+") : '';
		$email		 	= !empty($request->input('email')) ? trim($request->input('email')) : '';
		//$mobile	 	= !empty($request->input('mobile')) ? trim($request->input('mobile')) : '';
		$password    	= !empty($request->input('password')) ? trim($request->input('password')) : '';
		$deviceToken 	= !empty($request->input('deviceToken')) ? trim($request->input('deviceToken')) : '';

		if (!empty($email) && !empty($password)) {
			if (is_numeric($email)) {
				if (!empty($country_code)) {
					$checkUser = User::where("country_code", $country_code)->where("phone", $email)->first();
					if (!empty($checkUser)) {
						if($checkUser->name!='' && $checkUser->class_id!='' && $checkUser->password!=''){
							if(!empty($checkUser) && $checkUser->deleted == 1){
								$msg = 'Please contact to our Team first to activate your account!';
								return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
							}
							if (Hash::check($password, $checkUser->password)) {
								if (Auth::attempt(['phone' => $email, 'password' => $password])) {
									$user = Auth::user();
									if($user->role_id != 3){
										$msg = "You are not allowed to login here!";
										return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
									}
									if($user->status != 1){
										$msg = "Your account not activated, Please contact to Team!";
										return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
									}
									$otp = ($email=='**********') ? '5555' : rand(1111, 9999); //'5555';
									$user->generateToken();
									$userss = User::find($user->id);
									$userss->deviceToken = $deviceToken;
									$userss->otp_match = $otp;
									$userss->save();
									/*
									//ak
									$this->helper->sms($user->phone, $otp);
									$this->helper->sendEmail($user->email, 'Guruathome: Verify OTP', $data = array('userName' => $user->name, 'message' => '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: ' . $otp . '</p>'));
									*/
									$code = 200;
									$msg = 'Login successfully.';
									$returndata = $user;
									$returndata['image'] = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
									$returndata['current_otp'] = $otp;
								}else{
									$code = 400;
									$msg = 'Please enter correct mobile no or password!';
									$returndata = array("id" => "");
								}
							}else{
								$code = 400;
								$msg = 'Please enter correct password!';
								$returndata = array("id" => "");
							}
						}else{
							$code = 500;
							$msg = 'Please complete your first profile!';
							$returndata = array("id" => $checkUser->id);
						}
					}else{
						$code = 400;
						$msg = 'Please enter correct mobile no!';
						$returndata = array("id" => "");
					}
				}else{
					$code = 400;
					$msg = 'Please enter correct country code!';
					$returndata = array("id" => "");
				}
			} else {
				$checkUser = User::where("email", $email)->first();
				if (!empty($checkUser)) {
					if($checkUser->name!='' && $checkUser->class_id!='' && $checkUser->password!=''){
						if(!empty($checkUser) && $checkUser->deleted == 1){
							$msg = 'Please contact to our Team first to activate your account!';
							return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
						}
						if (Hash::check($password, $checkUser->password)) {
							if(Auth::attempt(['email' => $email, 'password' => $password])){
								$user = Auth::user();
								if($user->role_id != 3){
									$msg = "You are not allowed to login here!";
									return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
								}
								if($user->status != 1){
									$msg = "Your account not activated, Please contact to Team!";
									return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
								}
								$otp = ($email=='<EMAIL>') ? '5555' : rand(1111, 9999); //'5555';
								//$user['token'] =  $user->createToken('MyApp')->accessToken;
								$user->generateToken();
								$userss = User::find($user->id);
								$userss->deviceToken = $deviceToken;
								$userss->otp_match = $otp;
								$userss->save();
/*//ak
								$this->helper->sms($user->phone, $otp);
								$this->helper->sendEmail($user->email, 'Guruathome: Verify OTP', $data = array('userName' => $user->name, 'message' => '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: ' . $otp . '</p>'));
*/

								$code = 200;
								$msg = 'Login successfully.';
								$returndata = $user;
								$returndata['image'] = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
								$returndata['current_otp'] = $otp;
							}else{
								$code = 400;
								$msg = 'Please enter correct email or password!';
								$returndata = array("id" => "");
							}
						}else{
							$code = 400;
							$msg = 'Please enter correct password!';
							$returndata = array("id" => "");
						}
					}else{
						$code = 500;
						$msg = 'Please complete your first profile!';
						$returndata = array("id" => $checkUser->id);
					}
				}else{
					$code = 400;
					$msg = 'Please enter correct email!';
					$returndata = array("id" => "");
				}
			}
		}else{
			$code = 400;
			$msg = 'Please enter correct mobile no or email!';
			$returndata = array("id" => "");
		}
		return response()->json(['statusCode' => $code, 'message' => $msg, 'data' => $returndata]);
	}

	public function forgetPasswordNew(Request $request)
	{
		$returndata = array();
		$country_code = !empty($request->country_code) ? trim($request->country_code,"+") : '';
		$email = $request->email;
		if ($email != '') {
			if (is_numeric($email)) {
				$user = User::where('country_code', $country_code)->where('phone', $email)->first();
			} else {
				$user = User::where('email', $email)->first();
			}
		} else {
			$msg = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("type" => "forgetpassword")]);
		}
		if (!empty($user)) {
			$otp = rand(1111, 9999);
			$user->otp_match = $otp;
			$user->save();
			$msg = 'Verification OTP send on your mobile and email address, Please Check.';
			/*//ak
			$this->helper->sms($user->phone, $otp);
			$this->helper->sendEmail($user->email, 'Guruathome: Forgot Password', $data = array('userName' => $user->name, 'message' => '<p>You have been forgotton your password, don\'t worry, Please reset your password </p><p>You have got successfully your OTP: '. $otp . '</p>'));
			*/
			
			$returndata['type'] = 'forgetpassword';
			$returndata['userId'] = $user->id;
			$returndata['otp'] = $otp;
			return response()->json(['statusCode' => 200, 'message' => $msg, "data" => $returndata]);
		} else {
			$msg = "Record Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("type" => "forgetpassword")]);
		}
	}
	public function resendOtpNew(Request $request)
	{
		$otpnumber = rand(1111, 9999);
		$country_code 	= trim($request->input('country_code'),"+");
		$email 			= $request->input('email');
		if ($email != '') {
			if ($email=='**********') {$otpnumber = '5555'; }
			if ($email=='<EMAIL>') {$otpnumber = '5555'; }
			if (is_numeric($email)) {
				$user = User::where('country_code', $country_code)->where('phone', $email)->first();
				if ($user) {
					$update = $user->update(['otp_match' => $otpnumber]);
				
					$userTemp = UserTemp::where('country_code', $country_code)->where('phone', $email)->first();
					$userTempId = $userTemp->id;
					$update1 = $userTemp->update(['otp' => $otpnumber]);
				}
			} else {
				$user = User::where('email', $email)->first();
				if ($user) {
					$update = $user->update(['otp_match' => $otpnumber]);
				
					$userTemp = UserTemp::where('email', $email)->first();
					$userTempId = $userTemp->id;
					$update1 = $userTemp->update(['otp' => $otpnumber]);
				}
			}
			if ($user) {
				if ($update) {
					$userName = isset($user->name) ? $user->name : 'user';
					$phone = $user->phone;
					$msg = 'Verification Otp Send, Please Check.';
					/*//ak
					$this->helper->sms($phone, $otpnumber);
					$this->helper->sendEmail($user->email, 'Guruathome: Verify OTP', $data = array('userName' => $userName, 'message' => '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: ' . $otpnumber . '</p>'));
					*/
					
					$returndata['id'] = $userTempId;					
					$returndata['otp'] = $otpnumber;					
					return response()->json(['statusCode' => 200, 'message' => $msg, "data" => $returndata]);
				} else {
					return response()->json(['statusCode' => 400, 'message' => 'Somthing Went Wrong!', 'data' => array("id" => "", "otp" => "")]);
				}
			} else {
				if (is_numeric($email)) {
					$userTemp = UserTemp::where('country_code', $country_code)->where('phone', $email)->first();
					if ($userTemp) {
						$userTempId = $userTemp->id;
						$update1 = $userTemp->update(['otp' => $otpnumber]);
					}
				} else {
					$userTemp = UserTemp::where('email', $email)->first();
					if ($userTemp) {
						$userTempId = $userTemp->id;
						$update1 = $userTemp->update(['otp' => $otpnumber]);
					}
				}
				if ($userTemp) {
					if ($update1) {
						$userName = 'user';
						$phone = $userTemp->phone;
						$msg = 'Verification Otp Send, Please Check.';
						
						/*
						//ak
						$this->helper->sms($phone, $otpnumber);
						$this->helper->sendEmail($userTemp->email, 'Guruathome: Verify OTP', $data = array('userName' => $userName, 'message' => '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: ' . $otpnumber . '</p>'));
						
						*/
						$returndata['id'] = $userTempId;					
						$returndata['otp'] = $otpnumber;					
						return response()->json(['statusCode' => 200, 'message' => $msg, "data" => $returndata]);
					}
				}
			}
		} else {
			$msg = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "", "otp" => "")]);
		}
	}

	public function forgetpassword(Request $request)
	{
		$returndata = array();
		$email = $request->email;
		if ($email != '') {
			if (is_numeric($email)) {
				$user = User::where('phone', $email)->first();
			} else {
				$user = User::where('email', $email)->first();
			}
		} else {
			$msg = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("type" => "forgetpassword")]);
		}
		if (!empty($user)) {
			$otp = rand(1111, 9999);
			$user->otp_match = $otp;
			$user->save();
			$msg = 'Verification OTP send on your mobile and email address, Please Check.';
			/*
			//ak
			$this->helper->sms($user->phone, $otp);
			$this->helper->sendEmail($user->email, 'Guruathome: Forgot Password', $data = array('userName' => $user->name, 'message' => '<p>You have been forgotton your password, don\'t worry, Please reset your password </p><p>You have got successfully your OTP: '. $otp . '</p>'));
			*/
			
			$returndata['type'] = 'forgetpassword';
			$returndata['userId'] = $user->id;
			$returndata['otp'] = $otp;
			return response()->json(['statusCode' => 200, 'message' => $msg, "data" => $returndata]);
		} else {
			$msg = "Phone Number Not Exist!";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("type" => "forgetpassword")]);
		}
	}
	public function resetPassword(Request $request)
	{
		$userId 			= $request->userId;
		$newPassword 		= $request->newPassword;
		$confirmPassword 	= $request->confirmPassword;
		$checkUser = User::where('id', $userId)->first();
		if (!empty($checkUser)) {
			if (!empty($newPassword)) {
				if ($newPassword != $confirmPassword) {
					$msg = "Password and Confirm password not matched!";
					return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("userId" => $userId)]);
				}
				$updateData = User::where('id', $userId)->update([
					'password'		=> bcrypt($newPassword),
					'userpass'		=> $newPassword,
					'updated_at' 	=> date('Y-m-d H:i:s')
				]);
				$message = 'Password Reset Successfully';
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userId" => $userId)]);
			} else {
				$message = 'Please enter new password!';
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userId" => $userId)]);
			}
		} else {
			$msg = "Invalid User Id ";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("userId" => $userId)]);
		}
	}

	public function checkUserToken(Request $request)
	{
		$userId 	= $request->userId;
		$bearerToken = $request->bearerToken();
		if ($userId > 0) {
			$user = User::where("id", $userId)->where("deleted", 0)->first();
			$userStatus = !empty($user) ? $user->status : 0;
			if ($userStatus == 1) {
				if ($bearerToken != $user->api_token) {
					$userStatus = 0;
				}
			}
		} else {
			$userStatus = 0;
		}
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message]);
		} else {
			$message = "User Available.";
			return response()->json(['statusCode' => 200, 'message' => $message]);
		}
	}

	public function isUserActive($userId,$bearerToken=NULL,$deviceType='android')
	{
		if ($userId > 0) {
			$user = User::where("id", $userId)->where("deleted", 0)->first();
			$userStatus = !empty($user) ? $user->status : 0;
			if ($userStatus == 1) {
				if ($bearerToken != $user->api_token) {
					$userStatus = 0;
				}
			}
		} else {
			$userStatus = 0;
		}
		if ($userStatus==0) {
			$this->helper->trackingApi($userId, 'logout', '', 0, '', $deviceType, 1);
		}
		return $userStatus;
	}

	public function userLogout(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId 	= $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userId" => "", "deviceType" => "")]);
		}
		if (!empty($userId) && !empty($deviceType)) {
			$this->helper->trackingApi($userId, 'logout', '', 0, '', $deviceType, 1);
			$message = "User Logout Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userId" => $userId, "deviceType" => $deviceType)]);
		} else {
			$message = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userId" => "", "deviceType" => "")]);
		}
	}


	public function getAboutUsDetails(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId 	= $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "aboutus" => array("id" => ""), "portfolios" => [])]);
		}
		$userSubscription = $this->helper->userSubscription($userId);
		$aboutUs = AboutUs::where("id", 1)->first();
		$aboutdata = array();
		if (!empty($aboutUs)) {
			$aboutdata['id']  			= $aboutUs->id;
			$aboutdata['title'] 		= $aboutUs->title;
			$aboutdata['content']		= !empty($aboutUs->content) ? $aboutUs->content : '';
			$aboutdata['organization']	= $aboutUs->organization;
			$aboutdata['vision']		= $aboutUs->vision;
			$aboutdata['mission']		= $aboutUs->mission;
			$aboutdata['process']		= $aboutUs->process;
			$aboutdata['video']			= asset('upload/aboutus') . "/" . $aboutUs->video;
			$interesting_facts_arr = array();
			$interesting_facts = json_decode($aboutUs->interesting_facts, true);
			if (!empty($interesting_facts)) {
				foreach ($interesting_facts as $key => $value) {
					$fact['fact_icon'] = asset('upload/aboutus') . "/" . $value['fact_icon'];
					$fact['fact_title'] = $value['fact_title'];
					$fact['fact_sub_title'] = $value['fact_sub_title'];
					array_push($interesting_facts_arr, $fact);
				}
			}
			$aboutdata['interesting_facts']	= $interesting_facts_arr;

			$portfolios = Portfolio::where("status", 1)->where('deleted', 0)->get();
			$portfoliodata = array();
			if (!empty($portfolios)) {
				foreach ($portfolios as $key => $value) {
					$portfoliodata[$key]['id'] 			= $value['id'];
					$portfoliodata[$key]['title'] 		= $value['title'];
					$portfoliodata[$key]['sub_title'] 	= $value['sub_title'];
					$portfoliodata[$key]['image'] 		= asset('upload/portfolios') . "/" . $value['image'];
				}
			}
			$this->helper->trackingApi($userId, 'aboutus', '', 0, '', $deviceType);
			$message = "About Us Details.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "aboutus" => $aboutdata, "portfolios" => $portfoliodata)
			]);
		} else {
			$message = "About Us Details Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "aboutus" => "", "portfolios" => "")]);
		}
	}

	public function userHomepage(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "subscriptionImg" => "", "classId" => "", "className" => "", "profileComplete" => "", "pin_message" => "", "banners" => [], "popular_videos" => [], "continue_studying" => [], "courses" => [])]);
		}
		$user = User::where("id", $userId)->first();
		//$board_id = !empty($user) ? $user->board_id : '';
		$class_id = !empty($user) ? $user->class_id : '';
		//$boardId = ($request->board_id) ? $request->board_id : $board_id;
		$classId = ($request->class_id) ? $request->class_id : $class_id;
		$search   = $request->search;
		$userSubscription = $this->helper->userSubscription($userId);
		$subscriptionImg = asset('img/subscriptionImg.png');

		$userClass = StudentClass::where("id", $classId)->first();
		$className = !empty($userClass) ? $userClass->class_name : 'NA';
		$profileComplete = $this->helper->profileComplete($userId);
		$pinMessage = PinMessage::whereRaw("find_in_set($classId,class_ids)")->where("msg_type", 201)->where("status", 1)->orderBy('id', 'DESC')->first();
		$pinMessageHome = !empty($pinMessage) ? $pinMessage->message : '';
		$banners = Banner::where('class_id', $classId)->where("status", 1)->orderBy('sort_id', 'ASC')->limit(10)->get();
		$lessions = Lession::where("status", 1)->where('deleted', 0);
		$popularvideos = Popularvideo::whereRaw("find_in_set($classId,class_ids)")->where("status", 1)->where('deleted', 0);
		$continueStudy = ContinueStudy::where("user_id", $userId)->where('is_complete', 0);
		$courses  = Courses::where("isHome", 502)->whereRaw("find_in_set($classId,class_ids)")->where("status", 1)->where('deleted', 0);
		if (!empty($lessions)) {
			$lessions = $lessions->where("name", 'like', "%" . $search . "%");
		}
		$lessions = $lessions->orderBy('sort_id', 'ASC')->get();
		if (!empty($popularvideos)) {
			$popularvideos = $popularvideos->where("name", 'like', "%" . $search . "%");
		}
		$popularvideos = $popularvideos->orderBy('sort_id', 'ASC')->limit(10)->get();
		$continueStudy = $continueStudy->orderBy('updated_at', 'DESC')->limit(10)->get();
		if (!empty($courses)) {
			$courses = $courses->where("name", 'like', "%" . $search . "%");
		}
		$courses = $courses->orderBy('sort_id', 'ASC')->get();

		$bannerdata = $videodata = $studydata = $coursedata = array();
		$courseId = $lessionId = $topicId = 0;

		foreach ($banners as $bankey => $banner) {
			if ($banner['module_name']=='QUIZ') {
				$getQuiz = Quiz::where("id", $banner['module_id'])->first();
				$courseId = !empty($getQuiz) ? $getQuiz->courseId : 0;
				$lessionId = !empty($getQuiz) ? $getQuiz->lessionId : 0;
				$topicId = !empty($getQuiz) ? $getQuiz->topicId : 0;
			}
			$bannerdata[$bankey]['id'] 			= $banner['id'];
			$bannerdata[$bankey]['image'] 		= !empty($banner['image']) ? asset('upload/banners/'.$banner['image']) : '';
			$bannerdata[$bankey]['module_name'] = $banner['module_name'];
			$bannerdata[$bankey]['module_id'] 	= $banner['module_id'];
			$bannerdata[$bankey]['courseId'] 	= $courseId;
			$bannerdata[$bankey]['lessionId'] 	= $lessionId;
			$bannerdata[$bankey]['topicId'] 	= $topicId;
			$bannerdata[$bankey]['web_url'] 	= ($banner['web_url']) ? $banner['web_url'] : '';
		}
		foreach ($popularvideos as $key => $val) {
			$videodata[$key]['id'] 				= $val['id'];
			$videodata[$key]['name'] 			= $val['name'];
			$videodata[$key]['video_thumb'] 	= asset('upload/popularvideos') . "/" . $val['video_thumb'];
			if ($val['uploads3']==1){
				$video = config('constant.S3PUBLICURL').$val['video'];
			} else {
				$video = asset('upload/popularvideos') . "/" . $val['video'];
			}
			$videodata[$key]['original_video'] 	= isset($val['video']) ? $video : 'NA'; //'https://multiplatform-f.akamaihd.net/i/multi/april11/sintel/sintel-hd_,512x288_450_b,640x360_700_b,768x432_1000_b,1024x576_1400_m,.mp4.csmil/master.m3u8';
			if (!empty($val['video_1']) && $val['video_1']!='NA') {
				if ($val['uploads3v1']==1){
					$video_1 = config('constant.S3PUBLICURL').$val['video_1'];
				} else {
					if(file_exists( public_path().'/upload/popularvideos/'.$val['video_1'] )) {
						$video_1 = asset('upload/popularvideos') . "/" . $val['video_1'];
					} else {
						$video_1 = $videodata[$key]['original_video'];
					}
				}
			} else {
				$video_1 = $videodata[$key]['original_video'];
			}
			if (!empty($val['video_2']) && $val['video_2']!='NA') {
				if ($val['uploads3v2']==1){
					$video_2 = config('constant.S3PUBLICURL').$val['video_2'];
				} else {
					if(file_exists( public_path().'/upload/popularvideos/'.$val['video_2'] )) {
						$video_2 = asset('upload/popularvideos') . "/" . $val['video_2'];
					} else {
						$video_2 = $videodata[$key]['original_video'];
					}
				}
			} else {
				$video_2 = $videodata[$key]['original_video'];
			}
			if (!empty($val['video_3']) && $val['video_3']!='NA') {
				if ($val['uploads3v3']==1){
					$video_3 = config('constant.S3PUBLICURL').$val['video_3'];
				} else {
					if(file_exists( public_path().'/upload/popularvideos/'.$val['video_3'] )) {
						$video_3 = asset('upload/popularvideos') . "/" . $val['video_3'];
					} else {
						$video_3 = $videodata[$key]['original_video'];
					}
				}
			} else {
				$video_3 = $videodata[$key]['original_video'];
			}
			$videodata[$key]['low_video'] 		= $video_1;
			$videodata[$key]['medium_video'] 	= $video_2;
			$videodata[$key]['high_video'] 		= $video_3;
			$videodata[$key]['paid'] 			= $val['paid'];
		}
		foreach ($continueStudy as $key => $study) {
			if ($study['lession_id']==0) {
				$course  = Courses::where("id", $study['course_id'])->where("status", 1)->where('deleted', 0)->first();
				$studydata[$key]['id']  = $study['id'];
				$studydata[$key]['courseId']  		= $study['course_id'];
				$studydata[$key]['lessionId'] 		= 0;
				$studydata[$key]['topicId'] 		= 0;
				$studydata[$key]['name'] 			= isset($course->name) ? $course->name : '';
				$studydata[$key]['image'] 			= isset($course->image) ? asset('course') . "/" . $course->image : '';
				if ($course->uploads3==1){
					$video = config('constant.S3PUBLICURL').$course->video;
				} else {
					$video = asset('course') . "/" . $course->video;
				}
				$studydata[$key]['original_video'] 	= isset($course->video) ? $video : 'NA'; //'https://multiplatform-f.akamaihd.net/i/multi/april11/sintel/sintel-hd_,512x288_450_b,640x360_700_b,768x432_1000_b,1024x576_1400_m,.mp4.csmil/master.m3u8';
				if (!empty($course->video_1) && $course->video_1!='NA') {
					if ($course->uploads3v1==1){
						$video_1 = config('constant.S3PUBLICURL').$course->video_1;
					} else {
						if(file_exists( public_path().'/course/'.$course->video_1 )) {
							$video_1 = asset('course') . "/" . $course->video_1;
						} else {
							$video_1 = $studydata[$key]['original_video'];
						}
					}
				} else {
					$video_1 = $studydata[$key]['original_video'];
				}
				if (!empty($course->video_2) && $course->video_2!='NA') {
					if ($course->uploads3v2==1){
						$video_2 = config('constant.S3PUBLICURL').$course->video_2;
					} else {
						if(file_exists( public_path().'/course/'.$course->video_2 )) {
							$video_2 = asset('course') . "/" . $course->video_2;
						} else {
							$video_2 = $studydata[$key]['original_video'];
						}
					}
				} else {
					$video_2 = $studydata[$key]['original_video'];
				}
				if (!empty($course->video_3) && $course->video_3!='NA') {
					if ($course->uploads3v3==1){
						$video_3 = config('constant.S3PUBLICURL').$course->video_3;
					} else {
						if(file_exists( public_path().'/course/'.$course->video_3 )) {
							$video_3 = asset('course') . "/" . $course->video_3;
						} else {
							$video_3 = $studydata[$key]['original_video'];
						}
					}
				} else {
					$video_3 = $studydata[$key]['original_video'];
				}
				$studydata[$key]['low_video'] 		= $video_1;
				$studydata[$key]['medium_video'] 	= $video_2;
				$studydata[$key]['high_video'] 		= $video_3;
				$lesson_count = Lession::where("courseId", $study['course_id'])->where("status", 1)->where('deleted', 0)->count();
				$topic_count = Chapter::where("courseId", $study['course_id'])->where("status", 1)->where('deleted', 0)->count();
				$total_lessions = $lesson_count + $topic_count;
				$studydata[$key]['total_lessions'] 	= $total_lessions;
				$studydata[$key]['isFree'] 			= isset($course->isFree) ? $course->isFree : '';
			} else {
				if ($study['lession_id']>0) {
					if ($study['topic_id']==0) {
						$lession  = Lession::where("id", $study['lession_id'])->where("status", 1)->where('deleted', 0)->first();
						$studydata[$key]['id'] 				= $study['id'];
						$studydata[$key]['courseId']  		= $study['course_id'];
						$studydata[$key]['lessionId'] 		= $study['lession_id'];
						$studydata[$key]['topicId'] 		= 0;
						$studydata[$key]['name'] 			= isset($lession->name) ? $lession->name : '';
						$studydata[$key]['image'] 			= isset($lession->video_thumb) ? asset('lessions') . "/" . $lession->video_thumb : '';
						if ($lession->uploads3==1){
							$video = config('constant.S3PUBLICURL').$lession->fullvideo;
						} else {
							$video = asset('lessions') . "/" . $lession->fullvideo;
						}
						$studydata[$key]['original_video'] 	= isset($lession->fullvideo) ? $video : 'NA'; //'https://multiplatform-f.akamaihd.net/i/multi/april11/sintel/sintel-hd_,512x288_450_b,640x360_700_b,768x432_1000_b,1024x576_1400_m,.mp4.csmil/master.m3u8';
						if (!empty($lession->video_1) && $lession->video_1!='NA') {
							if ($lession->uploads3v1==1){
								$video_1 = config('constant.S3PUBLICURL').$lession->video_1;
							} else {
								if(file_exists( public_path().'/lessions/'.$lession->video_1 )) {
									$video_1 = asset('lessions') . "/" . $lession->video_1;
								} else {
									$video_1 = $studydata[$key]['original_video'];
								}
							}
						} else {
							$video_1 = $studydata[$key]['original_video'];
						}
						if (!empty($lession->video_2) && $lession->video_2!='NA') {
							if ($lession->uploads3v2==1){
								$video_2 = config('constant.S3PUBLICURL').$lession->video_2;
							} else {
								if(file_exists( public_path().'/lessions/'.$lession->video_2 )) {
									$video_2 = asset('lessions') . "/" . $lession->video_2;
								} else {
									$video_2 = $studydata[$key]['original_video'];
								}
							}
						} else {
							$video_2 = $studydata[$key]['original_video'];
						}
						if (!empty($lession->video_3) && $lession->video_3!='NA') {
							if ($lession->uploads3v3==1){
								$video_3 = config('constant.S3PUBLICURL').$lession->video_3;
							} else {
								if(file_exists( public_path().'/lessions/'.$lession->video_3 )) {
									$video_3 = asset('lessions') . "/" . $lession->video_3;
								} else {
									$video_3 = $studydata[$key]['original_video'];
								}
							}
						} else {
							$video_3 = $studydata[$key]['original_video'];
						}
						$studydata[$key]['low_video'] 		= $video_1;
						$studydata[$key]['medium_video'] 	= $video_2;
						$studydata[$key]['high_video'] 		= $video_3;
						$lesson_count = Lession::where("courseId", $study['course_id'])->where("status", 1)->where('deleted', 0)->count();
						$topic_count = Chapter::where("courseId", $study['course_id'])->where("status", 1)->where('deleted', 0)->count();
						$total_lessions = $lesson_count + $topic_count;
						$studydata[$key]['total_lessions'] 	= $total_lessions;
						$studydata[$key]['isFree']			= isset($lession->isFree) ? $lession->isFree : '';
					} else {
						$topic = Chapter::where("id", $study['topic_id'])->where("status", 1)->where('deleted', 0)->first();
						$studydata[$key]['id'] 				= $study['id'];
						$studydata[$key]['courseId']  		= $study['course_id'];
						$studydata[$key]['lessionId'] 		= $study['lession_id'];
						$studydata[$key]['topicId'] 		= $study['topic_id'];
						$studydata[$key]['name'] 			= isset($topic->name) ? $topic->name : '';
						$studydata[$key]['image'] 			= isset($topic->video_thumb) ? asset('lessions') . "/" . $topic->video_thumb : '';
						if ($topic->uploads3==1){
							$video = config('constant.S3PUBLICURL').$topic->fullvideo;
						} else {
							$video = asset('lessions') . "/" . $topic->fullvideo;
						}
						$studydata[$key]['original_video'] 	= isset($topic->fullvideo) ? $video : 'NA'; //'https://multiplatform-f.akamaihd.net/i/multi/april11/sintel/sintel-hd_,512x288_450_b,640x360_700_b,768x432_1000_b,1024x576_1400_m,.mp4.csmil/master.m3u8';
						if (!empty($topic->video_1) && $topic->video_1!='NA') {
							if ($topic->uploads3v1==1){
								$video_1 = config('constant.S3PUBLICURL').$topic->video_1;
							} else {
								if(file_exists( public_path().'/lessions/'.$topic->video_1 )) {
									$video_1 = asset('lessions') . "/" . $topic->video_1;
								} else {
									$video_1 = $studydata[$key]['original_video'];
								}
							}
						} else {
							$video_1 = $studydata[$key]['original_video'];
						}
						if (!empty($topic->video_2) && $topic->video_2!='NA') {
							if ($topic->uploads3v2==1){
								$video_2 = config('constant.S3PUBLICURL').$topic->video_2;
							} else {
								if(file_exists( public_path().'/lessions/'.$topic->video_2 )) {
									$video_2 = asset('lessions') . "/" . $topic->video_2;
								} else {
									$video_2 = $studydata[$key]['original_video'];
								}
							}
						} else {
							$video_2 = $studydata[$key]['original_video'];
						}
						if (!empty($topic->video_3) && $topic->video_3!='NA') {
							if ($topic->uploads3v3==1){
								$video_3 = config('constant.S3PUBLICURL').$topic->video_3;
							} else {
								if(file_exists( public_path().'/lessions/'.$topic->video_3 )) {
									$video_3 = asset('lessions') . "/" . $topic->video_3;
								} else {
									$video_3 = $studydata[$key]['original_video'];
								}
							}
						} else {
							$video_3 = $studydata[$key]['original_video'];
						}
						$studydata[$key]['low_video'] 		= $video_1;
						$studydata[$key]['medium_video'] 	= $video_2;
						$studydata[$key]['high_video'] 		= $video_3;
						$total_lessions = Chapter::where("courseId", $study['course_id'])->where("lessionId", $study['lession_id'])->where("status", 1)->where('deleted', 0)->count();
						$studydata[$key]['total_lessions'] 	= $total_lessions;
						$studydata[$key]['isFree']			= isset($topic->isFree) ? $topic->isFree : '';
					}
				}
			}
			$checkStudentExam = StudentExam::where("user_id", $userId)->where("course_id", $study['course_id'])->where("lession_id", $study['lession_id'])->orderBy("id","DESC")->first();
			if (!empty($checkStudentExam)) {
				$is_complete = $checkStudentExam->is_complete + 1;
			} else {
				$is_complete = 0;
			}
			$studydata[$key]['quizStatus']	= $is_complete;
			$total_time = !empty($study['video_total_time']) ? $study['video_total_time'] : 0;
			$viewed_time = !empty($study['video_viewed_time']) ? $study['video_viewed_time'] : 0;
			$view_percent = $percentLeft = 0;
			if ($total_time > 0) {
				$view_percent = ($viewed_time / $total_time) * 100;
				if ($view_percent > 99) {
					$data = array(
						'is_complete' => 1,
						'updated_at'  => date('Y-m-d H:i:s'),
					);
					$update = ContinueStudy::where("id", $study['id'])->update($data);
					$view_percent = 100;
				}
				if ($view_percent < 0) {
					$view_percent = 0;
				}
				$percentLeft = (($total_time - $viewed_time) / $total_time) * 100;
			}
			//echo 'TT '.$total_time.' VT '.$viewed_time.' VP '.$view_percent.' LP '.$percentLeft; die;
			$studydata[$key]['video_total_time']  = !empty($study['video_total_time']) ? $study['video_total_time'] : 0;
			$studydata[$key]['video_viewed_time'] = !empty($study['video_viewed_time']) ? $study['video_viewed_time'] : 0;
			$studydata[$key]['view_percent']      = round($view_percent);
		}
		foreach ($courses as $key => $value) {
			$coursedata[$key]['id'] 			= $value['id'];
			$coursedata[$key]['name'] 			= $value['name'];
			$coursedata[$key]['image'] 			= asset('course') . "/" . $value['image'];
			if ($value['uploads3']==1){
				$video = config('constant.S3PUBLICURL').$value['video'];
			} else {
				$video = asset('course') . "/" . $value['video'];
			}
			$coursedata[$key]['original_video'] = isset($value['video']) ? $video : 'NA'; //'https://multiplatform-f.akamaihd.net/i/multi/april11/sintel/sintel-hd_,512x288_450_b,640x360_700_b,768x432_1000_b,1024x576_1400_m,.mp4.csmil/master.m3u8';
			if (!empty($value['video_1']) && $value['video_1']!='NA') {
				if ($value['uploads3v1']==1){
					$video_1 = config('constant.S3PUBLICURL').$value['video_1'];
				} else {
					if(file_exists( public_path().'/course/'.$value['video_1'] )) {
						$video_1 = asset('course') . "/" . $value['video_1'];
					} else {
						$video_1 = $coursedata[$key]['original_video'];
					}
				}
			} else {
				$video_1 = $coursedata[$key]['original_video'];
			}
			if (!empty($value['video_2']) && $value['video_2']!='NA') {
				if ($value['uploads3v2']==1){
					$video_2 = config('constant.S3PUBLICURL').$value['video_2'];
				} else {
					if(file_exists( public_path().'/course/'.$value['video_2'] )) {
						$video_2 = asset('course') . "/" . $value['video_2'];
					} else {
						$video_2 = $coursedata[$key]['original_video'];
					}
				}
			} else {
				$video_2 = $coursedata[$key]['original_video'];
			}
			if (!empty($value['video_3']) && $value['video_3']!='NA') {
				if ($value['uploads3v3']==1){
					$video_3 = config('constant.S3PUBLICURL').$value['video_3'];
				} else {
					if(file_exists( public_path().'/course/'.$value['video_3'] )) {
						$video_3 = asset('course') . "/" . $value['video_3'];
					} else {
						$video_3 = $coursedata[$key]['original_video'];
					}
				}
			} else {
				$video_3 = $coursedata[$key]['original_video'];
			}
			$coursedata[$key]['low_video'] 		= $video_1;
			$coursedata[$key]['medium_video'] 	= $video_2;
			$coursedata[$key]['high_video'] 	= $video_3;
			$coursedata[$key]['pdf'] 			= asset('course') . "/" . $value['pdf'];
			$lesson_count = Lession::where("courseId", $value['id'])->where("status", 1)->where('deleted', 0)->count();
			$topic_count = Chapter::where("courseId", $value['id'])->where("status", 1)->where('deleted', 0)->count();
			$total_lessions = $lesson_count + $topic_count;
			$coursedata[$key]['total_lessions'] = $total_lessions;
			$coursedata[$key]['isFree'] 		= $value['isFree'];
		}
		$this->helper->trackingApi($userId, 'home', '', 0, '', $deviceType);
		$message = "User Home Page Data.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "subscriptionImg" => $subscriptionImg, "classId" => $classId, "className" => $className, "profileComplete" => $profileComplete, "pin_message" => $pinMessageHome, "banners" => $bannerdata, "popular_videos" => $videodata, "continue_studying" => $studydata, "courses" => $coursedata)]);
	}
	public function allPopularVideos(Request $request)
	{
		$search   = $request->search;
		$popularvideos = Popularvideo::where("status", 1)->where('deleted', 0);
		if (!empty($popularvideos)) {
			$popularvideos = $popularvideos->where("name", 'like', "%" . $search . "%");
		}
		$popularvideos = $popularvideos->orderBy('sort_id', 'ASC')->get();
		$videodata = array();
		foreach ($popularvideos as $key => $val) {
			$videodata[$key]['id'] 				= $val['id'];
			$videodata[$key]['name'] 			= $val['name'];
			$videodata[$key]['video_thumb'] 	= asset('upload/popularvideos') . "/" . $val['video_thumb'];
			if ($val['uploads3']==1){
				$video = config('constant.S3PUBLICURL').$val['video'];
			} else {
				$video = asset('upload/popularvideos') . "/" . $val['video'];
			}
			$videodata[$key]['original_video'] 	= isset($val['video']) ? $video : 'NA';
			if (!empty($val['video_1']) && $val['video_1']!='NA') {
				if ($val['uploads3v1']==1){
					$video_1 = config('constant.S3PUBLICURL').$val['video_1'];
				} else {
					if(file_exists( public_path().'/upload/popularvideos/'.$val['video_1'] )) {
						$video_1 = asset('upload/popularvideos') . "/" . $val['video_1'];
					} else {
						$video_1 = $videodata[$key]['original_video'];
					}
				}
			} else {
				$video_1 = $videodata[$key]['original_video'];
			}
			if (!empty($val['video_2']) && $val['video_2']!='NA') {
				if ($val['uploads3v2']==1){
					$video_2 = config('constant.S3PUBLICURL').$val['video_2'];
				} else {
					if(file_exists( public_path().'/upload/popularvideos/'.$val['video_2'] )) {
						$video_2 = asset('upload/popularvideos') . "/" . $val['video_2'];
					} else {
						$video_2 = $videodata[$key]['original_video'];
					}
				}
			} else {
				$video_2 = $videodata[$key]['original_video'];
			}
			if (!empty($val['video_3']) && $val['video_3']!='NA') {
				if ($val['uploads3v3']==1){
					$video_3 = config('constant.S3PUBLICURL').$val['video_3'];
				} else {
					if(file_exists( public_path().'/upload/popularvideos/'.$val['video_3'] )) {
						$video_3 = asset('upload/popularvideos') . "/" . $val['video_3'];
					} else {
						$video_3 = $videodata[$key]['original_video'];
					}
				}
			} else {
				$video_3 = $videodata[$key]['original_video'];
			}
			$videodata[$key]['low_video'] 		= $video_1;
			$videodata[$key]['medium_video']	= $video_2;
			$videodata[$key]['high_video'] 		= $video_3;
			$videodata[$key]['paid'] 			= $val['paid'];
		}
		$message = "Get All Popular Videos List.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("popular_videos" => $videodata)]);
	}
	public function allPopularVideosNew(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("classId" => "", "className" => "", "academic_videos" => [], "skill_videos" => [])]);
		}
		$user = User::where("id", $userId)->first();
		//$board_id = !empty($user) ? $user->board_id : '';
		$class_id = !empty($user) ? $user->class_id : '';
		//$boardId = ($request->board_id) ? $request->board_id : $board_id;
		$classId = ($request->class_id) ? $request->class_id : $class_id;
		$userClass = StudentClass::where("id", $classId)->first();
		$className = !empty($userClass) ? $userClass->class_name : 'NA';
		$search   = $request->search;

		$academicVideos = Popularvideo::where("class_type", 301)->whereRaw("find_in_set($classId,class_ids)")->where("status", 1)->where('deleted', 0);
		if (!empty($search)) {
			$academicVideos = $academicVideos->where("name", 'like', "%" . $search . "%");
		}
		$academicVideos = $academicVideos->orderBy('sort_id', 'ASC')->get();
		$academicVideo = array();
		if (!empty($academicVideos)) {
			foreach ($academicVideos as $key => $val) {
				$academicVideo[$key]['id'] 				= $val['id'];
				$academicVideo[$key]['name'] 			= $val['name'];
				$academicVideo[$key]['video_thumb'] 	= asset('upload/popularvideos') . "/" . $val['video_thumb'];
				if ($val['uploads3']==1){
					$video = config('constant.S3PUBLICURL').$val['video'];
				} else {
					$video = asset('upload/popularvideos') . "/" . $val['video'];
				}
				$academicVideo[$key]['original_video'] 	= isset($val['video']) ? $video : 'NA';
				if (!empty($val['video_1']) && $val['video_1']!='NA') {
					if ($val['uploads3v1']==1){
						$video_1 = config('constant.S3PUBLICURL').$val['video_1'];
					} else {
						if(file_exists( public_path().'/upload/popularvideos/'.$val['video_1'] )) {
							$video_1 = asset('upload/popularvideos') . "/" . $val['video_1'];
						} else {
							$video_1 = $academicVideo[$key]['original_video'];
						}
					}
				} else {
					$video_1 = $academicVideo[$key]['original_video'];
				}
				if (!empty($val['video_2']) && $val['video_2']!='NA') {
					if ($val['uploads3v2']==1){
						$video_2 = config('constant.S3PUBLICURL').$val['video_2'];
					} else {
						if(file_exists( public_path().'/upload/popularvideos/'.$val['video_2'] )) {
							$video_2 = asset('upload/popularvideos') . "/" . $val['video_2'];
						} else {
							$video_2 = $academicVideo[$key]['original_video'];
						}
					}
				} else {
					$video_2 = $academicVideo[$key]['original_video'];
				}
				if (!empty($val['video_3']) && $val['video_3']!='NA') {
					if ($val['uploads3v3']==1){
						$video_3 = config('constant.S3PUBLICURL').$val['video_3'];
					} else {
						if(file_exists( public_path().'/upload/popularvideos/'.$val['video_3'] )) {
							$video_3 = asset('upload/popularvideos') . "/" . $val['video_3'];
						} else {
							$video_3 = $academicVideo[$key]['original_video'];
						}
					}
				} else {
					$video_3 = $academicVideo[$key]['original_video'];
				}
				$academicVideo[$key]['low_video'] 		= $video_1;
				$academicVideo[$key]['medium_video']	= $video_2;
				$academicVideo[$key]['high_video'] 		= $video_3;
				$academicVideo[$key]['paid'] 			= $val['paid'];
			}
		}
		$skillVideos = Popularvideo::where("class_type", 302)->whereRaw("find_in_set($classId,class_ids)")->where("status", 1)->where('deleted', 0);
		if (!empty($search)) {
			$skillVideos = $skillVideos->where("name", 'like', "%" . $search . "%");
		}
		$skillVideos = $skillVideos->orderBy('sort_id', 'ASC')->get();
		$skillVideo = array();
		if (!empty($skillVideos)) {
			foreach ($skillVideos as $key => $val) {
				$skillVideo[$key]['id'] 				= $val['id'];
				$skillVideo[$key]['name'] 			= $val['name'];
				$skillVideo[$key]['video_thumb'] 	= asset('upload/popularvideos') . "/" . $val['video_thumb'];
				if ($val['uploads3']==1){
					$video = config('constant.S3PUBLICURL').$val['video'];
				} else {
					$video = asset('upload/popularvideos') . "/" . $val['video'];
				}
				$skillVideo[$key]['original_video'] 	= isset($val['video']) ? $video : 'NA';
				if (!empty($val['video_1']) && $val['video_1']!='NA') {
					if ($val['uploads3v1']==1){
						$video_1 = config('constant.S3PUBLICURL').$val['video_1'];
					} else {
						if(file_exists( public_path().'/upload/popularvideos/'.$val['video_1'] )) {
							$video_1 = asset('upload/popularvideos') . "/" . $val['video_1'];
						} else {
							$video_1 = $skillVideo[$key]['original_video'];
						}
					}
				} else {
					$video_1 = $skillVideo[$key]['original_video'];
				}
				if (!empty($val['video_2']) && $val['video_2']!='NA') {
					if ($val['uploads3v2']==1){
						$video_2 = config('constant.S3PUBLICURL').$val['video_2'];
					} else {
						if(file_exists( public_path().'/upload/popularvideos/'.$val['video_2'] )) {
							$video_2 = asset('upload/popularvideos') . "/" . $val['video_2'];
						} else {
							$video_2 = $skillVideo[$key]['original_video'];
						}
					}
				} else {
					$video_2 = $skillVideo[$key]['original_video'];
				}
				if (!empty($val['video_3']) && $val['video_3']!='NA') {
					if ($val['uploads3v3']==1){
						$video_3 = config('constant.S3PUBLICURL').$val['video_3'];
					} else {
						if(file_exists( public_path().'/upload/popularvideos/'.$val['video_3'] )) {
							$video_3 = asset('upload/popularvideos') . "/" . $val['video_3'];
						} else {
							$video_3 = $skillVideo[$key]['original_video'];
						}
					}
				} else {
					$video_3 = $skillVideo[$key]['original_video'];
				}
				$skillVideo[$key]['low_video'] 		= $video_1;
				$skillVideo[$key]['medium_video']	= $video_2;
				$skillVideo[$key]['high_video'] 	= $video_3;
				$skillVideo[$key]['paid'] 			= $val['paid'];
			}
		}
		$message = "Get All Popular Videos List.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("classId" => $classId, "className" => $className, "academic_videos" => $academicVideo, "skill_videos" => $skillVideo)]);
	}
	public function ourCourses(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "courses" => [])]);
		}
		$user = User::where("id", $userId)->first();
		//$board_id = !empty($user) ? $user->board_id : '';
		$class_id = !empty($user) ? $user->class_id : '';
		//$boardId = ($request->board_id) ? $request->board_id : $board_id;
		$classId = ($request->class_id) ? $request->class_id : $class_id;
		$userClass = StudentClass::where("id", $classId)->first();
		$className = !empty($userClass) ? $userClass->class_name : 'NA';
		$search   = $request->search;
		$userSubscription = $this->helper->userSubscription($userId);
		
		$courses  = Courses::where("status", 1)->where('deleted', 0);
		if (!empty($courses)) {
			$courses = $courses->where("name", 'like', "%" . $search . "%");
		}
		$courses = $courses->orderBy('sort_id', 'ASC')->get();
		$coursedata = array();
		foreach ($courses as $key => $value) {
			$coursedata[$key]['id'] 			= $value['id'];
			$coursedata[$key]['name']			= $value['name'];
			$coursedata[$key]['image'] 			= asset('course') . "/" . $value['image'];
			if ($value['uploads3']==1){
				$video = config('constant.S3PUBLICURL').$value['video'];
			} else {
				$video = asset('course') . "/" . $value['video'];
			}
			$coursedata[$key]['original_video'] = isset($value['video']) ? $video : 'NA';
			if (!empty($value['video_1']) && $value['video_1']!='NA') {
				if ($value['uploads3v1']==1){
					$video_1 = config('constant.S3PUBLICURL').$value['video_1'];
				} else {
					if(file_exists( public_path().'/course/'.$value['video_1'] )) {
						$video_1 = asset('course') . "/" . $value['video_1'];
					} else {
						$video_1 = $coursedata[$key]['original_video'];
					}
				}
			} else {
				$video_1 = $coursedata[$key]['original_video'];
			}
			if (!empty($value['video_2']) && $value['video_2']!='NA') {
				if ($value['uploads3v2']==1){
					$video_2 = config('constant.S3PUBLICURL').$value['video_2'];
				} else {
					if(file_exists( public_path().'/course/'.$value['video_2'] )) {
						$video_2 = asset('course') . "/" . $value['video_2'];
					} else {
						$video_2 = $coursedata[$key]['original_video'];
					}
				}
			} else {
				$video_2 = $coursedata[$key]['original_video'];
			}
			if (!empty($value['video_3']) && $value['video_3']!='NA') {
				if ($value['uploads3v3']==1){
					$video_3 = config('constant.S3PUBLICURL').$value['video_3'];
				} else {
					if(file_exists( public_path().'/course/'.$value['video_3'] )) {
						$video_3 = asset('course') . "/" . $value['video_3'];
					} else {
						$video_3 = $coursedata[$key]['original_video'];
					}
				}
			} else {
				$video_3 = $coursedata[$key]['original_video'];
			}
			$coursedata[$key]['low_video']		= $video_1;
			$coursedata[$key]['medium_video']	= $video_2;
			$coursedata[$key]['high_video']		= $video_3;
			$coursedata[$key]['pdf']			= asset('course') . "/" . $value['pdf'];
			$lesson_count = Lession::where("courseId", $value['id'])->where("status", 1)->where('deleted', 0)->count();
			$topic_count = Chapter::where("courseId", $value['id'])->where("status", 1)->where('deleted', 0)->count();
			$total_lessions = $lesson_count + $topic_count;
			$coursedata[$key]['total_lessions'] = $total_lessions;
			$coursedata[$key]['isFree']			= $value['isFree'];
		}
		$this->helper->trackingApi($userId, 'course', '', 0, '', $deviceType);
		$message = "Our Courses List.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "courses" => $coursedata)]);
	}
	public function ourCoursesNew(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "classId" => "", "className" => "", "academicCourses" => [], "skillCourses" => [])]);
		}
		$user = User::where("id", $userId)->first();
		//$board_id = !empty($user) ? $user->board_id : '';
		$class_id = !empty($user) ? $user->class_id : '';
		//$boardId = ($request->board_id) ? $request->board_id : $board_id;
		$classId = ($request->class_id) ? $request->class_id : $class_id;
		$userClass = StudentClass::where("id", $classId)->first();
		$className = !empty($userClass) ? $userClass->class_name : 'NA';
		$search   = $request->search;
		$userSubscription = $this->helper->userSubscription($userId);
		
		$classSubject = StudentClassSubject::where("class_id", $classId)->first();
		$academicCourseData = $skillCourseData = array();
		if (!empty($classSubject)) {
			$subject_ids = !empty($classSubject->subject_ids) ? explode(",", $classSubject->subject_ids) : [];
			$acad = $skil = 0;
			foreach ($subject_ids as $subject) {
				$subjectdata = Subject::where("id",$subject)->first();
				$academicCourses = Courses::where("class_type", 301)->whereRaw("find_in_set($classId,class_ids)")->where("subject_id", $subject)->where("status", 1)->where('deleted', 0);
				if (!empty($search)) {
					$academicCourses = $academicCourses->where("name", 'like', "%" . $search . "%");
				}
				$academicCourses = $academicCourses->orderBy('sort_id', 'ASC')->get();
				$academicCourseArr = array();
				if (!empty($academicCourses)) {
					foreach ($academicCourses as $key => $value) {
						$academicCourse['id'] 				= $value['id'];
						$academicCourse['name']				= $value['name'];
						$academicCourse['image'] 			= asset('course') . "/" . $value['image'];
						if ($value['uploads3']==1){
							$video = config('constant.S3PUBLICURL').$value['video'];
						} else {
							$video = asset('course') . "/" . $value['video'];
						}
						$academicCourse['original_video'] 	= isset($value['video']) ? $video : 'NA';
						if (!empty($value['video_1']) && $value['video_1']!='NA') {
							if ($value['uploads3v1']==1){
								$video_1 = config('constant.S3PUBLICURL').$value['video_1'];
							} else {
								if(file_exists( public_path().'/course/'.$value['video_1'] )) {
									$video_1 = asset('course') . "/" . $value['video_1'];
								} else {
									$video_1 = $academicCourse['original_video'];
								}
							}
						} else {
							$video_1 = $academicCourse['original_video'];
						}
						if (!empty($value['video_2']) && $value['video_2']!='NA') {
							if ($value['uploads3v2']==1){
								$video_2 = config('constant.S3PUBLICURL').$value['video_2'];
							} else {
								if(file_exists( public_path().'/course/'.$value['video_2'] )) {
									$video_2 = asset('course') . "/" . $value['video_2'];
								} else {
									$video_2 = $academicCourse['original_video'];
								}
							}
						} else {
							$video_2 = $academicCourse['original_video'];
						}
						if (!empty($value['video_3']) && $value['video_3']!='NA') {
							if ($value['uploads3v3']==1){
								$video_3 = config('constant.S3PUBLICURL').$value['video_3'];
							} else {
								if(file_exists( public_path().'/course/'.$value['video_3'] )) {
									$video_3 = asset('course') . "/" . $value['video_3'];
								} else {
									$video_3 = $academicCourse['original_video'];
								}
							}
						} else {
							$video_3 = $academicCourse['original_video'];
						}
						$academicCourse['low_video']		= $video_1;
						$academicCourse['medium_video']		= $video_2;
						$academicCourse['high_video']		= $video_3;
						$academicCourse['pdf']				= asset('course') . "/" . $value['pdf'];
						$lesson_count = Lession::where("courseId", $value['id'])->where("status", 1)->where('deleted', 0)->count();
						$topic_count = Chapter::where("courseId", $value['id'])->where("status", 1)->where('deleted', 0)->count();
						$total_lessions = $lesson_count + $topic_count;
						$academicCourse['total_lessions'] 	= $total_lessions;
						$academicCourse['isFree']			= $value['isFree'];
						array_push($academicCourseArr, $academicCourse);
					}
					if (!empty($academicCourseArr)) {
						$academicCourseData[$acad]['subject_title'] = $subjectdata->title;
						$academicCourseData[$acad++]['academic_courses'] = $academicCourseArr;
					}
				}
				$skillCourses = Courses::where("class_type", 302)->whereRaw("find_in_set($classId,class_ids)")->where("subject_id", $subject)->where("status", 1)->where('deleted', 0);
				if (!empty($search)) {
					$skillCourses = $skillCourses->where("name", 'like', "%" . $search . "%");
				}
				$skillCourses = $skillCourses->orderBy('sort_id', 'ASC')->get();
				$skillCourseArr = array();
				if (!empty($skillCourses)) {
					foreach ($skillCourses as $key => $value) {
						$skillCourse['id'] 					= $value['id'];
						$skillCourse['name']				= $value['name'];
						$skillCourse['image'] 				= asset('course') . "/" . $value['image'];
						if ($value['uploads3']==1){
							$video = config('constant.S3PUBLICURL').$value['video'];
						} else {
							$video = asset('course') . "/" . $value['video'];
						}
						$skillCourse['original_video'] 	= isset($value['video']) ? $video : 'NA';
						if (!empty($value['video_1']) && $value['video_1']!='NA') {
							if ($value['uploads3v1']==1){
								$video_1 = config('constant.S3PUBLICURL').$value['video_1'];
							} else {
								if(file_exists( public_path().'/course/'.$value['video_1'] )) {
									$video_1 = asset('course') . "/" . $value['video_1'];
								} else {
									$video_1 = $skillCourse['original_video'];
								}
							}
						} else {
							$video_1 = $skillCourse['original_video'];
						}
						if (!empty($value['video_2']) && $value['video_2']!='NA') {
							if ($value['uploads3v2']==1){
								$video_2 = config('constant.S3PUBLICURL').$value['video_2'];
							} else {
								if(file_exists( public_path().'/course/'.$value['video_2'] )) {
									$video_2 = asset('course') . "/" . $value['video_2'];
								} else {
									$video_2 = $skillCourse['original_video'];
								}
							}
						} else {
							$video_2 = $skillCourse['original_video'];
						}
						if (!empty($value['video_3']) && $value['video_3']!='NA') {
							if ($value['uploads3v3']==1){
								$video_3 = config('constant.S3PUBLICURL').$value['video_3'];
							} else {
								if(file_exists( public_path().'/course/'.$value['video_3'] )) {
									$video_3 = asset('course') . "/" . $value['video_3'];
								} else {
									$video_3 = $skillCourse['original_video'];
								}
							}
						} else {
							$video_3 = $skillCourse['original_video'];
						}
						$skillCourse['low_video']			= $video_1;
						$skillCourse['medium_video']		= $video_2;
						$skillCourse['high_video']			= $video_3;
						$skillCourse['pdf']					= asset('course') . "/" . $value['pdf'];
						$lesson_count = Lession::where("courseId", $value['id'])->where("status", 1)->where('deleted', 0)->count();
						$topic_count = Chapter::where("courseId", $value['id'])->where("status", 1)->where('deleted', 0)->count();
						$total_lessions = $lesson_count + $topic_count;
						$skillCourse['total_lessions'] 		= $total_lessions;
						$skillCourse['isFree']				= $value['isFree'];
						array_push($skillCourseArr, $skillCourse);
					}
					if (!empty($skillCourseArr)) {
						$skillCourseData[$skil]['subject_title'] = $subjectdata->title;
						$skillCourseData[$skil++]['skill_courses'] = $skillCourseArr;
					}
				}
			}
			$this->helper->trackingApi($userId, 'course', '', 0, '', $deviceType);
			$message = "Our Courses List.";
		} else {
			$message = "Subject not assigned in this class!";
		}
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "classId" => $classId, "className" => $className, "academicCourses" => $academicCourseData, "skillCourses" => $skillCourseData)]);
	}
	public function getCourseDetails(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "subscriptionstatus" => "", "course" => array("id" => ""), "course_features" => [], "course_faqs" => [], "course_videos" => [], "course_pdfs" => [], "course_assignments" => [] )]);
		}
		$courseId = $request->courseId;
		$today = date('Y-m-d');
		$subscription = UserSubscription::where("user_id", $userId)->where("end_date", ">=", $today)->where("paymentStatus", 1)->orderBy('id', 'DESC')->first();
		$userSubscription = !empty($subscription) ? 1 : 0;
		if ($userSubscription==0) {
			$subscriptionstatus = 0; //not subscribe
		} else {
			if ($subscription->mode=='FREE') {
				$subscriptionstatus = 1; //freetrial
			} else {
				$subscriptionstatus = 2; //subscribed currently plan running
			}
		}
		$course = Courses::where("id", $courseId)->where("status", 1)->where('deleted', 0)->first();
		$coursedata = array();
		if (!empty($course)) {
			$coursedata['id'] 				= $course->id;
			$coursedata['class_type'] 		= $course->class_type;
			$coursedata['name'] 			= $course->name;
			$coursedata['image']			= asset('course') . "/" . $course->image;
			if ($course->uploads3==1){
				$video = config('constant.S3PUBLICURL').$course->video;
			} else {
				$video = asset('course') . "/" . $course->video;
			}
			$coursedata['original_video']	= isset($course->video) ? $video : 'NA';
			if (!empty($course->video_1) && $course->video_1!='NA') {
				if ($course->uploads3v1==1){
					$video_1 = config('constant.S3PUBLICURL').$course->video_1;
				} else {
					if(file_exists( public_path().'/course/'.$course->video_1 )) {
						$video_1 = asset('course') . "/" . $course->video_1;
					} else {
						$video_1 = $coursedata['original_video'];
					}
				}
			} else {
				$video_1 = $coursedata['original_video'];
			}
			if (!empty($course->video_2) && $course->video_2!='NA') {
				if ($course->uploads3v2==1){
					$video_2 = config('constant.S3PUBLICURL').$course->video_2;
				} else {
					if(file_exists( public_path().'/course/'.$course->video_2 )) {
						$video_2 = asset('course') . "/" . $course->video_2;
					} else {
						$video_2 = $coursedata['original_video'];
					}
				}
			} else {
				$video_2 = $coursedata['original_video'];
			}
			if (!empty($course->video_3) && $course->video_3!='NA') {
				if ($course->uploads3v3==1){
					$video_3 = config('constant.S3PUBLICURL').$course->video_3;
				} else {
					if(file_exists( public_path().'/course/'.$course->video_3 )) {
						$video_3 = asset('course') . "/" . $course->video_3;
					} else {
						$video_3 = $coursedata['original_video'];
					}
				}
			} else {
				$video_3 = $coursedata['original_video'];
			}
			$coursedata['low_video'] 		= $video_1;
			$coursedata['medium_video'] 	= $video_2;
			$coursedata['high_video'] 		= $video_3;
			$coursedata['pdf']				= asset('course') . "/" . $course->pdf;
			$course_videos = Lession::where("courseId", $courseId)->where("fullvideo", "!=", "")->where("status", 1)->where('deleted', 0)->orderBy('sort_id', 'ASC')->get();
			$topic_count = Chapter::where("courseId", $courseId)->where("fullvideo", "!=", "")->where("status", 1)->where('deleted', 0)->count();
			$coursedata['total_videos'] 	= count($course_videos) + $topic_count;
			$coursedata['video_duration']	= $course->video_duration;
			//$coursedata['total_duration'] = 0;
			foreach ($course_videos as $key => $val) {
				//$coursedata['total_duration'] += $val['id'];
			}
			$courseComplete = ContinueStudy::where("user_id", $userId)->where("course_id", $courseId)->get();
			$study_total_time = $study_viewed_time = 0;
			foreach ($courseComplete as $key => $val) {
				$study_total_time += $val['video_total_time'];
				$study_viewed_time += $val['video_viewed_time'];
			}
			if ($study_total_time > 0) {
				$course_complete = ($study_viewed_time / $study_total_time) * 100;
				if ($course_complete > 99) {
					$course_complete = 100;
				}
			} else {
				$course_complete = 0;
			}
			$coursedata['course_complete'] = round($course_complete);
			$coursedata['overview'] = $course->overview;
			$coursedata['course_certificate'] = '';
			$coursedata['isFree']  = $course->isFree;
			$checkStudentExam = StudentExam::where("user_id", $userId)->where("course_id", $courseId)->where("lession_id", 0)->where("topic_id", 0)->orderBy("id", "DESC")->first();
			if (!empty($checkStudentExam)) {
				if ($checkStudentExam->start_time != '00:00:00') {
					$certificate_status = $checkStudentExam->is_complete + 1;
				} else {
					$certificate_status = 0;
				}
			} else {
				$certificate_status = 0;
			}
			$coursedata['certificateStatus']	= $certificate_status;
			$continueStudy = ContinueStudy::where("user_id", $userId)->where("course_id", $courseId)->where("lession_id", 0)->where('is_complete', 0)->first();
			$study['video_total_time'] = !empty($continueStudy->video_total_time) ? $continueStudy->video_total_time : 0;
			$study['video_viewed_time'] = !empty($continueStudy->video_viewed_time) ? $continueStudy->video_viewed_time : 0;
			$total_time = $study['video_total_time'];
			$viewed_time = $study['video_viewed_time'];
			if ($total_time > 0) {
				$view_percent = ($viewed_time / $total_time) * 100;
				if ($view_percent > 99) {
					$view_percent = 100;
				}
				if ($view_percent < 0) {
					$view_percent = 0;
				}
			} else {
				$view_percent = 0;
			}
			$coursedata['video_total_time']  = $study['video_total_time'];
			$coursedata['video_viewed_time'] = $study['video_viewed_time'];
			$coursedata['view_percent']      = round($view_percent);
			$courseQuiz = Quiz::where("courseId", $courseId)->where("lessionId", 0)->where("topicId", 0)->where("islession", 1)->orderBy("id", "DESC")->first();
			$coursedata['quiz_available']    = !empty($courseQuiz) ? 1 : 0;
			$course_features = Coursefeature::where("courseId", $courseId)->get();
			$featuredata = array();
			foreach ($course_features as $key => $val) {
				$featuredata[$key]['feature'] = $val['feature'];
			}
			$course_faqs = Coursefeq::where("courseId", $courseId)->get();
			$coursefaqdata = array();
			foreach ($course_faqs as $key => $val) {
				$coursefaqdata[$key]['id']       = $val['id'];
				$coursefaqdata[$key]['question'] = $val['title'];
				$coursefaqdata[$key]['answer']   = $val['contant'];
			}
			$lessions = Lession::where("courseId", $courseId)->where("status", 1)->where('deleted', 0)->orderBy('sort_id', 'ASC')->get();
			$videodata = array();
			foreach ($lessions as $key => $value) {
				if($value['fullvideo'] != ''){
					$videodata[$key]['lession_id']		= $value['id'];
					$videodata[$key]['name']			= $value['name'];
					$videodata[$key]['image']			= asset('lessions') . "/" . $value['video_thumb'];
					if ($value['uploads3']==1){
						$video = config('constant.S3PUBLICURL').$value['fullvideo'];
					} else {
						$video = asset('lessions') . "/" . $value['fullvideo'];
					}
					$videodata[$key]['original_video']	= isset($value['fullvideo']) ? $video : 'NA';
					if (!empty($value['video_1']) && $value['video_1']!='NA') {
						if ($value['uploads3v1']==1){
							$video_1 = config('constant.S3PUBLICURL').$value['video_1'];
						} else {
							if(file_exists( public_path().'/lessions/'.$value['video_1'] )) {
								$video_1 = asset('lessions') . "/" . $value['video_1'];
							} else {
								$video_1 = $videodata[$key]['original_video'];
							}
						}
					} else {
						$video_1 = $videodata[$key]['original_video'];
					}
					if (!empty($value['video_2']) && $value['video_2']!='NA') {
						if ($value['uploads3v2']==1){
							$video_2 = config('constant.S3PUBLICURL').$value['video_2'];
						} else {
							if(file_exists( public_path().'/lessions/'.$value['video_2'] )) {
								$video_2 = asset('lessions') . "/" . $value['video_2'];
							} else {
								$video_2 = $videodata[$key]['original_video'];
							}
						}
					} else {
						$video_2 = $videodata[$key]['original_video'];
					}
					if (!empty($value['video_3']) && $value['video_3']!='NA') {
						if ($value['uploads3v3']==1){
							$video_3 = config('constant.S3PUBLICURL').$value['video_3'];
						} else {
							if(file_exists( public_path().'/lessions/'.$value['video_3'] )) {
								$video_3 = asset('lessions') . "/" . $value['video_3'];
							} else {
								$video_3 = $videodata[$key]['original_video'];
							}
						}
					} else {
						$video_3 = $videodata[$key]['original_video'];
					}
					$videodata[$key]['low_video']		= $video_1;
					$videodata[$key]['medium_video']	= $video_2;
					$videodata[$key]['high_video']		= $video_3;
					$videodata[$key]['content']			= !empty($value['content']) ? $value['content'] : '';
					$videodata[$key]['isFree']			= $value['isFree'];
					$checkStudentExam = StudentExam::where("user_id", $userId)->where("course_id", $courseId)->where("lession_id", $value['id'])->where("topic_id", 0)->orderBy("id","DESC")->first();
					if (!empty($checkStudentExam)) {
						$is_complete = $checkStudentExam->is_complete + 1;
					} else {
						$is_complete = 0;
					}
					$videodata[$key]['quizStatus']		= $is_complete;
					$continueStudy = ContinueStudy::where("user_id", $userId)->where("course_id", $courseId)->where("lession_id", $value['id'])->where('is_complete', 0)->first();
					$study['video_total_time'] = !empty($continueStudy->video_total_time) ? $continueStudy->video_total_time : 0;
					$study['video_viewed_time'] = !empty($continueStudy->video_viewed_time) ? $continueStudy->video_viewed_time : 0;
					$total_time = $study['video_total_time'];
					$viewed_time = $study['video_viewed_time'];
					if ($total_time > 0) {
						$view_percent = ($viewed_time / $total_time) * 100;
						if ($view_percent > 99) {
							$view_percent = 100;
						}
						if ($view_percent < 0) {
							$view_percent = 0;
						}
					} else {
						$view_percent = 0;
					}
					$videodata[$key]['video_total_time']  = $study['video_total_time'];
					$videodata[$key]['video_viewed_time'] = $study['video_viewed_time'];
					$videodata[$key]['view_percent']      = round($view_percent);
					$lessionQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $value['id'])->where("topicId", 0)->where("islession", 0)->orderBy("id", "DESC")->first();
					$videodata[$key]['quiz_available']    = !empty($lessionQuiz) ? 1 : 0;
				}
			}
			$pdfdata = array();
			foreach ($lessions as $key => $value) {
				$lessionQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $value['id'])->where("topicId", 0)->where("islession", 0)->orderBy("id", "DESC")->first();
				//if($value['pdf'] != '' || !empty($lessionQuiz)){
					$pdfs['lession_id']			= $value['id'];
					$pdfs['name']				= $value['name'];
					$pdfs['image']				= asset('lessions') . "/" . $value['video_thumb'];
					$pdfs['pdf']				= !empty($value['pdf']) ? asset('lessions') . "/" . $value['pdf'] : '';
					$pdfs['content']			= !empty($value['content']) ? $value['content'] : '';
					$pdfs['isFree']				= $value['isFree'];
					$checkStudentExam = StudentExam::where("user_id", $userId)->where("course_id", $courseId)->where("lession_id", $value['id'])->where("topic_id", 0)->orderBy("id","DESC")->first();
					if (!empty($checkStudentExam)) {
						$is_complete = $checkStudentExam->is_complete + 1;
					} else {
						$is_complete = 0;
					}
					$pdfs['quizStatus']			= $is_complete;
					$pdfs['quiz_available']		= !empty($lessionQuiz) ? 1 : 0;
					array_push($pdfdata, $pdfs);
				//}
			}
			$assignments = Assignment::where("course_id", $courseId)->where("status", 1)->where('deleted', 0)->orderBy('sort_id', 'ASC')->get();
			$assignmentdata = array();
			foreach ($assignments as $a => $assignmnt) {
				$assignmentdata[$a]['assignment_id']	= $assignmnt['id'];
				$assignmentdata[$a]['assignment_title']	= $assignmnt['title'];
				$userAssignmentSubmitted = AssignmentSubmission::where("assignment_id", $assignmnt['id'])->where("user_id", $userId)->where("user_status", 1)->where("deleted", 0)->orderBy("id","DESC")->first();
				$submit_status = !empty($userAssignmentSubmitted) ? 1 : 0;
				$teacher_approved = 0;
				if (!empty($userAssignmentSubmitted)) {
					if ($userAssignmentSubmitted->teacher_images != '' || $userAssignmentSubmitted->teacher_videos != '' || $userAssignmentSubmitted->teacher_content != '') {
						$teacher_approved = ($userAssignmentSubmitted->teacher_approved == 1) ? 1 : 2;
					} elseif ($userAssignmentSubmitted->teacher_approved == 1) {
						$teacher_approved = 1;
					} else {
						$teacher_approved = 0;
					}
				}
				$assignmentdata[$a]['submitStatus']	= $submit_status;
				$assignmentdata[$a]['teacher_approved']	= !empty($userAssignmentSubmitted) ? $teacher_approved : 0;
			}
			$this->helper->trackingApi($userId, 'course', '', $courseId, '', $deviceType);
			
			$message = "Course Details.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "subscriptionstatus" => $subscriptionstatus, "course" => $coursedata, "course_features" => $featuredata, "course_faqs" => $coursefaqdata, "course_videos" => $videodata, "course_pdfs" => $pdfdata, "course_assignments" => $assignmentdata)]);
		} else {
			$message = "Course Details Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "subscriptionstatus" => $subscriptionstatus, "course" => "", "course_features" => "", "course_faqs" => "", "course_videos" => "", "course_pdfs" => "", "course_assignments" => "")]);
		}
	}
	public function getLessionDetails(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "lession" => array("id" => ""), "topics" => [])]);
		}
		$lessionId  = $request->lessionId;
		$userSubscription = $this->helper->userSubscription($userId);
		$lession = Lession::where("id", $lessionId)->where("status", 1)->where('deleted', 0)->first();
		$lessiondata = array();
		if (!empty($lession)) {
			$lessiondata['course_id']		= $lession->courseId;
			$lessiondata['lession_id']		= $lession->id;
			$lessiondata['name']			= $lession->name;
			$lessiondata['image']			= asset('lessions') . "/" . $lession->video_thumb;
			if ($lession->uploads3==1){
				$video = config('constant.S3PUBLICURL').$lession->fullvideo;
			} else {
				$video = asset('lessions') . "/" . $lession->fullvideo;
			}
			$lessiondata['original_video']	= isset($lession->fullvideo) ? $video : 'NA';
			if (!empty($lession->video_1) && $lession->video_1!='NA') {
				if ($lession->uploads3v1==1){
					$video_1 = config('constant.S3PUBLICURL').$lession->video_1;
				} else {
					if(file_exists( public_path().'/lessions/'.$lession->video_1 )) {
						$video_1 = asset('lessions') . "/" . $lession->video_1;
					} else {
						$video_1 = $lessiondata['original_video'];
					}
				}
			} else {
				$video_1 = $lessiondata['original_video'];
			}
			if (!empty($lession->video_2) && $lession->video_2!='NA') {
				if ($lession->uploads3v2==1){
					$video_2 = config('constant.S3PUBLICURL').$lession->video_2;
				} else {
					if(file_exists( public_path().'/lessions/'.$lession->video_2 )) {
						$video_2 = asset('lessions') . "/" . $lession->video_2;
					} else {
						$video_2 = $lessiondata['original_video'];
					}
				}
			} else {
				$video_2 = $lessiondata['original_video'];
			}
			if (!empty($lession->video_3) && $lession->video_3!='NA') {
				if ($lession->uploads3v3==1){
					$video_3 = config('constant.S3PUBLICURL').$lession->video_3;
				} else {
					if(file_exists( public_path().'/lessions/'.$lession->video_3 )) {
						$video_3 = asset('lessions') . "/" . $lession->video_3;
					} else {
						$video_3 = $lessiondata['original_video'];
					}
				}
			} else {
				$video_3 = $lessiondata['original_video'];
			}
			$lessiondata['low_video']		= $video_1;
			$lessiondata['medium_video']	= $video_2;
			$lessiondata['high_video']		= $video_3;
			$lessiondata['pdf']				= asset('lessions') . "/" . $lession->pdf;
			$lessiondata['content']			= !empty($lession->content) ? $lession->content : '';
			$lessiondata['isFree']			= $lession->isFree;

			$checkStudentExam = StudentExam::where("user_id", $userId)->where("course_id", $lession->courseId)->where("lession_id", $lession->id)->where("topic_id", 0)->orderBy("id","DESC")->first();
			if (!empty($checkStudentExam)) {
				$is_complete = $checkStudentExam->is_complete + 1;
			} else {
				$is_complete = 0;
			}
			$lessiondata['quizStatus']	= $is_complete;

			$continueStudy = ContinueStudy::where("user_id", $userId)->where("course_id", $lession->courseId)->where("lession_id", $lession->id)->where('is_complete', 0)->first();
			$study['video_total_time'] = !empty($continueStudy->video_total_time) ? $continueStudy->video_total_time : 0;
			$study['video_viewed_time'] = !empty($continueStudy->video_viewed_time) ? $continueStudy->video_viewed_time : 0;
			$total_time = $study['video_total_time'];
			$viewed_time = $study['video_viewed_time'];
			if ($total_time > 0) {
				$view_percent = ($viewed_time / $total_time) * 100;
				if ($view_percent > 99) {
					$view_percent = 100;
				}
				if ($view_percent < 0) {
					$view_percent = 0;
				}
			} else {
				$view_percent = 0;
			}
			$lessiondata['video_total_time']  = $study['video_total_time'];
			$lessiondata['video_viewed_time'] = $study['video_viewed_time'];
			$lessiondata['view_percent']      = round($view_percent);
			$lessionQuiz = Quiz::where("courseId", $lession->courseId)->where("lessionId", $lessionId)->where("topicId", 0)->where("islession", 0)->orderBy("id", "DESC")->first();
			$lessiondata['quiz_available']    = !empty($lessionQuiz) ? 1 : 0;

			$topics = Chapter::where("lessionId", $lession->id)->where("status", 1)->where('deleted', 0)->orderBy('sort_id', 'ASC')->get();
			$topicdata = array();
			if (!empty($topics)) {
				foreach ($topics as $key => $value) {
					$chapterNum = $key + 1;
					$topicdata[$key]['chapter_id']		= $value['lessionId'].'.'.$chapterNum;
					$topicdata[$key]['course_id']		= $value['courseId'];
					$topicdata[$key]['lession_id']		= $value['lessionId'];
					$topicdata[$key]['topic_id']		= $value['id'];
					$topicdata[$key]['name']			= $value['name'];
					$topicdata[$key]['image']			= asset('lessions') . "/" . $value['video_thumb'];
					if ($value['uploads3']==1){
						$video = config('constant.S3PUBLICURL').$value['fullvideo'];
					} else {
						$video = asset('lessions') . "/" . $value['fullvideo'];
					}
					$topicdata[$key]['original_video']	= isset($value['fullvideo']) ? $video : 'NA';
					if (!empty($value['video_1']) && $value['video_1']!='NA') {
						if ($value['uploads3v1']==1){
							$video_1 = config('constant.S3PUBLICURL').$value['video_1'];
						} else {
							if(file_exists( public_path().'/lessions/'.$value['video_1'] )) {
								$video_1 = asset('lessions') . "/" . $value['video_1'];
							} else {
								$video_1 = $topicdata[$key]['original_video'];
							}
						}
					} else {
						$video_1 = $topicdata[$key]['original_video'];
					}
					if (!empty($value['video_2']) && $value['video_2']!='NA') {
						if ($value['uploads3v2']==1){
							$video_2 = config('constant.S3PUBLICURL').$value['video_2'];
						} else {
							if(file_exists( public_path().'/lessions/'.$value['video_2'] )) {
								$video_2 = asset('lessions') . "/" . $value['video_2'];
							} else {
								$video_2 = $topicdata[$key]['original_video'];
							}
						}
					} else {
						$video_2 = $topicdata[$key]['original_video'];
					}
					if (!empty($value['video_3']) && $value['video_3']!='NA') {
						if ($value['uploads3v3']==1){
							$video_3 = config('constant.S3PUBLICURL').$value['video_3'];
						} else {
							if(file_exists( public_path().'/lessions/'.$value['video_3'] )) {
								$video_3 = asset('lessions') . "/" . $value['video_3'];
							} else {
								$video_3 = $topicdata[$key]['original_video'];
							}
						}
					} else {
						$video_3 = $topicdata[$key]['original_video'];
					}
					$topicdata[$key]['low_video']		= $video_1;
					$topicdata[$key]['medium_video']	= $video_2;
					$topicdata[$key]['high_video']		= $video_3;
					$topicdata[$key]['pdf']				= asset('lessions') . "/" . $value['pdf'];
					$topicdata[$key]['content']			= !empty($value['content']) ? $value['content'] : '';
					$topicdata[$key]['isFree']			= $value['isFree'];
					
					$checkStudentExam = StudentExam::where("user_id", $userId)->where("course_id", $value['courseId'])->where("lession_id", $value['lessionId'])->where("topic_id", $value['id'])->orderBy("id","DESC")->first();
					if (!empty($checkStudentExam)) {
						$is_complete = $checkStudentExam->is_complete + 1;
					} else {
						$is_complete = 0;
					}
					$topicdata[$key]['quizStatus']	= $is_complete;
					
					$continueStudy = ContinueStudy::where("user_id", $userId)->where("course_id", $value['courseId'])->where("lession_id", $value['lessionId'])->where("topic_id", $value['id'])->where('is_complete', 0)->first();
					$study['video_total_time'] = !empty($continueStudy->video_total_time) ? $continueStudy->video_total_time : 0;
					$study['video_viewed_time'] = !empty($continueStudy->video_viewed_time) ? $continueStudy->video_viewed_time : 0;
					$total_time = $study['video_total_time'];
					$viewed_time = $study['video_viewed_time'];
					if ($total_time > 0) {
						$view_percent = ($viewed_time / $total_time) * 100;
						if ($view_percent > 99) {
							$view_percent = 100;
						}
						if ($view_percent < 0) {
							$view_percent = 0;
						}
					} else {
						$view_percent = 0;
					}
					$topicdata[$key]['video_total_time']  = $study['video_total_time'];
					$topicdata[$key]['video_viewed_time'] = $study['video_viewed_time'];
					$topicdata[$key]['view_percent']      = round($view_percent);
					$topicQuiz = Quiz::where("courseId", $value['courseId'])->where("lessionId", $value['lessionId'])->where("topicId", $value['id'])->where("islession", 2)->orderBy("id", "DESC")->first();
					$topicdata[$key]['quiz_available']    = !empty($topicQuiz) ? 1 : 0;
				}
			}
			$this->helper->trackingApi($userId, 'lesson', '', $lessionId, '', $deviceType);
			$message = "Lession Details.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "lession" => $lessiondata, "topics" => $topicdata)
			]);
		} else {
			$message = "Lession Details Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "lession" => "", "topics" => "")]);
		}
	}
	public function getTopicDetails(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "topicdata" => array("id" => "") )]);
		}
		$topicId  = $request->topicId;
		$userSubscription = $this->helper->userSubscription($userId);
		$topic = Chapter::where("id", $topicId)->where("status", 1)->where('deleted', 0)->first();
		$topicdata = array();
		if (!empty($topic)) {
			$topicdata['course_id']		= $topic->courseId;
			$topicdata['lession_id']	= $topic->lessionId;
			$topicdata['topic_id']		= $topic->id;
			$topicdata['name']			= $topic->name;
			$topicdata['image']			= asset('lessions') . "/" . $topic->video_thumb;
			if ($topic->uploads3==1){
				$video = config('constant.S3PUBLICURL').$topic->fullvideo;
			} else {
				$video = asset('lessions') . "/" . $topic->fullvideo;
			}
			$topicdata['original_video']	= isset($topic->fullvideo) ? $video : 'NA';
			if (!empty($topic->video_1) && $topic->video_1!='NA') {
				if ($topic->uploads3v1==1){
					$video_1 = config('constant.S3PUBLICURL').$topic->video_1;
				} else {
					if(file_exists( public_path().'/lessions/'.$topic->video_1 )) {
						$video_1 = asset('lessions') . "/" . $topic->video_1;
					} else {
						$video_1 = $topicdata['original_video'];
					}
				}
			} else {
				$video_1 = $topicdata['original_video'];
			}
			if (!empty($v->video_2) && $topic->video_2!='NA') {
				if ($topic->uploads3v2==1){
					$video_2 = config('constant.S3PUBLICURL').$topic->video_2;
				} else {
					if(file_exists( public_path().'/lessions/'.$topic->video_2 )) {
						$video_2 = asset('lessions') . "/" . $topic->video_2;
					} else {
						$video_2 = $topicdata['original_video'];
					}
				}
			} else {
				$video_2 = $topicdata['original_video'];
			}
			if (!empty($topic->video_3) && $topic->video_3!='NA') {
				if ($topic->uploads3v3==1){
					$video_3 = config('constant.S3PUBLICURL').$topic->video_3;
				} else {
					if(file_exists( public_path().'/lessions/'.$topic->video_3 )) {
						$video_3 = asset('lessions') . "/" . $topic->video_3;
					} else {
						$video_3 = $topicdata['original_video'];
					}
				}
			} else {
				$video_3 = $topicdata['original_video'];
			}
			$topicdata['low_video']		= $video_1;
			$topicdata['medium_video']	= $video_2;
			$topicdata['high_video']	= $video_3;
			$topicdata['pdf']			= asset('lessions') . "/" . $topic->pdf;
			$topicdata['content']		= !empty($topic->content) ? $topic->content : '';
			$topicdata['isFree']		= $topic->isFree;

			$checkStudentExam = StudentExam::where("user_id", $userId)->where("course_id", $topic->courseId)->where("lession_id", $topic->lessionId)->where("topic_id", $topic->id)->orderBy("id","DESC")->first();
			if (!empty($checkStudentExam)) {
				$is_complete = $checkStudentExam->is_complete + 1;
			} else {
				$is_complete = 0;
			}
			$topicdata['quizStatus']	= $is_complete;

			$continueStudy = ContinueStudy::where("user_id", $userId)->where("course_id", $topic->courseId)->where("lession_id", $topic->lessionId)->where("topic_id", $topic->id)->where('is_complete', 0)->first();
			$study['video_total_time'] = !empty($continueStudy->video_total_time) ? $continueStudy->video_total_time : 0;
			$study['video_viewed_time'] = !empty($continueStudy->video_viewed_time) ? $continueStudy->video_viewed_time : 0;
			$total_time = $study['video_total_time'];
			$viewed_time = $study['video_viewed_time'];
			if ($total_time > 0) {
				$view_percent = ($viewed_time / $total_time) * 100;
				if ($view_percent > 99) {
					$view_percent = 100;
				}
				if ($view_percent < 0) {
					$view_percent = 0;
				}
			} else {
				$view_percent = 0;
			}
			$topicdata['video_total_time']  = $study['video_total_time'];
			$topicdata['video_viewed_time'] = $study['video_viewed_time'];
			$topicdata['view_percent']      = round($view_percent);
			$topicQuiz = Quiz::where("courseId", $topic->courseId)->where("lessionId", $topic->lessionId)->where("topicId", $topic->id)->where("islession", 2)->orderBy("id", "DESC")->first();
			$topicdata['quiz_available']    = !empty($topicQuiz) ? 1 : 0;

			
			$this->helper->trackingApi($userId, 'topic', '', $topicId, '', $deviceType);
			$message = "Topic Details.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "topicdata" => $topicdata)
			]);
		} else {
			$message = "Topic Details Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "topicdata" => "")]);
		}
	}
	public function getRatingTypes(Request $request)
	{
		$ratingTypes = RatingType::get();
		$ratingTypedata = array();
		if (!empty($ratingTypes)) {
			foreach ($ratingTypes as $key => $value) {
				$ratingTypedata[$key]['id']	= $value['id'];
				$ratingTypedata[$key]['ratingType']	= $value['ratingType'];
			}
			$message = "Get Rating Types Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("ratingTypes" => $ratingTypedata)
			]);
		} else {
			$message = "Rating Types Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("ratingTypes" => "")]);
		}
	}
	public function getRatingMessages(Request $request)
	{
		$ratingMessages = RatingMessage::get();
		$sadMsgdata = array();
		$scepticMsgdata = array();
		$happyMsgdata = array();
		if (!empty($ratingMessages)) {
			foreach ($ratingMessages as $key => $value) {
				//$ratingMsgdata[$key]['message']	= $value['message'];
				if ($value['ratingType']==1) {
					$sadMsg['message']	= $value['message'];
					array_push($sadMsgdata, $sadMsg);
				} else if ($value['ratingType']==2) {
					$scepticMsg['message']	= $value['message'];
					array_push($scepticMsgdata, $scepticMsg);
				} else if ($value['ratingType']==3) {
					$happyMsg['message']	= $value['message'];
					array_push($happyMsgdata, $happyMsg);
				} else {
				}
			}
			$message = "Get Rating Messages Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("sadMessages" => $sadMsgdata, "scepticMessages" => $scepticMsgdata, "happyMessages" => $happyMsgdata)
			]);
		} else {
			$message = "Rating Messages Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("sadMessages" => "", "scepticMessages" => "", "happyMessages" => "")]);
		}
	}
	public function saveRatingsbyUser(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("rating_id" => "")]);
		}
		$courseId 	= $request->courseId;
		$lessionId  = $request->lessionId;
		$topicId 	= $request->topicId;
		$ratingType 	= $request->ratingType;
		$ratingMessage  = $request->ratingMessage;
		$message 		= !empty($request->message) ? $request->message : '';
		$msg = '';
		if (!empty($userId)  && !empty($courseId) && !empty($ratingMessage)) {
			$data = array(
				'userId' 	=> $userId,
				'courseId'  => $courseId,
				'lessionId' => $lessionId,
				'topicId' 	=> $topicId,
				'ratingType' => $ratingType,
				'ratingMessage' => $ratingMessage,
				'message'    => $message,
				'status'     => 0,
				'created_at' => date('Y-m-d H:i:s'),
			);
			$insertId = RatingUser::insertGetId($data);
			$message = 'Rating Submitted Successfully.';
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array('rating_id' => $insertId)]);
		} else {
			$message = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("rating_id" => "")]);
		}
	}
	public function userContinueStudyHistory(Request $request)
	{
		$search   = $request->search;
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userSubscription = $this->helper->userSubscription($userId);
		$continueStudy = ContinueStudy::where("user_id", $userId)->where('is_complete', 0);
		$continueStudy = $continueStudy->orderBy('updated_at', 'DESC')->get();
		$studydata = array();
		foreach ($continueStudy as $key => $study) {
			if ($study['lession_id']==0) {
				$courseId = $study['course_id'];
				$course = Courses::where("id", $study['course_id'])->where("status", 1)->where('deleted', 0)->first();
				$studydata[$key]['id']				= $study['id'];
				$studydata[$key]['name']			= $course->name;
				$studydata[$key]['image']			= asset('course') . "/" . $course->image;
				if ($course->uploads3==1){
					$video = config('constant.S3PUBLICURL').$course->video;
				} else {
					$video = asset('course') . "/" . $course->video;
				}
				$studydata[$key]['original_video']	= isset($course->video) ? $video : 'NA';
				if (!empty($course->video_1) && $course->video_1!='NA') {
					if ($course->uploads3v1==1){
						$video_1 = config('constant.S3PUBLICURL').$course->video_1;
					} else {
						if(file_exists( public_path().'/course/'.$course->video_1 )) {
							$video_1 = asset('course') . "/" . $course->video_1;
						} else {
							$video_1 = $studydata[$key]['original_video'];
						}
					}
				} else {
					$video_1 = $studydata[$key]['original_video'];
				}
				if (!empty($course->video_2) && $course->video_2!='NA') {
					if ($course->uploads3v2==1){
						$video_2 = config('constant.S3PUBLICURL').$course->video_2;
					} else {
						if(file_exists( public_path().'/course/'.$course->video_2 )) {
							$video_2 = asset('course') . "/" . $course->video_2;
						} else {
							$video_2 = $studydata[$key]['original_video'];
						}
					}
				} else {
					$video_2 = $studydata[$key]['original_video'];
				}
				if (!empty($course->video_3) && $course->video_3!='NA') {
					if ($course->uploads3v3==1){
						$video_3 = config('constant.S3PUBLICURL').$course->video_3;
					} else {
						if(file_exists( public_path().'/course/'.$course->video_3 )) {
							$video_3 = asset('course') . "/" . $course->video_3;
						} else {
							$video_3 = $studydata[$key]['original_video'];
						}
					}
				} else {
					$video_3 = $studydata[$key]['original_video'];
				}
				$studydata[$key]['low_video']		= $video_1;
				$studydata[$key]['medium_video']	= $video_2;
				$studydata[$key]['high_video']		= $video_3;
				$lesson_count = Lession::where("courseId", $study['course_id'])->where("status", 1)->where('deleted', 0)->count();
				$topic_count = Chapter::where("courseId", $study['course_id'])->where("status", 1)->where('deleted', 0)->count();
				$total_lessions = $lesson_count + $topic_count;
				$studydata[$key]['total_lessions']  = $total_lessions;
				$this->helper->trackingApi($userId, 'course', '', $courseId, 'video', $deviceType);
			} else {
				if ($study['topic_id']==0) {
					$lessionId = $study['lession_id'];
					$lession = Lession::where("id", $study['lession_id'])->where("status", 1)->where('deleted', 0)->first();
					$studydata[$key]['id']				= $study['id'];
					$studydata[$key]['name']			= $lession->name;
					$studydata[$key]['image']			= asset('lessions') . "/" . $lession->video_thumb;
					if ($lession->uploads3==1){
						$video = config('constant.S3PUBLICURL').$lession->fullvideo;
					} else {
						$video = asset('lessions') . "/" . $lession->fullvideo;
					}
					$studydata[$key]['original_video']	= isset($lession->fullvideo) ? $video : 'NA';
					if (!empty($lession->video_1) && $lession->video_1!='NA') {
						if ($lession->uploads3v1==1){
							$video_1 = config('constant.S3PUBLICURL').$lession->video_1;
						} else {
							if(file_exists( public_path().'/lessions/'.$lession->video_1 )) {
								$video_1 = asset('lessions') . "/" . $lession->video_1;
							} else {
								$video_1 = $studydata[$key]['original_video'];
							}
						}
					} else {
						$video_1 = $studydata[$key]['original_video'];
					}
					if (!empty($lession->video_2) && $lession->video_2!='NA') {
						if ($lession->uploads3v2==1){
							$video_2 = config('constant.S3PUBLICURL').$lession->video_2;
						} else {
							if(file_exists( public_path().'/lessions/'.$lession->video_2 )) {
								$video_2 = asset('lessions') . "/" . $lession->video_2;
							} else {
								$video_2 = $studydata[$key]['original_video'];
							}
						}
					} else {
						$video_2 = $studydata[$key]['original_video'];
					}
					if (!empty($lession->video_3) && $lession->video_3!='NA') {
						if ($lession->uploads3v3==1){
							$video_3 = config('constant.S3PUBLICURL').$lession->video_3;
						} else {
							if(file_exists( public_path().'/lessions/'.$lession->video_3 )) {
								$video_3 = asset('lessions') . "/" . $lession->video_3;
							} else {
								$video_3 = $studydata[$key]['original_video'];
							}
						}
					} else {
						$video_3 = $studydata[$key]['original_video'];
					}
					$studydata[$key]['low_video']		= $video_1;
					$studydata[$key]['medium_video']	= $video_2;
					$studydata[$key]['high_video']		= $video_3;
					$lesson_count = Lession::where("courseId", $study['course_id'])->where("status", 1)->where('deleted', 0)->count();
					$topic_count = Chapter::where("courseId", $study['course_id'])->where("status", 1)->where('deleted', 0)->count();
					$total_lessions = $lesson_count + $topic_count;
					$studydata[$key]['total_lessions']	= $total_lessions;
					$this->helper->trackingApi($userId, 'lesson', '', $lessionId, 'video', $deviceType);
				} else {
					$topicId = $study['topic_id'];
					$topic = Chapter::where("id", $study['topic_id'])->where("status", 1)->where('deleted', 0)->first();
					$studydata[$key]['id']				= $study['id'];
					$studydata[$key]['name']			= $topic->name;
					$studydata[$key]['image']			= asset('lessions') . "/" . $topic->video_thumb;
					if ($topic->uploads3==1){
						$video = config('constant.S3PUBLICURL').$topic->fullvideo;
					} else {
						$video = asset('lessions') . "/" . $topic->fullvideo;
					}
					$studydata[$key]['original_video']	= isset($topic->fullvideo) ? $video : 'NA';
					if (!empty($topic->video_1) && $topic->video_1!='NA') {
						if ($topic->uploads3v1==1){
							$video_1 = config('constant.S3PUBLICURL').$topic->video_1;
						} else {
							if(file_exists( public_path().'/lessions/'.$topic->video_1 )) {
								$video_1 = asset('lessions') . "/" . $topic->video_1;
							} else {
								$video_1 = $studydata[$key]['original_video'];
							}
						}
					} else {
						$video_1 = $studydata[$key]['original_video'];
					}
					if (!empty($topic->video_2) && $topic->video_2!='NA') {
						if ($topic->uploads3v2==1){
							$video_2 = config('constant.S3PUBLICURL').$topic->video_2;
						} else {
							if(file_exists( public_path().'/lessions/'.$topic->video_2 )) {
								$video_2 = asset('lessions') . "/" . $topic->video_2;
							} else {
								$video_2 = $studydata[$key]['original_video'];
							}
						}
					} else {
						$video_2 = $studydata[$key]['original_video'];
					}
					if (!empty($topic->video_3) && $topic->video_3!='NA') {
						if ($topic->uploads3v3==1){
							$video_3 = config('constant.S3PUBLICURL').$topic->video_3;
						} else {
							if(file_exists( public_path().'/lessions/'.$topic->video_3 )) {
								$video_3 = asset('lessions') . "/" . $topic->video_3;
							} else {
								$video_3 = $studydata[$key]['original_video'];
							}
						}
					} else {
						$video_3 = $studydata[$key]['original_video'];
					}
					$studydata[$key]['low_video']		= $video_1;
					$studydata[$key]['medium_video']	= $video_2;
					$studydata[$key]['high_video']		= $video_3;
					$total_chapters = Chapter::where("courseId", $study['course_id'])->where("lessionId", $study['lession_id'])->where("status", 1)->where('deleted', 0)->count();
					$studydata[$key]['total_lessions']	= $total_chapters;
					$this->helper->trackingApi($userId, 'topic', '', $topicId, 'video', $deviceType);
				}
			}
			$total_time = !empty($study['video_total_time']) ? $study['video_total_time'] : 0;
			$viewed_time = !empty($study['video_viewed_time']) ? $study['video_viewed_time'] : 0;
			$view_percent = $percentLeft = 0;
			if ($total_time > 0) {
				$view_percent = ($viewed_time / $total_time) * 100;
				if ($view_percent > 99) {
					$view_percent = 100;
				}
				if ($view_percent < 0) {
					$view_percent = 0;
				}
				$percentLeft = (($total_time - $viewed_time) / $total_time) * 100;
			}
			//echo 'TT '.$total_time.' VT '.$viewed_time.' VP '.$view_percent.' LP '.$percentLeft; die;
			$studydata[$key]['video_total_time']  = $study['video_total_time'];
			$studydata[$key]['video_viewed_time'] = $study['video_viewed_time'];
			$studydata[$key]['view_percent']      = round($view_percent);
		}
		$message = "User Continue Study History Data.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "continue_studying" => $studydata)]);
	}
	public function postContinueStudy(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("continueStudyId" => "")]);
		}
		$courseId 			= $request->courseId;
		$lessionId 			= $request->lessionId;
		$topicId 			= $request->topicId;
		$videoTotalTime 	= $request->videoTotalTime;
		$videoViewedTime 	= $request->videoViewedTime;
		if ($videoTotalTime == $videoViewedTime) {
			$is_complete = 1;
		} else {
			$is_complete = 0;
		}
		if ($topicId!=0) {
			$module = 'topic';
			$moduleId = $topicId;
		} elseif ($lessionId!=0) {
			$module = 'lesson';
			$moduleId = $lessionId;
		} else {
			$module = 'course';
			$moduleId = $courseId;
		}
		$checkContStudy = ContinueStudy::where("user_id", $userId)->where("course_id", $courseId)->where("lession_id", $lessionId)->where("topic_id", $topicId)->where('is_complete', 0)->first();
		if (!empty($checkContStudy)) {
			$cshId = $checkContStudy->id;
			$data = array(
				'video_total_time'  => $videoTotalTime,
				'video_viewed_time' => $videoViewedTime,
				'is_complete'  		=> $is_complete,
				'updated_at'        => date('Y-m-d H:i:s'),
			);
			$update = ContinueStudy::where("id", $cshId)->update($data);
			if ($is_complete==1) {
				if ($lessionId > 0) {
					$lession = Lession::where("id", $lessionId)->first();
					$msg = 'You have successfully completed your '.$lession->name.' lession.';
				} else {
					$course = Courses::where("id", $courseId)->first();
					$msg = 'You have successfully completed your '.$course->name.' course.';
				}
				$this->helper->addNotification($userId,$msg);
			}
			$delete = ContinueStudy::where("is_complete", 1)->delete();
			$this->helper->trackingApi($userId, $module, '', $moduleId, 'video', $deviceType);
			$message = "Continue Study Updated Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("continueStudyId" => $cshId)]);
		} else {
			$data = array(
				'user_id'  			=> $userId,
				'course_id'  		=> $courseId,
				'lession_id'  		=> $lessionId,
				'topic_id'  		=> $topicId,
				'video_total_time'  => $videoTotalTime,
				'video_viewed_time' => $videoViewedTime,
				'is_complete'  		=> $is_complete,
				'created_at'        => date('Y-m-d H:i:s'),
				'updated_at'        => date('Y-m-d H:i:s'),
			);
			$cshId = ContinueStudy::insertGetId($data);
			$this->helper->trackingApi($userId, $module, '', $moduleId, 'video', $deviceType);
			$message = "Continue Study Added Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("continueStudyId" => $cshId)]);
		}
	}

	public function getAssignmentDetails(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "assignment" => array("assignment_id" => "") )]);
		}
		$assignmentId  = $request->assignmentId;
		$userSubscription = $this->helper->userSubscription($userId);
		$assignment = Assignment::with('user','course')->where("id", $assignmentId)->where("status", 1)->where('deleted', 0)->first();
		$assignmentdata = array();
		if (!empty($assignment)) {
			$assignmentdata['assignment_id']	= $assignment->id;
			$assignmentdata['assignment_title']	= $assignment->title;
			$assignmentdata['course_name']		= $assignment->course->name;
			$assignmentdata['content']			= !empty($assignment->content) ? $assignment->content : '';
			$assignmentdata['image']			= !empty($assignment->image) ? $assignment->image : 'NA';
			$assignmentdata['video']			= !empty($assignment->video) ? $assignment->video : 'NA';
			$assignmentdata['download_file']	= !empty($assignment->other_file) ? $assignment->other_file : 'NA';
			$this->helper->trackingApi($userId, 'assignment', '', $assignmentId, '', $deviceType);
			
			$message = "Assignment Details.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "assignment" => $assignmentdata )
			]);
		} else {
			$message = "Assignment Details Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "assignment" => array("assignment_id" => "") )]);
		}
	}
	public function assignmentSubmit(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("assignsub_id" => "")]);
		}
		$assignmentId = $request->assignmentId;
		$userContent  = $request->userContent;
		$msg = '';
		if (!empty($userId)  && !empty($assignmentId) || !empty($userContent) || !empty($request->file('user_images_file')) || !empty($request->file('user_videos_file')) ) {
			$image_files = $request->file('user_images_file');
			$video_file = $request->file('user_videos_file');
			$other_files = $request->file('user_other_file');
			$imagesData = [];
			if($request->hasFile('user_images_file')){
				foreach($image_files as $i => $image_file){
					if($image_file){
						$destinationPath = public_path().'/upload/assignments/';
						$imageOriginalFile = $image_file->getClientOriginalName();
						$imageFilename = "user_".$assignmentId."_".time().$i.$imageOriginalFile;
						$image_file->move($destinationPath, $imageFilename);
						/*$url = 'upload/assignments/' . $imageFilename;
						$thumb_img = Image::make($url)->resize(200, 200);
						$thumb_img->save('upload/assignments/thumb/'.$imageFilename,80);*/
						//S3 Upload
						$s3 = AWS::createClient('s3');
						$s3->putObject(array(
							'Bucket'     => env('AWS_BUCKET'),
							'Key'        => "assignment_data/".$imageFilename,
							'SourceFile' => $destinationPath.$imageFilename,
						));
						$imagesData[] = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $imageFilename;
						if(!empty($image_file) && file_exists( $destinationPath.$imageFilename )) {
							unlink( $destinationPath.$imageFilename );
						}
					}
				}
			}
			$videosData = [];
			if($request->hasFile('user_videos_file')){
				//foreach($video_files as $video_file){
					if($video_file){
						$destinationPath = public_path().'/upload/assignments/';
						$videoOriginalFile = $video_file->getClientOriginalName();
						//$videoFilename = rand(111,999).strtotime(date('Y-m-d-H:isa')).$videoOriginalFile;
						$videoFilename = "user_".$assignmentId."_".time()."_org.mp4";
						$video_file->move($destinationPath, $videoFilename);
						//S3 Upload
						$s3 = AWS::createClient('s3');
						$s3->putObject(array(
							'Bucket'     => env('AWS_BUCKET'),
							'Key'        => "assignment_data/".$videoFilename,
							'SourceFile' => $destinationPath.$videoFilename,
						));
						//$videosData[] = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $videoFilename;
						$videosData = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $videoFilename;
						if(!empty($video_file) && file_exists( $destinationPath.$videoFilename )) {
							unlink( $destinationPath.$videoFilename );
						}
					}
				//}
			}
			$otherfilesData = [];
			if($request->hasFile('user_other_file')){
				foreach($other_files as $other_file){
					if($other_file){
						$destinationPath = public_path().'/upload/assignments/';
						$otherOriginalFile = $other_file->getClientOriginalName();
						$otherFilename = "user_".$assignmentId."_".time().$otherOriginalFile;
						$other_file->move($destinationPath, $otherFilename);
						//S3 Upload
						$s3 = AWS::createClient('s3');
						$s3->putObject(array(
							'Bucket'     => env('AWS_BUCKET'),
							'Key'        => "assignment_data/".$otherFilename,
							'SourceFile' => $destinationPath.$otherFilename,
						));
						$otherfilesData[] = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $otherFilename;
						if(!empty($other_file) && file_exists( $destinationPath.$otherFilename )) {
							unlink( $destinationPath.$otherFilename );
						}
					}
				}
			}
			$getUserPreAssignmnt = AssignmentSubmission::where("assignment_id", $assignmentId)->where("user_id", $userId)->where("assigned_to", ">", 0)->where("deleted", 0)->orderBy("id", "DESC")->first();
			$assigned_to = !empty($getUserPreAssignmnt) ? $getUserPreAssignmnt->assigned_to : 0;
			$data = array(
				'assignment_id' 	=> $assignmentId,
				'user_id' 			=> $userId,
				'user_images'   	=> !empty($imagesData) ? implode(",", $imagesData) : NULL,
				//'user_videos'   	=> !empty($videosData) ? implode(",", $videosData) : NULL,
				'user_videos' 		=> !empty($videosData) ? $videosData : NULL,
				'user_other_files' 	=> !empty($otherfilesData) ? implode(",", $otherfilesData) : NULL,
				'user_content' 		=> $userContent,
				'user_status'   	=> 1,
				'assigned_to'   	=> $assigned_to,
				'teacher_approved'  => 2,
				'created_at' 		=> date('Y-m-d H:i:s'),
			);
			$insertId = AssignmentSubmission::insertGetId($data);
			$message = 'Your Assignment uploaded successfully, Brain Science Experts will check and revert.';
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("assignsub_id" => $insertId)]);
		} else {
			$message = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("assignsub_id" => "")]);
		}
	}
	public function getUserPostedAssignment(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "userAssignment" => array("assignSubId" => "") )]);
		}
		$assignSubId  = $request->assignSubId;
		$userSubscription = $this->helper->userSubscription($userId);
		$userAssignmnt = AssignmentSubmission::with('assignment','user')->where("id", $assignSubId)->where('deleted', 0)->first();
		$userAssignmntdata = $user_images_array = $user_videos_array = $user_other_files_array = array();
		if (!empty($userAssignmnt)) {
			if($userAssignmnt->user_images!=''){
				$user_images_arr = !empty($userAssignmnt->user_images) ? explode(",", $userAssignmnt->user_images) : [];
				foreach($user_images_arr as $user_image){
					$user_images['image'] = $user_image;
					array_push($user_images_array, $user_images);
				}
			}
			if($userAssignmnt->user_videos!=''){
				$user_videos_arr = !empty($userAssignmnt->user_videos) ? explode(",", $userAssignmnt->user_videos) : [];
				foreach($user_videos_arr as $user_video){
					$user_videos['video'] = $user_video;
					array_push($user_videos_array, $user_videos);
				}
			}
			if($userAssignmnt->user_other_files!=''){
				$user_other_files_arr = !empty($userAssignmnt->user_other_files) ? explode(",", $userAssignmnt->user_other_files) : [];
				foreach($user_other_files_arr as $user_other_file){
					$user_other_files['other_file'] = $user_other_file;
					array_push($user_other_files_array, $user_other_files);
				}
			}
			$userAssignmntdata['assignSubId']	= $userAssignmnt->id;
			$userAssignmntdata['assignment_title'] = $userAssignmnt->assignment->title;
			$userAssignmntdata['course_name']	= $userAssignmnt->assignment->course->name;
			$userAssignmntdata['user_images']	= $user_images_array;
			$userAssignmntdata['user_videos']	= $user_videos_array;
			$userAssignmntdata['user_other_files']	= $user_other_files_array;
			$userAssignmntdata['user_content']	= !empty($userAssignmnt->user_content) ? $userAssignmnt->user_content : '';
			
			$message = "Submitted Assignment Details.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "userAssignment" => $userAssignmntdata )
			]);
		} else {
			$message = "Submitted Assignment Details Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "userAssignment" => "" )]);
		}
	}
	public function postedAssignmentSubmit(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId 	 = $request->userId;
		$userStatus  = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("assignsub_id" => "")]);
		}
		$assignSubId = $request->assignSubId;
		$msg = '';
		if (!empty($userId) && !empty($assignSubId) ) {
			$assigned_to = 0;
			$userAssignmnt = AssignmentSubmission::where("id", $assignSubId)->where("deleted",0)->first();
			if($userAssignmnt){
				$getUserPreAssignmnt = AssignmentSubmission::where("assignment_id", $userAssignmnt->assignment_id)->where("user_id", $userId)->where("assigned_to", ">", 0)->where("deleted", 0)->orderBy("id", "DESC")->first();
				$assigned_to = !empty($getUserPreAssignmnt) ? $getUserPreAssignmnt->assigned_to : 0;
			}
			$update  = AssignmentSubmission::where('id', $assignSubId)->update([ 'user_status' => 1, 'assigned_to' => $assigned_to ]);
			$message = 'Your Assignment uploaded successfully, Brain Science Experts will check and revert.';
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("assignsub_id" => $assignSubId)]);
		} else {
			$message = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("assignsub_id" => "")]);
		}
	}
	public function getUserAssignments(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId      = $request->userId;
		$deviceType  = $request->deviceType;
		$userStatus  = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "userAssignment" => array("assignSubId" => "") )]);
		}
		$search      = $request->search;
		$userSubscription = $this->helper->userSubscription($userId);
		$userAssignments = AssignmentSubmission::with('assignment','user')
								->where("user_id", $userId)
								->WhereHas('assignment', function($query) use ($search) {
									if (isset($search) && !empty($search)) {
										$query->where('assignments.title', 'LIKE', "%".$search."%");
										//$query->orWhere('courses.name', 'LIKE', "%".$search."%");
										$query->orWhere('assignment_submissions.user_content', 'LIKE', "%".$search."%");
									}
								})->where('deleted', 0)->groupBy('assignment_id')->get();
		$userAssignmntdata = array();
		if (!empty($userAssignments)) {
			foreach ($userAssignments as $key => $userAssignmnt) {
				$user_images_array = $user_videos_array = $user_other_files_array = array();
				if($userAssignmnt->user_images!=''){
					$user_images_arr = !empty($userAssignmnt->user_images) ? explode(",", $userAssignmnt->user_images) : [];
					foreach($user_images_arr as $user_image){
						$user_images['image'] = $user_image;
						array_push($user_images_array, $user_images);
					}
				}
				if($userAssignmnt->user_videos!=''){
					$user_videos_arr = !empty($userAssignmnt->user_videos) ? explode(",", $userAssignmnt->user_videos) : [];
					foreach($user_videos_arr as $user_video){
						$user_videos['video'] = $user_video;
						array_push($user_videos_array, $user_videos);
					}
				}
				if($userAssignmnt->user_other_files!=''){
					$user_other_files_arr = !empty($userAssignmnt->user_other_files) ? explode(",", $userAssignmnt->user_other_files) : [];
					foreach($user_other_files_arr as $user_other_file){
						$user_other_files['other_file'] = $user_other_file;
						array_push($user_other_files_array, $user_other_files);
					}
				}
				$teacher_approved = 0;
				if (!empty($userAssignmnt)) {
					if ($userAssignmnt->teacher_images != '' || $userAssignmnt->teacher_videos != '' || $userAssignmnt->teacher_content != '') {
						$teacher_approved = ($userAssignmnt->teacher_approved == 1) ? 1 : 2;
					} elseif ($userAssignmnt->teacher_approved == 1) {
						$teacher_approved = 1;
					} else {
						$teacher_approved = 0;
					}
				}
				$userAssignmntdata[$key]['assignSubId']		 = $userAssignmnt->id;
				$userAssignmntdata[$key]['assignmentId']	 = $userAssignmnt->assignment_id;
				$userAssignmntdata[$key]['assignment_title'] = $userAssignmnt->assignment->title;
				$userAssignmntdata[$key]['course_name']		 = $userAssignmnt->assignment->course->name;
				$userAssignmntdata[$key]['created_at'] 		 = $userAssignmnt->created_at->diffForHumans();
				$userAssignmntdata[$key]['user_status'] 	 = $userAssignmnt->user_status;
				$userAssignmntdata[$key]['teacher_approved'] = !empty($userAssignmnt) ? $teacher_approved : 0;
			}
			$this->helper->trackingApi($userId, 'assignment', '', 0, '', $deviceType);
			
			$message = "Get User Submitted Assignments.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "userAssignment" => $userAssignmntdata )
			]);
		} else {
			$message = "Submitted Assignment Details Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "userAssignment" => "" )]);
		}
	}
	public function getUserSubmittedAssignments(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId      = $request->userId;
		$deviceType  = $request->deviceType;
		$userStatus  = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "userAssignment" => array("assignSubId" => "") )]);
		}
		$search      = $request->search;
		$assignmentId = $request->assignmentId;
		$userSubscription = $this->helper->userSubscription($userId);
		$userAssignments = AssignmentSubmission::with('assignment','user')
								->where("assignment_id", $assignmentId)
								->where("user_id", $userId)
								->WhereHas('assignment', function($query) use ($search) {
									if (isset($search) && !empty($search)) {
										$query->where('assignments.title', 'LIKE', "%".$search."%");
										//$query->orWhere('courses.name', 'LIKE', "%".$search."%");
										$query->orWhere('assignment_submissions.user_content', 'LIKE', "%".$search."%");
									}
								})->where('deleted', 0)->get();
		$userAssignmntdata = array();
		if (!empty($userAssignments)) {
			foreach ($userAssignments as $key => $userAssignmnt) {
				$user_images_array = $user_videos_array = $user_other_files_array = $user_images_videos_array = $user_submitted_files_array = array();
				if($userAssignmnt->user_images!=''){
					$user_images_arr = !empty($userAssignmnt->user_images) ? explode(",", $userAssignmnt->user_images) : [];
					foreach($user_images_arr as $user_image){
						$user_images['file_path'] = $user_image;
						$user_images['file_type'] = 'image';
						array_push($user_images_array, $user_images);
					}
				}
				if($userAssignmnt->user_videos!=''){
					$user_videos_arr = !empty($userAssignmnt->user_videos) ? explode(",", $userAssignmnt->user_videos) : [];
					foreach($user_videos_arr as $user_video){
						$user_videos['file_path'] = $user_video;
						$user_videos['file_type'] = 'video';
						array_push($user_videos_array, $user_videos);
					}
				}
				$user_images_videos_array = array_merge($user_images_array, $user_videos_array);
				if($userAssignmnt->user_other_files!=''){
					$user_other_files_arr = !empty($userAssignmnt->user_other_files) ? explode(",", $userAssignmnt->user_other_files) : [];
					foreach($user_other_files_arr as $user_other_file){
						$user_other_files['file_path'] = $user_other_file;
						$user_other_files['file_type'] = 'other_file';
						array_push($user_other_files_array, $user_other_files);
					}
				}
				$user_submitted_files_array = array_merge($user_images_videos_array, $user_other_files_array);
				$teacher_approved = 0;
				if (!empty($userAssignmnt)) {
					if ($userAssignmnt->teacher_images != '' || $userAssignmnt->teacher_videos != '' || $userAssignmnt->teacher_content != '') {
						$teacher_approved = ($userAssignmnt->teacher_approved == 1) ? 1 : 2;
					} elseif ($userAssignmnt->teacher_approved == 1) {
						$teacher_approved = 1;
					} else {
						$teacher_approved = 0;
					}
				}
				$userAssignmntdata[$key]['assignSubId']			 = $userAssignmnt->id;
				$userAssignmntdata[$key]['assignment_title']	 = $userAssignmnt->assignment->title;
				$userAssignmntdata[$key]['course_name']			 = $userAssignmnt->assignment->course->name;
				$userAssignmntdata[$key]['created_at'] 			 = $userAssignmnt->created_at->diffForHumans();
				$userAssignmntdata[$key]['user_submitted_files'] = $user_submitted_files_array;
				$userAssignmntdata[$key]['user_content'] 	 	 = !empty($userAssignmnt->user_content) ? $userAssignmnt->user_content : '';
				$userAssignmntdata[$key]['user_status'] 	 	 = $userAssignmnt->user_status;
				$userAssignmntdata[$key]['teacher_approved'] 	 = !empty($userAssignmnt) ? $teacher_approved : 0;
			}
			$this->helper->trackingApi($userId, 'assignment', 'user submitted', $assignmentId, '', $deviceType);
			
			$message = "Get User Submitted Assignments.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "userAssignment" => $userAssignmntdata )
			]);
		} else {
			$message = "Submitted Assignment Details Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "userAssignment" => "" )]);
		}
	}
	public function getRemarkedAssignment(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "userAssignment" => array("assignSubId" => "") )]);
		}
		$assignSubId  = $request->assignSubId;
		$userSubscription = $this->helper->userSubscription($userId);
		$userAssignmnt = AssignmentSubmission::with('assignment','user')->where("id", $assignSubId)->where('user_status', 1)->where('assigned_to', '>', 0)->where('deleted', 0)->first();
		$userAssignmntdata = $teacher_images_array = $teacher_videos_array = $teacher_other_files_array = $teacher_images_videos_array = $teacher_submitted_files_array = array();
		if (!empty($userAssignmnt)) {
			if($userAssignmnt->teacher_images!=''){
				$teacher_images_arr = !empty($userAssignmnt->teacher_images) ? explode(",", $userAssignmnt->teacher_images) : [];
				foreach($teacher_images_arr as $teacher_image){
					$teacher_images['file_path'] = $teacher_image;
					$teacher_images['file_type'] = 'image';
					array_push($teacher_images_array, $teacher_images);
				}
			}
			if($userAssignmnt->teacher_videos!=''){
				$teacher_videos_arr = !empty($userAssignmnt->teacher_videos) ? explode(",", $userAssignmnt->teacher_videos) : [];
				foreach($teacher_videos_arr as $teacher_video){
					$teacher_videos['file_path'] = $teacher_video;
					$teacher_videos['file_type'] = 'video';
					array_push($teacher_videos_array, $teacher_videos);
				}
			}
			$teacher_images_videos_array = array_merge($teacher_images_array, $teacher_videos_array);
			if($userAssignmnt->teacher_other_files!=''){
				$teacher_other_files_arr = !empty($userAssignmnt->teacher_other_files) ? explode(",", $userAssignmnt->teacher_other_files) : [];
				foreach($teacher_other_files_arr as $teacher_other_file){
					$teacher_other_files['file_path'] = $teacher_other_file;
					$teacher_other_files['file_type'] = 'other_file';
					array_push($teacher_other_files_array, $teacher_other_files);
				}
			}
			$teacher_submitted_files_array = array_merge($teacher_images_videos_array, $teacher_other_files_array);
			$teacher_approved = 0;
			if (!empty($userAssignmnt)) {
				if ($userAssignmnt->teacher_images != '' || $userAssignmnt->teacher_videos != '' || $userAssignmnt->teacher_content != '') {
					$teacher_approved = ($userAssignmnt->teacher_approved == 1) ? 1 : 2;
				} elseif ($userAssignmnt->teacher_approved == 1) {
					$teacher_approved = 1;
				} else {
					$teacher_approved = 0;
				}
			}
			$userAssignmntdata['assignSubId']				= $userAssignmnt->id;
			$userAssignmntdata['assignment_title']			= $userAssignmnt->assignment->title;
			$userAssignmntdata['course_name']				= $userAssignmnt->assignment->course->name;
			$userAssignmntdata['teacher_submitted_files']	= $teacher_submitted_files_array;
			$userAssignmntdata['teacher_content']			= !empty($userAssignmnt->teacher_content) ? $userAssignmnt->teacher_content : '';
			$userAssignmntdata['teacher_approved']			= !empty($userAssignmnt) ? $teacher_approved : 0;
			$this->helper->trackingApi($userId, 'user_assignment', 'teacher reviewed', $assignSubId, '', $deviceType);
			
			$message = "Teacher Checked Assignment Details.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "userAssignment" => $userAssignmntdata)]);
		} else {
			$message = "Teacher Not Checked Assignment Yet!";
			return response()->json(['statusCode' => 400, 'message' => $message]);
		}
	}

	public function getCommunityPostTypes(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "postTypes" => [] )]);
		}
		$userSubscription = $this->helper->userSubscription($userId);
		$post_type_array[] = array('id' => 100, 'title' => "All");
		foreach(config('constant.POST_TYPE') as $value => $key){
			$post_type['id'] = $key;
			$post_type['title'] = ucwords(strtolower($value));
			array_push($post_type_array, $post_type);
		}
		if (!empty($post_type_array)) {
			$message = "Get Community Post Types Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "postTypes" => $post_type_array )
			]);
		} else {
			$message = "Community Post Types Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "postTypes" => [] )]);
		}
	}
	public function getCommunityPosts(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "communityCatPosts" => [], "topPosts" => [] )]);
		}
		$postTypeId = $request->postTypeId;
		$userSubscription = $this->helper->userSubscription($userId);
		$cPostCategories = CommunityCategory::OrderBy('title','ASC')->get();
		$cPostCategorydata = array();
		if (!empty($cPostCategories)) {
			if ($postTypeId==100) {
				$cat = 0;
				foreach ($cPostCategories as $category) {
					$communityPosts = CommunityPost::where("cat_id", $category->id)->where("status", 1)->where("deleted", 0)->orderBy("sort_id", "ASC")->get();
					$communityPostArr = array();
					if (!empty($communityPosts)) {
						foreach ($communityPosts as $communityPost) {
							$communityPostdata['id']		= $communityPost['id'];
							$communityPostdata['title'] 	= $communityPost['title'];
							$communityPostdata['image'] 	= $communityPost['image'];
							$communityPostdata['end_date']	= !empty($communityPost['end_date']) ? $communityPost['end_date'] : '';
							array_push($communityPostArr, $communityPostdata);
						}
						if (!empty($communityPostArr)) {
							$cPostCategorydata[$cat]['title'] = $category['title'];
							$cPostCategorydata[$cat++]['communityPosts'] = $communityPostArr;
						}
					}
				}
			} else {
				$cat = 0;
				foreach ($cPostCategories as $category) {
					$communityPosts = CommunityPost::where("post_type", $postTypeId)->where("cat_id", $category->id)->where("status", 1)->where("deleted", 0)->orderBy("sort_id", "ASC")->get();
					$communityPostArr = array();
					if (!empty($communityPosts)) {
						foreach ($communityPosts as $communityPost) {
							$communityPostdata['id']		= $communityPost['id'];
							$communityPostdata['title'] 	= $communityPost['title'];
							$communityPostdata['image'] 	= $communityPost['image'];
							$communityPostdata['end_date']	= !empty($communityPost['end_date']) ? $communityPost['end_date'] : '';
							array_push($communityPostArr, $communityPostdata);
						}
						if (!empty($communityPostArr)) {
							$cPostCategorydata[$cat]['title'] = $category['title'];
							$cPostCategorydata[$cat++]['communityPosts'] = $communityPostArr;
						}
					}
				}
				//$postTypeData[] = array(strtolower($value)."CommunityPosts" => $cPPostTypeCategorydata);
			}

			$topPosts = PostEntry::selectRaw('post_entries.*, count(post_entries_likes.id) as likes_count')
				->leftJoin('post_entries_likes', function ($join) {
					$join->on('post_entries_likes.post_entry_id', '=', 'post_entries.id');
				})
				->where("status", 1)
				->where('deleted', 0)
				->groupBy('post_entries.id')
				->orderBy('likes_count', 'desc')
				->take(10)
				->get();
			$topPostdata = array();
			if (!empty($topPosts)) {
				foreach ($topPosts as $top => $topPost) {
					if ($topPost->post_likes_data()->count()>0) {
						$topPostdata[$top]['id'] 			 = $topPost['id'];
						$topPostdata[$top]['user_name']		 = $topPost->user_data->name;
						$topPostdata[$top]['user_image'] 	 = !empty($topPost->user_data->image) ? asset('upload/profile/'.$topPost->user_data->image) : 'NA';
						$topPostdata[$top]['created_at'] 	 = $topPost->created_at->diffForHumans();
						//$topPostdata[$top]['title'] 		 = $topPost['title'];
						$topPostdata[$top]['description'] 	 = !empty($topPost['description']) ? $topPost['description'] : '';
						$topPostdata[$top]['image'] 		 = !empty($topPost['image']) ? $topPost['image'] : 'NA';
						$topPostdata[$top]['video'] 		 = !empty($topPost['video']) ? $topPost['video'] : 'NA';
						$topPostdata[$top]['likes_count'] 	 = $topPost->post_likes_data()->count();
						$topPostdata[$top]['comments_count'] = $topPost->post_comments_data()->count();
						$topPostdata[$top]['user_like'] 	 = PostEntryLike::where('post_entry_id', $topPost['id'])->where('user_id', $userId)->count();
						$topPostdata[$top]['share_url'] 	 = '';
					}
				}
			}
			//echo '<pre />'; print_r($postTypeData[101]); die;
			$this->helper->trackingApi($userId, 'community_post', '', 0, '', $deviceType);

			$message = "Get Community Posts Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "communityCatPosts" => $cPostCategorydata, "topPosts" => $topPostdata )
			]);
		} else {
			$message = "Community Post Category Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "communityCatPosts" => [], "topPosts" => [] )]);
		}
	}
	public function communityPostDetails(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "communityPost" => array("id"=>""), "entriesPosts" => [], "leaderboard" => [] )]);
		}
		$communityPostId = $request->communityPostId;
		$userSubscription = $this->helper->userSubscription($userId);
		$today	  = date('Y-m-d');
		$communityPost = CommunityPost::where("id", $communityPostId)->where("status", 1)->where('deleted', 0)->first();
		$communityPostdata = array();
		if (!empty($communityPost)) {
			$communityPostdata['id']	= $communityPost->id;
			$communityPostdata['title']	= $communityPost->title;
			$communityPostdata['image']	= !empty($communityPost->image) ? $communityPost->image : 'NA';
			$communityPostdata['video']	= !empty($communityPost->video) ? $communityPost->video : 'NA';
			$communityPostdata['description'] = !empty($communityPost->description) ? $communityPost->description : '';
			$communityPostdata['end_date'] = !empty($communityPost->end_date) ? $communityPost->end_date : '';
			if (!empty($communityPost->end_date)) {
				if (strtotime($communityPost->end_date) >= strtotime($today)) {
					$challengeStatusBtn = 1;
				}
			}
			$communityPostdata['upload_entry_btn'] = isset($challengeStatusBtn) ? $challengeStatusBtn : 0;
			$postEntries = $communityPost->post_entries_data()->where("status", 1)->where('deleted', 0)->orderBy('sort_id', 'ASC')->get();
			//$postEntries = PostEntry::where("community_post_id", $communityPost->id)->where("status", 1)->where('deleted', 0)->orderBy('id', 'DESC')->get();
			$entriesPostdata = array();
			if (!empty($postEntries)) {
				foreach ($postEntries as $key => $postEntry) {
					$entriesPostdata[$key]['id'] 			 = $postEntry['id'];
					$entriesPostdata[$key]['user_name']		 = $postEntry->user_data->name;
					$entriesPostdata[$key]['user_image'] 	 = !empty($postEntry->user_data->image) ? asset('upload/profile/'.$postEntry->user_data->image) : 'NA';
					$entriesPostdata[$key]['created_at'] 	 = $postEntry->created_at->diffForHumans();
					//$entriesPostdata[$key]['title'] 		 = $postEntry['title'];
					$entriesPostdata[$key]['description'] 	 = !empty($postEntry['description']) ? $postEntry['description'] : '';
					$entriesPostdata[$key]['image'] 		 = !empty($postEntry['image']) ? $postEntry['image'] : 'NA';
					$entriesPostdata[$key]['video'] 		 = !empty($postEntry['video']) ? $postEntry['video'] : 'NA';
					$entriesPostdata[$key]['likes_count'] 	 = $postEntry->post_likes_data()->count();
					$entriesPostdata[$key]['comments_count'] = $postEntry->post_comments_data()->count();
					$entriesPostdata[$key]['user_like'] 	 = PostEntryLike::where('post_entry_id', $postEntry['id'])->where('user_id', $userId)->count();
					$entriesPostdata[$key]['share_url'] 	 = '';
				}
			}
			/*$leaderboardPosts = PostEntry::selectRaw('post_entries.*, count(post_entries_likes.id) as likes_count')
				->leftJoin('post_entries_likes', function ($join) {
					$join->on('post_entries_likes.post_entry_id', '=', 'post_entries.id');
				})
				->where("community_post_id", $communityPost->id)
				->where("status", 1)
				->where('deleted', 0)
				->groupBy('post_entries.id')
				->orderBy('likes_count', 'desc')
				->take(5)
				->get();
			$leaderboarddata = array();
			if (!empty($leaderboardPosts)) {
				foreach ($leaderboardPosts as $led => $leaderPost) {
					if ($leaderPost->post_likes_data()->count()>0) {
						$leaderboarddata[$led]['id'] 			 = $leaderPost['id'];
						$leaderboarddata[$led]['user_name']		 = $leaderPost->user_data->name;
						$leaderboarddata[$led]['user_image'] 	 = !empty($leaderPost->user_data->image) ? asset('upload/profile/'.$leaderPost->user_data->image) : 'NA';
						$leaderboarddata[$led]['created_at'] 	 = $leaderPost->created_at->diffForHumans();
						//$leaderboarddata[$led]['title'] 		 = $leaderPost['title'];
						$leaderboarddata[$led]['description'] 	 = !empty($leaderPost['description']) ? $leaderPost['description'] : '';
						$leaderboarddata[$led]['image'] 		 = !empty($leaderPost['image']) ? $leaderPost['image'] : 'NA';
						$leaderboarddata[$led]['video'] 		 = !empty($leaderPost['video']) ? $leaderPost['video'] : 'NA';
						$leaderboarddata[$led]['likes_count'] 	 = $leaderPost->post_likes_data()->count();
						$leaderboarddata[$led]['comments_count'] = $leaderPost->post_comments_data()->count();
						$leaderboarddata[$led]['user_like'] 	 = PostEntryLike::where('post_entry_id', $leaderPost['id'])->where('user_id', $userId)->count();
						$leaderboarddata[$led]['share_url'] 	 = '';
					}
				}
			}*/
			$leaderboardPosts = $communityPost->post_entries_data()->where("status", 1)->where('deleted', 0)->orderBy('sort_id', 'ASC')->take(5)->get();
			$leaderboarddata = array();
			if (!empty($leaderboardPosts)) {
				foreach ($leaderboardPosts as $led => $leaderPost) {
					$leaderboarddata[$led]['id'] 		 = $leaderPost['id'];
					$leaderboarddata[$led]['user_name']	 = $leaderPost->user_data->name;
					$leaderboarddata[$led]['user_image'] = !empty($leaderPost->user_data->image) ? asset('upload/profile/'.$leaderPost->user_data->image) : 'NA';
					$leaderboarddata[$led]['position']	 = $led+1;
				}
			}
			$this->helper->trackingApi($userId, 'community_post', '', $communityPostId, '', $deviceType);
			
			$message = "Get Community Post Details Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "communityPost" => $communityPostdata, "entriesPosts" => $entriesPostdata, "leaderboard" => $leaderboarddata )
			]);
		} else {
			$message = "Community Post Details Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "communityPost" => array("id"=>""), "entriesPosts" => [], "leaderboard" => [] )]);
		}
	}
	public function userPostLikes(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "userPostId" => "" )]);
		}
		$userPostId = $request->userPostId;
		$userSubscription = $this->helper->userSubscription($userId);
		$userPostLikes = PostEntryLike::where('post_entry_id', $userPostId)->where('user_id', $userId)->first();
		if (!empty($userPostLikes)) {
			$delete = $userPostLikes->delete();
			$message = "Post Unlike Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "userPostId" => $userPostId )]);
		} else {
			$data = array(
				'post_entry_id' => $userPostId,
				'user_id' 		=> $userId,
				'created_at' 	=> date('Y-m-d H:i:s'),
			);
			$insertId = PostEntryLike::insertGetId($data);
			$message = "Post Like Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "userPostId" => $userPostId )]);
		}
	}
	public function getUserPostComments(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "userPost" => array("id"=>""), "postComments" => [] )]);
		}
		$userPostId = $request->userPostId;
		$userSubscription = $this->helper->userSubscription($userId);
		$postEntry = PostEntry::where("id", $userPostId)->where("status", 1)->where('deleted', 0)->first();
		$postEntrydata = array();
		if (!empty($postEntry)) {
			$postEntrydata['id']			 = $postEntry->id;
			$postEntrydata['user_name']		 = $postEntry->user_data->name;
			$postEntrydata['user_image'] 	 = !empty($postEntry->user_data->image) ? asset('upload/profile/'.$postEntry->user_data->image) : 'NA';
			$postEntrydata['created_at'] 	 = $postEntry->created_at->diffForHumans();
			//$postEntrydata['title']			 = $postEntry->title;
			$postEntrydata['description'] 	 = !empty($postEntry->description) ? $postEntry->description : '';
			$postEntrydata['image']			 = !empty($postEntry->image) ? $postEntry->image : 'NA';
			$postEntrydata['video']			 = !empty($postEntry->video) ? $postEntry->video : 'NA';
			$postEntrydata['likes_count'] 	 = $postEntry->post_likes_data()->count();
			$postEntrydata['comments_count'] = $postEntry->post_comments_data()->count();
			$postEntrydata['user_like'] 	 = PostEntryLike::where('post_entry_id', $postEntry->id)->where('user_id', $userId)->count();
			$postEntrydata['share_url'] 	 = '';
			$postComments = $postEntry->post_comments_data()->orderBy('id', 'ASC')->get();
			//$postEntries = PostEntry::where("community_post_id", $communityPost->id)->where("status", 1)->where('deleted', 0)->orderBy('id', 'DESC')->get();
			$postCommentdata = array();
			if (!empty($postComments)) {
				foreach ($postComments as $key => $postComment) {
					$postCommentdata[$key]['id'] 		 = $postComment['id'];
					$postCommentdata[$key]['user_name']	 = $postComment->user_data->name;
					$postCommentdata[$key]['user_image'] = !empty($postComment->user_data->image) ? asset('upload/profile/'.$postComment->user_data->image) : 'NA';
					$postCommentdata[$key]['created_at'] = $postComment->created_at->diffForHumans();
					$postCommentdata[$key]['comment'] 	 = $postComment['comment'];
				}
			}
			$this->helper->trackingApi($userId, 'community_post_user', 'users post', $userPostId, '', $deviceType);
			
			$message = "Get User Post Details Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "userPost" => $postEntrydata, "postComments" => $postCommentdata )
			]);
		} else {
			$message = "User Post Details Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "userPost" => array("id"=>""), "postComments" => [] )]);
		}
	}
	public function savePostComment(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "userPostId" => "" )]);
		}
		$userPostId = $request->userPostId;
		$comment = $request->comment;
		$userSubscription = $this->helper->userSubscription($userId);
		$data = array(
			'post_entry_id' => $userPostId,
			'user_id' 		=> $userId,
			'comment' 		=> $comment,
			'created_at' 	=> date('Y-m-d H:i:s'),
		);
		$insertId = PostEntryComment::insertGetId($data);
		$message = "Post Comment Save Successfully.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "userPostId" => $userPostId )]);
	}
	public function userPostCreate(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("communityPostId" => "")]);
		}
		$communityPostId = $request->communityPostId;
		$title = $request->title;
		$content = $request->content;
		$msg = '';
		if (!empty($userId) && !empty($communityPostId) || !empty($content) || !empty($request->file('image_file')) || !empty($request->file('video_file')) ) {
			$image_file = $request->file('image_file');
			$video_file = $request->file('video_file');
			if($request->hasFile('image_file')){
				if($image_file){
					$destinationPath = public_path().'/upload/community_posts/';
					$imageOriginalFile = $image_file->getClientOriginalName();
					$imageFilename = "user_post_".$communityPostId."_".time().$imageOriginalFile;
					$image_file->move($destinationPath, $imageFilename);
					/*$url = 'upload/community_posts/' . $imageFilename;
					$thumb_img = Image::make($url)->resize(200, 200);
					$thumb_img->save('upload/community_posts/thumb/'.$imageFilename,80);*/
					//S3 Upload
					$s3 = AWS::createClient('s3');
					$s3->putObject(array(
						'Bucket'     => env('AWS_BUCKET'),
						'Key'        => "assignment_data/".$imageFilename,
						'SourceFile' => $destinationPath.$imageFilename,
					));
					$imageUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $imageFilename;
					if(!empty($image_file) && file_exists( $destinationPath.$imageFilename )) {
						unlink( $destinationPath.$imageFilename );
					}
				}
			}
			if($request->hasFile('video_file')){
				if($video_file){
					$destinationPath = public_path().'/upload/community_posts/';
					$videoOriginalFile = $video_file->getClientOriginalName();
					//$videoFilename = rand(111,999).strtotime(date('Y-m-d-H:isa')).$videoOriginalFile;
					$videoFilename = "user_post_".$communityPostId."_".time()."_org.mp4";
					$video_file->move($destinationPath, $videoFilename);
					//S3 Upload
					$s3 = AWS::createClient('s3');
					$s3->putObject(array(
						'Bucket'     => env('AWS_BUCKET'),
						'Key'        => "assignment_data/".$videoFilename,
						'SourceFile' => $destinationPath.$videoFilename,
					));
					$videoUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $videoFilename;
					if(!empty($video_file) && file_exists( $destinationPath.$videoFilename )) {
						unlink( $destinationPath.$videoFilename );
					}
				}
			}
			$data = array(
				'community_post_id' => $communityPostId,
				'user_id' 			=> $userId,
				'title' 			=> !empty($title) ? $title : NULL,
				'image' 			=> !empty($imageUrl) ? $imageUrl : NULL,
				'video' 			=> !empty($videoUrl) ? $videoUrl : NULL,
				'description' 		=> $content,
				'status' 			=> 0,
				'created_at' 		=> date('Y-m-d H:i:s'),
			);
			$insertId = PostEntry::insertGetId($data);
			$message = 'Your post uploaded successfully, Team will approve it.';
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("communityPostId" => $communityPostId)]);
		} else {
			$message = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("communityPostId" => "")]);
		}
	}

	public function quizGuideline(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("quiz" => array("id" => ""))]);
		}
		$courseId = $request->courseId;
		$lessionId = $request->lessionId;
		$topicId = $request->topicId;
		if ($lessionId > 0) {
			if ($topicId > 0) {
				$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where("topicId", $topicId)->where('islession', 2)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
			} else {
				$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 0)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
			}
		} else {
			$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 1)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
		}
		if (!empty($getQuiz)) {
			$course  = Courses::where("id", $courseId)->where("status", 1)->where('deleted', 0)->first();
			$lession = Lession::where("id", $lessionId)->where("status", 1)->where('deleted', 0)->first();
			//$topic = Chapter::where("id", $topicId)->where("status", 1)->where('deleted', 0)->first();
			$quizId = $getQuiz->id;
			$quiz['id'] = $getQuiz->id;
			$quiz['name'] = $getQuiz->name;
			$quiz['courseId'] = $courseId;
			$quiz['lessionId'] = $lessionId;
			$quiz['topicId'] = $topicId;
			$quiz['courseName'] = $course->name;
			$quiz['lessionName'] = !empty($lession->name) ? $lession->name : '';
			$quiz['topicName'] = !empty($getQuiz->topic->name) ? $getQuiz->topic->name : '';

			//$quiz['total_time'] = strtotime($getQuiz->duration);
			$hour         = date('H',strtotime($getQuiz->duration));
			$minute       = date('i',strtotime($getQuiz->duration));
			$seconds      = date('s',strtotime($getQuiz->duration));

			$milliseconds = $this->convertTimeinMiliseconds($hour,$minute,$seconds);
			$quiz['total_time'] = $milliseconds;

			$examQuestions = Quizquestions::where("quizId", $getQuiz->id)->get();
			$total_marks = 0;
			foreach ($examQuestions as $ques) {
				$total_marks += $ques['marking'];
			}
			$quiz['total_questions'] = count($examQuestions);
			$quiz['total_marks'] = $total_marks;
			$quiz['guideline'] = $getQuiz->guideline;
			$checkStudentExam = StudentExam::where("user_id", $userId)->where("course_id", $courseId)->where("lession_id", $lessionId)->where("topic_id", $topicId)->orderBy("id","DESC")->first();
			$remaining_time = "";
			if (!empty($checkStudentExam) && $checkStudentExam->is_complete==0) {
				$examId = $checkStudentExam->id;
				$examTotalTime = $checkStudentExam->total_time;
				$remaining_time = $checkStudentExam->remaining_time;
				$quiz['exam_id'] = $examId;
			} else {
				$quiz['exam_id'] = 0;
			}
			$quiz['remaining_time'] = !empty($remaining_time) ? $remaining_time : '';
			if (!empty($checkStudentExam)) {
				$is_complete = $checkStudentExam->is_complete + 1;
			} else {
				$is_complete = 0;
			}
			$quiz['quizStatus']	= $is_complete;
			$this->helper->trackingApi($userId, 'quiz', 'guideline', $quizId, '', $deviceType);
			$message = "Get quiz details successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("quiz" => $quiz)]);
		} else {
			$message = "Quiz Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("quiz" => array("id" => ""))]);
		}
	}
	public function letStartQuiz(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("quiz" => array("quiz_id" => ""), "quizdata" => [])]);
		}
		$courseId = $request->courseId;
		$lessionId = $request->lessionId;
		$topicId = $request->topicId;
		if ($lessionId > 0) {
			if ($topicId > 0) {
				$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where("topicId", $topicId)->where('islession', 2)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
			} else {
				$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 0)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
			}
		} else {
			$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 1)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
		}
		if (!empty($getQuiz)) {
			$quizId = $getQuiz->id;
			$quiz['quiz_id'] = $getQuiz->id;
			$quiz['name'] = $getQuiz->name;
			$checkStudentExam = StudentExam::where("user_id", $userId)->where("course_id", $courseId)->where("lession_id", $lessionId)->where("topic_id", $topicId)->where('is_complete', 0)->orderBy("id","DESC")->first();
			if (!empty($checkStudentExam)) {
				$examId = $checkStudentExam->id;
				$data = array(
					'start_time' => date('H:i:s'),
					'end_time'   => date('H:i:s'),
					'updated_at' => date('Y-m-d H:i:s'),
				);
				$update = StudentExam::where("id", $examId)->update($data);
				$quiz['exam_id'] = $examId;
				$quizdata = array();
				$examQuestions = Quizquestions::where("quizId", $getQuiz->id)->get();
				$total_questions = count($examQuestions);
				foreach ($examQuestions as $key => $val) {
					$quizdata[$key]['id'] = $val->id;
					$quizdata[$key]['questions'] = $val->questions;
					$quizdata[$key]['image'] = !empty($val->image) ? asset('upload/quizquestions') . "/" . $val->image : '';
					$quizdata[$key]['marking'] = $val->marking;
					$queoptions = Quizoption::where("questionId", $val->id)->orderBy("id", "ASC")->get();
					$optiondata = array();
					foreach ($queoptions as $key1 => $value) {
						if ($key1 <= 3) {
							$optiondata[$key1]['id'] = $value->id;
							if ($value->val_type==0) {
								$optiondata[$key1]['option'] = !empty($value->quizoption) ? asset('upload/quizquestions') . "/" . $value->quizoption : '';
							} else {
								$optiondata[$key1]['option'] = $value->quizoption;
							}
							$optiondata[$key1]['val_type'] = $value->val_type;
						}
					}
					$quizdata[$key]['answers'] = $optiondata;
					$stAnswer = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $val->id)->first();
					$quizdata[$key]['given_answer'] = !empty($stAnswer->answer) ? $stAnswer->answer : 0;
				}
				$total_attemped = StudentExamAnswer::where("exam_id", $examId)->where("attemp", 1)->count();
				$quiz['total_attemped'] = $total_attemped;
				$quiz['total_unattemped'] = $total_questions - $total_attemped;
				$this->helper->trackingApi($userId, 'quiz', 'quiz re-start', $quizId, '', $deviceType);
				$message = "Get all quiz questions list with previous exam.";
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("quiz" => $quiz, "quizdata" => $quizdata)]);
			} else {
				$data = array(
					'user_id'     => $userId,
					'course_id'   => $courseId,
					'lession_id'  => $lessionId,
					'topic_id' 	  => $topicId,
					'start_time'  => date('H:i:s'),
					'is_complete' => 0,
					'created_at'  => date('Y-m-d H:i:s'),
				);
				$examId = StudentExam::insertGetId($data);
				$quiz['exam_id'] = $examId;
				$quizdata = array();
				$examQuestions = Quizquestions::where("quizId", $getQuiz->id)->get();
				$total_questions = count($examQuestions);
				foreach ($examQuestions as $key => $val) {
					$quizdata[$key]['id'] = $val->id;
					$quizdata[$key]['questions'] = $val->questions;
					$quizdata[$key]['image'] = !empty($val->image) ? asset('upload/quizquestions') . "/" . $val->image : '';
					$quizdata[$key]['marking'] = $val->marking;
					$queoptions = Quizoption::where("questionId", $val->id)->get();
					$optiondata = array();
					foreach ($queoptions as $key1 => $value) {
						if ($key1 <= 3) {
							$optiondata[$key1]['id'] = $value->id;
							if ($value->val_type==0) {
								$optiondata[$key1]['option'] = !empty($value->quizoption) ? asset('upload/quizquestions') . "/" . $value->quizoption : '';
							} else {
								$optiondata[$key1]['option'] = $value->quizoption;
							}
							$optiondata[$key1]['val_type'] = $value->val_type;
						}
					}
					$quizdata[$key]['answers'] = $optiondata;
					$quizdata[$key]['given_answer'] = 0;
				}
				$total_attemped = StudentExamAnswer::where("exam_id", $examId)->where("attemp", 1)->count();
				$quiz['total_attemped'] = $total_attemped;
				$quiz['total_unattemped'] = $total_questions - $total_attemped;
				$this->helper->trackingApi($userId, 'quiz', 'quiz start', $quizId, '', $deviceType);
				$message = "Get all quiz questions list.";
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("quiz" => $quiz, "quizdata" => $quizdata)]);
			}
		} else {
			$message = "Quiz Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("quiz" => "", "quizdata" => "")]);
		}
	}
	public function pauseQuiz(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("exam_id" => "")]);
		}
		$examId = $request->examId;
		$remainingTime = $request->remainingTime;
		$studentExam = StudentExam::where("id", $examId)->where("user_id", $userId)->where('is_complete', 0)->orderBy("id","DESC")->first();
		if (!empty($studentExam)) {
			$start_time = $studentExam->start_time;
			$end_time = date('H:i:s');
			$start = Carbon::parse($start_time);
			$end = Carbon::parse($end_time);
			/*$hours = $end->diffInHours($start);
			$minutes = $end->diffInMinutes($start);*/
			$seconds = $end->diffInSeconds($start);
			$total_time = sprintf('%02d:%02d:%02d', ($seconds/3600),($seconds/60%60), $seconds%60);
			$before_total_time = $studentExam->total_time;
			if (!empty($before_total_time)) {
				$newDateTime = Carbon::parse($before_total_time)->addSeconds($seconds);
				$total_time = date('H:i:s', strtotime($newDateTime));
			}
			$data = array(
				'end_time'   => $end_time,
				'total_time' => $total_time,
				'remaining_time' => $remainingTime,
				'updated_at' => date('Y-m-d H:i:s'),
			);
			$update = StudentExam::where("id", $examId)->update($data);
			$message = "Exam paused successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("exam_id" => $examId)]);
		} else {
			$message = "Quiz Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("exam_id" => "")]);
		}
	}
	public function nextQuiz(Request $request)
	{
		$examId = $request->examId;
		$quesId = $request->quesId;
		$answer = $request->answer;
		if (!empty($answer)) {
			$attemp = 1;
		} else {
			$attemp = 0;
		}
		$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $quesId)->first();
		if (!empty($studentAns)) {
			$studentAnsId = $studentAns->id;
			$data = array(
				'answer'     => $answer,
				'attemp'     => $attemp,
				'updated_at' => date('Y-m-d H:i:s'),
			);
			$update = StudentExamAnswer::where("id", $studentAnsId)->update($data);
			$message = "Answer updated successfully.";
		} else {
			$data = array(
				'exam_id'    => $examId,
				'ques_id'    => $quesId,
				'answer'     => $answer,
				'attemp'     => $attemp,
				'created_at' => date('Y-m-d H:i:s'),
			);
			$studentAnsId = StudentExamAnswer::insertGetId($data);
			$message = "Answer inserted successfully.";
		}
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("ans_id" => $studentAnsId)]);
	}
	public function endQuiz(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("exam_id" => "")]);
		}
		$examId = $request->examId;
		$studentExam = StudentExam::where("id", $examId)->where("user_id", $userId)->where('is_complete', 0)->orderBy("id","DESC")->first();
		if (!empty($studentExam)) {
			$start_time = $studentExam->start_time;
			$end_time = date('H:i:s');
			$start = Carbon::parse($start_time);
			$end = Carbon::parse($end_time);
			/*$hours = $end->diffInHours($start);
			$minutes = $end->diffInMinutes($start);*/
			$seconds = $end->diffInSeconds($start);
			$total_time = sprintf('%02d:%02d:%02d', ($seconds/3600),($seconds/60%60), $seconds%60);
			$before_total_time = $studentExam->total_time;
			if (!empty($before_total_time)) {
				$newDateTime = Carbon::parse($before_total_time)->addSeconds($seconds);
				$total_time = date('H:i:s', strtotime($newDateTime));
			}
			$data = array(
				'end_time'   => $end_time,
				'total_time' => $total_time,
				'is_complete' => 1,
				'updated_at' => date('Y-m-d H:i:s'),
			);
			$update = StudentExam::where("id", $examId)->update($data);
			
			$getStudentExam = StudentExam::where("id", $examId)->where("user_id", $userId)->where("is_complete", 1)->first();
			if (!empty($getStudentExam)) {
				$courseId = $getStudentExam->course_id;
				$lessionId = $getStudentExam->lession_id;
				$topicId = $getStudentExam->topic_id;
				$total_time = $getStudentExam->total_time;
				$attemped_date = date('d M, Y', strtotime($getStudentExam->created_at));
				if ($lessionId > 0) {
					if ($topicId > 0) {
						$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where("topicId", $topicId)->where('islession', 2)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
					} else {
						$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 0)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
					}
				} else {
					$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 1)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
				}
				if (!empty($getQuiz)) {
					$quizId = $getQuiz->id;
					$passing_percent = $getQuiz->passing_percent;
					$quizdata = array();
					$examQuestions = Quizquestions::where("quizId", $getQuiz->id)->get();
					$total_questions = count($examQuestions);
					$total_marking = $score = $right = $wrong = $total_solved = $not_solved = 0;
					foreach ($examQuestions as $key => $val) {
						$quizdata[$key]['id'] = $val->id;
						$quizdata[$key]['questions'] = $val->questions;
						$quizdata[$key]['image'] = !empty($val->image) ? asset('upload/quizquestions') . "/" . $val->image : '';
						$quizdata[$key]['marking'] = $val->marking;
						$total_marking += $val->marking;

						$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $val->id)->first();
						if (!empty($studentAns)) {
							$quizAnswers = Quizoption::where("questionId", $val->id)->orderBy("id", "ASC")->get();
							$answer = '';
							foreach ($quizAnswers as $key1 => $value) {
								if ($key1 == ($val->currect_option - 1)){
									$answer = $value->id;
								}
							}
							if ($answer == $studentAns->answer) {
								$right++;
								$score += $val->marking;
							} else {
								$wrong++;
							}
						}
					}
					$total_solved = $right + $wrong;
					$not_solved = $total_questions - $total_solved;
					//$percentage = number_format((($right * 100) / $total_questions), 2);
					$percentage = number_format((($score * 100) / $total_marking), 2);
					if ($percentage >= $passing_percent) {
						$download_status = 1;
					} else {
						$download_status = 0;
					}
					
					$user = User::where("id", $userId)->first();
					if ($download_status == 1) {
						$msg = 'Hi, '.$user->name.' Wonderful score on the recent quiz. Keep learning';
					} else {
						$msg = 'Sorry, Minimum '.$passing_percent.'% was needed to get the certificate. Please re-attempt the test again. All the best.';
					}
					$this->helper->addNotification($userId,$msg);
					$this->helper->trackingApi($userId, 'quiz', 'quiz end', $quizId, '', $deviceType);
				}
			}
			$message = "Exam ended successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("exam_id" => $examId)]);
		} else {
			$message = "Quiz Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("exam_id" => "")]);
		}
	}
	public function cancelQuiz(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("quiz" => array("id" => ""))]);
		}
		$courseId = $request->courseId;
		$lessionId = $request->lessionId;
		$topicId = $request->topicId;
		if ($lessionId > 0) {
			if ($topicId > 0) {
				$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where("topicId", $topicId)->where('islession', 2)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
			} else {
				$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 0)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
			}
		} else {
			$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 1)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
		}
		if (!empty($getQuiz)) {
			$quizId = $getQuiz->id;
			$quiz['id'] = $getQuiz->id;
			$quiz['name'] = $getQuiz->name;
			$quiz['courseId'] = $courseId;
			$quiz['lessionId'] = $lessionId;
			$quiz['topicId'] = $topicId;
			//$cancelExam = StudentExam::where("user_id", $userId)->where("course_id", $courseId)->where("lession_id", $lessionId)->where('is_complete', 0)->delete();
			$studentExam = StudentExam::where("user_id", $userId)->where("course_id", $courseId)->where("lession_id", $lessionId)->where("topic_id", $topicId)->where('is_complete', 0)->orderBy("id","DESC")->first();
			$examId = $studentExam->id;
			//$answerDel = StudentExamAnswer::where("exam_id", $examId)->delete();
			$countStudentExam = StudentExam::where("user_id", $userId)->where("course_id", $courseId)->where("lession_id", $lessionId)->where("topic_id", $topicId)->where('is_complete', 1)->orderBy("id","DESC")->count();
			$completeStatus = -1;
			if ($countStudentExam > 1) {
				$completeStatus = 2; //Retake exam cancelled status
			}
			$data = array(
				'start_time' => '00:00:00',
				'end_time'   => '00:00:00',
				'total_time' => '00:00:00',
				'is_complete' => $completeStatus,
				'updated_at' => date('Y-m-d H:i:s'),
			);
			$update = StudentExam::where("id", $examId)->update($data);
			$this->helper->trackingApi($userId, 'quiz', 'quiz cancelled', $quizId, '', $deviceType);
			$message = "Quiz cancelled successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("quiz" => $quiz)]);
		} else {
			$message = "Quiz Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("quiz" => array("id" => ""))]);
		}
	}
	public function attempedQuiz(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("quiz" => array("total_questions" => ""), "allquiz" => [], "attempedquiz" => [], "unattempedquiz" => [])]);
		}
		$examId = $request->examId;
		$quizId = $request->quizId;
		$quizdata = array();
		$attempeddata = array();
		$unattempeddata = array();
		$examQuestions = Quizquestions::where("quizId", $quizId)->get();
		$total_questions = count($examQuestions);
		if (!empty($examQuestions)) {
			foreach ($examQuestions as $key => $val) {
				$quizdata[$key]['id'] = $val->id;
				//$quizdata[$key]['questions'] = $val->questions;
				$quizdata[$key]['ques_num'] = $key + 1;
				$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $val->id)->first();
				$quizdata[$key]['attemp'] = isset($studentAns->attemp) ? $studentAns->attemp : 2;
			}
			foreach ($examQuestions as $key1 => $val) {
				$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $val->id)->first();
				if (!empty($studentAns) && $studentAns->attemp == 1) {
					$attemp['id'] = $val->id;
					//$attemp['questions'] = $val->questions;
					$attemp['ques_num'] = $key1 + 1;
					$attemp['attemp'] = $studentAns->attemp;
					array_push($attempeddata, $attemp);
				}
			}
			foreach ($examQuestions as $key2 => $val) {
				$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $val->id)->first();
				if (empty($studentAns) || $studentAns->attemp == 0) {
					$unattemp['id'] = $val->id;
					$unattemp['ques_num'] = $key2 + 1;
					$unattemp['attemp'] = isset($studentAns->attemp) ? 0 : 2;
					array_push($unattempeddata, $unattemp);
				}
			}
			$quiz['total_questions'] = $total_questions;
			$total_attemped = StudentExamAnswer::where("exam_id", $examId)->where("attemp", 1)->count();
			$quiz['total_attemped'] = $total_attemped;
			$quiz['total_unattemped'] = $total_questions - $total_attemped;
			$this->helper->trackingApi($userId, 'exam', 'attemped/unattemped', $examId, '', $deviceType);
			$message = "Get all quiz questions list with attemped/unattemped data.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("quiz" => $quiz, "allquiz" => $quizdata, "attempedquiz" => $attempeddata, "unattempedquiz" => $unattempeddata)]);
		} else {
			$message = "Quiz Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("quiz" => "", "allquiz" => "", "attempedquiz" => "", "unattempedquiz" => "")]);
		}
	}
	public function getAllAttempedQuiz(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("attemped" => [])]);
		}
		$courseId = $request->courseId;
		$lessionId = $request->lessionId;
		$topicId = $request->topicId;
		$getStudentExams = StudentExam::where("user_id", $userId)->where("course_id", $courseId)->where("lession_id", $lessionId)->where("topic_id", $topicId)->where("is_complete", 1)->get();
		$attempdata = array();
		if (!empty($getStudentExams)) {
			foreach ($getStudentExams as $key => $val) {
				$attemp = $key + 1;
				$attempdata[$key]['attemp'] = 'Attempt ('.$attemp.')';
				$attempdata[$key]['examId'] = $val->id;
			}
			$this->helper->trackingApi($userId, 'exam', 'attemped list', 0, '', $deviceType);
			$message = "Get all quiz attemped list by user.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("attemped" => $attempdata)]);
		} else {
			$message = "Quiz Attemped Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("attemped" => [])]);
		}
	}
	public function getQuizHistory(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("quiz" => array("quiz_id" => ""), "quizhistory" => [])]);
		}
		$examId = $request->examId;
		/*$quizId = $request->quizId;
		$getQuiz = Quiz::where("id", $quizId)->where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->first();
		$courseId = $request->courseId;
		$lessionId = $request->lessionId;*/
		$getStudentExam = StudentExam::where("id", $examId)->where("user_id", $userId)->where("is_complete", 1)->first();
		if (!empty($getStudentExam)) {
			$examId = $getStudentExam->id;
			$courseId = $getStudentExam->course_id;
			$lessionId = $getStudentExam->lession_id;
			$topicId = $getStudentExam->topic_id;
			if ($lessionId > 0) {
				if ($topicId > 0) {
					$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where("topicId", $topicId)->where('islession', 2)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
				} else {
					$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 0)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
				}
			} else {
				$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 1)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
			}
			if (!empty($getQuiz)) {
				$quiz['quiz_id'] = $getQuiz->id;
				$quiz['name'] = $getQuiz->name;
				$quiz['courseId'] = $courseId;
				$quiz['lessionId'] = $lessionId;
				$quiz['topicId'] = $topicId;
				$quizdata = array();
				$examQuestions = Quizquestions::where("quizId", $getQuiz->id)->get();
				$total_questions = count($examQuestions);
				$quiz['total_questions'] = $total_questions;
				foreach ($examQuestions as $key => $val) {
					$quizdata[$key]['id'] = $val->id;
					$quizdata[$key]['questions'] = $val->questions;
					$quizdata[$key]['image'] = !empty($val->image) ? asset('upload/quizquestions') . "/" . $val->image : '';
					$quizdata[$key]['marking'] = $val->marking;
					$quizdata[$key]['solution'] = $val->solution;
					$queoptions = Quizoption::where("questionId", $val->id)->get();
					$optiondata = array();
					foreach ($queoptions as $key1 => $value) {
						if ($key1 <= 3) {
							$optiondata[$key1]['id'] = $value->id;
							if ($value->val_type==0) {
								$optiondata[$key1]['option'] = !empty($value->quizoption) ? asset('upload/quizquestions') . "/" . $value->quizoption : '';
							} else {
								$optiondata[$key1]['option'] = $value->quizoption;
							}
							$optiondata[$key1]['val_type'] = $value->val_type;
						}
					}
					$quizdata[$key]['answers'] = $optiondata;
					$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $val->id)->first();
					if (!empty($studentAns)) {
						$quizStAnswer = Quizoption::where("id", $studentAns->answer)->first();
					}
					//$quizdata[$key]['given_answer'] = isset($quizStAnswer->quizoption) ? $quizStAnswer->quizoption : 0;
					$quizdata[$key]['given_answer'] = isset($studentAns->answer) ? $studentAns->answer : 0;
					$quizAnswers = Quizoption::where("questionId", $val->id)->orderBy("id", "ASC")->get();
					$answer = '';
					foreach ($quizAnswers as $key1 => $value) {
						if ($key1 == ($val->currect_option - 1)){
							$answer = $value->id;
						}
					}
					$quizAnswer = Quizoption::where("id", $answer)->first();
					//$quizdata[$key]['correct_answer'] = isset($quizAnswer->quizoption) ? $quizAnswer->quizoption : 'NA';
					$quizdata[$key]['correct_answer'] = isset($answer) ? $answer : 'NA';
				}
				$this->helper->trackingApi($userId, 'exam', 'history', $examId, '', $deviceType);
				$message = "Get all quiz questions list history.";
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("quiz" => $quiz, "quizhistory" => $quizdata)]);
			} else {
				$message = "Quiz Not Found!";
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("quiz" => "", "quizhistory" => "")]);
			}
		} else {
			$message = "Quiz Attemped Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("quiz" => "", "quizhistory" => "")]);
		}
	}
	public function getQuizResult(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("quiz" => array("quiz_id" => ""))]);
		}
		$examId = $request->examId;
		/*$quizId = $request->quizId;
		$courseId = $request->courseId;
		$lessionId = $request->lessionId;*/
		$getStudentExam = StudentExam::where("id", $examId)->where("user_id", $userId)->where("is_complete", 1)->first();
		if (!empty($getStudentExam)) {
			//$examId = $getStudentExam->id;
			$courseId = $getStudentExam->course_id;
			$lessionId = $getStudentExam->lession_id;
			$topicId = $getStudentExam->topic_id;
			$total_time = $getStudentExam->total_time;
			$certificate = $getStudentExam->certificate;
			$attemped_date = date('d M, Y', strtotime($getStudentExam->created_at));
			//$getQuiz = Quiz::where("id", $quizId)->where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->first();
			if ($lessionId > 0) {
				if ($topicId > 0) {
					$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where("topicId", $topicId)->where('islession', 2)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
				} else {
					$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 0)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
				}
			} else {
				$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 1)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
			}
			if (!empty($getQuiz)) {
				$quiz['quiz_id'] = $getQuiz->id;
				$quiz['name'] = $getQuiz->name;
				$islession = $getQuiz->islession;
				$passing_percent = $getQuiz->passing_percent;
				$coursename = !empty($getQuiz->courses->name) ? $getQuiz->courses->name : '';
				$quiz['courseId'] = $courseId;
				$quiz['lessionId'] = $lessionId;
				$quiz['topicId'] = $topicId;
				$quizdata = array();
				$examQuestions = Quizquestions::where("quizId", $getQuiz->id)->get();
				$total_questions = count($examQuestions);
				$quiz['total_questions'] = $total_questions;
				$quiz['time_efficiency'] = $total_time;
				$quiz['attemped_date'] = $attemped_date;
				$total_marking = $score = $right = $wrong = $total_solved = $not_solved = 0;
				foreach ($examQuestions as $key => $val) {
					$quizdata[$key]['id'] = $val->id;
					$quizdata[$key]['questions'] = $val->questions;
					$quizdata[$key]['image'] = !empty($val->image) ? asset('upload/quizquestions') . "/" . $val->image : '';
					$quizdata[$key]['marking'] = $val->marking;
					$total_marking += $val->marking;
					$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $val->id)->first();
					if (!empty($studentAns)) {
						$quizAnswers = Quizoption::where("questionId", $val->id)->orderBy("id", "ASC")->get();
						$answer = '';
						foreach ($quizAnswers as $key1 => $value) {
							if ($key1 == ($val->currect_option - 1)){
								$answer = $value->id;
							}
						}
						if ($answer == $studentAns->answer) {
							$right++;
							$score += $val->marking;
						} else {
							$wrong++;
						}
					}
				}
				$total_solved = $right + $wrong;
				$not_solved = $total_questions - $total_solved;
				$quiz['total_attemped'] = $total_solved;
				$quiz['right_answer'] = $right;
				$quiz['wrong_answer'] = $wrong + $not_solved;
				$quiz['total_score'] = $score.' out of '.$total_marking;
				$percentage = number_format((($score * 100) / $total_marking), 2);
				$quiz['percentage'] = $percentage;
				if ($percentage >= $passing_percent) {
					$download_status = 1;
					$quiz['remark_status'] = 'Passed';
				} else {
					$download_status = 0;
					$quiz['remark_status'] = 'Failed';
				}
				if ($islession==1){
					$download_status = $download_status;
				} else {
					$download_status = 0;
				}
				$quiz['download_status'] = $download_status;
				if ($download_status==1) {
					if ($certificate !='') {
						$quiz['certificate_url'] = asset('upload/generatedPDF') . "/" . $certificate;
					} else {
						$user = User::where("id", $userId)->first();
						$username = $user->name;
						$newCertificate = $this->generatePDF($examId, $username, $coursename, $attemped_date);
						$quiz['certificate_url'] = asset('upload/generatedPDF') . "/" . $newCertificate;
					}
				} else {
					$quiz['certificate_url'] = '';
				}
				$this->helper->trackingApi($userId, 'exam', 'result', $examId, '', $deviceType);

				$message = "Get all quiz questions list score.";
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("quiz" => $quiz)]);
			} else {
				$message = "Quiz Not Found!";
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("quiz" => array("quiz_id" => ""))]);
			}
		} else {
			$message = "Quiz Attemped Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("quiz" => array("quiz_id" => ""))]);
		}
	}
	public function getQuizResultDetails(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("quiz" => array("total_questions" => ""), "allquiz" => [], "correctquiz" => [], "incorrectquiz" => [], "skipedquiz" => [])]);
		}
		$examId = $request->examId;
		/*$quizId = $request->quizId;
		$courseId = $request->courseId;
		$lessionId = $request->lessionId;*/
		$getStudentExam = StudentExam::where("id", $examId)->where("user_id", $userId)->where("is_complete", 1)->first();
		if (!empty($getStudentExam)) {
			//$examId = $getStudentExam->id;
			$courseId = $getStudentExam->course_id;
			$lessionId = $getStudentExam->lession_id;
			$topicId = $getStudentExam->topic_id;
			$total_time = $getStudentExam->total_time;
			$attemped_date = date('d M, Y', strtotime($getStudentExam->created_at));
			//$getQuiz = Quiz::where("id", $quizId)->where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->first();
			if ($lessionId > 0) {
				if ($topicId > 0) {
					$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where("topicId", $topicId)->where('islession', 2)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
				} else {
					$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 0)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
				}
			} else {
				$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 1)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
			}
			if (!empty($getQuiz)) {
				$quizdata = array();
				$correctdata = array();
				$incorrectdata = array();
				$unattempeddata = array();
				$examQuestions = Quizquestions::where("quizId", $getQuiz->id)->get();
				$total_questions = count($examQuestions);
				$quiz['total_questions'] = $total_questions;
				$score = $right = $wrong = $total_solved = $not_solved = 0;
				foreach ($examQuestions as $key => $val) {
					$quizdata[$key]['id'] = $val->id;
					$quizdata[$key]['ques_num'] = $key + 1;
					$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $val->id)->first();
					$quizdata[$key]['attemp'] = isset($studentAns->attemp) ? $studentAns->attemp : 2;
					$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $val->id)->first();
					if (!empty($studentAns)) {
						$quizAnswers = Quizoption::where("questionId", $val->id)->orderBy("id", "ASC")->get();
						$answer = '';
						foreach ($quizAnswers as $arr_key => $value) {
							if ($arr_key == ($val->currect_option - 1)){
								$answer = $value->id;
							}
						}
						if ($answer == $studentAns->answer) {
							$right++;
							$score += $val->marking;
						} else {
							if ($studentAns->attemp == 1) {
								$wrong++;
							}
						}
					}
				}
				$total_solved = $right + $wrong;
				$not_solved = $total_questions - $total_solved;
				$quiz['total_attemped'] = $total_solved;
				$quiz['right_answer'] = $right;
				$quiz['wrong_answer'] = $wrong;
				$quiz['skiped_answer'] = $not_solved;
				$quiz['total_score'] = $score;
				
				foreach ($examQuestions as $key1 => $val) {
					$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $val->id)->first();
					if (!empty($studentAns)) {
						$quizAnswers = Quizoption::where("questionId", $val->id)->orderBy("id", "ASC")->get();
						$answer = '';
						foreach ($quizAnswers as $arr_key => $value) {
							if ($arr_key == ($val->currect_option - 1)){
								$answer = $value->id;
							}
						}
						if ($answer == $studentAns->answer) {
							$correct['id'] = $val->id;
							//$correct['questions'] = $val->questions;
							$correct['ques_num'] = $key1 + 1;
							$correct['attemp'] = $studentAns->attemp;
							array_push($correctdata, $correct);
						} else {
							if ($studentAns->attemp == 1) {
								$incorrect['id'] = $val->id;
								//$incorrect['questions'] = $val->questions;
								$incorrect['ques_num'] = $key1 + 1;
								$incorrect['attemp'] = $studentAns->attemp;
								array_push($incorrectdata, $incorrect);
							}
						}
					}
				}
				foreach ($examQuestions as $key2 => $val) {
					$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $val->id)->first();
					if (empty($studentAns) || $studentAns->attemp == 0) {
						$unattemp['id'] = $val->id;
						$unattemp['ques_num'] = $key2 + 1;
						$unattemp['attemp'] = 2;
						array_push($unattempeddata, $unattemp);
					}
				}
				$this->helper->trackingApi($userId, 'exam', 'result', $examId, '', $deviceType);

				$message = "Get all quiz questions list result details.";
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("quiz" => $quiz, "allquiz" => $quizdata, "correctquiz" => $correctdata, "incorrectquiz" => $incorrectdata, "skipedquiz" => $unattempeddata)]);
			} else {
				$message = "Quiz Not Found!";
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("quiz" => "", "allquiz" => "", "correctquiz" => "", "incorrectquiz" => "", "skipedquiz" => "")]);
			}
		} else {
			$message = "Quiz Attemped Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("quiz" => "", "allquiz" => "", "correctquiz" => "", "incorrectquiz" => "", "skipedquiz" => "")]);
		}
	}
	public function generatePDF($examId, $username, $coursename, $date)
	{
		$date = date('d/m/Y',strtotime($date));
		$data = ['username' => $username, 'coursename' => $coursename, 'date' => $date];
		$pdf = PDF::loadView('front.myPDF', $data);

		//return $pdf->stream();
  
		//return $pdf->download('certificate.pdf');
		$destinationPath = public_path().'/upload/generatedPDF/';
		$filename = $examId.'_'.time().'.pdf';
		if($pdf->save($destinationPath.$filename)->stream('download.pdf')){
			$update = StudentExam::where("id", $examId)->update(array("certificate" => $filename));
			return $filename;
		} else {
			return 0;
		}
	}

	public function liveClassesNow(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "classId" => "", "className" => "", "downloadTimeTable" => "", "pin_message" => "", "academicLiveClasses" => [], "skillLiveClasses" => [])]);
		}
		$user = User::where("id", $userId)->first();
		$board_id = !empty($user) ? $user->board_id : '';
		$class_id = !empty($user) ? $user->class_id : '';
		$boardId = ($request->board_id) ? $request->board_id : $board_id;
		$classId = ($request->class_id) ? $request->class_id : $class_id;
		$userClass = StudentClass::where("id", $classId)->first();
		$className = !empty($userClass) ? $userClass->class_name : 'NA';
		$search = $request->search;
		$from = date('Y-m-d H:i:s', strtotime("-45 minutes")); //date('Y-m-d').' 00:00:00';
		$to = date('Y-m-d').' 23:59:59'; //date('Y-m-d H:i:s');
		//$dt = Carbon::now();
		$userSubscription = $this->helper->userSubscription($userId);
		
		//Inactive LiveClass after end
		$now = date('Y-m-d H:i:s');
		$pastClasses = LiveClass::where("status", 1)->where("deleted", 0)->where("end_time", "<", $now)->whereDate("end_time", "=", date('Y-m-d'))->orderBy("end_time", "DESC")->get();
		foreach($pastClasses as $pastCls){
			LiveClass::where("id", $pastCls->id)->update(["status" => 2]);
		}
		$timeTableDoc = LiveClassTimeTableDoc::where("class_id", $classId)->orderBy("id", "DESC")->first();
		$downloadTimeTable = !empty($timeTableDoc) ? asset('upload/liveclass_timetables/'.$timeTableDoc->doc_file) : '';
		$pinMessage = PinMessage::whereRaw("find_in_set($classId,class_ids)")->where("msg_type", 204)->where("status", 1)->orderBy('id', 'DESC')->first();
		$pinMessageLiveClass = !empty($pinMessage) ? $pinMessage->message : '';
		$classSubject = StudentClassSubject::where("class_id", $classId)->first();
		$academicClassData = $skillClassData = array();
		if (!empty($classSubject)) {
			$subject_data = $subject_ids = [];
			if (!empty($search)) {
				$subject_data = Subject::select("id")->where("title", 'like', "%" . $search . "%")->get();
				foreach ($subject_data as $subjects) {
					$subject_ids[] = $subjects->id;
				}
				if (empty($subject_ids)) {
					$subject_data = [];
					$subject_ids = !empty($classSubject->subject_ids) ? explode(",", $classSubject->subject_ids) : [];
				}
			} else {
				$subject_ids = !empty($classSubject->subject_ids) ? explode(",", $classSubject->subject_ids) : [];
			}
			$acad = $skil = 0;
			foreach ($subject_ids as $subject) {
				$subjectdata = Subject::where("id",$subject)->first();
				$academicLiveClasses = LiveClass::with('user','subject_data')->where("class_type", 301)->whereRaw("find_in_set($classId,class_ids)")->where("subject_id", $subject)->whereBetween('class_time', [$from, $to])->where("status", 1)->where("deleted", 0);
				//if (isset($subject_data) && empty($subject_data->toArray())) {
				if (empty($subject_data)) {
					//dd($subject_data);
					if (!empty($search)) {
						$academicLiveClasses = $academicLiveClasses->where("title", 'like', "%" . $search . "%");
					}
				}
				$academicLiveClasses = $academicLiveClasses->orderBy('class_time', 'ASC')->get();
				$academicClassNow = array();
				if (!empty($academicLiveClasses)) {
					$newindex='';
					foreach ($academicLiveClasses as $key => $val) {
						if (strtotime($val['class_time']) < strtotime(date("Y-m-d H:i:s", strtotime("+1 minutes"))) && strtotime($val['end_time']) > strtotime(date('Y-m-d H:i:s')) ) {
							$liveNow = 1;
						} else {
							$liveNow = 0;
						}
						$academicClass['id']			= $val['id'];
						$academicClass['added_by']		= ($val['added_by']>0) ? $val->user->name : 'NA';
						$academicClass['title'] 		= $val['title'];
						$academicClass['subject']		= ($val['subject_id']>0) ? $val->subject_data->title : 'NA';
						$academicClass['image'] 		= asset('upload/liveclasses') . "/" . $val['image'];
						$academicClass['meeting_id']	= $val['meeting_id'];
						$academicClass['pass_code']		= $val['pass_code'];
						$academicClass['master_class']	= $val['master_class'];
						$academicClass['class_time']	= date('d/m/Y, h:i A', strtotime($val['class_time']));
						$academicClass['isFree']		= $val['isFree'];
						$check_interest = LiveclassNotify::where("user_id", $userId)->where("class_id", $val['id'])->first();
						$my_interest = !empty($check_interest) ? 1 : 0;
						$academicClass['my_interest'] 	= $my_interest;
						$total_interest = LiveclassNotify::where("class_id", $val['id'])->count();
						$academicClass['total_interest'] = $total_interest;
						$academicClass['liveNow']		 = $liveNow;
						if(strtotime($newindex) < strtotime($val['class_time']) )
						{
							$newindex=strtotime($val['class_time']);
						}
						elseif(empty($newindex))
						{
							$newindex=strtotime($val['class_time']);
						}
						array_push($academicClassNow, $academicClass);
					}
					if (!empty($academicClassNow)) {
						$academicClassData[$newindex.$acad]['subject_title'] = $subjectdata->title;
						$academicClassData[$newindex.$acad]['academic_classes'] = $academicClassNow;
						$acad++;
						ksort($academicClassData);
					}
				}

				$skillLiveClasses = LiveClass::with('user','subject_data')->where("class_type", 302)->whereRaw("find_in_set($classId,class_ids)")->where("subject_id", $subject)->whereBetween('class_time', [$from, $to])->where("status", 1)->where("deleted", 0);
				if (empty($subject_data)) {
					if (!empty($search)) {
						$skillLiveClasses = $skillLiveClasses->where("title", 'like', "%" . $search . "%");
					}
				}
				$skillLiveClasses = $skillLiveClasses->orderBy('class_time', 'ASC')->limit(10)->get();
				$skillClassNow = array();
				if (!empty($skillLiveClasses)) {
					$newindex1='';
					foreach ($skillLiveClasses as $key => $val) {
						if (strtotime($val['class_time']) < strtotime(date("Y-m-d H:i:s", strtotime("+1 minutes"))) && strtotime($val['end_time']) > strtotime(date('Y-m-d H:i:s')) ) {
							$liveNow = 1;
						} else {
							$liveNow = 0;
						}
						$skillClass['id'] 			 = $val['id'];
						$skillClass['added_by'] 	 = ($val['added_by']>0) ? $val->user->name : 'NA';
						$skillClass['title'] 		 = $val['title'];
						$skillClass['subject'] 	 = ($val['subject_id']>0) ? $val->subject_data->title : 'NA';
						$skillClass['image'] 		 = asset('upload/liveclasses') . "/" . $val['image'];
						$skillClass['meeting_id'] 	 = $val['meeting_id'];
						$skillClass['pass_code'] 	 = $val['pass_code'];
						$skillClass['master_class'] = $val['master_class'];
						$skillClass['class_time'] 	 = date('d/m/Y, h:i A', strtotime($val['class_time']));
						$skillClass['isFree'] 		 = $val['isFree'];
						$check_interest = LiveclassNotify::where("user_id", $userId)->where("class_id", $val['id'])->first();
						$my_interest = !empty($check_interest) ? 1 : 0;
						$skillClass['my_interest']  = $my_interest;
						$total_interest = LiveclassNotify::where("class_id", $val['id'])->count();
						$skillClass['total_interest'] = $total_interest;
						$skillClass['liveNow']		  = $liveNow;
						if(strtotime($newindex1) < strtotime($val['class_time']) )
						{
							$newindex1=strtotime($val['class_time']);
						}
						elseif(empty($newindex1))
						{
							$newindex1=strtotime($val['class_time']);
						}
						array_push($skillClassNow, $skillClass);
					}
					if (!empty($skillClassNow)) {
						$skillClassData[$newindex1.$skil]['subject_title'] = $subjectdata->title;
						$skillClassData[$newindex1.$skil]['skill_classes'] = $skillClassNow;
						$skil++;
						ksort($skillClassData);
					}
				}
			}
			$this->helper->trackingApi($userId, 'live_class', 'Live Now Live Classes', 0, '', $deviceType);
			$message = "All Live Classes Data.";
			
			$academicClassData=array_values($academicClassData);
			$skillClassData=array_values($skillClassData);
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "classId" => $classId, "className" => $className, "downloadTimeTable" => $downloadTimeTable, "pin_message" => $pinMessageLiveClass, "academicLiveClasses" => $academicClassData, "skillLiveClasses" => $skillClassData)]);
		} else {
			$message = "Subject not assigned in this class!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "classId" => $classId, "className" => $className, "downloadTimeTable" => $downloadTimeTable, "pin_message" => $pinMessageLiveClass, "academicLiveClasses" => $academicClassData, "skillLiveClasses" => $skillClassData)]);
		}
	}
	public function liveClassesUpcoming(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "classId" => "", "className" => "", "downloadTimeTable" => "", "pin_message" => "", "todayAcademicLiveClasses" => [], "todaySkillLiveClasses" => [], "tomorrowAcademicLiveClasses" => [], "tomorrowSkillLiveClasses" => [], "dATAcademicLiveClasses" => [], "dATSkillLiveClasses" => [])]);
		}
		$user = User::where("id", $userId)->first();
		$board_id = !empty($user) ? $user->board_id : '';
		$class_id = !empty($user) ? $user->class_id : '';
		$boardId = ($request->board_id) ? $request->board_id : $board_id;
		$classId = ($request->class_id) ? $request->class_id : $class_id;
		$userClass = StudentClass::where("id", $classId)->first();
		$className = !empty($userClass) ? $userClass->class_name : 'NA';
		$search = $request->search;
		$todayFrom = date('Y-m-d H:i:s'); //date('Y-m-d').' 00:00:00';
		$todayTo = date('Y-m-d').' 23:59:59'; //date('Y-m-d H:i:s');
		$tomorrowFrom = date('Y-m-d', strtotime("+1 day")).' 00:00:00';
		$tomorrowTo = date('Y-m-d', strtotime("+1 day")).' 23:59:59';
		$dATomorrowFrom = date('Y-m-d', strtotime("+2 day")).' 00:00:00';
		$dATomorrowTo = date('Y-m-d', strtotime("+2 day")).' 23:59:59';
		//echo 'todayFrom: '.$todayFrom.' todayTo: '.$todayTo.' tomorrowFrom: '.$tomorrowFrom.' tomorrowTo: '.$tomorrowTo.' dATomorrowFrom: '.$dATomorrowFrom.' dATomorrowTo: '.$dATomorrowTo; die;
		//$dt = Carbon::now();
		$userSubscription = $this->helper->userSubscription($userId);
		
		$timeTableDoc = LiveClassTimeTableDoc::where("class_id", $classId)->orderBy("id", "DESC")->first();
		$downloadTimeTable = !empty($timeTableDoc) ? asset('upload/liveclass_timetables/'.$timeTableDoc->doc_file) : '';
		$pinMessage = PinMessage::whereRaw("find_in_set($classId,class_ids)")->where("msg_type", 205)->where("status", 1)->orderBy('id', 'DESC')->first();
		$pinMessageLiveClass = !empty($pinMessage) ? $pinMessage->message : '';
		$classSubject = StudentClassSubject::where("class_id", $classId)->first();
		$todayAcademicData = $todaySkillData = $tomorrowAcademicData = $tomorrowSkillData = $dATomorrowAcademicData = $dATomorrowSkillData = array();
		if (!empty($classSubject)) {
			$subject_data = $subject_ids = [];
			if (!empty($search)) {
				$subject_data = Subject::select("id")->where("title", 'like', "%" . $search . "%")->get();
				foreach ($subject_data as $subjects) {
					$subject_ids[] = $subjects->id;
				}
				if (empty($subject_ids)) {
					$subject_data = [];
					$subject_ids = !empty($classSubject->subject_ids) ? explode(",", $classSubject->subject_ids) : [];
				}
			} else {
				$subject_ids = !empty($classSubject->subject_ids) ? explode(",", $classSubject->subject_ids) : [];
			}
			$todayAca = $todaySki = $tomorrowAca = $tomorrowSki = $dATomorrowAca = $dATomorrowSki = 0;
			foreach ($subject_ids as $subject) {
				$subjectdata = Subject::where("id",$subject)->first();
				//start today classes
				$todayAcademicClasses = LiveClass::with('user','subject_data')->where("class_type", 301)->whereRaw("find_in_set($classId,class_ids)")->where("subject_id", $subject)->whereBetween('class_time', [$todayFrom, $todayTo])->where("status", 1)->where("deleted", 0);
				if (empty($subject_data)) {
					if (!empty($search)) {
						$todayAcademicClasses = $todayAcademicClasses->where("title", 'like', "%" . $search . "%")->orWhere('subject_title', 'LIKE', "%".$search."%");
					}
				}
				$todayAcademicClasses = $todayAcademicClasses->orderBy('class_time', 'ASC')->get();
				$todayAcademicClassArr = array();
				if (!empty($todayAcademicClasses)) {
					foreach ($todayAcademicClasses as $key => $val) {
						$todayAcademic['id']			= $val['id'];
						$todayAcademic['added_by']		= ($val['added_by']>0) ? $val->user->name : 'NA';
						$todayAcademic['title'] 		= $val['title'];
						$todayAcademic['subject']		= ($val['subject_id']>0) ? $val->subject_data->title : 'NA';
						$todayAcademic['image'] 		= asset('upload/liveclasses') . "/" . $val['image'];
						$todayAcademic['meeting_id']	= $val['meeting_id'];
						$todayAcademic['pass_code']	= $val['pass_code'];
						$todayAcademic['master_class']	= $val['master_class'];
						$todayAcademic['class_time']	= date('d/m/Y, h:i A', strtotime($val['class_time']));
						$todayAcademic['isFree']		= $val['isFree'];
						$check_interest = LiveclassNotify::where("user_id", $userId)->where("class_id", $val['id'])->first();
						$todayAcademic['my_interest'] 	= !empty($check_interest) ? 1 : 0;
						$total_interest = LiveclassNotify::where("class_id", $val['id'])->count();
						$todayAcademic['total_interest'] = $total_interest;
						array_push($todayAcademicClassArr, $todayAcademic);
					}
					if (!empty($todayAcademicClassArr)) {
						$todayAcademicData[$todayAca]['subject_title'] = $subjectdata->title;
						$todayAcademicData[$todayAca++]['today_academic_classes'] = $todayAcademicClassArr;
					}
				}
				$todaySkillClasses = LiveClass::with('user','subject_data')->where("class_type", 302)->whereRaw("find_in_set($classId,class_ids)")->where("subject_id", $subject)->whereBetween('class_time', [$todayFrom, $todayTo])->where("status", 1)->where("deleted", 0);
				if (empty($subject_data)) {
					if (!empty($search)) {
						$todaySkillClasses = $todaySkillClasses->where("title", 'like', "%" . $search . "%")->orWhere('subject_title', 'LIKE', "%".$search."%");
					}
				}
				$todaySkillClasses = $todaySkillClasses->orderBy('class_time', 'ASC')->limit(10)->get();
				$todaySkillClassArr = array();
				if (!empty($todaySkillClasses)) {
					foreach ($todaySkillClasses as $key => $val) {
						$todaySkillClass['id'] 			 = $val['id'];
						$todaySkillClass['added_by'] 	 = ($val['added_by']>0) ? $val->user->name : 'NA';
						$todaySkillClass['title'] 		 = $val['title'];
						$todaySkillClass['subject'] 	 = ($val['subject_id']>0) ? $val->subject_data->title : 'NA';
						$todaySkillClass['image'] 		 = asset('upload/liveclasses') . "/" . $val['image'];
						$todaySkillClass['meeting_id'] 	 = $val['meeting_id'];
						$todaySkillClass['pass_code'] 	 = $val['pass_code'];
						$todaySkillClass['master_class'] = $val['master_class'];
						$todaySkillClass['class_time'] 	 = date('d/m/Y, h:i A', strtotime($val['class_time']));
						$todaySkillClass['isFree'] 		 = $val['isFree'];
						$check_interest = LiveclassNotify::where("user_id", $userId)->where("class_id", $val['id'])->first();
						$my_interest = !empty($check_interest) ? 1 : 0;
						$todaySkillClass['my_interest']  = $my_interest;
						$total_interest = LiveclassNotify::where("class_id", $val['id'])->count();
						$todaySkillClass['total_interest'] = $total_interest;
						array_push($todaySkillClassArr, $todaySkillClass);
					}
					if (!empty($todaySkillClassArr)) {
						$todaySkillData[$todaySki]['subject_title'] = $subjectdata->title;
						$todaySkillData[$todaySki++]['today_skill_classes'] = $todaySkillClassArr;
					}
				}
				//end today classes
				//start tomorrow classes
				$tomorrowAcademicClasses = LiveClass::with('user','subject_data')->where("class_type", 301)->whereRaw("find_in_set($classId,class_ids)")->where("subject_id", $subject)->whereBetween('class_time', [$tomorrowFrom, $tomorrowTo])->where("status", 1)->where("deleted", 0);
				if (empty($subject_data)) {
					if (!empty($search)) {
						$tomorrowAcademicClasses = $tomorrowAcademicClasses->where("title", 'like', "%" . $search . "%")->orWhere('subject_title', 'LIKE', "%".$search."%");
					}
				}
				$tomorrowAcademicClasses = $tomorrowAcademicClasses->orderBy('class_time', 'ASC')->get();
				$tomorrowAcademicClassArr = array();
				if (!empty($tomorrowAcademicClasses)) {
					foreach ($tomorrowAcademicClasses as $key => $val) {
						$tomorrowAcademic['id']			= $val['id'];
						$tomorrowAcademic['added_by']		= ($val['added_by']>0) ? $val->user->name : 'NA';
						$tomorrowAcademic['title'] 		= $val['title'];
						$tomorrowAcademic['subject']		= ($val['subject_id']>0) ? $val->subject_data->title : 'NA';
						$tomorrowAcademic['image'] 		= asset('upload/liveclasses') . "/" . $val['image'];
						$tomorrowAcademic['meeting_id']	= $val['meeting_id'];
						$tomorrowAcademic['pass_code']	= $val['pass_code'];
						$tomorrowAcademic['master_class']	= $val['master_class'];
						$tomorrowAcademic['class_time']	= date('d/m/Y, h:i A', strtotime($val['class_time']));
						$tomorrowAcademic['isFree']		= $val['isFree'];
						$check_interest = LiveclassNotify::where("user_id", $userId)->where("class_id", $val['id'])->first();
						$tomorrowAcademic['my_interest'] 	= !empty($check_interest) ? 1 : 0;
						$total_interest = LiveclassNotify::where("class_id", $val['id'])->count();
						$tomorrowAcademic['total_interest'] = $total_interest;
						array_push($tomorrowAcademicClassArr, $tomorrowAcademic);
					}
					if (!empty($tomorrowAcademicClassArr)) {
						$tomorrowAcademicData[$tomorrowAca]['subject_title'] = $subjectdata->title;
						$tomorrowAcademicData[$tomorrowAca++]['tomorrow_academic_classes'] = $tomorrowAcademicClassArr;
					}
				}
				$tomorrowSkillClasses = LiveClass::with('user','subject_data')->where("class_type", 302)->whereRaw("find_in_set($classId,class_ids)")->where("subject_id", $subject)->whereBetween('class_time', [$tomorrowFrom, $tomorrowTo])->where("status", 1)->where("deleted", 0);
				if (empty($subject_data)) {
					if (!empty($search)) {
						$tomorrowSkillClasses = $tomorrowSkillClasses->where("title", 'like', "%" . $search . "%")->orWhere('subject_title', 'LIKE', "%".$search."%");
					}
				}
				$tomorrowSkillClasses = $tomorrowSkillClasses->orderBy('class_time', 'ASC')->limit(10)->get();
				$tomorrowSkillClassArr = array();
				if (!empty($tomorrowSkillClasses)) {
					foreach ($tomorrowSkillClasses as $key => $val) {
						$tomorrowSkillClass['id'] 			 = $val['id'];
						$tomorrowSkillClass['added_by'] 	 = ($val['added_by']>0) ? $val->user->name : 'NA';
						$tomorrowSkillClass['title'] 		 = $val['title'];
						$tomorrowSkillClass['subject'] 	 = ($val['subject_id']>0) ? $val->subject_data->title : 'NA';
						$tomorrowSkillClass['image'] 		 = asset('upload/liveclasses') . "/" . $val['image'];
						$tomorrowSkillClass['meeting_id'] 	 = $val['meeting_id'];
						$tomorrowSkillClass['pass_code'] 	 = $val['pass_code'];
						$tomorrowSkillClass['master_class'] = $val['master_class'];
						$tomorrowSkillClass['class_time'] 	 = date('d/m/Y, h:i A', strtotime($val['class_time']));
						$tomorrowSkillClass['isFree'] 		 = $val['isFree'];
						$check_interest = LiveclassNotify::where("user_id", $userId)->where("class_id", $val['id'])->first();
						$my_interest = !empty($check_interest) ? 1 : 0;
						$tomorrowSkillClass['my_interest']  = $my_interest;
						$total_interest = LiveclassNotify::where("class_id", $val['id'])->count();
						$tomorrowSkillClass['total_interest'] = $total_interest;
						array_push($tomorrowSkillClassArr, $tomorrowSkillClass);
					}
					if (!empty($tomorrowSkillClassArr)) {
						$tomorrowSkillData[$tomorrowSki]['subject_title'] = $subjectdata->title;
						$tomorrowSkillData[$tomorrowSki++]['tomorrow_skill_classes'] = $tomorrowSkillClassArr;
					}
				}
				//end tomorrow classes
				//start day after tomorrow classes
				$dATomorrowAcademicClasses = LiveClass::with('user','subject_data')->where("class_type", 301)->whereRaw("find_in_set($classId,class_ids)")->where("subject_id", $subject)->whereBetween('class_time', [$dATomorrowFrom, $dATomorrowTo])->where("status", 1)->where("deleted", 0);
				if (empty($subject_data)) {
					if (!empty($search)) {
						$dATomorrowAcademicClasses = $dATomorrowAcademicClasses->where("title", 'like', "%" . $search . "%")->orWhere('subject_title', 'LIKE', "%".$search."%");
					}
				}
				$dATomorrowAcademicClasses = $dATomorrowAcademicClasses->orderBy('class_time', 'ASC')->get();
				$dATomorrowAcademicClassArr = array();
				if (!empty($dATomorrowAcademicClasses)) {
					foreach ($dATomorrowAcademicClasses as $key => $val) {
						$dATomorrowAcademic['id']			= $val['id'];
						$dATomorrowAcademic['added_by']		= ($val['added_by']>0) ? $val->user->name : 'NA';
						$dATomorrowAcademic['title'] 		= $val['title'];
						$dATomorrowAcademic['subject']		= ($val['subject_id']>0) ? $val->subject_data->title : 'NA';
						$dATomorrowAcademic['image'] 		= asset('upload/liveclasses') . "/" . $val['image'];
						$dATomorrowAcademic['meeting_id']	= $val['meeting_id'];
						$dATomorrowAcademic['pass_code']	= $val['pass_code'];
						$dATomorrowAcademic['master_class']	= $val['master_class'];
						$dATomorrowAcademic['class_time']	= date('d/m/Y, h:i A', strtotime($val['class_time']));
						$dATomorrowAcademic['isFree']		= $val['isFree'];
						$check_interest = LiveclassNotify::where("user_id", $userId)->where("class_id", $val['id'])->first();
						$dATomorrowAcademic['my_interest'] 	= !empty($check_interest) ? 1 : 0;
						$total_interest = LiveclassNotify::where("class_id", $val['id'])->count();
						$dATomorrowAcademic['total_interest'] = $total_interest;
						array_push($dATomorrowAcademicClassArr, $dATomorrowAcademic);
					}
					if (!empty($dATomorrowAcademicClassArr)) {
						$dATomorrowAcademicData[$dATomorrowAca]['subject_title'] = $subjectdata->title;
						$dATomorrowAcademicData[$dATomorrowAca++]['dATomorrow_academic_classes'] = $dATomorrowAcademicClassArr;
					}
				}
				$dATomorrowSkillClasses = LiveClass::with('user','subject_data')->where("class_type", 302)->whereRaw("find_in_set($classId,class_ids)")->where("subject_id", $subject)->whereBetween('class_time', [$dATomorrowFrom, $dATomorrowTo])->where("status", 1)->where("deleted", 0);
				if (empty($subject_data)) {
					if (!empty($search)) {
						$dATomorrowSkillClasses = $dATomorrowSkillClasses->where("title", 'like', "%" . $search . "%")->orWhere('subject_title', 'LIKE', "%".$search."%");
					}
				}
				$dATomorrowSkillClasses = $dATomorrowSkillClasses->orderBy('class_time', 'ASC')->limit(10)->get();
				$dATomorrowSkillClassArr = array();
				if (!empty($dATomorrowSkillClasses)) {
					foreach ($dATomorrowSkillClasses as $key => $val) {
						$dATomorrowSkillClass['id'] 		 = $val['id'];
						$dATomorrowSkillClass['added_by'] 	 = ($val['added_by']>0) ? $val->user->name : 'NA';
						$dATomorrowSkillClass['title'] 		 = $val['title'];
						$dATomorrowSkillClass['subject'] 	 = ($val['subject_id']>0) ? $val->subject_data->title : 'NA';
						$dATomorrowSkillClass['image'] 		 = asset('upload/liveclasses') . "/" . $val['image'];
						$dATomorrowSkillClass['meeting_id']  = $val['meeting_id'];
						$dATomorrowSkillClass['pass_code'] 	 = $val['pass_code'];
						$dATomorrowSkillClass['master_class'] = $val['master_class'];
						$dATomorrowSkillClass['class_time']   = date('d/m/Y, h:i A', strtotime($val['class_time']));
						$dATomorrowSkillClass['isFree'] 	  = $val['isFree'];
						$check_interest = LiveclassNotify::where("user_id", $userId)->where("class_id", $val['id'])->first();
						$my_interest = !empty($check_interest) ? 1 : 0;
						$dATomorrowSkillClass['my_interest']  = $my_interest;
						$total_interest = LiveclassNotify::where("class_id", $val['id'])->count();
						$dATomorrowSkillClass['total_interest'] = $total_interest;
						array_push($dATomorrowSkillClassArr, $dATomorrowSkillClass);
					}
					if (!empty($dATomorrowSkillClassArr)) {
						$dATomorrowSkillData[$dATomorrowSki]['subject_title'] = $subjectdata->title;
						$dATomorrowSkillData[$dATomorrowSki++]['dATomorrow_skill_classes'] = $dATomorrowSkillClassArr;
					}
				}
				//end day after tomorrow classes
			}
			$this->helper->trackingApi($userId, 'live_class', 'Upcoming Live Classes', 0, '', $deviceType);
			$message = "All Upcoming Live Classes Data.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "classId" => $classId, "className" => $className, "downloadTimeTable" => $downloadTimeTable, "pin_message" => $pinMessageLiveClass, "todayAcademicLiveClasses" => $todayAcademicData, "todaySkillLiveClasses" => $todaySkillData, "tomorrowAcademicLiveClasses" => $tomorrowAcademicData, "tomorrowSkillLiveClasses" => $tomorrowSkillData, "dATAcademicLiveClasses" => $dATomorrowAcademicData, "dATSkillLiveClasses" => $dATomorrowSkillData)]);
		}
	}
	public function liveClassesPast(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "classId" => "", "className" => "", "pin_message" => "", "academicLiveClasses" => [], "skillLiveClasses" => [])]);
		}
		$user = User::where("id", $userId)->first();
		$class_id = !empty($user) ? $user->class_id : '';
		$classId = ($request->class_id) ? $request->class_id : $class_id;
		$userClass = StudentClass::where("id", $classId)->first();
		$className = !empty($userClass) ? $userClass->class_name : 'NA';
		$search = $request->search;
		$subject_id = $request->subject_id;
		//$search_date = ($request->search_date) ? date('Y-m-d',strtotime($request->search_date)) : '';
		$search_date = ($request->search_date) ? $request->search_date : '';
		$old_date = explode('/', $search_date);
		if(isset($old_date) && $search_date!=''){
			$search_date = $old_date[2].'-'.$old_date[1].'-'.$old_date[0];
		}
		$now = date('Y-m-d H:i:s');
		//$dt = Carbon::now();
		$userSubscription = $this->helper->userSubscription($userId);
		
		$pinMessage = PinMessage::whereRaw("find_in_set($classId,class_ids)")->where("msg_type", 206)->where("status", 1)->orderBy('id', 'DESC')->first();
		$pinMessageLiveClass = !empty($pinMessage) ? $pinMessage->message : '';
		$academicLiveClasses = LiveClass::with('user','subject_data')->where("class_type", 301)->whereRaw("find_in_set($classId,class_ids)")->where("status", 1)->where("deleted", 0);
		if (!empty($search)) {
			$subject_data = Subject::select("id")->where("title", 'like', "%" . $search . "%")->first();
			$srch_subject_id = !empty($subject_data) ? $subject_data->id : '';
			if (!empty($srch_subject_id)) {
				$academicLiveClasses = $academicLiveClasses->where('subject_id', $srch_subject_id);
			} else {
				$academicLiveClasses = $academicLiveClasses->where("title", 'like', "%" . $search . "%");
			}
		}
		if (!empty($subject_id)) {
			$academicLiveClasses = $academicLiveClasses->whereHas('subject_data', function($query) use ($subject_id) {
						if (isset($subject_id) && !empty($subject_id)) {
							$query->where('subject_id', $subject_id);
						}
					});
		}
		if (!empty($search_date)) {
			$academicLiveClasses = $academicLiveClasses->whereDate('class_time', $search_date);
		/*} else {
			$academicLiveClasses = $academicLiveClasses->where('end_time', '<', $now);*/
		}
		$academicLiveClasses = $academicLiveClasses->orderBy('class_time', 'ASC')->get();
		$academicClassData = array();
		if (!empty($academicLiveClasses)) {
			foreach ($academicLiveClasses as $key => $val) {
				$academicClassData[$key]['id']				= $val['id'];
				$academicClassData[$key]['class_date']		= date('d F, Y', strtotime($val['class_time']));
				$academicClassData[$key]['class_time']		= date('h:i A', strtotime($val['class_time'])).' to '.date('h:i A', strtotime($val['end_time']));
				$academicClassData[$key]['subject']			= ($val['subject_id']>0) ? $val->subject_data->title : 'NA';
				$academicClassData[$key]['title']			= $val['title'];
				if ($val['uploads3']==1){
					$video = config('constant.S3PUBLICURL').$val['video'];
				} else {
					$video = asset('upload/liveclasses') . "/" . $val['video'];
				}
				$academicClassData[$key]['original_video'] = isset($val['video']) ? $video : 'NA';
				if (!empty($val['video_1']) && $val['video_1']!='NA') {
					if ($val['uploads3v1']==1){
						$video_1 = config('constant.S3PUBLICURL').$val['video_1'];
					} else {
						if(file_exists( public_path().'/upload/liveclasses/'.$val['video_1'] )) {
							$video_1 = asset('upload/liveclasses') . "/" . $val['video_1'];
						} else {
							$video_1 = $academicClassData[$key]['original_video'];
						}
					}
				} else {
					$video_1 = $academicClassData[$key]['original_video'];
				}
				if (!empty($val['video_2']) && $val['video_2']!='NA') {
					if ($val['uploads3v2']==1){
						$video_2 = config('constant.S3PUBLICURL').$val['video_2'];
					} else {
						if(file_exists( public_path().'/upload/liveclasses/'.$val['video_2'] )) {
							$video_2 = asset('upload/liveclasses') . "/" . $val['video_2'];
						} else {
							$video_2 = $academicClassData[$key]['original_video'];
						}
					}
				} else {
					$video_2 = $academicClassData[$key]['original_video'];
				}
				if (!empty($val['video_3']) && $val['video_3']!='NA') {
					if ($val['uploads3v3']==1){
						$video_3 = config('constant.S3PUBLICURL').$val['video_3'];
					} else {
						if(file_exists( public_path().'/upload/liveclasses/'.$val['video_3'] )) {
							$video_3 = asset('upload/liveclasses') . "/" . $val['video_3'];
						} else {
							$video_3 = $academicClassData[$key]['original_video'];
						}
					}
				} else {
					$video_3 = $academicClassData[$key]['original_video'];
				}
				$academicClassData[$key]['low_video']		= $video_1;
				$academicClassData[$key]['medium_video']	= $video_2;
				$academicClassData[$key]['high_video']		= $video_3;
				$academicClassData[$key]['notes']			= !empty($val['pdf']) ? asset('upload/liveclasses') . "/" . $val['pdf'] : '';
				$academicClassData[$key]['isFree']			= $val['isFree'];
			}
		}
		$skillLiveClasses = LiveClass::with('user','subject_data')->where("class_type", 302)->whereRaw("find_in_set($classId,class_ids)")->where("status", 1)->where("deleted", 0);
		if (!empty($search)) {
			$subject_data = Subject::select("id")->where("title", 'like', "%" . $search . "%")->first();
			$srch_subject_id = !empty($subject_data) ? $subject_data->id : '';
			if (!empty($srch_subject_id)) {
				$skillLiveClasses = $skillLiveClasses->where('subject_id', $srch_subject_id);
			} else {
				$skillLiveClasses = $skillLiveClasses->where("title", 'like', "%" . $search . "%");
			}
		}
		if (!empty($subject_id)) {
			$skillLiveClasses = $skillLiveClasses->whereHas('subject_data', function($query) use ($subject_id) {
						if (isset($subject_id) && !empty($subject_id)) {
							$query->where('subject_id', $subject_id);
						}
					});
		}
		if (!empty($search_date)) {
			$skillLiveClasses = $skillLiveClasses->whereDate('class_time', $search_date);
		/*} else {
			$skillLiveClasses = $skillLiveClasses->where('end_time', '<', $now);*/
		}
		$skillLiveClasses = $skillLiveClasses->orderBy('class_time', 'ASC')->get();
		$skillClassData = array();
		if (!empty($skillLiveClasses)) {
			foreach ($skillLiveClasses as $key => $val) {
				$skillClassData[$key]['id']				= $val['id'];
				$skillClassData[$key]['class_date']		= date('d F, Y', strtotime($val['class_time']));
				$skillClassData[$key]['class_time']		= date('h:i A', strtotime($val['class_time'])).' to '.date('h:i A', strtotime($val['end_time']));
				$skillClassData[$key]['subject']		= ($val['subject_id']>0) ? $val->subject_data->title : 'NA';
				$skillClassData[$key]['title']			= $val['title'];
				if ($val['uploads3']==1){
					$video = config('constant.S3PUBLICURL').$val['video'];
				} else {
					$video = asset('upload/liveclasses') . "/" . $val['video'];
				}
				$skillClassData[$key]['original_video'] = isset($val['video']) ? $video : 'NA';
				if (!empty($val['video_1']) && $val['video_1']!='NA') {
					if ($val['uploads3v1']==1){
						$video_1 = config('constant.S3PUBLICURL').$val['video_1'];
					} else {
						if(file_exists( public_path().'/upload/liveclasses/'.$val['video_1'] )) {
							$video_1 = asset('upload/liveclasses') . "/" . $val['video_1'];
						} else {
							$video_1 = $skillClassData[$key]['original_video'];
						}
					}
				} else {
					$video_1 = $skillClassData[$key]['original_video'];
				}
				if (!empty($val['video_2']) && $val['video_2']!='NA') {
					if ($val['uploads3v2']==1){
						$video_2 = config('constant.S3PUBLICURL').$val['video_2'];
					} else {
						if(file_exists( public_path().'/upload/liveclasses/'.$val['video_2'] )) {
							$video_2 = asset('upload/liveclasses') . "/" . $val['video_2'];
						} else {
							$video_2 = $skillClassData[$key]['original_video'];
						}
					}
				} else {
					$video_2 = $skillClassData[$key]['original_video'];
				}
				if (!empty($val['video_3']) && $val['video_3']!='NA') {
					if ($val['uploads3v3']==1){
						$video_3 = config('constant.S3PUBLICURL').$val['video_3'];
					} else {
						if(file_exists( public_path().'/upload/liveclasses/'.$val['video_3'] )) {
							$video_3 = asset('upload/liveclasses') . "/" . $val['video_3'];
						} else {
							$video_3 = $skillClassData[$key]['original_video'];
						}
					}
				} else {
					$video_3 = $skillClassData[$key]['original_video'];
				}
				$skillClassData[$key]['low_video']		= $video_1;
				$skillClassData[$key]['medium_video']	= $video_2;
				$skillClassData[$key]['high_video']		= $video_3;
				$skillClassData[$key]['notes']			= !empty($val['pdf']) ? asset('upload/liveclasses') . "/" . $val['pdf'] : '';
				$skillClassData[$key]['isFree']			= $val['isFree'];
			}
		}
		$this->helper->trackingApi($userId, 'live_class', 'Past Live Classes', 0, '', $deviceType);
		$message = "All Past Live Classes Data.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "classId" => $classId, "className" => $className, "pin_message" => $pinMessageLiveClass, "academicLiveClasses" => $academicClassData, "skillLiveClasses" => $skillClassData)]);
	}
	public function liveClassTimeTable(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "classId" => "", "className" => "", "timeTableData" => [], "skillLiveClasses" => [])]);
		}
		$user = User::where("id", $userId)->first();
		$class_id = !empty($user) ? $user->class_id : '';
		$classId = ($request->class_id) ? $request->class_id : $class_id;
		$search = $request->search;
		$subject_id = $request->subject_id;
		//$search_date = ($request->search_date) ? date('Y-m-d',strtotime($request->search_date)) : '';
		$search_date = ($request->search_date) ? $request->search_date : '';
		$old_date = explode('/', $search_date);
		if(isset($old_date) && $search_date!=''){
			$search_date = $old_date[2].'-'.$old_date[1].'-'.$old_date[0];
		}
		$from = date('Y-m-d').' 00:00:00';
		$to = date('Y-m-d').' 23:59:59'; //date('Y-m-d H:i:s');
		//$dt = Carbon::now();
		$userSubscription = $this->helper->userSubscription($userId);
		
		$userClass = StudentClass::where("id", $classId)->first();
		$className = !empty($userClass) ? $userClass->class_name : '';
		//$LiveClassTimeTables = LiveClassTimeTable::with('class_data','subject_data')->where("class_id", $classId)->whereBetween('start_time', [$from, $to])->where("status", 1)->orderBy('start_time', 'ASC')->get();
		$LiveClassTimeTables = LiveClassTimeTable::with('class_data','subject_data')->where("class_id", $classId)->where("status", 1);
		if (!empty($search)) {
			$subject_data = Subject::select("id")->where("title", 'like', "%" . $search . "%")->first();
			$srch_subject_id = !empty($subject_data) ? $subject_data->id : '';
			if (!empty($srch_subject_id)) {
				$LiveClassTimeTables = $LiveClassTimeTables->where('subject_id', $srch_subject_id);
			} else {
				$LiveClassTimeTables = $LiveClassTimeTables->where("title", 'like', "%" . $search . "%");
			}
		}
		if (!empty($subject_id)) {
			$LiveClassTimeTables = $LiveClassTimeTables->whereHas('subject_data', function($query) use ($subject_id) {
						if (isset($subject_id) && !empty($subject_id)) {
							$query->where('subject_id', $subject_id);
						}
					});
		}
		if (!empty($search_date)) {
			$LiveClassTimeTables = $LiveClassTimeTables->whereDate('start_time', $search_date);
		/*} else {
			$LiveClassTimeTables = $LiveClassTimeTables->where('start_time', '>', $from);*/
		}
		$LiveClassTimeTables = $LiveClassTimeTables->orderBy('start_time', 'ASC')->get();
		$timeTableData = array();
		if (!empty($LiveClassTimeTables)) {
			foreach ($LiveClassTimeTables as $key => $val) {
				$timeTableData[$key]['id']		= $val['id'];
				$timeTableData[$key]['date']	= date('d F, Y', strtotime($val['start_time']));
				$timeTableData[$key]['time']	= date('h:i A', strtotime($val['start_time'])).' to '.date('h:i A', strtotime($val['end_time']));
				$timeTableData[$key]['title'] 	= $val['title'];
				$timeTableData[$key]['subject']	= ($val['subject_id']>0) ? $val->subject_data->title : 'NA';
			}
			//$this->helper->trackingApi($userId, 'live_class', 'Time Table Live Class', 0, '', $deviceType);
			$message = "Live Class Time Table Data.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "classId" => $classId, "className" => $className, "timeTableData" => $timeTableData)]);
		}
	}
	public function notifyClass(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("notifyId" => "")]);
		}
		$classId = $request->classId;
		if (!empty($userId) && !empty($classId)) {
			$user = User::where("id", $userId)->first();
			$check_interest = LiveclassNotify::where("user_id", $userId)->where("class_id", $classId)->first();
			if (empty($check_interest)) {
				$data = array(
					'user_id'  		=> $userId,
					'class_id'  	=> $classId,
					'status'  		=> 0,
					'created_at'    => date('Y-m-d H:i:s'),
				);
				$insertId = LiveclassNotify::insertGetId($data);
			} else {
				$data = array(
					'status'  		=> 0,
					'updated_at'    => date('Y-m-d H:i:s'),
				);
				$insertId = LiveclassNotify::where("id", $check_interest->id)->update($data);
			}
			$liveClass = LiveClass::with('subject_data')->where("id", $classId)->where("status", 1)->where("deleted", 0)->first();
			if ($liveClass) {
				$subject_data = ($liveClass->subject_id>0) ? $liveClass->title.$liveClass->subject_data->title : $liveClass->title;
			//ak	$this->helper->smsWithTemplate($user->phone, 'LiveClassSMS', $user->name, $subject_data, $liveClass->class_time);
			}
			$message = "Your Class Added Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("notifyId" => $insertId)]);
		} else {
			$message = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("notifyId" => "")]);
		}
	}

	public function getCourses(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "classId" => "", "className" => "", "askFreeQuestions" => "", "askedQuestionCount" => "", "courses" => [])]);
		}
		$user = User::where("id", $userId)->first();
		//$board_id = !empty($user) ? $user->board_id : '';
		$class_id = !empty($user) ? $user->class_id : '';
		//$boardId = ($request->board_id) ? $request->board_id : $board_id;
		$classId = ($request->class_id) ? $request->class_id : $class_id;
		$userClass = StudentClass::where("id", $classId)->first();
		$className = !empty($userClass) ? $userClass->class_name : 'NA';
		$userSubscription = $this->helper->userSubscription($userId);
		$askFreeQuestions = 3;
		$askedQuestionCount = QuestionAsk::where('user_id', $userId)->count();
		$courses  = Courses::whereRaw("find_in_set($classId,class_ids)")->where("status", 1)->where('deleted', 0)->orderBy('sort_id', 'ASC')->get();
		$coursedata = array();
		foreach ($courses as $key => $value) {
			$coursedata[$key]['id']  = $value['id'];
			$coursedata[$key]['name']  = $value['name'];
		}
		$message = "All Courses List.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "classId" => $classId, "className" => $className, "askFreeQuestions" => $askFreeQuestions, "askedQuestionCount" => $askedQuestionCount, "courses" => $coursedata)]);
	}
	public function getLessionsBycourse(Request $request)
	{
		$courseId = $request->courseId;
		$lessions = Lession::where("courseId", $courseId)->where("status", 1)->where('deleted', 0)->orderBy('sort_id', 'ASC')->get();
		$lessiondata = array();
		if (!empty($lessions)) {
			foreach ($lessions as $key => $value) {
				$lessiondata[$key]['id']  = $value['id'];
				$lessiondata[$key]['name']  = $value['name'];
			}
			$message = "Lessions List by Course.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("lessions" => $lessiondata)]);
		} else {
			$message = "Lessions List not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("lessions" => "")]);
		}
	}
	public function getTopicsByLession(Request $request)
	{
		$lessionId = $request->lessionId;
		$topics = Chapter::where("lessionId", $lessionId)->where("status", 1)->where('deleted', 0)->orderBy('sort_id', 'ASC')->get();
		$topicdata = array();
		if (!empty($topics)) {
			foreach ($topics as $key => $value) {
				$topicdata[$key]['id']  = $value['id'];
				$topicdata[$key]['name']  = $value['name'];
			}
			$message = "Topics List by Lession.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("topics" => $topicdata)]);
		} else {
			$message = "Topics List not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("topics" => "")]);
		}
	}
	public function askQuestion(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("ques_id" => "")]);
		}
		$courseId 	= $request->courseId;
		$lessionId  = $request->lessionId;
		$topicId 	= $request->topicId;
		$question 	= $request->question;
		$msg = '';
		if (!empty($userId)  && !empty($courseId) && !empty($lessionId) || !empty($question) || isset($_FILES['image']['name']) ) {
			$questioncount = QuestionAsk::where('user_id', $userId)->count();
			if ($questioncount < 3) {
				$imagesData = [];
				$files = $request->file('image');
				if ($request->hasFile('image')) {
					foreach ($files as $file) {
						if($file){
							$destinationPath = public_path().'/upload/questionask/';
							$originalFile = $file->getClientOriginalName();
							$newImage = rand(111,999).strtotime(date('Y-m-d-H:isa')).$originalFile;
							$file->move($destinationPath, $newImage);
							//$imagesData[] = $newImage;
							$url = 'upload/questionask/' . $newImage;
							$thumb_img = Image::make($url)->resize(200, 200);
							$thumb_img->save('upload/questionask/thumb/'.$newImage,80);
							$imagesData[] =  $thumb_img->basename;
						}
					}
				}
				$data = array(
					'user_id' 	 => $userId,
					'course_id'  => $courseId,
					'lession_id' => $lessionId,
					'topic_id' 	 => $topicId,
					'question'   => $question,
					'image'      => implode(",", $imagesData),
					'status'     => 0,
					'created_at' => date('Y-m-d H:i:s'),
				);
				$insertId = QuestionAsk::insertGetId($data);
				$message = 'Your Question Submitted Successfully, Team will approve it.';
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array('ques_id' => $insertId)]);
			} else {
				//Check subcription by user later work will be do.
				$subcriptionCheck = User::where('id', $userId)->where('status', 1)->first();
				if (!empty($subcriptionCheck)) {
					$imagesData = [];
					$files = $request->file('image');
					if ($request->hasFile('image')) {
						foreach ($files as $file) {
							if($file){
								$destinationPath = public_path().'/upload/questionask/';
								$originalFile = $file->getClientOriginalName();
								$newImage = rand(111,999).strtotime(date('Y-m-d-H:isa')).$originalFile;
								$file->move($destinationPath, $newImage);
								//$imagesData[] = $newImage;
								$url = 'upload/questionask/' . $newImage;
								$thumb_img = Image::make($url)->resize(200, 200);
								$thumb_img->save('upload/questionask/thumb/'.$newImage,80);
								$imagesData[] =  $thumb_img->basename;
							}
						}
					}
					$data = array(
						'user_id' 	 => $userId,
						'course_id'  => $courseId,
						'lession_id' => $lessionId,
						'topic_id' 	 => $topicId,
						'question'   => $question,
						'image'      => implode(",", $imagesData),
						'status'     => 0,
						'created_at' => date('Y-m-d H:i:s'),
					);
					$insertId = QuestionAsk::insertGetId($data);
					$message = 'Your Question Submitted Successfully, Team will approve it.';
					return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("ques_id" => $insertId)]);
				} else {
					$message = "Please subscribed a package first to ask more questions!";
					return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("ques_id" => "")]);
				}
			}
		} else {
			$message = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("ques_id" => "")]);
		}
	}
	public function latestQuestion(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("latestQuestions" => [])]);
		}
		$user = User::where("id", $userId)->first();
		//$board_id = !empty($user) ? $user->board_id : '';
		$class_id = !empty($user) ? $user->class_id : '';
		//$boardId = ($request->board_id) ? $request->board_id : $board_id;
		$classId = ($request->class_id) ? $request->class_id : $class_id;
		$userClass = StudentClass::where("id", $classId)->first();
		$className = !empty($userClass) ? $userClass->class_name : 'NA';
		$search = $request->search;
		$courses  = Courses::whereRaw("find_in_set($classId,class_ids)")->where("status", 1)->where('deleted', 0)->orderBy('sort_id', 'ASC')->get();
		$course_ids_arr = array();
		foreach ($courses as $key => $value) {
			$course_ids_arr[]  = $value['id'];
		}
		//dd($course_ids_arr);
		$dt = Carbon::now();
		$from = $dt->subMonth();
		$to = date('Y-m-d H:i:s');
		/*$from = date('Y-m-d').' 00:00:00';
		$to = date('Y-m-d').' 23:59:59';*/
		//$latestQuesAsks = QuestionAsk::where("status", 1)->whereBetween("created_at", [$from, $to]);
		$latestQuesAsks = QuestionAsk::with('user','course','lession','topic')->whereIn("course_id",$course_ids_arr)->where("status", 1)->where("deleted", 0);
		if (!empty($latestQuesAsks)) {
			$latestQuesAsks = $latestQuesAsks->where("question", 'like', "%" . $search . "%");
		}
		$latestQuesAsks = $latestQuesAsks->orderBy("id", "DESC")->get();
		$latestQuestion = array();
		if (!empty($latestQuesAsks)) {
			foreach ($latestQuesAsks as $key => $val) {
				$courseLessionTopicName = '';
				if($val['topic_id'] > 0){
					$courseLessionTopicName = $val->course->name.' / '.$val->lession->name.' / '.$val->topic->name;
				}elseif($val['lession_id'] > 0){
					$courseLessionTopicName = $val->course->name.' / '.$val->lession->name;
				}else{
					if($val['course_id'] > 0){
						$courseLessionTopicName = $val->course->name;
					}
				}
				$images = $images_arr = array();
				if (!empty($val['image'])) {
					$images = explode(',', $val['image']);
				}
				foreach ($images as $queImg) {
					$imgPath['image'] = asset('upload/questionask') . "/" . $queImg;
					array_push($images_arr, $imgPath);
				}

				$latestQuestion[$key]['id']            = $val['id'];
				$latestQuestion[$key]['added_by']      = ($val['user_id']>0) ? $val->user->name : '';
				$latestQuestion[$key]['profile_image'] = !empty($val->user->image) ? asset('upload/profile') . "/" . $val->user->image : '';
				$latestQuestion[$key]['course_name']   = $courseLessionTopicName;
				$latestQuestion[$key]['created_at']    = $val['created_at']->diffForHumans(); //date('h:i A', strtotime($val['created_at']));
				$latestQuestion[$key]['question']      = $val['question'];
				$latestQuestion[$key]['images']        = $images_arr; //!empty($val['image']) ? asset('upload/questionask') . "/" . $val['image'] : '';
				$latestQuestion[$key]['total_answers'] = QuestionAnswer::where("ques_id", $val['id'])->count();
				$latestQuestion[$key]['share_url'] = route('questionAnswerView',$val['id']);
				if ($userId == $val['user_id']) {
					$latestQuestion[$key]['my_question']  = 1;
				} else {
					$latestQuestion[$key]['my_question']  = 0;
				}
			}
			$this->helper->trackingApi($userId, 'q&a', 'Latest Questions', 0, '', $deviceType);
			$message = "All Latest Questions Data.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("latestQuestions" => $latestQuestion)]);
		} else {
			$message = "Latest Question Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("latestQuestions" => "")]);
		}
	}
	public function myQuestion(Request $request)
	{
		//echo $token = $request->bearerToken(); die;
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("myQuestions" => [])]);
		}
		/*$dt = Carbon::now();
		$from = $dt->subMonth();
		$to = date('Y-m-d H:i:s');*/
		$myQuestions = QuestionAsk::where("user_id", $userId)->where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->get();
		$myQuestion = array();
		if (!empty($myQuestions)) {
			foreach ($myQuestions as $key => $val) {
				$courseLessionTopicName = '';
				if($val['topic_id'] > 0){
					$courseLessionTopicName = $val->course->name.' / '.$val->lession->name.' / '.$val->topic->name;
				}elseif($val['lession_id'] > 0){
					$courseLessionTopicName = $val->course->name.' / '.$val->lession->name;
				}else{
					if($val['course_id'] > 0){
						$courseLessionTopicName = $val->course->name;
					}
				}
				$images = $images_arr = array();
				if (!empty($val['image'])) {
					$images = explode(',', $val['image']);
				}
				foreach ($images as $queImg) {
					$imgPath['image'] = asset('upload/questionask') . "/" . $queImg;
					array_push($images_arr, $imgPath);
				}

				$myQuestion[$key]['id']            = $val['id'];
				$myQuestion[$key]['added_by']      = ($val['user_id']>0) ? $val->user->name : '';
				$myQuestion[$key]['profile_image'] = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
				$myQuestion[$key]['course_name']   = $courseLessionTopicName;
				$myQuestion[$key]['created_at']    = $val['created_at']->diffForHumans();
				$myQuestion[$key]['question']      = $val['question'];
				$myQuestion[$key]['images']        = $images_arr; //!empty($val['image']) ? asset('upload/questionask') . "/" . $val['image'] : '';
				$myQuestion[$key]['total_answers'] = QuestionAnswer::where("ques_id", $val['id'])->count();
				$myQuestion[$key]['share_url'] = route('questionAnswerView',$val['id']);
			}
			$this->helper->trackingApi($userId, 'q&a', 'My Questions', 0, '', $deviceType);
			$message = "All My Questions Data.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("myQuestions" => $myQuestion)]);
		} else {
			$message = "My Question Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("myQuestions" => "")]);
		}
	}
	public function answerAQuestion(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("answer_id" => "")]);
		}
		$quesId 	= $request->quesId;
		$answer 	= $request->answer;
		$msg = '';
		if (!empty($userId)  && !empty($quesId)) {
			$questioncheck = QuestionAsk::where('id', $quesId)->first();
			if (!empty($questioncheck)) {
				$imagesData = [];
				$files = $request->file('image');
				if ($request->hasFile('image')) {
					foreach ($files as $file) {
						if($file){
							$destinationPath = public_path().'/upload/questionask/';
							$originalFile = $file->getClientOriginalName();
							$newImage = rand(111,999).strtotime(date('Y-m-d-H:isa')).$originalFile;
							$file->move($destinationPath, $newImage);
							//$imagesData[] = $newImage;
							$url = 'upload/questionask/' . $newImage;
							$thumb_img = Image::make($url)->resize(200, 200);
							$thumb_img->save('upload/questionask/thumb/'.$newImage,80);
							$imagesData[] =  $thumb_img->basename;
						}
					}
				}
				$data = array(
					'user_id' 	 => $userId,
					'ques_id'    => $quesId,
					'answer'     => $answer,
					//'image'      => $imagess,
					'image'      => implode(",", $imagesData),
					'status'     => 1,
					'created_at' => date('Y-m-d H:i:s'),
				);
				$insertId = QuestionAnswer::insertGetId($data);
				$user = User::where("id", $userId)->first();
				$ques = QuestionAsk::where("id", $quesId)->first();
				$msg = $user->name.', Answered a question '.$ques->question.' in Q&A, check it now.';
				$click_action = 'QA';
				$module_id = $quesId;
				$this->helper->addNotification($ques->user_id,$msg, $click_action, $module_id);
				$message = 'Your Answer Submitted Successfully.';
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("answer_id" => $insertId)]);
			} else {
				$message = "Question not Found!";
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("answer_id" => "")]);
			}
		} else {
			$message = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("answer_id" => "")]);
		}
	}
	public function viewAnswers(Request $request)
	{
		$userId   = ($request->userId) ? $request->userId : 0;
		$deviceType = $request->deviceType;
		$quesId   = $request->quesId;
		$question = QuestionAsk::where("id", $quesId)->where("status", 1)->first();
		$questiondata = array();
		if (!empty($question)) {
			$user = User::where("id", $question->user_id)->first();
			$course = Courses::where("id", $question->course_id)->first();
			$lession = Lession::where("id", $question->lession_id)->first();
			$topic = Chapter::where("id", $question->topic_id)->first();
			$courseLessionTopicName = '';
			if($question->topic_id > 0){
				$courseLessionTopicName = $course->name.' / '.$lession->name.' / '.$topic->name;
			}elseif($question->lession_id > 0){
				$courseLessionTopicName = $course->name.' / '.$lession->name;
			}else{
				if($question->course_id > 0){
					$courseLessionTopicName = $course->name;
				}
			}
			$images = $images_arr = array();
			if (!empty($question->image)) {
				$images = explode(',', $question->image);
			}
			foreach ($images as $queImg) {
				$imgPath['image'] = asset('upload/questionask') . "/" . $queImg;
				array_push($images_arr, $imgPath);
			}

			$questiondata['id']            = $question->id;
			$questiondata['added_by']      = isset($user->name) ? $user->name : '';
			$questiondata['profile_image'] = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
			$questiondata['course_name']   = $courseLessionTopicName;
			$questiondata['created_at']    = $question->created_at->diffForHumans();
			$questiondata['question']      = $question->question;
			$questiondata['question_images'] = $images_arr; //!empty($question->image) ? asset('upload/questionask') . "/" . $question->image : '';
			$questiondata['total_answers'] = QuestionAnswer::where("ques_id", $question->id)->count();
			$questiondata['share_url'] = route('questionAnswerView',$question->id);
			$answers = QuestionAnswer::where("ques_id", $quesId)->where("expert", "!=", 0)->where("status", 1)->orderBy("expert", "ASC")->orderBy("ans_like", "DESC")->get();
			$answerdata = array();
			$expertanswerdata = array();
			if (!empty($answers)) {
				foreach ($answers as $key => $val) {
					$user = User::where("id", $val['user_id'])->first();
					$images = $images_arr = array();
					if (!empty($val['image'])) {
						$images = explode(',', $val['image']);
					}
					foreach ($images as $queImg) {
						$imgPath['image'] = asset('upload/questionask') . "/" . $queImg;
						array_push($images_arr, $imgPath);
					}

					$expertans['id']            = $val['id'];
					$expertans['added_by']      = isset($user->name) ? $user->name : '';
					$expertans['profile_image'] = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
					$expertans['created_at']    = $val['created_at']->diffForHumans();
					$expertans['answer']        = $val['answer'];
					$expertans['images']        = $images_arr; //!empty($val['image']) ? asset('upload/questionask') . "/" . $val['image'] : '';
					$expertans['ans_like']      = $val['ans_like'];
					$expertans['ans_unlike']    = $val['ans_unlike'];
					$expertans['expert']		= $val['expert'];
					array_push($expertanswerdata, $expertans);
				}
			}
			$useranswers = QuestionAnswer::where("ques_id", $quesId)->where("expert", 0)->where("status", 1)->orderBy("ans_like", "DESC")->get();
			$useranswerdata = array();
			if (!empty($useranswers)) {
				foreach ($useranswers as $key => $val) {
					$user = User::where("id", $val['user_id'])->first();
					$images = $images_arr = array();
					if (!empty($val['image'])) {
						$images = explode(',', $val['image']);
					}
					foreach ($images as $queImg) {
						$imgPath['image'] = asset('upload/questionask') . "/" . $queImg;
						array_push($images_arr, $imgPath);
					}
					
					$userans['id']            = $val['id'];
					$userans['added_by']      = isset($user->name) ? $user->name : '';
					$userans['profile_image'] = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
					$userans['created_at']    = $val['created_at']->diffForHumans();
					$userans['answer']        = $val['answer'];
					$userans['images']        = $images_arr; //!empty($val['image']) ? asset('upload/questionask') . "/" . $val['image'] : '';
					$userans['ans_like']      = $val['ans_like'];
					$userans['ans_unlike']    = $val['ans_unlike'];
					$userans['expert']		  = $val['expert'];
					array_push($useranswerdata, $userans);
				}
			}
			$answerdata = array_merge($expertanswerdata, $useranswerdata);
			if ($userId>0) {
				$this->helper->trackingApi($userId, 'q&a', 'view Answers', $quesId, '', $deviceType);
			}
			$message = "Get All Answers of a Question Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("question" => $questiondata, "answers" => $answerdata)]);
		} else {
			$message = "Question Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("question" => "", "answers" => "")]);
		}
	}
	public function likeAnswer(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("answer_id" => "")]);
		}
		$answerId = $request->answerId;
		$msg = '';
		if (!empty($userId) && !empty($answerId)) {
			$getAnswer = QuestionAnswer::where('id', $answerId)->first();
			if (!empty($getAnswer)) {
				$getAnswerLike = QuestionAnswerLike::where('user_id', $userId)->where('answer_id', $answerId)->first();
				$userLiked = !empty($getAnswerLike) ? $getAnswerLike->ans_like : 0;
				$userDisliked = !empty($getAnswerLike) ? $getAnswerLike->ans_unlike : 0;
				if ($userLiked==0) {
					$preLike = $getAnswer->ans_like;
					$preDislike = $getAnswer->ans_unlike;
					if ($userDisliked==1) {
						$preDislike = $preDislike - 1;
					}
					$like = $preLike + 1;
					$data = array(
						'ans_like' 	 => $like,
						'ans_unlike' => $preDislike,
						'updated_at' => date('Y-m-d H:i:s'),
					);
					$update = QuestionAnswer::where("id", $answerId)->update($data);
					if (!empty($getAnswerLike)) {
						$data1 = array(
							'ans_like' 	 => 1,
							'ans_unlike' => 0,
							'updated_at' => date('Y-m-d H:i:s'),
						);
						$update = QuestionAnswerLike::where("id", $getAnswerLike->id)->update($data1);
					} else {
						$data1 = array(
							'user_id' 	 => $userId,
							'answer_id'  => $answerId,
							'ans_like' 	 => 1,
							'ans_unlike' => 0,
							'created_at' => date('Y-m-d H:i:s'),
						);
						$insert = QuestionAnswerLike::insertGetId($data1);
					}
					$msg = 'Your Like Submitted Successfully.';
					return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => array("answer_id" => $answerId)]);
				} else {
					$msg = 'You have already liked this answer.';
					return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => array("answer_id" => $answerId)]);
				}
			} else {
				$msg = "Answer not Found!";
				return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("answer_id" => "")]);
			}
		} else {
			$msg = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("answer_id" => "")]);
		}
	}
	public function unlikeAnswer(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("answer_id" => "")]);
		}
		$answerId = $request->answerId;
		$msg = '';
		if (!empty($userId) && !empty($answerId)) {
			$getAnswer = QuestionAnswer::where('id', $answerId)->first();
			if (!empty($getAnswer)) {
				$getAnswerLike = QuestionAnswerLike::where('user_id', $userId)->where('answer_id', $answerId)->first();
				$userLiked = !empty($getAnswerLike) ? $getAnswerLike->ans_like : 0;
				$userDisliked = !empty($getAnswerLike) ? $getAnswerLike->ans_unlike : 0;
				if ($userDisliked==0) {
					$preLike = $getAnswer->ans_like;
					if ($userLiked==1) {
						$preLike = $preLike - 1;
					}
					$preUnLike = $getAnswer->ans_unlike;
					$unlike = $preUnLike + 1;
					$data = array(
						'ans_like' 	 => $preLike,
						'ans_unlike' => $unlike,
						'updated_at' => date('Y-m-d H:i:s'),
					);
					$update = QuestionAnswer::where("id", $answerId)->update($data);
					if (!empty($getAnswerLike)) {
						$data1 = array(
							'ans_like' 	 => 0,
							'ans_unlike' => 1,
							'updated_at' => date('Y-m-d H:i:s'),
						);
						$update = QuestionAnswerLike::where("id", $getAnswerLike->id)->update($data1);
					} else {
						$data1 = array(
							'user_id' 	 => $userId,
							'answer_id'  => $answerId,
							'ans_like' 	 => 0,
							'ans_unlike' => 1,
							'created_at' => date('Y-m-d H:i:s'),
						);
						$insert = QuestionAnswerLike::insertGetId($data1);
					}
					$msg = 'Your Unlike Submitted Successfully.';
					return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => array("answer_id" => $answerId)]);
				} else {
					$msg = 'You have already disliked this answer.';
					return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => array("answer_id" => $answerId)]);
				}
			} else {
				$msg = "Answer not Found!";
				return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("answer_id" => "")]);
			}
		} else {
			$msg = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("answer_id" => "")]);
		}
	}

	public function getAcademicChatGroupList(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "classId" => "", "className" => "", "pin_message" => "", "popular_groups" => [], "other_groups" => [])]);
		}
		$user = User::where("id", $userId)->first();
		//$board_id = !empty($user) ? $user->board_id : '';
		$class_id = !empty($user) ? $user->class_id : '';
		//$boardId = ($request->board_id) ? $request->board_id : $board_id;
		$classId = ($request->class_id) ? $request->class_id : $class_id;
		$userClass = StudentClass::where("id", $classId)->first();
		$className = !empty($userClass) ? $userClass->class_name : 'NA';
		$search   = $request->search;
		$userSubscription = $this->helper->userSubscription($userId);
		
		$pinMessage = PinMessage::whereRaw("find_in_set($classId,class_ids)")->where("msg_type", 202)->where("status", 1)->orderBy('id', 'DESC')->first();
		$pinMessageChatGroup = !empty($pinMessage) ? $pinMessage->message : '';
		$populargroups = ChatGroup::where('class_type', 301)->whereRaw("find_in_set($classId,class_ids)")->whereRaw("find_in_set(402,type)")->where("status", 1);
		if (!empty($search)) {
			$populargroups = $populargroups->where("title", "like", "%" . $search . "%");
		}
		$populargroups = $populargroups->orderBy('sort_id', 'ASC')->get();
		$othergroups = ChatGroup::where('class_type', 301)->whereRaw("find_in_set($classId,class_ids)")->whereRaw("find_in_set(401,type)")->where("status", 1);
		if (!empty($search)) {
			$othergroups = $othergroups->where("title", "like", "%" . $search . "%");
		}
		$othergroups = $othergroups->orderBy('sort_id', 'ASC')->get();
		$populardata = array();
		$otherdata = array();
		if (!empty($populargroups)) {
			foreach ($populargroups as $key => $val) {
				$pinMessageIn = PinMessage::whereRaw("find_in_set($classId,class_ids)")->where("msg_type", 203)->where("group_id", $val['id'])->where("status", 1)->orderBy('id', 'DESC')->first();
				$pinMessageChatInbox = !empty($pinMessageIn) ? $pinMessageIn->message : '';
				$userJoinedGroup = ChatGroupUser::where("group_id", $val['id'])->where("user_id", $userId)->first();
				$populardata[$key]['id'] 		  = $val['id'];
				$populardata[$key]['title'] 	  = $val['title'];
				$populardata[$key]['image'] 	  = !empty($val['image']) ? asset('upload/chat_images/' . $val['image']) : '';
				$populardata[$key]['pin_msg'] 	  = $pinMessageChatInbox;
				$populardata[$key]['description'] = $val['description'];
				$populardata[$key]['joined'] 	  = !empty($userJoinedGroup) ? 1 : 0;
				$populardata[$key]['mute_status'] = !empty($userJoinedGroup) ? $userJoinedGroup->mute_status : 0;
			}
		}
		if (!empty($othergroups)) {
			foreach ($othergroups as $key => $val) {
				$pinMessageIn = PinMessage::whereRaw("find_in_set($classId,class_ids)")->where("msg_type", 203)->where("group_id", $val['id'])->where("status", 1)->orderBy('id', 'DESC')->first();
				$pinMessageChatInbox = !empty($pinMessageIn) ? $pinMessageIn->message : '';
				$userJoinedGroup = ChatGroupUser::where("group_id", $val['id'])->where("user_id", $userId)->first();
				$otherdata[$key]['id'] 		    = $val['id'];
				$otherdata[$key]['title'] 	    = $val['title'];
				$otherdata[$key]['image'] 	    = !empty($val['image']) ? asset('upload/chat_images/' . $val['image']) : '';
				$otherdata[$key]['pin_msg'] 	= $pinMessageChatInbox;
				$otherdata[$key]['description'] = $val['description'];
				$otherdata[$key]['joined'] 	    = !empty($userJoinedGroup) ? 1 : 0;
				$otherdata[$key]['mute_status'] = !empty($userJoinedGroup) ? $userJoinedGroup->mute_status : 0;
			}
		}
		//$this->helper->trackingApi($userId, 'chat', '', 0, '', $deviceType);
		$message = "Get Academic Chat Groups List.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "classId" => $classId, "className" => $className, "pin_message" => $pinMessageChatGroup, "popular_groups" => $populardata, "other_groups" => $otherdata)]);
	}
	public function getSkillChatGroupList(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "classId" => "", "className" => "", "pin_message" => "", "popular_groups" => [], "other_groups" => [])]);
		}
		$user = User::where("id", $userId)->first();
		//$board_id = !empty($user) ? $user->board_id : '';
		$class_id = !empty($user) ? $user->class_id : '';
		//$boardId = ($request->board_id) ? $request->board_id : $board_id;
		$classId = ($request->class_id) ? $request->class_id : $class_id;
		$userClass = StudentClass::where("id", $classId)->first();
		$className = !empty($userClass) ? $userClass->class_name : 'NA';
		$search   = $request->search;
		$userSubscription = $this->helper->userSubscription($userId);
		
		$pinMessage = PinMessage::whereRaw("find_in_set($classId,class_ids)")->where("msg_type", 202)->where("status", 1)->orderBy('id', 'DESC')->first();
		$pinMessageChatGroup = !empty($pinMessage) ? $pinMessage->message : '';
		$populargroups = ChatGroup::where('class_type', 302)->whereRaw("find_in_set($classId,class_ids)")->whereRaw("find_in_set(402,type)")->where("status", 1);
		if (!empty($search)) {
			$populargroups = $populargroups->where("title", "like", "%" . $search . "%");
		}
		$populargroups = $populargroups->orderBy('sort_id', 'ASC')->get();
		$othergroups = ChatGroup::where('class_type', 302)->whereRaw("find_in_set($classId,class_ids)")->whereRaw("find_in_set(401,type)")->where("status", 1);
		if (!empty($search)) {
			$othergroups = $othergroups->where("title", "like", "%" . $search . "%");
		}
		$othergroups = $othergroups->orderBy('sort_id', 'ASC')->get();
		$populardata = array();
		$otherdata = array();
		if (!empty($populargroups)) {
			foreach ($populargroups as $key => $val) {
				$pinMessageIn = PinMessage::whereRaw("find_in_set($classId,class_ids)")->where("msg_type", 203)->where("group_id", $val['id'])->where("status", 1)->orderBy('id', 'DESC')->first();
				$pinMessageChatInbox = !empty($pinMessageIn) ? $pinMessageIn->message : '';
				$userJoinedGroup = ChatGroupUser::where("group_id", $val['id'])->where("user_id", $userId)->first();
				$populardata[$key]['id'] 		  = $val['id'];
				$populardata[$key]['title'] 	  = $val['title'];
				$populardata[$key]['image'] 	  = !empty($val['image']) ? asset('upload/chat_images/' . $val['image']) : '';
				$populardata[$key]['pin_msg'] 	  = $pinMessageChatInbox;
				$populardata[$key]['description'] = $val['description'];
				$populardata[$key]['joined'] 	  = !empty($userJoinedGroup) ? 1 : 0;
				$populardata[$key]['mute_status'] = !empty($userJoinedGroup) ? $userJoinedGroup->mute_status : 0;
			}
		}
		if (!empty($othergroups)) {
			foreach ($othergroups as $key => $val) {
				$pinMessageIn = PinMessage::whereRaw("find_in_set($classId,class_ids)")->where("msg_type", 203)->where("group_id", $val['id'])->where("status", 1)->orderBy('id', 'DESC')->first();
				$pinMessageChatInbox = !empty($pinMessageIn) ? $pinMessageIn->message : '';
				$userJoinedGroup = ChatGroupUser::where("group_id", $val['id'])->where("user_id", $userId)->first();
				$otherdata[$key]['id'] 		    = $val['id'];
				$otherdata[$key]['title'] 	    = $val['title'];
				$otherdata[$key]['image'] 	    = !empty($val['image']) ? asset('upload/chat_images/' . $val['image']) : '';
				$otherdata[$key]['pin_msg'] 	= $pinMessageChatInbox;
				$otherdata[$key]['description'] = $val['description'];
				$otherdata[$key]['joined'] 	    = !empty($userJoinedGroup) ? 1 : 0;
				$otherdata[$key]['mute_status'] = !empty($userJoinedGroup) ? $userJoinedGroup->mute_status : 0;
			}
		}
		//$this->helper->trackingApi($userId, 'chat', '', 0, '', $deviceType);
		$message = "Get Skill Chat Groups List.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "classId" => $classId, "className" => $className, "pin_message" => $pinMessageChatGroup, "popular_groups" => $populardata, "other_groups" => $otherdata)]);
	}

	public function getChatGroupList(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "popular_groups" => [], "other_groups" => [])]);
		}
		$search   = $request->search;
		$userSubscription = $this->helper->userSubscription($userId);
		
		$populargroups = ChatGroup::where("status", 1)->where("type", 402);
		if (!empty($populargroups)) {
			$populargroups = $populargroups->where("title", "like", "%" . $search . "%");
		}
		$populargroups = $populargroups->orderBy('sort_id', 'ASC')->get();
		$othergroups = ChatGroup::where("status", 1)->where("type", 401);
		if (!empty($othergroups)) {
			$othergroups = $othergroups->where("title", "like", "%" . $search . "%");
		}
		$othergroups = $othergroups->orderBy('sort_id', 'ASC')->get();
		$populardata = array();
		$otherdata = array();
		if (!empty($populargroups)) {
			foreach ($populargroups as $key => $val) {
				$userJoinedGroup = ChatGroupUser::where("group_id", $val['id'])->where("user_id", $userId)->first();
				$populardata[$key]['id'] 		  = $val['id'];
				$populardata[$key]['title'] 	  = $val['title'];
				$populardata[$key]['image'] 	  = !empty($val['image']) ? asset('upload/chat_images/' . $val['image']) : '';
				$populardata[$key]['pin_msg'] 	  = $val['pin_msg'];
				$populardata[$key]['description'] = $val['description'];
				$populardata[$key]['joined'] 	  = !empty($userJoinedGroup) ? 1 : 0;
			}
		}
		if (!empty($othergroups)) {
			foreach ($othergroups as $key => $val) {
				$userJoinedGroup = ChatGroupUser::where("group_id", $val['id'])->where("user_id", $userId)->first();
				$otherdata[$key]['id'] 		    = $val['id'];
				$otherdata[$key]['title'] 	    = $val['title'];
				$otherdata[$key]['image'] 	    = !empty($val['image']) ? asset('upload/chat_images/' . $val['image']) : '';
				$otherdata[$key]['pin_msg'] 	= $val['pin_msg'];
				$otherdata[$key]['description'] = $val['description'];
				$otherdata[$key]['joined'] 	    = !empty($userJoinedGroup) ? 1 : 0;
			}
		}
		//$this->helper->trackingApi($userId, 'chat', '', 0, '', $deviceType);
		$message = "All Chat Group List.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "popular_groups" => $populardata, "other_groups" => $otherdata)]);
	}
	public function getChatGroupListSearch(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "all_groups" => [])]);
		}
		$user = User::where("id", $userId)->first();
		//$board_id = !empty($user) ? $user->board_id : '';
		$class_id = !empty($user) ? $user->class_id : '';
		//$boardId = ($request->board_id) ? $request->board_id : $board_id;
		$classId = ($request->class_id) ? $request->class_id : $class_id;
		$userClass = StudentClass::where("id", $classId)->first();
		$className = !empty($userClass) ? $userClass->class_name : 'NA';
		$search   = $request->search;
		$userSubscription = $this->helper->userSubscription($userId);
		
		$allgroups = ChatGroup::whereRaw("find_in_set($classId,class_ids)")->where("status", 1);
		if (!empty($allgroups)) {
			$allgroups = $allgroups->where("title", "like", "%" . $search . "%");
		}
		$allgroups = $allgroups->orderBy('sort_id', 'ASC')->get();
		$allgroupdata = array();
		if (!empty($allgroups)) {
			foreach ($allgroups as $key => $val) {
				$userJoinedGroup = ChatGroupUser::where("group_id", $val['id'])->where("user_id", $userId)->first();
				$allgroupdata[$key]['id'] 		   = $val['id'];
				$allgroupdata[$key]['title'] 	   = $val['title'];
				$allgroupdata[$key]['image'] 	   = !empty($val['image']) ? asset('upload/chat_images/' . $val['image']) : '';
				$allgroupdata[$key]['pin_msg'] 	   = $val['pin_msg'];
				$allgroupdata[$key]['description'] = $val['description'];
				$allgroupdata[$key]['joined'] 	   = !empty($userJoinedGroup) ? 1 : 0;
				$allgroupdata[$key]['mute_status'] = !empty($userJoinedGroup) ? $userJoinedGroup->mute_status : 0;
			}
		}
		//$this->helper->trackingApi($userId, 'chat', '', 0, '', $deviceType);
		$message = "All Chat Group List.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "all_groups" => $allgroupdata)]);
	}
	public function joinChatGroup(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "joinGroupId" => "")]);
		}
		$groupId  = $request->groupId;
		$userSubscription = $this->helper->userSubscription($userId);
		
		$userGroup = ChatGroupUser::where("group_id", $groupId)->where("user_id", $userId)->first();
		if (!empty($userGroup)) {
			$joinGroupId = $userGroup->id;
			if ($userGroup->block_status == 1) {
				$message = "You have been blocked in this group, please contact to our Team to join the group.";
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "joinGroupId" => $joinGroupId)]);
			} else {
				$message = "You have been already joined this group.";
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "joinGroupId" => $joinGroupId)]);
			}
		} else {
			$data = array(
				'group_id'		=> $groupId,
				'user_id'		=> $userId,
				'created_at'	=> date('Y-m-d H:i:s'),
			);
			$joinGroupId = ChatGroupUser::insertGetId($data);
			$message = "Welcome! You have been joined the group successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "joinGroupId" => $joinGroupId)]);
		}
	}
	public function userChatGroupList(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "pin_message" => "", "user_groups" => [])]);
		}
		$user = User::where("id", $userId)->first();
		//$board_id = !empty($user) ? $user->board_id : '';
		$class_id = !empty($user) ? $user->class_id : '';
		//$boardId = ($request->board_id) ? $request->board_id : $board_id;
		$classId = ($request->class_id) ? $request->class_id : $class_id;
		$search   = $request->search;
		$userSubscription = $this->helper->userSubscription($userId);
		
		$pinMessage = PinMessage::whereRaw("find_in_set($classId,class_ids)")->where("msg_type", 202)->where("status", 1)->orderBy('id', 'DESC')->first();
		$pinMessageChatGroup = !empty($pinMessage) ? $pinMessage->message : '';
		$userJoinedGroups = ChatGroupUser::select("group_id")->where("user_id", $userId)->where("block_status", 0)->get();
		$usergroupdata = array();
		if (!empty($userJoinedGroups) && !empty($userJoinedGroups->toArray())) {
			foreach ($userJoinedGroups as $value) {
				$userJoinedGroupIds[] = $value['group_id'];
			}
			$usergroups = ChatGroup::whereIn("id", $userJoinedGroupIds)->where("status", 1);
			if (!empty($usergroups)) {
				$usergroups = $usergroups->where("title", "like", "%" . $search . "%");
			}
			$usergroups = $usergroups->orderBy('sort_id', 'ASC')->get();
			if (!empty($usergroups)) {
				foreach ($usergroups as $key => $val) {
					$pinMessageIn = PinMessage::whereRaw("find_in_set($classId,class_ids)")->where("msg_type", 203)->where("group_id", $val['id'])->where("status", 1)->orderBy('id', 'DESC')->first();
					$pinMessageChatInbox = !empty($pinMessageIn) ? $pinMessageIn->message : '';
					$userGroup = ChatGroupUser::where("group_id", $val['id'])->where("user_id", $userId)->first();
					$usergroupdata[$key]['id'] 			= $val['id'];
					$usergroupdata[$key]['title'] 		= $val['title'];
					$usergroupdata[$key]['image'] 		= !empty($val['image']) ? asset('upload/chat_images/' . $val['image']) : '';
					$usergroupdata[$key]['pin_msg'] 	= $pinMessageChatInbox;
					$usergroupdata[$key]['description'] = $val['description'];
					$usergroupdata[$key]['mute_status'] = !empty($userGroup) ? $userGroup->mute_status : 0;
				}
			}
			//$this->helper->trackingApi($userId, 'chat', '', 0, '', $deviceType);
			$message = "User Chat Group List.";
		} else {
			$message = "User Chat Group List Not Found.";
		}
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "pin_message" => $pinMessageChatGroup, "user_groups" => $usergroupdata)]);
	}
	public function getChatGroupMessages(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "group_data" => "", "message_data" => [])]);
		}
		$user = User::where("id", $userId)->first();
		//$board_id = !empty($user) ? $user->board_id : '';
		$class_id = !empty($user) ? $user->class_id : '';
		//$boardId = ($request->board_id) ? $request->board_id : $board_id;
		$classId = ($request->class_id) ? $request->class_id : $class_id;
		$groupId  = $request->groupId;
		$page = $request->has('page') ? $request->get('page') : 1;
		$limit = $request->has('limit') ? $request->get('limit') : 10;
		$userSubscription = $this->helper->userSubscription($userId);
		
		$group = ChatGroup::findOrFail($groupId);
		$groupdata = array();
		$messagedata_arr = array();
		if (!empty($group)) {
			$userGroup = ChatGroupUser::where("group_id", $groupId)->where("user_id", $userId)->first();
			$pinMessageIn = PinMessage::whereRaw("find_in_set($classId,class_ids)")->where("msg_type", 203)->where("group_id", $groupId)->where("status", 1)->orderBy('id', 'DESC')->first();
			$pinMessageChatInbox = !empty($pinMessageIn) ? $pinMessageIn->message : '';
			$groupdata['id'] 			= $group['id'];
			$groupdata['title'] 		= $group['title'];
			$groupdata['image'] 		= !empty($group['image']) ? asset('upload/chat_images/' . $group['image']) : '';
			$groupdata['pin_msg'] 		= $pinMessageChatInbox;
			$groupdata['description'] 	= $group['description'];
			$groupdata['mute_status'] 	= !empty($userGroup) ? $userGroup->mute_status : 0;
			$chat_messages_data = ChatMessage::where("group_id", $groupId)->orderBy("id", "DESC")->paginate($limit);
			// dd($group->chat_messages_data);
			if ($group->chat_messages_data->count()>0) {
				$offset = ($page - 1) * $limit;
				//$chat_messages_data = $group->chat_messages_data->slice($offset)->take($limit)->sortByDesc('id');
				foreach ($chat_messages_data as $key => $message) {
					if ($message->parent_id > 0) {
						if ($message->user_id == $userId) {
							$type = 3;
						} else {
							$type = 4;
						}
						if ($message->user_data->role_id == 3) {
						} elseif ($message->user_data->role_id == 2) {
							$type = 7;
						} else {
							$type = 8;
						}
					} else {
						if ($message->user_id == $userId) {
							$type = 1;
						} else {
							$type = 2;
						}
						if ($message->user_data->role_id == 3) {
						} elseif ($message->user_data->role_id == 2) {
							$type = 5;
						} else {
							$type = 6;
						}
					}
					$messagedata['id'] 			= $message->id;
					$messagedata['parent_id'] 	= $message->parent_id;
					//$messagedata['user_id'] 	= $message->user_id;
					$messagedata['user_name'] 	= $message->user_data->name;
					$messagedata['user_image'] 	= ($message->user_data->image) ? asset('upload/profile/' . $message->user_data->image) : '';
					$messagedata['image'] 		= ($message->status==1) ? !empty($message->image) ? $message->image : '' : '';
					$messagedata['video'] 		= ($message->status==1) ? !empty($message->video) ? $message->video : '' : '';
					$messagedata['message'] 	= ($message->status==1) ? $this->helper->checkBadWords($message->message) : 'This Message was Deleted!';
					$messagedata['created_at'] 	= $message->created_at->diffForHumans();
					$messagedata['type'] 		= $type;
					$messagedata['oldmsg_user_name'] 	= ($message->parent_id > 0) ? $message->oldmsg_data->user_data->name : '';
					$messagedata['oldmsg_user_image'] 	= ($message->parent_id > 0) ? ($message->oldmsg_data->user_data->image) ? asset('upload/profile/' . $message->oldmsg_data->user_data->image) : '' : '';
					$messagedata['oldmsg_image'] 		= ($message->status==1) ? ($message->parent_id > 0) ? !empty($message->oldmsg_data->image) ? $message->oldmsg_data->image : '' : '' : '';
					$messagedata['oldmsg_video'] 		= ($message->status==1) ? ($message->parent_id > 0) ? !empty($message->oldmsg_data->video) ? $message->oldmsg_data->video : '' : '' : '';
					$messagedata['oldmsg_message'] 		= ($message->status==1) ? ($message->parent_id > 0) ? $this->helper->checkBadWords($message->oldmsg_data->message) : '' : 'This Message was Deleted!';
					$messagedata['oldmsg_created_at'] 	= ($message->parent_id > 0) ? $message->oldmsg_data->created_at->diffForHumans() : '';
					array_push($messagedata_arr, $messagedata);
				}
			}
		}
		//$this->helper->trackingApi($userId, 'chat', '', $groupId, '', $deviceType);
		$message = "All Chat Group List.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "group_data" => $groupdata, "message_data" => $messagedata_arr)]);
	}
	public function sendChatGroupMessage(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "messageId" => "")]);
		}
		$messageId  = ($request->messageId) ? $request->messageId : 0;
		$groupId  = $request->groupId;
		//$chatMessage = ($request->message) ? $this->helper->checkBadWords($request->message) : NULL;
		$chatMessage = ($request->message) ? $request->message : NULL;
		$userSubscription = $this->helper->userSubscription($userId);
		if ($userSubscription == 0) {
			$chatMessageCount = ChatMessage::where("group_id", $groupId)->where("user_id", $userId)->count();
			if ($chatMessageCount >= config('constant.FREE_CHAT_MSG')) {
				$message = "Please subscribe a package first to chat here!";
				return response()->json(['statusCode' => 201, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "messageId" => $messageId)]);
			}
		}
		$getUserStatus = ChatGroupUser::where("group_id", $groupId)->where("user_id", $userId)->first();
		if (empty($getUserStatus)) {
			$message = "Please First Join the Group then you can Start the Chat.";
			return response()->json(['statusCode' => 201, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "messageId" => $messageId)]);
		} else {
			if ($getUserStatus->block_status == 1) {
				$message = "You have been blocked due to Violating user chat policies. Contact Support.";
				return response()->json(['statusCode' => 201, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "messageId" => $messageId)]);
			}
		}

		$s3 = AWS::createClient('s3');

		if(!empty($request->file('video'))) {
			$validator = Validator::make($request->all(), [
				//'message' => 'required',
				//'image' => 'required',
				'video' => 'mimes:mp4',
			]);

			if($validator->fails()){
				$msg = $validator->messages()->first();
				return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("userSubscription" => "", "messageId" => "")]);
			}
		}
		$image = $request->file('image');
		$video = $request->file('video');
		$imageUrl = $videoUrl = NULL;
		$destinationPath = public_path().'/upload/chat_message_images/';
		if($image){
			$originalFile = $image->getClientOriginalName();
			$imageFilename = "chat_".time().$originalFile;
			$image->move($destinationPath, $imageFilename);
			//S3 Upload
			$s3->putObject(array(
				'Bucket'     => env('AWS_BUCKET'),
				'Key'        => "assignment_data/".$imageFilename,
				'SourceFile' => $destinationPath.$imageFilename,
			));
			$imageUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $imageFilename;
			if(!empty($image) && file_exists( $destinationPath.$imageFilename )) {
				unlink( $destinationPath.$imageFilename );
			}
		}
		if($video){
			$originalFile = $video->getClientOriginalName();
			$videoFilename = "chat_".time().".mp4";
			$video->move($destinationPath, $videoFilename);
			//S3 Upload
			$s3->putObject(array(
				'Bucket'     => env('AWS_BUCKET'),
				'Key'        => "assignment_data/".$videoFilename,
				'SourceFile' => $destinationPath.$videoFilename,
			));
			$videoUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $videoFilename;
			if(!empty($video_file) && file_exists( $destinationPath.$videoFilename )) {
				unlink( $destinationPath.$videoFilename );
			}
		}
		$data = array(
			'parent_id'		=> $messageId,
			'group_id'		=> $groupId,
			'user_id'		=> $userId,
			'image'			=> $imageUrl,
			'video'			=> $videoUrl,
			'message'		=> $chatMessage,
			'created_at'	=> date('Y-m-d H:i:s'),
		);
		$insertId = ChatMessage::insertGetId($data);
		$chatMessage = ChatMessage::with('user_data','group_data')->where("id", $insertId)->first();
		if (!empty($chatMessage)) {
			$userName = $chatMessage->user_data->name;
			$groupName = $chatMessage->group_data->title;
			$getGroupUsers = ChatGroupUser::select("user_id")->where("group_id", $groupId)->where("block_status", 0)->where("mute_status", 0)->get();
			if (!empty($getGroupUsers) && !empty($getGroupUsers->toArray())) {
				foreach ($getGroupUsers as $value) {
					//$groupUserIds[] = $value['user_id'];
					$user = User::select("deviceToken")->where("id", "!=", $userId)->where("id", $value['user_id'])->first();
					$token = isset($user->deviceToken) ? $user->deviceToken : '';
					if ($token!='') {
						$title = 'Guruathome';
						$click_action = 'Chat';
						$module_id = $groupId;
						$message = $userName . ' sent a message in ' . $groupName . ' ' . $chatMessage->message;
						//$this->helper->notificationsend($token, $title, $message, $click_action, $module_id);//ak;
					}
				}
			}
		}
		$message = "Your chat message submitted successfully.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "messageId" => $insertId)]);
	}
	public function chatMuteNotification(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "groupId" => "", "muteStatus" => "")]);
		}
		$groupId  = $request->groupId;
		$userSubscription = $this->helper->userSubscription($userId);
		
		$chatGroupUser = ChatGroupUser::where("group_id", $groupId)->where("user_id", $userId)->first();
		if (!empty($chatGroupUser)) {
			if ($chatGroupUser->mute_status == 0) {
				$mute_status = 1;
			} else {
				$mute_status = 0;
			}
			$chatGroupUser->update(["mute_status" => $mute_status]);
			$message = "Chat group mute notification updated successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "groupId" => $groupId, "muteStatus" => $mute_status)]);
		} else {
			$message = "Chat group mute notification not updated!";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "groupId" => $groupId, "muteStatus" => "")]);
		}
	}
	public function chatGroupLeave(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "groupId" => "")]);
		}
		$groupId  = $request->groupId;
		$userSubscription = $this->helper->userSubscription($userId);
		
		$chatGroupUser = ChatGroupUser::where("group_id", $groupId)->where("user_id", $userId)->first();
		if (!empty($chatGroupUser)) {
			$chatGroupUser->delete();
			$message = "Chat group left successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "groupId" => $groupId)]);
		} else {
			$message = "Chat group not found!";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "groupId" => $groupId)]);
		}
	}

	public function userDetail(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("user" => array("id"=>""))]);
		}
		$user     = User::where("id", $userId)->where("status", 1)->first();
		$studentClass = StudentClass::where("id", $user->class_id)->first();
		$class_name = !empty($studentClass) ? $studentClass->class_name : 'NA';
		$userdata = array();
		if (!empty($user)) {
			$userdata['id']             = $user->id;
			$userdata['name']           = isset($user->name) ? $user->name : '';
			$userdata['email']          = isset($user->email) ? $user->email : '';
			$userdata['country_code']   = isset($user->country_code) ? $user->country_code : '';
			$userdata['country_flag']   = isset($user->country_flag) ? $user->country_flag : '';
			$userdata['phone']          = isset($user->phone) ? $user->phone : '';
			$userdata['gender']         = isset($user->gender) ? $user->gender : '';
			$userdata['dob']            = !empty($user->dob) ? date('d/m/Y', strtotime($user->dob)) : '';
			$userdata['image']          = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
			$userdata['class_id']       = isset($user->class_id) ? $user->class_id : '';
			$userdata['class_name']     = isset($user->class_id) ? $class_name : '';
			$userdata['school_college'] = isset($user->school_college) ? $user->school_college : '';
			$userdata['state']          = isset($user->state) ? $user->state : '';
			if ($userdata['state']!='') {
				$states = State::where('state', $userdata['state'])->first();
			}
			$userdata['state_id']       = isset($states->id) ? $states->id : 0;
			$userdata['city']           = isset($user->city) ? $user->city : '';
			$userdata['postal_code']    = isset($user->postal_code) ? $user->postal_code : '';
			$userdata['parents_phone']  = isset($user->parents_phone) ? $user->parents_phone : '';
			$userdata['earned_point']   = 0;
			$userdata['created_at']     = $user->created_at->diffForHumans();
			$this->helper->trackingApi($userId, 'user', 'user profile', $userId, '', $deviceType);
			$message = "Get User Detail Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("user" => $userdata)]);
		} else {
			$message = "User Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("user" => "")]);
		}
	}
	public function updateProfile(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("id"=>"")]);
		}
		$userArray = array();
		$validator = Validator::make($request->all(), [
			'userId' => 'required|numeric',
			'name' => 'required|regex:/^[\pL\s\-]+$/u',
			'email' => 'required|string|email',
			'phone' => 'required|numeric',
			'postal_code' => 'numeric|min:6',
		]);

		if($validator->fails()){
			$msg = $validator->messages()->first();
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}

		$dob_date = ($request->dob) ? $request->dob : '';
		$old_date = explode('/', $dob_date);
		if(isset($old_date) && $dob_date!=''){
			$new_date = $old_date[2].'-'.$old_date[1].'-'.$old_date[0];
		}

		$name 		    = ucwords($request->name);
		$email 			= $request->email;
		$country_code 	= trim($request->country_code,"+");
		$country_flag 	= strtoupper($request->country_flag);
		$phone 			= $request->phone;
		$gender 		= $request->gender;
		$dob 			= ($request->dob) ? $new_date : NULL;
		$class_id 		= $request->class_id;
		$school_college = $request->school_college;
		$state 			= $request->state;
		$city 			= $request->city;
		$postal_code 	= $request->postal_code;
		$parents_phone 	= $request->parents_phone;

		$checkUser = User::where('id', $userId)->first();
		if (!empty($checkUser)) {
			$checkPhone = User::where('id', '!=', $userId)->where('phone', $phone)->first();
			if (!empty($checkPhone)) {
				$msg = "Phone number already exists!";
				return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
			}
			$checkEmail = User::where('id', '!=', $userId)->where('email', $email)->first();
			if (!empty($checkEmail)) {
				$msg = "Email address already exists!";
				return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
			}
			
			$imagess = '';
			//print_r($_FILES); exit;
			if (isset($_FILES['image']['name']) && $_FILES['image']['name']!='') {
				$profileimagename = $_FILES['image']['name'];
				$tmpimage1 = $_FILES['image']['tmp_name'];
				$newprofileImage = rand(00000, 99999) . date('d') . $profileimagename;
				$location = "upload/profile/";
				move_uploaded_file($tmpimage1, $location . $newprofileImage);
				$url = 'upload/profile/' . $newprofileImage;
				$img = Image::make($url)->resize(200, 200);
				$imagess =  $img->basename;
			} else {
				$imagess = $checkUser->image;
			}
			$otp = rand(1111, 9999);
			$updateData = User::where('id', $userId)->update([
				'name' 				=> $name,
				//'email' 			=> $email,
				//'country_code'		=> $country_code,
				//'country_flag'		=> $country_flag,
				//'phone' 			=> $phone,
				'gender' 			=> $gender,
				'dob' 				=> $dob,
				'image' 			=> $imagess,
				'class_id' 			=> $class_id,
				'school_college'	=> $school_college,
				'state' 			=> $state,
				'city' 				=> $city,
				'postal_code' 		=> $postal_code,
				'parents_phone' 	=> $parents_phone,
				'otp_match' 		=> $otp,
				'updated_at' 		=> date('Y-m-d H:i:s')
			]);
			$userArray = $this->getuserDetail($userId);
			//echo $userArray['email']; die;
			if ($userArray['email']!=$email && $userArray['phone']!=$phone) {
				$changeStatus = 3;
				$newCountryCode = $country_code;
				$newCountryFlag = $country_flag;
				$newPhone = $phone;
				$newEmail = $email;
			} elseif ($userArray['email']!=$email && $userArray['phone']==$phone) {
				$changeStatus = 2;
				$newCountryCode = '';
				$newCountryFlag = '';
				$newPhone = '';
				$newEmail = $email;
			} elseif ($userArray['email']==$email && $userArray['phone']!=$phone) {
				$changeStatus = 1;
				$newCountryCode = $country_code;
				$newCountryFlag = $country_flag;
				$newPhone = $phone;
				$newEmail = '';
			} else {
				$changeStatus = 0;
				$newCountryCode = '';
				$newCountryFlag = '';
				$newPhone = '';
				$newEmail = '';
			}
			$userArray['changeStatus'] = $changeStatus;
			$userArray['newCountryCode'] = $newCountryCode;
			$userArray['newCountryFlag'] = $newCountryFlag;
			$userArray['newPhone'] = $newPhone;
			$userArray['newEmail'] = $newEmail;
			if ($changeStatus > 0) {
				//send otp
				$msg = 'Verification Otp Send, Please Check.';
			/*
				$this->helper->sms($phone, $otp);
				$this->helper->sendEmail($userArray['email'], 'Guruathome: Verify OTP', $data = array('userName' => $userArray['name'], 'message' => '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: ' . $otp . '</p>'));
*/
			}
			//Update Lead on MLM
			$user = User::findOrFail($userId);
			if ($user) {
				$studentClass = StudentClass::where("id", $user->class_id)->first();
				$class_name = !empty($studentClass) ? $studentClass->class_name : 'NA';
				$gender = $user->gender;
				$email = $user->email;
				$phone = $user->phone;
				$city = !empty($user->city) ? $user->city : $city;
				$state = !empty($user->state) ? $user->state : $state;
				$refer_code = $user->refer_code;
				$franchiseUserId = 0;
				if (!empty($refer_code)) {
					$franchise = Franchise::where("refer_code", $refer_code)->first();
					if (!empty($franchise)) {
						$franchiseUserId = $franchise->user_id;
						if ($gender=='Female'){
							$user_gender = 202;
						} elseif ($gender=='Non Binary'){
							$user_gender = 203;
						} else {
							$user_gender = 201;
						}
						$ch = curl_init();
						$url = config('constant.FMSLEADAPIURL');
						curl_setopt($ch, CURLOPT_URL,$url);
						curl_setopt($ch, CURLOPT_POST, true);
						curl_setopt($ch, CURLOPT_POSTFIELDS, "user_id=$franchiseUserId&full_name=$name&email_address=$email&mobile_number=$phone&class_name=$class_name&user_gender=$user_gender&city_name=$city&state_name=$state");
						curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
						$output = curl_exec ($ch);

						curl_close ($ch);

						$response = json_decode($output);
						//echo '<pre />'; print_r($response); die; // Show output
						//echo $city . $state; die;
						if ($response->Status==false) {
							$msg = $response->Message;
						}
					}
				}
			}
			$message = 'Updated Successfully';
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => $userArray]);
		} else {
			$msg = "Invalid User Id ";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}
	}
	public function updatePhoneEmail(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("id"=>"")]);
		}
		$userArray = array();
		$validator = Validator::make($request->all(), [
			'userId' => 'required|numeric',
			'otp' => 'required|numeric',
			// 'email' => 'string|email',
			// 'phone' => 'min:10|max:13',
		]);

		if($validator->fails()){
			$msg = $validator->messages()->first();
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}

		$country_code 	= trim($request->country_code,"+");
		$country_flag 	= strtoupper($request->country_flag);
		$phone 			= $request->phone;
		$email 			= $request->email;
		$otp 			= $request->otp;
		$checkUser = User::where('id', $userId)->where('otp_match', $otp)->first();
		if (!empty($checkUser)) {
			if (!empty($phone)) {
				$checkPhone = User::where('id', '!=', $userId)->where('phone', $phone)->first();
				if (empty($checkPhone)) {
					$updateData = User::where('id', $userId)->update([
						'country_code'	=> $country_code,
						'country_flag'	=> $country_flag,
						'phone' 		=> $phone,
						'updated_at' 	=> date('Y-m-d H:i:s')
					]);
				}
			}
			if (!empty($email)) {
				$checkEmail = User::where('id', '!=', $userId)->where('email', $email)->first();
				if (empty($checkEmail)) {
					$updateData = User::where('id', $userId)->update([
						'email' 		 => $email,
						'updated_at' 	 => date('Y-m-d H:i:s')
					]);
				}
			}
			$userArray = $this->getuserDetail($userId);
			$message = 'Updated Successfully';
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => $userArray]);
		} else {
			$msg = "User Id or OTP not matched!";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}
	}
	public function phoneEmailResendOtp(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("otp"=>"")]);
		}
		$country_code 	= trim($request->country_code,"+");
		$phone   		= $request->phone;
		$email   		= $request->email;
		if (!empty($userId)) {
			$checkUser = User::where('id', $userId)->first();
			if ($checkUser) {
				$otpnumber = rand(1111, 9999);
				if($phone==''){
					$phone = $checkUser->phone;
				}
				$update = User::where('id', $userId)->update(['otp_match' => $otpnumber]);
				if ($update) {
					$returndata['otp'] = $otpnumber;
					$msg = 'Verification Otp Send, Please Check.';
					/*//ak
					$this->helper->sms($phone, $otpnumber);
					$this->helper->sendEmail($checkUser->email, 'Guruathome: Verify OTP', $data = array('userName' => $checkUser->name, 'message' => '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: ' . $otpnumber . '</p>'));
					*/
					return response()->json(['statusCode' => 200, 'message' => $msg, "data" => $returndata]);
				} else {
					return response()->json(['statusCode' => 400, 'message' => 'Somthing Went Wrong!', 'data' => array("otp" => "")]);
				}
			} else {
				return response()->json(['statusCode' => 400, 'message' => 'Invaild User Id!', 'data' => array("otp" => "")]);
			}
		} else {
			return response()->json(['statusCode' => 400, 'message' => 'Wrong Paramenter Passed!', 'data' => array("otp" => "")]);
		}
	}
	public function changePassword(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("id"=>"")]);
		}
		$validator = Validator::make($request->all(), [
			'userId' => 'required|numeric',
			'current_password' => 'required',
			'password' => 'required|min:6',
			'confirm_password' => 'required|min:6|max:20|same:password',
		]);

		if($validator->fails()){
			$msg = $validator->messages()->first();
			//return $this->sendError($validator->messages()->first());
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}

		$currentPassword = $request->current_password;
		$newPassword = $request->password;
		$confirmPassword = $request->confirm_password;
		$user = User::where("id", $userId)->first();
		if (Hash::check($currentPassword, $user->password)) {
			if ($newPassword != $confirmPassword) {
				$msg = 'Password and Confirm password not matched!';
				return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
			}
			$users = User::findOrFail($userId);
			$users->password = bcrypt($newPassword);
			$users->userpass = $newPassword;
			$users->save();

			$msg = 'Password updated successfully.';
			return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => array("id" => $userId)]);
		} else {
			$msg = 'Current Password not matched!';
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}
	}

	public function getSubscriptions(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("subscriptions" => [])]);
		}
		$today = date('Y-m-d');
		$user  = User::where("id", $userId)->first();
		$checkSubscriptionTaken = UserSubscription::where("user_id", $userId)->where("mode", "!=", "FREE")->where("paymentStatus", 1)->orderBy('id', 'DESC')->first();
		$userSubscription = !empty($checkSubscriptionTaken) ? 1 : 0;
		$userSubscriptionEnd = !empty($checkSubscriptionTaken) ? $checkSubscriptionTaken->end_date : '';
		$userSubscriptionId = !empty($checkSubscriptionTaken) ? $checkSubscriptionTaken->subscription_id : 0;
		$checkSubscription  = Subscription::where("id", $userSubscriptionId)->first();
		$userSubscribedPlanType = !empty($checkSubscription) ? $checkSubscription->plan_type : 0;
		if ($userSubscription==0) {
			//$subscriptions  = Subscription::where("status", 1)->where("deleted", 0)->get();
			$subscriptions  = Subscription::where("plan_type", 0)->where("deleted", 0)->get();
		} else {
			if($userSubscriptionEnd!='' && $userSubscriptionEnd >= $today) {
				if ($userSubscribedPlanType == 0) {
					$subscriptions  = Subscription::where("deleted", 0)->get();
				} else {
					$subscriptions  = Subscription::where("plan_type", 1)->where("deleted", 0)->get();
				}
			} else {
				$subscriptions  = Subscription::where("plan_type", 1)->where("deleted", 0)->get();
			}
		}
		$subscriptiondata = array();
		if (!empty($subscriptions)) {
			foreach ($subscriptions as $key => $value) {
				if ($value['status']==1) {
					$sub_plan['id']    = $value['id'];
					$sub_plan['name']  = $value['name'];
					$sub_plan['month'] = $value['month'];
					$sub_plan['price'] = $value['price'];
					$sub_plan['image'] = isset($value['image']) ? asset('upload/subscriptions') . "/" . $value['image'] : 'NA';
					$sub_plan['video'] = isset($value['video']) ? asset('upload/subscriptions') . "/" . $value['video'] : 'NA';
					$sub_plan['description'] = !empty($value['description']) ? $value['description'] : '';
					$faqs_arr = array();
					$faqs = json_decode($value['faqs'], true);
					if (!empty($faqs)) {
						foreach ($faqs as $val) {
							if (isset($val['question'])) {
								$faq['question'] = isset($val['question']) ? $val['question'] : '';
								$faq['answer'] = isset($val['answer']) ? $val['answer'] : '';
								array_push($faqs_arr, $faq);
							}
						}
					}
					$sub_plan['faqs']	= $faqs_arr;
					$sub_plan['plan_type'] = $value['plan_type'];
					$subscription = UserSubscription::where("user_id", $userId)->where("subscription_id", $value['id'])->where("end_date", ">=", $today)->where("paymentStatus", 1)->orderBy('id', 'DESC')->first();
					$sub_plan['start_date'] = !empty($subscription->start_date) ? $subscription->start_date : '';
					$sub_plan['end_date'] = !empty($subscription->end_date) ? $subscription->end_date : '';
					$sub_plan['status'] = !empty($subscription) ? 1 : 0;
					$sub_plan['mode'] = !empty($subscription) ? $subscription->mode : ''; //FREE
					//Franchise student applied coupon code
					$couponCode = '';
					$amount = $discount = $discountAmt = $payableAmt = 0;
					if ($userId > 0) {
						$franchise = Franchise::where("refer_code", $user->refer_code)->first();
						if (!empty($franchise)) {
							$checkCoupon = CouponCode::where('user_id', $userId)->where('condition_1', 1)->where("end_date", ">=", $today)->where("status", 1)->where("deleted", 0)->first();
							if (!empty($checkCoupon)) {
								$couponCode = $checkCoupon->coupon;
								if ($checkCoupon->discount > 0) {
									$discount = $checkCoupon->discount;
								}
								$checkUserSubscription = UserSubscription::where("user_id", $userId)->where("coupon_code", $couponCode)->where("paymentStatus", 1)->orderBy('id', 'DESC')->first();
								if (empty($checkUserSubscription)) {
									if ($value['price'] > 1) {
										$amount = $value['price'];
										if ($discount > 0) {
											$discountAmt = ($amount * $discount) / 100;
										}
										if ($discountAmt > 0) {
											if ($amount > $discountAmt) {
												$payableAmt = $amount - $discountAmt;
											} else {
												$payableAmt = 0;
											}
										}
									}
								}
							}
						}
					}
					$sub_plan['couponCode']	= $couponCode;
					$sub_plan['discount']	= $discount;
					$sub_plan['payableAmt']	= $payableAmt;
					array_push($subscriptiondata, $sub_plan);
				} else {
					$subscription = UserSubscription::where("user_id", $userId)->where("subscription_id", $value['id'])->where("paymentStatus", 1)->orderBy('id', 'DESC')->first();
					if (!empty($subscription)) {
						$inactive_sub_plan['id']    = $value['id'];
						$inactive_sub_plan['name']  = $value['name'];
						$inactive_sub_plan['month'] = $value['month'];
						$inactive_sub_plan['price'] = $value['price'];
						$inactive_sub_plan['image'] = isset($value['image']) ? asset('upload/subscriptions') . "/" . $value['image'] : 'NA';
						$inactive_sub_plan['video'] = isset($value['video']) ? asset('upload/subscriptions') . "/" . $value['video'] : 'NA';
						$inactive_sub_plan['description'] = !empty($value['description']) ? $value['description'] : '';
						$faqs_arr = array();
						$faqs = json_decode($value['faqs'], true);
						if (!empty($faqs)) {
							foreach ($faqs as $val) {
								if (isset($val['question'])) {
									$faq['question'] = isset($val['question']) ? $val['question'] : '';
									$faq['answer'] = isset($val['answer']) ? $val['answer'] : '';
									array_push($faqs_arr, $faq);
								}
							}
						}
						$inactive_sub_plan['faqs']	= $faqs_arr;
						$inactive_sub_plan['plan_type'] = $value['plan_type'];
						$subscription = UserSubscription::where("user_id", $userId)->where("subscription_id", $value['id'])->where("end_date", ">=", $today)->where("paymentStatus", 1)->orderBy('id', 'DESC')->first();
						$inactive_sub_plan['start_date'] = !empty($subscription->start_date) ? $subscription->start_date : '';
						$inactive_sub_plan['end_date'] = !empty($subscription->end_date) ? $subscription->end_date : '';
						$inactive_sub_plan['status'] = !empty($subscription) ? 1 : 0;
						$inactive_sub_plan['mode'] = !empty($subscription) ? $subscription->mode : '';
						array_push($subscriptiondata, $inactive_sub_plan);
					}
				}
			}
			$this->helper->trackingApi($userId, 'subscription', '', 0, '', $deviceType);
			$message = "Get Subscription List.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "subscriptions" => $subscriptiondata)]);
		} else {
			$message = "Subscription not Found.";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "subscriptions" => "")]);
		}
	}
	public function applyCoupon(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("subscriptionId" => "", "orderId" => "", "paymentGateway" => "", "couponCode" => "")]);
		}
		$subscriptionId = $request->subscriptionId;
		$couponCode		= $request->couponCode;
		$discount = $discountAmt = $payableAmt = $coupon_no_of_users = $coupon_user_id = $coupon_subscription_id = 0;
		if (!empty($userId) && !empty($subscriptionId) && !empty($couponCode)) {
			$today	  = date('Y-m-d');
			/*$date = strtotime($today);
			$date = strtotime("-3 days", $date);
			$end_date = date('Y-m-d', $date);*/
			$subscription = UserSubscription::where("user_id", $userId)->where("end_date", ">=", $today)->where("paymentStatus", 1)->orderBy('id', 'DESC')->first();
			$userSubscription = !empty($subscription) ? 1 : 0;
			$subscriptionMode = !empty($subscription) ? $subscription->mode : ''; //FREE
			if ($userSubscription==1 && $subscriptionMode!='FREE') {
				$message = "You have already subscribed a plan, after expired you can take new subscription!";
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("subscriptionId" => $subscriptionId, "orderId" => "", "paymentGateway" => "", "couponCode" => $couponCode)]);
			}
			//$getCoupon = CouponCode::where("user_id", $userId)->where("coupon", $couponCode)->where("status", 1)->where("deleted", 0)->first();
			$getCoupon = CouponCode::where("coupon", $couponCode)->where("end_date", ">=", $today)->where("status", 1)->where("deleted", 0)->first();
			if (!empty($getCoupon) && $getCoupon->discount > 0) {
				$discount = $getCoupon->discount;
			} else {
				$message = "This Coupon Code not found!";
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("subscriptionId" => $subscriptionId, "orderId" => "", "paymentGateway" => "", "couponCode" => $couponCode)]);
			}
			$checkSubscription = UserSubscription::where("user_id", $userId)->where("coupon_code", $couponCode)->where("paymentStatus", 1)->first();
			if (!empty($checkSubscription)) {
				$message = "This Coupon Code already in used!";
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("subscriptionId" => $subscriptionId, "orderId" => "", "paymentGateway" => "", "couponCode" => $couponCode)]);
			} else {
				if ($getCoupon->condition_1 == 1){
					if ($getCoupon->user_id > 0){
						$coupon_user_id = $getCoupon->user_id;
						if ($userId != $coupon_user_id) {
							$message = "This Coupon Code not made for you!";
							return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("subscriptionId" => $subscriptionId, "orderId" => "", "paymentGateway" => "", "couponCode" => $couponCode)]);
						}
					}
				} elseif ($getCoupon->condition_1 == 2){
					if ($getCoupon->no_of_users > 0){
						$coupon_no_of_users = $getCoupon->no_of_users;
					}
				} else {
					if ($getCoupon->no_of_users > 0){
						$coupon_no_of_users = $getCoupon->no_of_users;
					}
				}
				if ($getCoupon->subscription_id > 0){
					$coupon_subscription_id = $getCoupon->subscription_id;
					if ($subscriptionId != $coupon_subscription_id) {
						$message = "This Coupon Code not made for this subscription plan!";
						return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("subscriptionId" => $subscriptionId, "orderId" => "", "paymentGateway" => "", "couponCode" => $couponCode)]);
					}
				}
				if ($coupon_no_of_users > 0) {
					$totalUsedCouponCode = UserSubscription::where("coupon_code", $couponCode)->where("paymentStatus", 1)->count();
					if ($totalUsedCouponCode >= $coupon_no_of_users) {
						$message = "This Coupon Code is expired!";
						return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("subscriptionId" => $subscriptionId, "orderId" => "", "paymentGateway" => "", "couponCode" => $couponCode)]);
					}
				}
			}
			$getSubscription = Subscription::where("id", $subscriptionId)->where("status", 1)->where("deleted", 0)->first();
			if (!empty($getSubscription) && $getSubscription->price > 1) {
				$amount = $getSubscription->price;
				if ($discount > 0) {
					$discountAmt = ($amount * $discount) / 100;
				}
				if ($discountAmt > 0) {
					if ($amount > $discountAmt) {
						$payableAmt = $amount - $discountAmt;
					} else {
						$payableAmt = 0;
					}
				}
				if ($payableAmt > 0) {
					$genString = $this->generateRandomString(35);
					/*$key_id = '***********************';
					$secret = 'qIB1iMh3EIR6VyzycKhAzNzh';
					$api = new Api($key_id, $secret);*/
					$api = new Api(env('RAZOR_KEY'), env('RAZOR_SECRET'));

					$order  = $api->order->create([
					  'receipt' => 'order_rcptid_11',
					  'amount'  => $payableAmt * 100,
					  'currency' => 'INR'
					]);
					$orderId = $order['id'];
					$payment_gateway = 1;
					$message = "Order Id Created Successfully.";
				} else {
					$month = !empty($getSubscription->month) ? $getSubscription->month : 0;
					$today = date('Y-m-d');
					$end_date = date('Y-m-d', strtotime('+'.$month.' months'));
					$data1 = array(
						'user_id'			=> $userId,
						'subscription_id'	=> $subscriptionId,
						'start_date'		=> $today,
						'end_date'			=> $end_date,
						'amount'			=> $payableAmt,
						'coupon_code'		=> $couponCode,
						'mode'				=> 'Coupon',
						'paymentStatus'		=> 1,
						'created_at'		=> date('Y-m-d H:i:s'),
					);
					$orderId = UserSubscription::insertGetId($data1);
					$user = User::where("id", $userId)->first();
					$msg = 'Subscription plan activated';
					$this->helper->addNotification($userId,$msg);
					/*
					/ak
					$this->helper->smsWithTemplate($user->phone, 'Mambership', $user->name, $getSubscription->name);
					$this->helper->sendEmail($user->email, 'Guruathome: Subscription payment initiated', $data = array('userName' => $user->name, 'message' => '<p>Thank you for payment initiated at Guruathome,</p><p>You have subscribed a plan successfully with Coupon Code: ' . $couponCode . '</p>'));
*/
					$payment_gateway = 0;
					$message = "Subscription Plan Activated Successfully.";
				}
				$this->helper->trackingApi($userId, 'subscription', 'apply Coupon', $subscriptionId, '', $deviceType);
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("subscriptionId" => $subscriptionId, "orderId" => $orderId, "paymentGateway" => $payment_gateway, "couponCode" => $couponCode)]);
			} else {
				$message = "Subscription not found!";
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("subscriptionId" => $subscriptionId, "orderId" => "", "paymentGateway" => "", "couponCode" => $couponCode)]);
			}
		} else {
			$message = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("subscriptionId" => "", "orderId" => "", "paymentGateway" => "", "couponCode" => $couponCode)]);
		}
	}
	public function getRazorpayOrderid(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("subscriptionId" => "", "orderId" => "")]);
		}
		$subscriptionId = $request->subscriptionId;
		if (!empty($userId) && !empty($subscriptionId)) {
			$today = date('Y-m-d');
			/*$date = strtotime($today);
			$date = strtotime("-3 days", $date);
			$end_date = date('Y-m-d', $date);*/
			$user  = User::where("id", $userId)->first();
			$subscription = UserSubscription::where("user_id", $userId)->where("end_date", ">=", $today)->where("paymentStatus", 1)->orderBy('id', 'DESC')->first();
			$userSubscription = !empty($subscription) ? 1 : 0;
			$subscriptionMode = !empty($subscription) ? $subscription->mode : ''; //FREE
			if ($userSubscription==1 && $subscriptionMode!='FREE') {
				$message = "You have already subscribed a plan, after expired you can take new subscription!";
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("subscriptionId" => $subscriptionId, "orderId" => "")]);
			}
			$getSubscription = Subscription::where("id", $subscriptionId)->where("status", 1)->where("deleted", 0)->first();
			if (!empty($getSubscription) && $getSubscription->price > 1) {
				//Franchise student applied coupon code
				$couponCode = '';
				$amount = $discount = $discountAmt = $payableAmt = 0;
				if ($userId > 0) {
					$franchise = Franchise::where("refer_code", $user->refer_code)->first();
					if (!empty($franchise)) {
						$checkCoupon = CouponCode::where('user_id', $userId)->where('condition_1', 1)->where("end_date", ">=", $today)->where("status", 1)->where("deleted", 0)->first();
						if (!empty($checkCoupon)) {
							$couponCode = $checkCoupon->coupon;
							if ($checkCoupon->discount > 0) {
								$discount = $checkCoupon->discount;
							}
							$checkUserSubscription = UserSubscription::where("user_id", $userId)->where("coupon_code", $couponCode)->where("paymentStatus", 1)->orderBy('id', 'DESC')->first();
							if (empty($checkUserSubscription)) {
								if ($getSubscription->price > 1) {
									$amount = $getSubscription->price;
									if ($discount > 0) {
										$discountAmt = ($amount * $discount) / 100;
									}
									if ($discountAmt > 0) {
										if ($amount > $discountAmt) {
											$payableAmt = $amount - $discountAmt;
										} else {
											$payableAmt = 0;
										}
									}
								}
							}
						}
					}
				}
				if ($payableAmt > 0) {
					$amount = $payableAmt;
				} else {
					$amount = $getSubscription->price;
				}

				$genString = $this->generateRandomString(35);
				/*$key_id = '***********************';
				$secret = 'qIB1iMh3EIR6VyzycKhAzNzh';
				$api = new Api($key_id, $secret);*/
				$api = new Api(env('RAZOR_KEY'), env('RAZOR_SECRET'));

				$order  = $api->order->create([
				  'receipt' => 'order_rcptid_11',
				  'amount'  => $amount * 100,
				  'currency' => 'INR'
				]);
				$orderId = $order['id'];
				$this->helper->trackingApi($userId, 'subscription', 'generate order id', $subscriptionId, '', $deviceType);
				$message = "Order Id Created Successfully.";
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("subscriptionId" => $subscriptionId, "orderId" => $orderId)]);
			} else {
				$message = "Subscription not found!";
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("subscriptionId" => $subscriptionId, "orderId" => "")]);
			}
		} else {
			$message = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("subscriptionId" => "", "orderId" => "")]);
		}
	}
	public function takeSubscription(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("id" => "","payment_status" => "","txn_id" => "")]);
		}
		$subscriptionId = $request->subscriptionId;
		$orderId 		= $request->orderId;
		$couponCode		= !empty($request->couponCode) ? $request->couponCode : '';
		if (!empty($userId) && !empty($subscriptionId) && !empty($orderId)) {
			/*$key_id = '***********************';
			$secret = 'qIB1iMh3EIR6VyzycKhAzNzh';
			$api = new Api($key_id, $secret);*/
			$api = new Api(env('RAZOR_KEY'), env('RAZOR_SECRET'));

			$payments = $api->order->fetch($orderId)->payments(); // Returns array of payment objects against an order
			//echo '<pre />'; print_r($payments); die;
			$txn_id = '';
			if (!empty($payments)) {
				//$orderId = isset($payments['items'][0]['order_id']) ? $payments['items'][0]['order_id'] : NULL;
				$paymentStatus = isset($payments['items'][0]['status']) ? $payments['items'][0]['status'] : 'failed';
				//if ($paymentStatus!='failed') {
					$txn_id = isset($payments['items'][0]['id']) ? $payments['items'][0]['id'] : '';
					$amount = isset($payments['items'][0]['amount']) ? $payments['items'][0]['amount'] : '';
					if ($txn_id != '' && $amount != '') {
						$subPrice = $amount / 100;
						$paymentMode = 'Online';
						if(!empty($couponCode)){
							$paymentMode = 'Coupon & Online';
						}

						$getSubscription = Subscription::where("id", $subscriptionId)->where("status", 1)->where("deleted", 0)->first();
						$month = !empty($getSubscription->month) ? $getSubscription->month : 0;
						$today = date('Y-m-d');
						$end_date = date('Y-m-d', strtotime('+'.$month.' months'));
						$data1 = array(
							'user_id'			=> $userId,
							'subscription_id'	=> $subscriptionId,
							'start_date'		=> $today,
							'end_date'			=> $end_date,
							'txn_id'			=> $txn_id,
							'amount'			=> $subPrice,
							'coupon_code'		=> $couponCode,
							'mode'				=> $paymentMode,
							'order_id'			=> $orderId,
							'paymentStatus'		=> ($paymentStatus!='failed') ? 1 : 0,
							'created_at'		=> date('Y-m-d H:i:s'),
						);
						$inserId = UserSubscription::insertGetId($data1);
						if ($paymentStatus!='failed') {
							$user = User::where("id", $userId)->first();
							$msg = 'Subscription payment initiated';
							$this->helper->addNotification($userId,$msg);
						/*
						//ak
							$this->helper->smsWithTemplate($user->phone, 'Mambership', $user->name, $getSubscription->name);
							$this->helper->sendEmail($user->email, 'Guruathome: Subscription payment initiated', $data = array('userName' => $user->name, 'message' => '<p>Thank you for payment initiated at Guruathome,</p><p>You have subscribed a plan successfully with Payment id: ' . $txn_id . '</p>'));
							*/
						}
						$this->helper->trackingApi($userId, 'subscription', 'payment initiated', $subscriptionId, '', $deviceType);
						
						$message = "Your Subscription Added Successfully.";
						return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("id" => $inserId,"payment_status" => "success","txn_id" => $txn_id)]);
					} else {
						$message = "Payment not initiated!";
						return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("id" => "","payment_status" => "failed","txn_id" => "")]);
					}
				/*} else {
					$message = "Payment failed!";
					return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("id" => "","payment_status" => "failed","txn_id" => "")]);
				}*/
			} else {
				$message = "Payment not initiated!";
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("id" => "","payment_status" => "failed","txn_id" => "")]);
			}
		} else {
			$message = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("id" => "","payment_status" => "","txn_id" => "")]);
		}
	}
	public function takeSubscriptionIos(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken,'ios');
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("id" => "","payment_status" => "","txn_id" => "")]);
		}
		$subscriptionId = $request->subscriptionId;
		$txnId 			= $request->txnId;
		$couponCode		= !empty($request->couponCode) ? $request->couponCode : '';
		$amount 		= $request->amount;
		if (!empty($userId) && !empty($subscriptionId) && !empty($txnId) && !empty($amount)) {
			$paymentMode = 'Online';
			if(!empty($couponCode)){
				$paymentMode = 'Coupon & Online';
			}

			$getSubscription = Subscription::where("id", $subscriptionId)->where("status", 1)->where("deleted", 0)->first();
			$month = !empty($getSubscription->month) ? $getSubscription->month : 0;
			$today = date('Y-m-d');
			$end_date = date('Y-m-d', strtotime('+'.$month.' months'));
			$data1 = array(
				'user_id'			=> $userId,
				'subscription_id'	=> $subscriptionId,
				'start_date'		=> $today,
				'end_date'			=> $end_date,
				'txn_id'			=> "IOS_".$txnId,
				'amount'			=> $amount,
				'coupon_code'		=> $couponCode,
				'mode'				=> $paymentMode,
				'order_id'			=> $txnId,
				'paymentStatus'		=> 1,
				'created_at'		=> date('Y-m-d H:i:s'),
			);
			$inserId = UserSubscription::insertGetId($data1);
			$user = User::where("id", $userId)->first();
			$msg = 'Subscription payment initiated';
			$this->helper->addNotification($userId,$msg);
			/*
			//ak
			$this->helper->smsWithTemplate($user->phone, 'Mambership', $user->name, $getSubscription->name);
			$this->helper->sendEmail($user->email, 'Guruathome: Subscription payment initiated', $data = array('userName' => $user->name, 'message' => '<p>Thank you for payment initiated at Guruathome,</p><p>You have subscribed a plan successfully with Payment id: ' . $txnId . '</p>'));
			*/
			$this->helper->trackingApi($userId, 'subscription', 'payment initiated', $subscriptionId, '', $deviceType);
			
			$message = "Your Subscription Added Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("id" => $inserId,"payment_status" => "success","txn_id" => $txnId)]);
		} else {
			$message = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("id" => "","payment_status" => "","txn_id" => "")]);
		}
	}

	public function contactUs(Request $request)
	{
		$userId  = $request->userId;
		$deviceType = $request->deviceType;
		$message = $request->message;
		if (!empty($userId) && !empty($message)) {
			$data = array(
				'user_id'  		=> $userId,
				'message'  		=> $message,
				'created_at'    => date('Y-m-d H:i:s'),
			);
			$inserId = Contactus::insertGetId($data);
			$this->helper->trackingApi($userId, 'contactus', '', $inserId, '', $deviceType);
			$message = "Your Message Added Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("contactId" => $inserId)]);
		} else {
			$message = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("contactId" => "")]);
		}
	}

	public function getNotifications(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("notifications" => [])]);
		}
		$notifications = Notification::where("user_id",$userId)->orderBy("id","DESC")->get();
		$notificationdata = array();
		if (!empty($notifications)) {
			foreach ($notifications as $key => $value) {
				$notificationdata[$key]['id']    = $value['id'];
				$notificationdata[$key]['message'] = $value['message'];
				$notificationdata[$key]['created_at'] = $value['created_at']->diffForHumans();
			}
			$this->helper->trackingApi($userId, 'notification', '', 0, '', $deviceType);
			$message = "Get Notification List.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("notifications" => $notificationdata)]);
		} else {
			$message = "Notification not Found.";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("notifications" => "")]);
		}
	}
	public function notificationstatus(Request $request)
	{
		$userId = $request->header("userId");
		$noti = Notification::where('user_id', $userId)->where('status', 0)->update(['status' => 1]);
		$msg = "notification seen successfully";
		return response()->json(['statusCode' => 200, 'message' => $msg]);
	}

	public function testemail(Request $request)
	{
		$otp = 1234;
		/*$msg = 'Email Send Successfully.';
		$data = array('username' => "User", 'remember_token' =>  $otp, 'msg' =>  $msg);
		Mail::send('emails.forgot_password', $data, function ($message) {
			$email = $_POST['email'];
			$message->to($email, 'From Guruathome')->subject('This is your testing mail');
			$message->from('<EMAIL>', 'Guruathome');
		});*/
		//$this->helper->sendEmail('<EMAIL>', 'Guruathome: Testing from API', $data = array('userName' => 'Rajendra', 'message' => '<p>Thank you for connected at Guruathome,</p>'));

		$phone = '9588841525';
		$msg = 'SMS Send Successfully.';
		//$this->helper->sms($phone, $otp, $msg);//ak//
		return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => ""]);
	}



	//MLM APIs
	public function franchiseCreate(Request $request)
	{
		//echo '<pre />'; print_r($request->all()); die;
		$userId  = $request->userId;
		$userName  = $request->userName;
		$userCode = $request->userCode;
		$refCode = $request->refCode;
		if (!empty($userId) && !empty($userName) && !empty($userCode) ) {
			$franchise = Franchise::where("user_id",$userId)->first();
			if (!empty($franchise)) {
				$data = array(
					'user_id'  		=> $userId,
					'user_name'  	=> $userName,
					'user_code'		=> $userCode,
					'refer_code'	=> $refCode,
					'updated_at'    => date('Y-m-d H:i:s'),
				);
				$insertId = Franchise::where("id",$franchise->id)->update($data);
			} else {
				$data = array(
					'user_id'  		=> $userId,
					'user_name'  	=> $userName,
					'user_code'		=> $userCode,
					'refer_code'	=> $refCode,
					'created_at'    => date('Y-m-d H:i:s'),
				);
				$insertId = Franchise::insertGetId($data);
			}
			$message = "Data Added Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("insertId" => $insertId)]);
		} else {
			$message = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("insertId" => "")]);
		}
	}
	public function getAllFranchiseStudentData(Request $request)
	{
		$user_id 		= $request->user_id;
		$from_date 		= $request->from_date;
		$to_date 		= $request->to_date;
		$plan_name 		= $request->plan_name;
		$plan_type_name = $request->plan_type_name;
		$subscription 	= User::with('franchise_data','subscription_data','subscription_data.plan_data')
						->where('franchise_user_id', '>',0)
						->where(function($query) use ($user_id) {
							if (isset($user_id) && !empty($user_id)) {
								$query->where('franchise_user_id', $user_id);
							}
							$query->where('franchise_user_id', '>',0);
						})
						->whereHas('subscription_data.plan_data', function($query) use ($plan_name, $plan_type_name) {
							if (isset($plan_name) && !empty($plan_name)) {
								$query->where('name', 'LIKE', "%".$plan_name."%");
							}
							if (isset($plan_type_name) && !empty($plan_type_name)) {
								$query->where('plan_type', $plan_type_name);
							}
						})
						->whereHas('subscription_data', function($query) use ($from_date, $to_date) {
							if (isset($from_date) && !empty($from_date) && isset($to_date) && !empty($to_date)) {
								$query->whereBetween('end_date',[$from_date,$to_date]);
							}
						})
						->orderBy('id', 'DESC')->get()->toArray();
		$message = "Get All Franchise Student Data.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => $subscription]);
	}
	public function uploadMLMLiveclassVideo(Request $request)
	{
		$destinationPath = public_path().'/franchisemanagement/public/storage/class_thum/';
		$videoFilename = $request->video_name;
		//$videoFilename = "FMS_".$request->class_id."_".time().$video_file;

		$s3 = AWS::createClient('s3');
		$s3->putObject(array(
			'Bucket'     => env('AWS_BUCKET'),
			'Key'        => "assignment_data/".$videoFilename,
			'SourceFile' => $destinationPath.$videoFilename,
		));
		$videoData = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $videoFilename;
		if(!empty($videoFilename) && file_exists( $destinationPath.$videoFilename )) {
			unlink( $destinationPath.$videoFilename );
		}
		return response()->json(['statusCode' => 200, 'message' => 'DONE', 'data' => $videoData]);
	}


//////////////////////////////////////////
	///// jai hddshshs

}
