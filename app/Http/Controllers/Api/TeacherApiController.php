<?php
namespace App\Http\Controllers\Api;

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

use App\Http\Controllers\Controller;
use App\Http\Helper as Helper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use App\Models\Assignment;
use App\Models\AssignmentSubmission;
use App\Models\Chapter;
use App\Models\ChatGroup;
use App\Models\ChatGroupUser;
use App\Models\ChatMessage;
use App\Models\Courses;
use App\Models\Lession;
use App\Models\LiveClass;
use App\Models\LiveclassNotify;
use App\Models\Notification;
use App\Models\QuestionAnswer;
use App\Models\QuestionAnswerLike;
use App\Models\QuestionAsk;
use App\Models\State;
use App\Models\StudentClass;
use App\Models\StudentClassSubject;
use App\Models\Subject;
use App\Models\User;
use App\Models\VideoTemp;
use AWS;
use Carbon\Carbon;
use Image;
use PDF;
use DB;
use App\Models\UserTemp;
use App\Models\TeacherAvailabiltyOfSlots;
use App\Models\TeacherAvailabiltyOfSlotsDetails;
use App\Models\Banner;
use App\Models\Category;
use App\Models\TeachingDetails;
use App\Models\StudentBookingFrees;

use App\Models\StudentApplyForDemo;
use JWTAuth;




use Tymon\JWTAuth\Exceptions\JWTException;

class TeacherApiController extends Controller
{
	protected $helper;

	public function __construct(Helper $helper)
	{
		$this->helper = $helper;
	}
	public function generateRandomString($length = 50)
	{
		$characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
		$charactersLength = strlen($characters);
		$randomString = '';
		for ($i = 0; $i < $length; $i++) {
			$randomString .= $characters[rand(0, $charactersLength - 1)];
		}
		return $randomString;
	}
	//teacher register
	public function register(Request $request)
	{
			//dd($request->all());
		$validator = Validator::make($request->all(), [
			'name' => 'required|regex:/^[\pL\s\-]+$/u',
			'email' => 'required',
			//'phone' => 'numeric|min:10|unique:users,phone',
			'password' => 'required|min:6',
			'confirm_password' => 'required|min:6|max:20|same:password',
		]);

		if($validator->fails()){
        	$msg = $validator->messages()->first();
			//return $this->sendError($validator->messages()->first());
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}

	    //echo '<pre />'; print_r($request->all()); die;
		$role 				= 2;//ucwords($request->input('role'));//2 for teacher 6 for student
		$name 				= ucwords($request->input('name'));
		$email 				= $request->input('email');
		//$phone 				= $request->input('phone');
		$phone="";
		$password 			= $request->input('password');
		$confirm_password 	= $request->input('confirm_password');
		//$coupon_code = $request->input('coupon_code');
		//	$gender 			= $request->input('gender');
		///$class_name 		= $request->input('class_name');
		//$state 				= $request->input('state');
		//$city 				= $request->input('city');
		//$deviceToken 		= $request->input('deviceToken');
		//$firebase_user_id = $request->input('firebase_user_id');
		$msg = '';
		if ( !empty($email)  && !empty($password)) {
			if ($password != $confirm_password) {
				$msg = "Password and Confirm password not matched!";
				return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
			}
			$usercheck = User::where('email', $email)->where("deleted",0)->where("role_id",2)->first();
			//dd($usercheck);
			if (!empty($usercheck)) {
				$msg = "Email id already exists. Please login directly.";
				return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
			} else {
				
				if ($phone!=null) {
					
					$phonecheck = User::where('phone', $phone)->where("deleted",0)->where('role_id',2)->first();
					if(!empty($phonecheck))
					{
						$msg = "Phone number already exists. Please login directly.";
						return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
					}
					else
					{
						$imagess = '';
						if (isset($_FILES['image']['name']) && $_FILES['image']['name']!='') {
							$profileimagename = $_FILES['image']['name'];
							$tmpimage1 = $_FILES['image']['tmp_name'];
							$newprofileImage = rand(00000, 99999) . date('d') . $profileimagename;
							$location = "upload/profile/";
							move_uploaded_file($tmpimage1, $location . $newprofileImage);
							$url = 'upload/profile/' . $newprofileImage;
							$img = Image::make($url)->resize(200, 200);
							$imagess =  $img->basename;
						}
							$otp = rand(1111, 9999);
							$data = array(
							'role_id' 	=> $role,
							'name' 		=> $name,
							'email' 	=> $email,
							'phone' 	=> $phone,
							'password' 	=> bcrypt($password),
							'userpass' 	=> $password,
							'otp'=>$otp,
						//	'coupon_code'=>$coupon_code,
								//'image'   => $imagess,
							//	'gender' 	=> $gender,
								//'dob'     => $dob,
							//	'class_name' => $class_name,
								//'school_college' => $school_college,
							//	'state' 	=> $state,
							//	'city' 		=> $city,
							//	'remember_token' => Str::random(60),
							//	'api_token' => Str::random(60),
							//	'devicetoken' => $deviceToken,
							//'otp_match' => $otp,
							'status'    =>0,
							//'firebase_user_id' => $firebase_user_id,
							//'isDevice'=>'App',
							'created_at' => date('Y-m-d H:i:s'),
							);
									$msg = ' Otp Send Successfully Your Email address.';
								$userId= UserTemp::insertGetId($data);
								$refercode = strtoupper(substr($this->generateRandomString(), 0, 3));
						//	$refercode = strtoupper(substr($name, 0, 3));
							$refercode = $refercode . $user->id;
							$user->refer_code  = $refercode;
							$user->save(); 
										$subject ='Guruathome: Verify your account';
								 $message ="Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: "  .$otp  ;


									$this->sendMail($request->email, $subject,$message);
						
								// $this->sendMail($email, 'Guruathome: Verify your account', $data = array('userName' => $name, 'message' => '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: ' . $otp . '</p>'));

							$returndata = array();
							//	$returndata = $this->getuserDetail($userId);
							//	$returndata['otp'] = $otp;
							return response()->json(['statusCode' => 200,"id"=>$userId, 'message' => $msg]);
						}
					}
				 else 
				 {
				
					$imagess = '';
					if (isset($_FILES['image']['name']) && $_FILES['image']['name']!='') {
						$profileimagename = $_FILES['image']['name'];
						$tmpimage1 = $_FILES['image']['tmp_name'];
						$newprofileImage = rand(00000, 99999) . date('d') . $profileimagename;
						$location = "upload/profile/";
						move_uploaded_file($tmpimage1, $location . $newprofileImage);
						$url = 'upload/profile/' . $newprofileImage;
						$img = Image::make($url)->resize(200, 200);
						$imagess =  $img->basename;
					}
					$otp = rand(1111, 9999);
					$data = array(
						'role_id' 	=> $role,
						'name' 		=> $name,
						'email' 	=> $email,
					//	'phone' 	=> $phone,
						'password' 	=>bcrypt($password), //Hash::make($password),
						'userpass' 	=> $password,
					//	'coupon_code'=>$coupon_code,
						'otp'=>$otp,
						//'image'   => $imagess,
					//	'gender' 	=> $gender,
						//'dob'     => $dob,
					//	'class_name' => $class_name,
						//'school_college' => $school_college,
					//	'state' 	=> $state,
					//	'city' 		=> $city,
					//	'remember_token' => Str::random(60),
					//	'api_token' => Str::random(60),
					//	'devicetoken' => $deviceToken,
						//'otp_match' => $otp,
						'status'    => 0,
						//'firebase_user_id' => $firebase_user_id,
						//'isDevice'=>'App',
						'created_at' => date('Y-m-d H:i:s'),
						'updated_at' => date('Y-m-d H:i:s'),	
					);

				
				$userId=	UserTemp::insertGetId($data);
				//	$userId = User::insertGetId($data);

					 $user=UserTemp::find($userId);
					$refercode = strtoupper(substr($this->generateRandomString(), 0, 3));
				//	$refercode = strtoupper(substr($name, 0, 3));
					$refercode = $refercode . $user->id;
					$user->refer_code = $refercode;
					$user->save(); 
					//$msg = 'User Registration Completed Successfully.';
					$msg = ' Otp Send Successfully Your Email address.';
		//			$this->sms($phone, $otp);
				//	$this->addNotification($userId,$msg);
					/*$data = array('username' => $name, 'OTP' =>  $otp, 'msg' =>  $msg);
					Mail::send('emails.register', $data, function ($message) {
						$email = $_POST['email'];
						$message->to($email, 'From Guruathome')->subject('Guruathome: Verify your account');
						$message->from('<EMAIL>', 'Guruathome');
					});*/
					$subject ='Guruathome: Verify your account';
				 $message ="Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: "  .$otp  ;


					$this->sendMail($request->email, $subject,$message);
			
					// $this->sendMail($email, 'Guruathome: Verify your account', $data = array('userName' => $name, 'message' => '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: ' . $otp . '</p>'));

					$returndata = array();
				//	$returndata = $this->getuserDetail($userId);
				//	$returndata['otp'] = $otp;
					return response()->json(['statusCode' => 200,"id"=>$userId, "otp"=>$otp,'message' => $msg]);
				}
			}
		} else {
			$msg = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}
		//$token = auth()->login($user);
		//return $this->respondWithToken($token);
	}

	//teacher match otp for login
	public function otpMatch(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'userId' => 'required|numeric',
			'otp' => 'required|numeric',
		]);

		if($validator->fails()){
        	$msg = $validator->messages()->first();
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}
		$userId = $request->input('userId');
		$otp = $request->input('otp');
		if (empty($otp) || empty($userId)) {
			return response()->json(['statusCode' => 400, 'message' => 'Wrong parameter Passed!', 'data' => array("id" => "")]);
		}
		$user = UserTemp::where('otp', $otp)->where("id", $userId)->first();

		$status=1;
	
		if ($user!=null) {
			if( $user->role_id==2)
			{
				$status = 0;
			}
			$data = array(
				"name"=>$user->name,
				"role_id"=>$user->role_id,
				"email"=>$user->email,
				"country_code"=>$user->country_code,
				"country_flag"=>$user->country_flag,
				"phone"=>$user->phone,
				"password"=>$user->password,
				"userpass"=>$user->userpass,
				"status"=>$status,
				"refer_code"=>$user->refer_code,
				'api_token' => Str::random(60),
				'created_at' => date('Y-m-d H:i:s'),
				'updated_at' => date('Y-m-d H:i:s'),			
			);
			//	dd($data);
			//	DB::table('users')->where('id', $userId)->update(array('status' => 1));
			$users=	User::insertGetId($data);
			UserTemp::where("id",$user->id)->delete();
			$returndata = array();
			$returndata = $this->getuserDetail($users);

			//	$this->smsWithTemplate($user->phone, 'AfterVerificationAccount', '+************', '<EMAIL>');//ak

			
			return response()->json(['statusCode' => 200, 'message' => 'Account Verified Successfully.', 'data' => $returndata]);
		} else {
			return response()->json(['statusCode' => 400, 'message' => 'otp does not match!', 'data' =>  array("id" => "")]);
		}
	}
	//teacher login
	public function login(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'email' => 'required',
			'password' => 'required',
			//'deviceToken' => 'required',
		]);

		if($validator->fails()){
			$msg = $validator->messages()->first();
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}
		//dd($request->all());
		$email		        = !empty($_REQUEST['email']) ? trim($_REQUEST['email']) : '';
		//$mobile		    = !empty($_REQUEST['mobile']) ? trim($_REQUEST['mobile']) : '';
		$password           = !empty($_REQUEST['password']) ? trim($_REQUEST['password']) : '';
		$deviceToken        = !empty($_REQUEST['deviceToken']) ? trim($_REQUEST['deviceToken']) : '';

		if (!empty($email) && !empty($password)) {
			if (is_numeric($email)) {
				$checkUser = User::where("phone", $email)->where("deleted",0)->where("role_id",2)->first();
			
				if (!empty($checkUser)) {
					if (Hash::check($password, $checkUser->password)) {
						if (Auth::attempt(['phone' => $email, 'password' => $password])) {
							$user =Auth::user();
						 $token = JWTAuth::fromUser($checkUser);
			
							if($user->role_id != 2){
								$msg = "You are not allowed to login here!";
								return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
							}
							if($user->status != 1){
								$msg = "Your account not activated, Please contact to Team!";
								return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
							}
							if($user->deleted != 1){
								$msg = "Your account deleted, Please contact to Team!";
								return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
							}
							$otp = rand(1111, 9999); //'5555';
							$user->generateToken();
							$userss = User::find($user->id);
							$userss->deviceToken = $deviceToken;
							$userss->otp_match = $otp;
							$userss->save();
							//$this->helper->sms($user->phone, $otp);
						//ak	$this->helper->sendEmail($user->email, 'BrainyWood: Verify OTP', $data = array('userName' => $user->name, 'message' => '<p>Thank you for connecting at BrainyWood,</p><p>You have got successfully your OTP: ' . $otp . '</p>'));

							$code = 200;
							$msg = 'Login successfully.';
							$user->api_token = $token;
							$returndata = $user;
							$returndata['image'] = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
							$returndata['current_otp'] = $otp;
						}else{
							$code = 400;
							$msg = 'Mobile number and Password not matched!';
							$returndata = array("id" => "");
						}
					}else{
						$code = 400;
						$msg = 'Password not matched!';
						$returndata = array("id" => "");
					}
				}else{
					$code = 400;
					$msg = 'Mobile number not matched!';
					$returndata = array("id" => "");
				}
			} else {
				$checkUser = User::where("email", $email)->where("role_id",2)->where("deleted",0)->first();

				if (!empty($checkUser)) {
					if (Hash::check($password, $checkUser->password)) {
						//	dd($checkUser);
						if(Auth::attempt(['email' => $email, 'password' => $password,"deleted"=>0])){
							$user =$checkUser;// Auth::user();
				   	      $token = JWTAuth::fromUser($checkUser);
			
							if($user->role_id != 2){
								$msg = "You are not allowed to login heres!";
								return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
							}
							if($user->status != 1){
								$msg = "Your account not activated, Please contact to Team!";
								return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
							}
							if($user->deleted != 0){
								$msg = "Your account deleted, Please contact to Team!";
								return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
							}
							$otp = rand(1111, 9999); //'5555';
							$user->generateToken();
							$userss = User::find($user->id);
							$userss->deviceToken = $deviceToken;
							//$userss->otp_match = $otp;
							$userss->save();
							//$this->helper->sms($user->phone, $otp);
						//ak	$this->helper->sendEmail($user->email, 'BrainyWood: Verify OTP', $data = array('userName' => $user->name, 'message' => '<p>Thank you for connecting at BrainyWood,</p><p>You have got successfully your OTP: ' . $otp . '</p>'));

							$code = 200;
							$msg = 'Login successfully.';
								$user->api_token = $token;
			
							$returndata = $user;
							$returndata['image'] = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
							$returndata['current_otp'] = $otp;
						}else{
							$code = 400;
							$msg = 'Email id and Password not matched!';
							$returndata = array("id" => "");
						}
					}else{
						$code = 400;
						$msg = 'Password not matched!';
						$returndata = array("id" => "");
					}
				}else{
					$code = 400;
					$msg = 'Email id not matched!';
					$returndata = array("id" => "");
				}
			}
		}else{
			$code = 400;
			$msg = 'Email id or password not found!';
			$returndata = array("id" => "");
		}
		return response()->json(['statusCode' => $code, 'message' => $msg, 'data' => $returndata]);
	}
	//teacher forget password
	public function forgetpassword(Request $request)
	{
		$returndata = array();
		$email = $request->email;
		if ($email != '') {
			if (is_numeric($email)) {
				//$user = User::where('phone', '=', $phone)->where('status', '=', 1)->where("role_id", $role_id)->first();
				$user = User::where('phone', $email)->where("role_id",2)->where("deleted",0)->first();
			} else {
				$user = User::where('email', $email)->where("role_id",2)->where("deleted",0)->first();

			}
		} else {
			$msg = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("type" => "forgetpassword")]);
		}

		if (!empty($user) && $user!=null) {
			$otp = rand(1111, 9999);
			$user->otp_match = $otp;
			$user->save();
			$msg = 'Verification OTP send on email address, Please Check.';
			//$this->sms($user->phone, $otp);
			/*$data = array('username' => $user->name, 'OTP' =>  $otp, 'msg' =>  $msg);
			Mail::send('emails.app_forgot_password', $data, function ($message) {
				$email = $_POST['email'];
				if ($email != '') {
					if (is_numeric($email)) {
						$user = User::where('phone', $email)->first();
					} else {
						$user = User::where('email', $email)->first();
					}
				}
				$email = $user->email;
				$message->to($email, 'From Guruathome')->subject('Guruathome: Forgot Password');
				$message->from('<EMAIL>', 'Guruathome');
			});*/
			//	$this->sendEmail($user->email, 'Guruathome: Forgot Password', $data = array('userName' => $user->name, 'message' => '<p>You have been forgotton your password, don\'t worry, Please reset your password </p><p>You have got successfully your OTP: '. $otp . '</p>'));
				
				$message = 'You have been forgotton your password, don\'t worry, Please reset your password You have got successfully your OTP: '. $otp ;

			//	$this->sendMail($request->email, $subject,$message);

				$this->sendMail($user->email,"Guruathome: Forgot Password",$message);
			$returndata['type'] = 'forgetpassword';
			$returndata['userId'] = $user->id;
			$returndata['otp'] = $otp;
			$checkUser = User::where('id', $user->id)->update(["otp_match"=>$otp]);
	
			return response()->json(['statusCode' => 200, 'message' => $msg, "data" => $returndata]);
		} else {
			$msg = "Email Not Exist!";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("type" => "forgetpassword")]);
		}
	}
	//teacher resendotp
	public function resendOtp(Request $request)
	{
		$userId = $request->input('userId');
		if (!empty($userId)) {
			$checkUser = User::where('id', $userId)->where("deleted",0)->where("role_id",2)->first();

			$checktemp = 0;
			$UserTemp =	UserTemp::where('id', $userId)->first();
			
			if(!is_null($UserTemp))
			{
				$checktemp=1;
				$checkUser = $UserTemp;
			}

			if ($checkUser) {
				$otpnumber = rand(1111, 9999);
				$phone = $checkUser->phone;
				
				if($checktemp==0)
				{
					$update = DB::table('users')->where("deleted",0)->where('id', $userId)->update(['otp_match' => $otpnumber]);
				}
				else if($checktemp==1)
				{
					$update = UserTemp::where('id', $userId)->update(['otp' => $otpnumber]);

				}
				if ($update) {
					$returndata['otp'] = $otpnumber;
					$msg = 'Verification Otp Send, Please Check.';
					//$this->sms($phone, $otpnumber);
					/*$data = array('username' => $checkUser->name, 'OTP' => $otpnumber, 'msg' => $msg);
					Mail::send('emails.otpmail', $data, function ($message) {
						$checkUser = User::where('id', $_POST['userId'])->first();
						$email = $checkUser->email;
						$message->to($email, 'From Guruathome')->subject('Guruathome: Verify OTP');
						$message->from('<EMAIL>', 'Guruathome');
					});*/
					$message = '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: ' . $otpnumber . '</p>';
					$this->sendMail($checkUser->email, 'Guruathome: Verify OTP',$message );
					
					return response()->json(['statusCode' => 200, 'message' => $msg, "data" => $returndata]);
				} else {
					return response()->json(['statusCode' => 400, 'message' => 'Somthing Went Wrong!', 'data' => array("otp" => "")]);
				}
			} else {
				return response()->json(['statusCode' => 400, 'message' => 'Invaild User Id!', 'data' => array("otp" => "")]);
			}
		} else {
			return response()->json(['statusCode' => 400, 'message' => 'Wrong Paramenter Passed!', 'data' => array("otp" => "")]);
		}
	}
	//teacher checkForgetPasswordOtp 
	public function checkForgetPasswordOtp(Request $request)
	{
		$userId 			= $request->userId;
		$otp 				= $request->otp;
		$checkUser = User::where('id', $userId)->where("otp_match",$otp)->where("deleted",0)->where('role_id',2)->first();
		//			dd($checkUser);
		if($checkUser!=null)
		{
			$message = 'Verification otp  Successfully';
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userId" => $userId)]);
			} else {
				$message = 'Invalid otp Please try again';
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userId" => $userId)]);
			}

	}
	// teacher resetPassword 
	public function resetPassword(Request $request)
	{
	//	$user = JWTAuth::user();
	
		$userId 			= $request->userId;
		$newPassword 		= $request->newPassword;
		$confirmPassword 	= $request->confirmPassword;
		$checkUser =User::where('id', $userId)->where("role_id",2)->first();

		if (!empty($checkUser)) {
			if (!empty($newPassword)) {
				if ($newPassword != $confirmPassword) {
					$msg = "Password and Confirm password not matched!";
					return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("userId" => $userId)]);
				}
				$updateData = User::where('id', $userId)->update([
					'password'		=> bcrypt($newPassword),
					'userpass'		=> $newPassword,
					'updated_at' 	=> date('Y-m-d H:i:s')
				]);
				$message = 'Password Reset Successfully';
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userId" => $userId)]);
			} else {
				$message = 'Please enter new password!';
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userId" => $userId)]);
			}
		} else {
			$msg = "Invalid User Id ";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("userId" => $userId)]);
		}
	}
	//teacher Verfy Otp 
	public function VerfyOtp(Request $request)
	{
		$userId 			= $request->userId;
		$otp 				= $request->otp;
		$checkUser = User::where('id', $userId)->where("otp_match",$otp)->where("deleted",0)->first();
		
		if($checkUser!=null)
		{
			$message = 'Verification otp  Successfully';
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userId" => $userId)]);
			} else {
				$message = 'Invalid otp Please try again';
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userId" => $userId)]);
			}

	}
	//student changepassword
	public function changePassword(Request $request)
	{
		if(!empty($request->All()))
		{

			$user = JWTAuth::user();

			//$bearerToken = $request->bearerToken();
			$userId   =$user->id; //$request->userId;
			$userStatus =$user->status; //$this->isUserActive($userId,$bearerToken);
			if ($userStatus == 0) {
				$message = "User not Available.";
				return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("id"=>"")]);
			}

			//$bearerToken = $request->bearerToken();
			//$userId   = $request->userId;
	//		$user = User::where("id", $userId)->where("role_id",2)->first();
			if($user!=null)
			{
				$userStatus = $user->status;
				if ($userStatus == 0) {
					$message = "Teacher not Available.";
					return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("id"=>"")]);
				}
				if ($user->deleted != 0) {
					$message = "Deleted Your Account.";
					return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("id"=>"")]);
				}
				$validator = Validator::make($request->all(), [
					'userId' => 'required|numeric',
					'current_password' => 'required',
					'newPassword' => 'required|min:6',
					'confirm_password' => 'required|min:6|max:20|same:newPassword',
				]);

				if($validator->fails()){
		        	$msg = $validator->messages()->first();
					//return $this->sendError($validator->messages()->first());
					return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
				}

				$currentPassword = $request->current_password;
				$newPassword = $request->newPassword;
				$confirmPassword = $request->confirm_password;
				//$user = User::where("id", $userId)->first();
				if (Hash::check($currentPassword, $user->password)) {
					if ($newPassword != $confirmPassword) {
						$msg = 'Password and Confirm password not matched!';
						return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
					}
					$users = User::findOrFail($userId);
					$users->password = hash::make($newPassword);
					$users->userpass = $newPassword;
					$users->save();

					$msg = 'Password updated successfully.';
					return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => array("id" => $userId)]);
				} else {
					$msg = 'Current Password not matched!';
					return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
				}
			}
			else
			{
				$msg = 'Invalid Teacher!';
					return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
			}
		}
	}
	//teacherupdateProfile
	public function teacherupdateProfile(Request $request)
	{
		//echo 'ww';die; 


		$user=JWTAuth::user();

				//	$bearerToken = $request->bearerToken();
	
			//$bearerToken = $request->bearerToken();
			$userId   = $user->id;
			//$userStatus = $this->isUserActive($userId,$bearerToken);
			$userStatus=$user->status;
			if ($userStatus == 0) {
				$message = "Teacher not Available.";
				return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("id"=>"")]);
			}

//		$user = User::where("id", $userId)->where("role_id",2)->where("deleted",0)->first();
		if($user!=null)
		{
			$userStatus =$user->status; //$this->isUserActive($userId,$bearerToken);
			if ($user->deleted != 0) {
				$message = "Your Account Deleted Teacher.";
				return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("id"=>"")]);
			}
			$userArray = array();
			$validator = Validator::make($request->all(), [
				'name'=>'required',
				'userId' => 'required|numeric',
				'phone' => 'required|min:10|max:10',
				'country_code' => 'required|min:2',
				'dob' => 'required',
				'timezone'=>'required',
				'currency'=>'required'
			]);

			if($validator->fails()){
	        	$msg = $validator->messages()->first();
				return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
			}
			$name =$request->name;
			$address = $request->address;
			$country_code 	= $request->country_code;
			$phone 			= $request->phone;
			 $school_college = $request->school_college;
			$dob = $request->dob;
			$class_url = $request->class_url;
			$currency = $request->currency;
			
			// $state 			= $request->state;
			// $city 			= $request->city;
			
			$teacher_exp=$request->teacher_exp;
			$highest_qualification = $request->highest_qualification;

			$checkUser = User::where('id', $userId)->first();
			if (!empty($checkUser)) {
				$imagess = '';
				//print_r($_FILES); exit;
				if (isset($_FILES['image']['name']) && $_FILES['image']['name']!='') {
					$profileimagename = $_FILES['image']['name'];
					$tmpimage1 = $_FILES['image']['tmp_name'];
					$newprofileImage = rand(00000, 99999) . date('d') . $profileimagename;
					$location = "upload/profile/";
					move_uploaded_file($tmpimage1, $location . $newprofileImage);
					$url = 'upload/profile/' . $newprofileImage;
					$img = Image::make($url)->resize(200, 200);
					$imagess =  $img->basename;
				} else {
					$imagess = $checkUser->image;
				}
				//	$otp = rand(1111, 9999);
				$updateData = User::where('id', $userId)->update([
					'name' 			 => $name,
					//'email' 		 => $email,
					'phone' 		 => $phone,
					//'parent_name' 		 => $parent_name,
					'dob' 			 => date('Y-m-d H:i:s', strtotime($dob)),
					'image' 		 => $imagess,
					//'class_id' 	 => $class_name,
					'school_college' => $school_college,
				//	'state' 		 => $state,
				//	'city' 			 => $city,
					'country_code' 	 => $country_code,
					'address'		=>$address,
					'currency'		=>$currency,
				//	'otp_match' 	 => $otp,
				//	'subject_id'=>$subject_id,
					'teacher_exp'=>$teacher_exp,
					'highest_qualification'=>$highest_qualification,
					'updated_at' 	 => date('Y-m-d H:i:s'),
					"class_url"=>$class_url,
					'timezone'=>$request->timezone,
				]);
				
				if($updateData){
					$message = 'Updated Successfully';
					return response()->json(['statusCode' => 200, 'message' => $message, 'data' => User::where('id', $userId)->first()]);
				}else {
					$msg = "Updated Failed";
					return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
				}
			}
		 	else {
					$msg = "Invalid User Id ";
					return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
			}
		}
		else {
					$msg = "User Not Found ";
					return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
			}


	}
	//teacherAvailabiltyOfSlots
	public function teacherAvailabiltyOfSlots(Request $request){
		if($request->all()){

			$user = JWTAuth::user();

			//$bearerToken = $request->bearerToken();
			$userId =$user->id;// $request->userId;
			$userStatus =$user->status; //$this->isUserActive($userId,$bearerToken);
			if ($userStatus == 0) {
				$message = "User not Available.";
				return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("user" => array("id"=>""))]);
			}

			$role_id = 2;
			$user = User::where("id", $userId)->where("role_id",2)->where("deleted",0)->first();
			
			if($user!=null)
			{
				$userStatus =$user->status; //$this->isUserActive($userId,$bearerToken);
				// if ($userStatus == 0) {
				// 	$message = "Teacher not Available.";
				// 	return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("id"=>"")]);
				// }
				if ($user->deleted != 0) {
					$message = "Your Account Deleted Teacher.";
					return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("id"=>"")]);
				}
				$requestData = json_decode($request->data);
						
				if(!empty($requestData))
				{
					$getTeacherAvailabiltyOfSlots =  TeacherAvailabiltyOfSlots::where("user_id",$userId)->get();
					if(!empty($getTeacherAvailabiltyOfSlots) && !is_null($getTeacherAvailabiltyOfSlots))
					{
						foreach($getTeacherAvailabiltyOfSlots as $v =>$s){
							if(!empty($s['id']))
							{
								TeacherAvailabiltyOfSlotsDetails::where('teacher_availabilty_of_slots_id',$s['id'])->delete();
							}
						}
						TeacherAvailabiltyOfSlots::where("user_id",$userId)->delete();
					}
					$i=1;
					foreach($requestData as $k =>$d)
					{
						$TeacherAvailabiltyOfSlots = new TeacherAvailabiltyOfSlots();
						$TeacherAvailabiltyOfSlots->role_id =  $role_id;
						$TeacherAvailabiltyOfSlots->user_id =  $userId;
						$TeacherAvailabiltyOfSlots->day =  $i;
						$TeacherAvailabiltyOfSlots->save();
						$inserted_Id = $TeacherAvailabiltyOfSlots->id;
						if(!empty($requestData->$k) && !is_null($requestData->$k))
						{
							$details  =$requestData->$k;
							$details_count = count($details);
							for($dc=0;$dc<$details_count;$dc++)
							{
								$TeacherAvailabiltyOfSlotsDetails = new TeacherAvailabiltyOfSlotsDetails();
								$TeacherAvailabiltyOfSlotsDetails->teacher_availabilty_of_slots_id=$inserted_Id;
								$TeacherAvailabiltyOfSlotsDetails->start_time=isset($details[$dc]->start_time) ? $details[$dc]->start_time : '';
								$TeacherAvailabiltyOfSlotsDetails->end_time=isset($details[$dc]->end_time) ? $details[$dc]->end_time :'';
								$TeacherAvailabiltyOfSlotsDetails->teacher_id=$userId;
								$TeacherAvailabiltyOfSlotsDetails->save();
							}
						}
						$i++;
					}
					$message = "Teacher Availabilty Details Added Successfully.";
					return response()->json(['statusCode' => 200, 'message' => $message]);
				}
			}
			else {
					$msg = "User Not Found ";
					return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
			}
		}
	}

	 public function get_AvailabiltyOfSlots(){
    	$user = JWTAuth::user();

    	 $get_TeacherAvailabiltyOfSlots =TeacherAvailabiltyOfSlots::where("user_id",$user->id)->get();

    	 if(!is_null($get_TeacherAvailabiltyOfSlots))
    	 {
    	 	foreach($get_TeacherAvailabiltyOfSlots as $value)
    	 	{
    	 		$value->teacher_availabilty_of_slots_details = $value->TeacherAvailabiltyOfSlotsDetailsData;
    	 		unset($value->teacher_availabilty_of_slots_details_data);
    	 	}
    	 	return response()->json(['statusCode' => 200, "message"=>"", 'data' => $get_TeacherAvailabiltyOfSlots]);
    	 }
    	 else
    	 {
    	 		$message = "No Record Found.";
				return response()->json(['statusCode' => 400, 'message' => $message,'data'=>""]);
    	 }
	}
	//enterteacherdetailScreen 
	public function enterteacherdetailScreen(Request $request){
		if($request->all()){
			$user = JWTAuth::user();

			//$bearerToken = $request->bearerToken();
			$userId = $user->id;
			$userStatus =$user->status; //$this->isUserActive($userId,$bearerToken);
			if ($userStatus == 0) {
				$message = "User not Available.";
				return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("user" => array("id"=>""))]);
			}
			else
			{
				$select_type="";
				$data =[];
		
				$AllData['classes'] = StudentClass::select('id','class_name')->where("deleted",0)->where("status",1)->get();
				$AllData['category'] = Category::select('id','class_id','name')->where("deleted",0)->where("status",1)->get();
				$message = "";
				$select_type.="All";
				if(!empty($AllData['classes']))
				{
					foreach($AllData['classes'] as $cls){
						$cls['subject_Details'] = Subject::select('id','class_id','title')->where("class_id",$cls->id)->where('status',1)->get();

					}
				}
				if(!empty($AllData['category']))
				{
					foreach($AllData['category'] as $cat){
						$cat['course_Details'] = Courses::select('id','name','category_id')->where("category_id",$cat->id)->where('status',1)->get();
					}
				}
				array_push($data,$AllData);
		
			return response()->json(['statusCode' => 200, 'select_type'=>$select_type,'message' => $message, 'data' => $data]);
			}
		}
	}
	//enterteacherdetails
	public function enterteacherdetails(Request $request){
		if($request->all()){
			$user = JWTAuth::user();

			//$bearerToken = $request->bearerToken();
			$userId = $user->id;

			//			$userStatus = $this->isUserActive($userId,$bearerToken);
			if ($user->status == 0) {
				$message = "User not Available.";
				return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("user" => array("id"=>""))]);
			}
			else
			{

				$requestData = json_decode($request->data);
				if(!empty($requestData) && isset($requestData))
				{
					
					TeachingDetails::where("user_id",$userId)->delete();
					foreach ($requestData as $key => $value) {
						
							TeachingDetails::create(array(
								"user_id"=>$userId,
								"class_or_category_id"=>$value->class_or_category_id,
								"subject_or_course_id"=>$value->subject_or_course_id,
								"type"=>$value->type,
								"fees"=>$value->fees,

								"currency_symbol"=>$value->currency_symbol,
							));
					}
					$message = "Teacher Details Added Successfully.";
					return response()->json(['statusCode' => 200, 'message' => $message,"userId"=>$request->userId]);
				}
			}
		}
	}
	//getteacherAssign
	public function getteacherAssign(Request $request){
			if($request->all()){
			$user = JWTAuth::user();
			$class_id_arr=[];
			$subject_id_arr=[];
		
			$teacher_slot_details_arr=[];
			$data = [];
			$student_Booking_Frees_arr = [];
			$userId = $user->id;
			$userStatus =$user->status;
			if ($userStatus == 0) {
				$message = "User not Available.";
				return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("user" => array("id"=>""))]);
			}
			else
			{
				$get_TeachingDetails = TeachingDetails::where("user_id",$user->id)->get();	
				$TeachingDetails_fees = $get_TeachingDetails[0]['fees'];
				//dd($TeachingDetails_fees);
					$subject_id_arr=[];
					$class_id_arr=[];
				if(!is_null($get_TeachingDetails))
				{
					foreach ($get_TeachingDetails as  $value) 
					{
						if($value->type==1)// class and  subject selected
						{
							$subject_id_arr[]=$value->subject_or_course_id;
							$class_id_arr[]=$value->class_or_category_id;
						}
						else if($value->type==2) //category and courses
						{
							$subject_id_arr[]=$value->subject_or_course_id;
							$class_id_arr[]=$value->class_or_category_id;
						}
					}

						$get_StudentApplyForDemo =  StudentApplyForDemo::join("student_booking_frees","student_booking_frees.studentapplyfordemo_id","=","studentapplyfordemo.id")-> whereIn("studentapplyfordemo.subject_id",$subject_id_arr)->get();
						//	dd($get_StudentApplyForDemo);
						// die;
						$response=array();
						$new_data = [];
			
						if(!is_null($get_StudentApplyForDemo))
						{
							foreach ($get_StudentApplyForDemo as $key => $value) {
								$student_start_time= $value->select_time;
								$student_end_time= $value->select_end_time;

								$student_select_price_range= $value->start_price_range;
								$student_end_select_range= $value->end_price_range;

								$timestamp = strtotime($value->created_at);
								$day = date('w', $timestamp);
								
								$get_TeacherAvailabiltyOfSlots = TeacherAvailabiltyOfSlots::join('teacher_availabilty_of_slots_details',"teacher_availabilty_of_slots_details.teacher_availabilty_of_slots_id","=","teacher_availabilty_of_slots.id")
								->join('teaching_details',"teaching_details.user_id","=","teacher_availabilty_of_slots.user_id")
									->where('teacher_availabilty_of_slots.day',$day)
									->where('teacher_availabilty_of_slots_details.start_time',">=",$student_start_time)
									->where('teacher_availabilty_of_slots_details.end_time',"<=",$student_end_time)
									->where("teacher_availabilty_of_slots_details.teacher_id",$user->id)
									->where("teacher_availabilty_of_slots.user_id",$user->id)
									->where("teaching_details.fees",">=",$student_select_price_range)
									->where("teaching_details.fees","<=",$student_end_select_range)
									->where("teacher_availabilty_of_slots_details.request_status",0)

									->select([
										'teacher_availabilty_of_slots_details.request_status as request_status',
										'teacher_availabilty_of_slots_details.id as teacher_availabilty_of_slots_details_id',
										'teacher_availabilty_of_slots_details.zoom_link as teqcher_zoom_link',
										'teacher_availabilty_of_slots_details.start_time',
										'teacher_availabilty_of_slots_details.end_time',
										'teacher_availabilty_of_slots.*','teaching_details.fees as teaching_details_fees'])->first();

								//	print_r($get_TeacherAvailabiltyOfSlots);
								// 	dd($get_TeacherAvailabiltyOfSlots);
								if($get_TeacherAvailabiltyOfSlots)
								{

									$value->teacher_availabilty_of_slots_details_id = $get_TeacherAvailabiltyOfSlots->teacher_availabilty_of_slots_details_id;
									$value->student_name=$value->getUser->name;
									$value->preferred_topic = $value->preferred_topic;
								

									$response[$key]['select_date']=$value->select_date;
									$response[$key]['start_time']=$get_TeacherAvailabiltyOfSlots->start_time;
									$response[$key]['end_time']=$get_TeacherAvailabiltyOfSlots->end_time;
									$response[$key]['request_status']=$get_TeacherAvailabiltyOfSlots->request_status;
								

									$response[$key]['student_apply_for_demo_id']=$value->id;
									$response[$key]['teacher_availabilty_of_slots_details_id']=$get_TeacherAvailabiltyOfSlots->teacher_availabilty_of_slots_details_id;
									$response[$key]['student_name']=$value->getUser->name;
									$response[$key]['preferred_topic'] = $value->preferred_topic;
									$response[$key]['zoom_link'] = isset($get_TeacherAvailabiltyOfSlots->teqcher_zoom_link) ? $get_TeacherAvailabiltyOfSlots->teqcher_zoom_link : $user->class_url;

									if($value->class_id!=0)
									{
										$value->class_name = $value->class->class_name;
										$value->subject_name = $value->subject->title;

										$response[$key]['class_name'] = $value->class->class_name;
										$response[$key]['subject_name'] = $value->subject->title;
										unset($value->class);
										unset($value->subject);
									}
									 if($value->course_id!=0)
									{

										$value->category_name = $value->category->name;
										$value->courses_name= $value->courses->name;
										

										$response[$key]['category_name'] = $value->category->name;
										$response[$key]['courses_name'] = $value->courses->name;
										unset($value->category);
										unset($value->courses);	
										unset($value->get_user);
									}
										array_push($new_data,$response[$key]);
								}

							}
						}


						//get All assign Apply for demo
						$get_teacher_availabilty_of_slots_details = TeacherAvailabiltyOfSlotsDetails::join('studentapplyfordemo','studentapplyfordemo.teacher_availabilty_of_slots_details_id','=','teacher_availabilty_of_slots_details.id')->select(
							[

								'studentapplyfordemo.class_id',
								'studentapplyfordemo.subject_id',
								'studentapplyfordemo.category_id',
								'studentapplyfordemo.course_id',
								'studentapplyfordemo.user_id as student_id' ,
								'studentapplyfordemo.class_start_time',
								'studentapplyfordemo.class_start_status',
								'studentapplyfordemo.class_end_time',
								'studentapplyfordemo.class_end_status',
								'studentapplyfordemo.cancel_by_teacher_status',

								'teacher_availabilty_of_slots_details.zoom_link',
								'studentapplyfordemo.id as studentapplyfordemo_id',
								'teacher_availabilty_of_slots_details.teacher_availabilty_of_slots_id',
								'teacher_availabilty_of_slots_details.teacher_id',
								'teacher_availabilty_of_slots_details.id as teacher_availabilty_of_slots_details_id',
								'teacher_availabilty_of_slots_details.start_time',
								'teacher_availabilty_of_slots_details.end_time'
							])->where('teacher_availabilty_of_slots_details.teacher_id',$user->id)
								->get();

								if(!empty($get_teacher_availabilty_of_slots_details))
								{
									foreach ($get_teacher_availabilty_of_slots_details as $key => $value) {
										if($value->class_id!=0)
										{
											$value->class_name = StudentClass::where('id',$value->class_id)->first()->class_name;
											$value->subject_name = Subject::where('id',$value->subject_id)->first()->title;
										}
										else if($value->category_id!=0)
										{
											$value->category_name = Category::where("id",$value->class_or_category_id)->first()->name;
											$value->courses_name = Courses::where("id",$value->subject_or_course_id)->first()->name;
										}	
										$value->update_zoom_link= isset($value->zoom_link) ? $value->zoom_link :$user->class_url;
									}
								}
						}

				}
				
				if(!empty($new_data))
				{
					return response()->json(['statusCode'=>200,"message"=>"","data"=>$new_data,'assign_by_admin'=>$get_teacher_availabilty_of_slots_details]);
				}
				else
				{
					return response()->json(['statusCode'=>400,"message"=>"No Record Found","data"=>'']);

				}
			}
		
	}
	//teacher_demo_class_start
	public function teacher_demo_class_start(Request $request){

		$user = JWTAuth::user();
		if($request->all())
		{
			$userStatus =$user->status;
			if ($userStatus == 0) {
				$message = "User not Available.";
				return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("user" => array("id"=>""))]);
			}
			else
			{
				$id =$request->studentapplyfordemo_id;
				$studentApplyForDemo = StudentApplyForDemo::findOrFail($id);
				$studentApplyForDemo->class_start_time=date('Y-m-d H:i:s');
				$studentApplyForDemo->class_start_status=1;
				if($studentApplyForDemo->save())
				{
					$message = "Student Demo Class Started Successfully.";
					return response()->json(['statusCode' => 200, 'message' => $message]);
				}		
				else
				{
					return response()->json(['statusCode' => 204, 'message' =>"Student Demo Class Started Failed"] );
				}
			}
		}
	}

	//teacher_demo_class_end 
	public function teacher_demo_class_end(Request $request)
	{
		$user = JWTAuth::user();
		if($request->all())
		{
			$userStatus =$user->status;
			if ($userStatus == 0) {
				$message = "User not Available.";
				return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("user" => array("id"=>""))]);
			}
			else
			{
				$id =$request->studentapplyfordemo_id;
				$studentApplyForDemo = StudentApplyForDemo::findOrFail($id);
				$studentApplyForDemo->class_end_time=date('Y-m-d H:i:s');
				$studentApplyForDemo->class_end_status=1;
				if($studentApplyForDemo->save())
				{
					$message = "Student Demo Class End Successfully.";
					return response()->json(['statusCode' => 200, 'message' => $message]);
				}		
				else
				{
					return response()->json(['statusCode' => 204, 'message' =>"Student Demo Class End Failed"] );
				}
			}
		}
	}

		//teacher_demo_class_cancel
	 public function teacher_demo_class_cancel(Request $request)
	 {
	 	$user = JWTAuth::user();
	 	if($request->all())
	 	{
	 		$userStatus =$user->status;
			if ($userStatus == 0) {
				$message = "User not Available.";
				return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("user" => array("id"=>""))]);
			}
			else
			{
				$id =$request->studentapplyfordemo_id;
				$studentApplyForDemo = StudentApplyForDemo::findOrFail($id);

				
				$studentApplyForDemo->cancel_by_teacher_status=1;
				if($studentApplyForDemo->save())
				{
					$message = "Student Demo Cancel Successfully.";
					return response()->json(['statusCode' => 200, 'message' => $message]);
				}		
				else
				{
					return response()->json(['statusCode' => 204, 'message' =>"Student Demo Class Cancel Failed"] );
				}
			}
	 	}
	 }


	 public function reschedule_demo_by_teacher(Request $request)
	 {
	 	$user = JWTAuth::user();
	
		if($request->all())
		{
			$userStatus =$user->status;
			if ($userStatus == 0) {
				$message = "User not Available.";
				return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("user" => array("id"=>""))]);
			}
			else
			{
				$reschedule_status = false;
				$id = $request->id;
				$get_TeachingDetails = TeachingDetails::where("user_id",$user->id)->get();	

				if(!is_null($get_TeachingDetails))
				{
					foreach ($get_TeachingDetails as  $value) 
					{
						if($value->type==1)// class and  subject selected
						{
							$subject_id_arr[]=$value->subject_or_course_id;
							$class_id_arr[]=$value->class_or_category_id;
						}
						else if($value->type==2) //category and courses
						{
							$subject_id_arr[]=$value->subject_or_course_id;
							$class_id_arr[]=$value->class_or_category_id;
						}
					}
				}

				$studentApplyForDemo = StudentApplyForDemo::findOrFail($id);
				if(!empty($studentApplyForDemo->teacher_availabilty_of_slots_details_id))
				{
				 $get_TeacherAvailabiltyOfSlotsDetails=	TeacherAvailabiltyOfSlotsDetails::findOrFail($studentApplyForDemo->teacher_availabilty_of_slots_details_id);
				 
				  $teacher_current_start_time = $get_TeacherAvailabiltyOfSlotsDetails->start_time;
				  $teacher_current_end_time = $get_TeacherAvailabiltyOfSlotsDetails->end_time;
					$current_time= date('H:i');
					$before_3hrsTime = date('H:i',strtotime('-3 hour',strtotime($teacher_current_start_time)));

				if($before_3hrsTime==$current_time)
				{
					$get_StudentApplyForDemo= StudentBookingFrees::where('studentapplyfordemo_id',$studentApplyForDemo->id)->where('select_time',"!=",$teacher_current_start_time)->where('select_end_time',"!=",$teacher_current_end_time)->get();
				

					if($get_StudentApplyForDemo)
					{
						foreach($get_StudentApplyForDemo as $key => $value) 
						{
						 		$student_start_time= $value->select_time;
								$student_end_time= $value->select_end_time;

								$timestamp = strtotime($value->created_at);
								 $day = date('w', $timestamp);
							
								$get_TeacherAvailabiltyOfSlots=	TeacherAvailabiltyOfSlotsDetails::join("teacher_availabilty_of_slots","teacher_availabilty_of_slots_details.teacher_availabilty_of_slots_id","=","teacher_availabilty_of_slots.id")
								->where("teacher_availabilty_of_slots.day",$day)
								->where("teacher_availabilty_of_slots_details.start_time",">=",$student_start_time)
								->where("teacher_availabilty_of_slots_details.end_time","<=",$student_end_time)
								->where('teacher_availabilty_of_slots_details.request_status',"!=",2)
								->where('teacher_availabilty_of_slots_details.teacher_id',"=",$user->id)
								->select([
									'teacher_availabilty_of_slots_details.start_time',
									'teacher_availabilty_of_slots_details.end_time',
									'teacher_availabilty_of_slots_details.id as teacher_availabilty_of_slots_details_id',
									'teacher_availabilty_of_slots.*'])
								->get();
								if(!empty($get_TeacherAvailabiltyOfSlots))
								{
									foreach($get_TeacherAvailabiltyOfSlots as $val)
									{
										$getTeacherAvailabiltyOfSlotsDetailsUpdate =  TeacherAvailabiltyOfSlotsDetails::findOrFail($val->teacher_availabilty_of_slots_details_id);
										$getTeacherAvailabiltyOfSlotsDetailsUpdate->reschedule_status=1;
										$getTeacherAvailabiltyOfSlotsDetailsUpdate->save();
										$reschedule_status= true;
									}
								}
						}

					}	
				}


					if($reschedule_status==true)
					{
						$message = "Reschedule  Update Successfully";
						return response()->json(['statusCode' => 200, 'message' => $message, 'data' => $reschedule_status]);

					}
					else
					{
						$message = "Reschedule Time Not Available";
						return response()->json(['statusCode' => 203, 'message' => $message, 'data' => $reschedule_status]);
					}
				}
			}
		}
	 }

	 //upcomming_request
	 public function upcomming_request(Request $request)
	 {
	 	
			$user = JWTAuth::user();
			$class_id_arr=[];
			$subject_id_arr=[];
		
			$teacher_slot_details_arr=[];
			$data = [];
			$student_Booking_Frees_arr = [];
			$userId = $user->id;
			$userStatus =$user->status;
				$response=array();
				$new_data = [];
				

			if ($userStatus == 0) {
				$message = "User not Available.";
				return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("user" => array("id"=>""))]);
			}
			else
			{
				$get_TeachingDetails = TeachingDetails::where("user_id",$user->id)->get();	
				$TeachingDetails_fees = $get_TeachingDetails[0]['fees'];
					$subject_id_arr=[];
					$class_id_arr=[];
				//	dd($get_TeachingDetails);
				if(!is_null($get_TeachingDetails))
				{
					foreach ($get_TeachingDetails as  $value) 
					{
						if($value->type==1)// class and  subject selected
						{
							$subject_id_arr[]=$value->subject_or_course_id;
							$class_id_arr[]=$value->class_or_category_id;
						}
						else if($value->type==2) //category and courses
						{
							$subject_id_arr[]=$value->subject_or_course_id;
							$class_id_arr[]=$value->class_or_category_id;
						}
					}

					$get_StudentApplyForDemo =  StudentApplyForDemo::join("student_booking_frees","student_booking_frees.studentapplyfordemo_id","=","studentapplyfordemo.id")
					->whereIn("studentapplyfordemo.subject_id",$subject_id_arr)
								->select(['studentapplyfordemo.*','student_booking_frees.select_date','student_booking_frees.studentapplyfordemo_id'])
					->get();
					
						if(!is_null($get_StudentApplyForDemo))
						{
							foreach ($get_StudentApplyForDemo as $key => $value) 
							{
								if($value->select_date > date('Y-m-d'))
								 {
									
									$student_start_time= $value->select_time;
									$student_end_time= $value->select_end_time;

									$student_select_price_range= $value->start_price_range;
									$student_end_select_range= $value->end_price_range;

									$timestamp = strtotime($value->created_at);
									$day = date('w', $timestamp);
									
									$get_TeacherAvailabiltyOfSlots = TeacherAvailabiltyOfSlots::join('teacher_availabilty_of_slots_details',"teacher_availabilty_of_slots_details.teacher_availabilty_of_slots_id","=","teacher_availabilty_of_slots.id")
										->join('teaching_details',"teaching_details.user_id","=","teacher_availabilty_of_slots.user_id")
										->where('teacher_availabilty_of_slots.day',$day)
									//	->where('teacher_availabilty_of_slots_details.start_time',">=",$student_start_time)
									//	->where('teacher_availabilty_of_slots_details.end_time',"<=",$student_end_time)
										->where("teacher_availabilty_of_slots_details.teacher_id",$user->id)
										->where("teacher_availabilty_of_slots.user_id",$user->id)
										->where("teaching_details.fees",">=",$student_select_price_range)
										->where("teaching_details.fees","<=",$student_end_select_range)
										->where("teacher_availabilty_of_slots_details.request_status",2)
										->select([
											'teacher_availabilty_of_slots_details.request_status as request_status',
											'teacher_availabilty_of_slots_details.id as teacher_availabilty_of_slots_details_id',
											'teacher_availabilty_of_slots_details.zoom_link as teqcher_zoom_link',
											'teacher_availabilty_of_slots_details.start_time',
											'teacher_availabilty_of_slots_details.end_time',
											'teacher_availabilty_of_slots.*','teaching_details.fees as teaching_details_fees'])->first();

									//	print_r($get_TeacherAvailabiltyOfSlots);
									// 	dd($get_TeacherAvailabiltyOfSlots);
									if($get_TeacherAvailabiltyOfSlots)
									{

										$value->teacher_availabilty_of_slots_details_id = $get_TeacherAvailabiltyOfSlots->teacher_availabilty_of_slots_details_id;
										$value->student_name=$value->getUser->name;
										$value->preferred_topic = $value->preferred_topic;
									

										$response[$key]['select_date']=$value->select_date;
										$response[$key]['start_time']=$get_TeacherAvailabiltyOfSlots->start_time;
										$response[$key]['end_time']=$get_TeacherAvailabiltyOfSlots->end_time;
										$response[$key]['request_status']=$get_TeacherAvailabiltyOfSlots->request_status;
									

										$response[$key]['student_apply_for_demo_id']=$value->id;
										$response[$key]['teacher_availabilty_of_slots_details_id']=$get_TeacherAvailabiltyOfSlots->teacher_availabilty_of_slots_details_id;
										$response[$key]['student_name']=$value->getUser->name;
										$response[$key]['preferred_topic'] = $value->preferred_topic;
										$response[$key]['zoom_link'] = isset($get_TeacherAvailabiltyOfSlots->teqcher_zoom_link) ? $get_TeacherAvailabiltyOfSlots->teqcher_zoom_link : $user->class_url;

										if($value->class_id!=0)
										{
											$value->class_name = $value->class->class_name;
											$value->subject_name = $value->subject->title;

											$response[$key]['class_name'] = $value->class->class_name;
											$response[$key]['subject_name'] = $value->subject->title;
											unset($value->class);
											unset($value->subject);
										}
										if($value->course_id!=0)
										{
											$value->category_name = $value->category->name;
											$value->courses_name= $value->courses->name;

											$response[$key]['category_name'] = $value->category->name;
											$response[$key]['courses_name'] = $value->courses->name;
											unset($value->category);
											unset($value->courses);	
											unset($value->get_user);
										}
										array_push($new_data,$response[$key]);
									}
								}
							}
						}
				}
			}
			if(!empty($new_data))
				{
					return response()->json(['statusCode'=>200,"message"=>"","data"=>$new_data]);
				}
				else
				{
					return response()->json(['statusCode'=>400,"message"=>"No Record Found","data"=>'']);

				}
	
	 }
	 //past_request_all
	 public function past_request_all(Request $request)
	 {
	 	
			$user = JWTAuth::user();
			$class_id_arr=[];
			$subject_id_arr=[];
		
			$teacher_slot_details_arr=[];
			$data = [];
			$student_Booking_Frees_arr = [];
			$userId = $user->id;
			$userStatus =$user->status;
				$response=array();
				$new_data = [];
				

			if ($userStatus == 0) {
				$message = "User not Available.";
				return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("user" => array("id"=>""))]);
			}
			else
			{
				$get_TeachingDetails = TeachingDetails::where("user_id",$user->id)->get();	
				$TeachingDetails_fees = $get_TeachingDetails[0]['fees'];
					$subject_id_arr=[];
					$class_id_arr=[];
				//	dd($get_TeachingDetails);
				if(!is_null($get_TeachingDetails))
				{
					foreach ($get_TeachingDetails as  $value) 
					{
						if($value->type==1)// class and  subject selected
						{
							$subject_id_arr[]=$value->subject_or_course_id;
							$class_id_arr[]=$value->class_or_category_id;
						}
						else if($value->type==2) //category and courses
						{
							$subject_id_arr[]=$value->subject_or_course_id;
							$class_id_arr[]=$value->class_or_category_id;
						}
					}

					$get_StudentApplyForDemo =  StudentApplyForDemo::join("student_booking_frees","student_booking_frees.studentapplyfordemo_id","=","studentapplyfordemo.id")
					->whereIn("studentapplyfordemo.subject_id",$subject_id_arr)
								->select(['studentapplyfordemo.*','student_booking_frees.select_date','student_booking_frees.studentapplyfordemo_id'])
					->get();
					
						if(!is_null($get_StudentApplyForDemo))
						{
							foreach ($get_StudentApplyForDemo as $key => $value) 
							{
							
								if($value->select_date < date('Y-m-d'))
								 {
									
									$student_start_time= $value->select_time;
									$student_end_time= $value->select_end_time;

									$student_select_price_range= $value->start_price_range;
									$student_end_select_range= $value->end_price_range;

									$timestamp = strtotime($value->created_at);
									$day = date('w', $timestamp);
									
									$get_TeacherAvailabiltyOfSlots = TeacherAvailabiltyOfSlots::join('teacher_availabilty_of_slots_details',"teacher_availabilty_of_slots_details.teacher_availabilty_of_slots_id","=","teacher_availabilty_of_slots.id")
										->join('teaching_details',"teaching_details.user_id","=","teacher_availabilty_of_slots.user_id")
										->where('teacher_availabilty_of_slots.day',$day)
									//	->where('teacher_availabilty_of_slots_details.start_time',">=",$student_start_time)
									//	->where('teacher_availabilty_of_slots_details.end_time',"<=",$student_end_time)
										->where("teacher_availabilty_of_slots_details.teacher_id",$user->id)
										->where("teacher_availabilty_of_slots.user_id",$user->id)
										->where("teaching_details.fees",">=",$student_select_price_range)
										->where("teaching_details.fees","<=",$student_end_select_range)
										->where("teacher_availabilty_of_slots_details.request_status",2)
										->select([
											'teacher_availabilty_of_slots_details.request_status as request_status',
											'teacher_availabilty_of_slots_details.id as teacher_availabilty_of_slots_details_id',
											'teacher_availabilty_of_slots_details.zoom_link as teqcher_zoom_link',
											'teacher_availabilty_of_slots_details.start_time',
											'teacher_availabilty_of_slots_details.end_time',
											'teacher_availabilty_of_slots.*','teaching_details.fees as teaching_details_fees'])->first();

									//	print_r($get_TeacherAvailabiltyOfSlots);
									// 	dd($get_TeacherAvailabiltyOfSlots);
									if($get_TeacherAvailabiltyOfSlots)
									{

										$value->teacher_availabilty_of_slots_details_id = $get_TeacherAvailabiltyOfSlots->teacher_availabilty_of_slots_details_id;
										$value->student_name=$value->getUser->name;
										$value->preferred_topic = $value->preferred_topic;
									

										$response[$key]['select_date']=$value->select_date;
										$response[$key]['start_time']=$get_TeacherAvailabiltyOfSlots->start_time;
										$response[$key]['end_time']=$get_TeacherAvailabiltyOfSlots->end_time;
										$response[$key]['request_status']=$get_TeacherAvailabiltyOfSlots->request_status;
									

										$response[$key]['student_apply_for_demo_id']=$value->id;
										$response[$key]['teacher_availabilty_of_slots_details_id']=$get_TeacherAvailabiltyOfSlots->teacher_availabilty_of_slots_details_id;
										$response[$key]['student_name']=$value->getUser->name;
										$response[$key]['preferred_topic'] = $value->preferred_topic;
										$response[$key]['zoom_link'] = isset($get_TeacherAvailabiltyOfSlots->teqcher_zoom_link) ? $get_TeacherAvailabiltyOfSlots->teqcher_zoom_link : $user->class_url;

										if($value->class_id!=0)
										{
											$value->class_name = $value->class->class_name;
											$value->subject_name = $value->subject->title;

											$response[$key]['class_name'] = $value->class->class_name;
											$response[$key]['subject_name'] = $value->subject->title;
											unset($value->class);
											unset($value->subject);
										}
										if($value->course_id!=0)
										{
											$value->category_name = $value->category->name;
											$value->courses_name= $value->courses->name;

											$response[$key]['category_name'] = $value->category->name;
											$response[$key]['courses_name'] = $value->courses->name;
											unset($value->category);
											unset($value->courses);	
											unset($value->get_user);
										}
										array_push($new_data,$response[$key]);
									}
								}
							}
						}
				}
			}
			if(!empty($new_data))
				{
					return response()->json(['statusCode'=>200,"message"=>"","data"=>$new_data]);
				}
				else
				{
					return response()->json(['statusCode'=>400,"message"=>"No Record Found","data"=>'']);

				}
	
	 }

	 public function reschedule_demo_by_teacherold(Request $request)
	 {
	 	$user = JWTAuth::user();
		if($request->all())
		{
			$userStatus =$user->status;
			if ($userStatus == 0) {
				$message = "User not Available.";
				return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("user" => array("id"=>""))]);
			}
			else
			{
				
				$id = $request->id;
				$studentApplyForDemo = StudentApplyForDemo::findOrFail($id);

				$studentBookingFrees = StudentBookingFrees::where("studentapplyfordemo_id",$id)->get();
			
				$newArra = array(
					'user_id'=>$studentApplyForDemo->user_id,
					'class_id'=>$studentApplyForDemo->class_id,
					'course_id'=>$studentApplyForDemo->course_id,
					'subject_id'=>$studentApplyForDemo->subject_id,
					'category_id'=>$studentApplyForDemo->category_id,
					'status'=>$studentApplyForDemo->status,
					'apply_status'=>$studentApplyForDemo->apply_status,
					'start_price_range'=>$studentApplyForDemo->start_price_range,
					'end_price_range'=>$studentApplyForDemo->end_price_range,
					'zoom_link'=>$studentApplyForDemo->zoom_link
				);
				$inserted=	StudentApplyForDemo::create($newArra)->id;
				$studentApplyForDemo->reschedule_status=1;
				$studentApplyForDemo->reschedule_id = $inserted;
				$studentApplyForDemo->save();


				if(!empty($studentBookingFrees))
				{
					foreach ($studentBookingFrees as $key => $value) {
						$arr = array(
							'user_id'=>$value->user_id,
							'studentapplyfordemo_id'=>$inserted,
							'select_date'=>$value->select_date,
							'select_time'=>$value->select_time,
							'select_end_time'=>$value->select_end_time,
							'preferred_topic'=>$value->preferred_topic,
							'deleted'=>$value->deleted,
							'status'=>$value->status,
							'apply_status'=>$value->apply_status,
							'currency'=>$value->currency,
						);
						$insertedIds=	StudentBookingFrees::create($arr)->id;
						if($insertedIds)
						{
							StudentBookingFrees::where("id",$value->id)->update(['reschedule_status'=>1,'reschedule_id'=>$insertedIds]);
						}
					}
				}



				if($studentApplyForDemo->save())
				{
					$message = "Reschedule Demo Successfully.";
					return response()->json(['statusCode' => 200, 'message' => $message, 'data' => StudentApplyForDemo::findOrFail($id)]);
				}
				else
				{
					$message = "Reschedule Demo Failed.";
					return response()->json(['statusCode' => 203, 'message' => $message, 'data' => ""]);
				}

			}
		}
	 }
	//apply_for_demo_class
	public function apply_for_demo_class(Request $request)
	{
		if($request->all())
		{
			$user = JWTAuth::user();
		
			$id  =$request->teacher_availabilty_of_slots_details_id;
			$zoom_link  =$request->zoom_link;
			$get_teacher_slot_details= TeacherAvailabiltyOfSlotsDetails::findOrFail($id);
			$get_teacher_slot_details->zoom_link = isset($zoom_link)  ? $zoom_link :$get_teacher_slot_details->zoom_link;
			$get_teacher_slot_details->request_status =1;
			$get_teacher_slot_details->save();

			if($get_teacher_slot_details->request_status==1)
			{
					$message = "Apply For Demo Successfully.";
					return response()->json(['statusCode' => 200, 'message' => $message,"data"=>$get_teacher_slot_details]);
			}
			else
			{
				$message = "Apply For Demo Failed.";
					return response()->json(['statusCode' => 204, 'message' => $message,"data"=>'']);
			}
			

		}	
	}

	public function getteacherAssignold(Request $request){
		if($request->all()){
			$user = JWTAuth::user();

			//$bearerToken = $request->bearerToken();
			$userId = $user->id;
			$userStatus =$user->status; //$this->isUserActive($userId,$bearerToken);
			if ($userStatus == 0) {
				$message = "User not Available.";
				return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("user" => array("id"=>""))]);
			}
			else
			{
				$TeachingDetails =  TeachingDetails::where("user_id",$userId)->get();
				if(!empty($TeachingDetails))
				{
					foreach($TeachingDetails as $teach)
					{
						$get_TeacherAvailabiltyOfSlots = TeacherAvailabiltyOfSlots::where("user_id",$userId)->get();
						if(!is_null($get_TeacherAvailabiltyOfSlots))
						{
							$teach->TeacherAvailabiltyOfSlots = $get_TeacherAvailabiltyOfSlots;
						}
						if(!is_null($teach->TeacherAvailabiltyOfSlots))
						{
							foreach($teach->TeacherAvailabiltyOfSlots as $t)
							{
								$get_TeacherAvailabiltyOfSlotsDetails = TeacherAvailabiltyOfSlotsDetails::where("teacher_availabilty_of_slots_id",$t->id)->get();	
								$t->TeacherAvailabiltyOfSlotsDetails = isset($get_TeacherAvailabiltyOfSlotsDetails) ? $get_TeacherAvailabiltyOfSlotsDetails :'';
							}
						}
					}
					return response()->json(['statusCode'=>200,"message"=>"","data"=> $TeachingDetails]);

				}
			}
		}
	}

	public function getuserDetail($userid)
	{
		$user = User::where('id', '=', $userid)->where("deleted",0)->first();
		if ($user->role_id == 2) {
			$role = "Teacher";
		}else if ($user->role_id == 6) {
			$role = "User";
		}else{
			$role = "Administrator";
		}
		$data = array(
			'userId' 		 => ($user->id) ? $user->id : '',
			//'role_id' 		 => ($role) ? $role : '',
			'name' 			 => ($user->name) ? $user->name : '',
			'email' 		 => ($user->email) ? $user->email : '',
			'phone' 		 => ($user->phone) ? $user->phone : '',
			'gender' 		 => ($user->gender) ? $user->gender : '',
			'dob' 			 => ($user->dob) ? date('d/m/Y', strtotime($user->dob)) : '',
			'class_name' 	 => ($user->class_name) ? $user->class_name : '',
			'school_college' => ($user->school_college) ? $user->school_college : '',
			'state' 		 => ($user->state) ? $user->state : '',
			'city' 			 => ($user->city) ? $user->city : '',
			'address' 		 => ($user->address) ? $user->address : '',
			'postal_code' 	 => ($user->postal_code) ? $user->postal_code : '',
			'image' 		 => ($user->image) ? asset('upload/profile') . '/' . $user->image : '',
			'api_token' 	 => ($user->api_token) ? $user->api_token : '',
			'otp_match' 	 => ($user->otp_match) ? $user->otp_match : '',
		);
		return $data;
	}
	
	

	public function isUserActive($userId,$bearerToken=NULL)
	{
		if ($userId > 0) {
			$user = User::where("id", $userId)->where("deleted", 0)->first();
			$userStatus = !empty($user) ? $user->status : 0;
			if ($userStatus == 1) {
				if ($bearerToken != $user->api_token) {
					$userStatus = 0;
				}
			}
		} else {
			$userStatus = 0;
		}
		return $userStatus;
	}

	public function teacherDetail(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("user" => array("id"=>""))]);
		}
		$user     = User::where("id", $userId)->where("status", 1)->first();
		$userdata = array();
		if (!empty($user)) {
			$courseArray = array();
			if(!empty($user->allow_courses)) {
				$allowedCourses = explode(",", $user->allow_courses);
				foreach ($allowedCourses as $course_id) {
					$course = Courses::where("id", $course_id)->first();
					$courseArray[] = array("course_id" => $course->id, "name" => $course->name);
				}
			}
			$studentClass = StudentClass::where("id", $user->class_id)->first();
			$class_name = !empty($studentClass) ? $studentClass->class_name : 'NA';
			$userdata['id']             = $user->id;
			$userdata['name']           = isset($user->name) ? $user->name : '';
			$userdata['email']          = isset($user->email) ? $user->email : '';
			$userdata['phone']          = isset($user->phone) ? $user->phone : '';
			$userdata['gender']         = isset($user->gender) ? $user->gender : '';
			$userdata['dob']            = !empty($user->dob) ? date('d/m/Y', strtotime($user->dob)) : '';
			$userdata['image']          = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
			$userdata['class_id']     = isset($user->class_id) ? $user->class_id : '';
			$userdata['class_name']     = isset($user->class_id) ? $class_name : '';
			$userdata['school_college'] = isset($user->school_college) ? $user->school_college : '';
			$userdata['state']          = isset($user->state) ? $user->state : '';
			if ($userdata['state']!='') {
				$states = State::where('state', $userdata['state'])->first();
			}
			$userdata['state_id']       = isset($states->id) ? $states->id : 0;
			$userdata['city']           = isset($user->city) ? $user->city : '';
			$userdata['address']        = isset($user->address) ? $user->address : '';
			$userdata['postal_code']    = isset($user->postal_code) ? $user->postal_code : '';
			$userdata['earned_point']   = 0;
			$userdata['created_at']     = $user->created_at->diffForHumans();
			$userdata['allowed_courses']= $courseArray;
			$message = "Get Teacher Details Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("user" => $userdata)]);
		} else {
			$message = "Teacher Details Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("user" => "")]);
		}
	}
	public function updateDetail(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("id"=>"")]);
		}
		$userArray = array();
		$validator = Validator::make($request->all(), [
			'userId' => 'required|numeric',
			'name' => 'required|regex:/^[\pL\s\-]+$/u',
			'email' => 'required|string|email',
			'phone' => 'required|min:10|max:10',
			'postal_code' => 'numeric|min:6',
		]);

		if($validator->fails()){
			$msg = $validator->messages()->first();
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}

		$dob_date = ($request->dob) ? $request->dob : '';
		$old_date = explode('/', $dob_date);
		if(isset($old_date) && $dob_date!=''){
			$new_date = $old_date[2].'-'.$old_date[1].'-'.$old_date[0];
		}

		$name 		    = ucwords($request->name);
		$email 			= $request->email;
		$phone 			= $request->phone;
		$gender 		= $request->gender;
		$dob 			= ($request->dob) ? $new_date : NULL;
		$class_name 	= $request->class_name;
		$school_college = $request->school_college;
		$state 			= $request->state;
		$city 			= $request->city;
		$address 		= $request->address;
		$postal_code 	= $request->postal_code;

		$checkUser = User::where('id', $userId)->first();
		if (!empty($checkUser)) {
			$checkPhone = User::where('id', '!=', $userId)->where('phone', $phone)->first();
			if (!empty($checkPhone)) {
				$msg = "Phone number already exists!";
				return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
			}
			$checkEmail = User::where('id', '!=', $userId)->where('email', $email)->first();
			if (!empty($checkEmail)) {
				$msg = "Email address already exists!";
				return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
			}
			
			$imagess = '';
			//print_r($_FILES); exit;
			if (isset($_FILES['image']['name']) && $_FILES['image']['name']!='') {
				$profileimagename = $_FILES['image']['name'];
				$tmpimage1 = $_FILES['image']['tmp_name'];
				$newprofileImage = rand(00000, 99999) . date('d') . $profileimagename;
				$location = "upload/profile/";
				move_uploaded_file($tmpimage1, $location . $newprofileImage);
				$url = 'upload/profile/' . $newprofileImage;
				$img = Image::make($url)->resize(200, 200);
				$imagess =  $img->basename;
			} else {
				$imagess = $checkUser->image;
			}
			$otp = rand(1111, 9999);
			$updateData = User::where('id', $userId)->update([
				'name' 			 => $name,
				//'email' 		 => $email,
				//'phone' 		 => $phone,
				'gender' 		 => $gender,
				'dob' 			 => $dob,
				'image' 		 => $imagess,
				'class_name' 	 => $class_name,
				'school_college' => $school_college,
				'state' 		 => $state,
				'city' 			 => $city,
				'address' 		 => $address,
				'postal_code' 	 => $postal_code,
				'otp_match' 	 => $otp,
				'updated_at' 	 => date('Y-m-d H:i:s')
			]);
			$userArray = $this->getuserDetail($userId);
			//echo $userArray['email']; die;
			if ($userArray['email']!=$email && $userArray['phone']!=$phone) {
				$changeStatus = 3;
				$newPhone = $phone;
				$newEmail = $email;
			} elseif ($userArray['email']!=$email && $userArray['phone']==$phone) {
				$changeStatus = 2;
				$newPhone = '';
				$newEmail = $email;
			} elseif ($userArray['email']==$email && $userArray['phone']!=$phone) {
				$changeStatus = 1;
				$newPhone = $phone;
				$newEmail = '';
			} else {
				$changeStatus = 0;
				$newPhone = '';
				$newEmail = '';
			}
			$userArray['changeStatus'] = $changeStatus;
			$userArray['newPhone'] = $newPhone;
			$userArray['newEmail'] = $newEmail;
			if ($changeStatus > 0) {
				//send otp
				$msg = 'Verification Otp Send, Please Check.';
				//$this->helper->sms($phone, $otp);
				//ak $this->helper->sendEmail($userArray['email'], 'BrainyWood: Verify OTP', $data = array('userName' => $userArray['name'], 'message' => '<p>Thank you for connecting at BrainyWood,</p><p>You have got successfully your OTP: ' . $otp . '</p>'));

			}
			$message = 'Updated Successfully';
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => $userArray]);
		} else {
			$msg = "Invalid User Id ";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}
	}
	public function updatePhoneEmail(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("id"=>"")]);
		}
		$userArray = array();
		$validator = Validator::make($request->all(), [
			'userId' => 'required|numeric',
			'otp' => 'required|numeric',
			// 'email' => 'string|email',
			// 'phone' => 'min:10|max:13',
		]);

		if($validator->fails()){
			$msg = $validator->messages()->first();
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}

		$phone 	= $request->phone;
		$email 	= $request->email;
		$otp 	= $request->otp;
		$checkUser = User::where('id', $userId)->where('otp_match', $otp)->first();
		if (!empty($checkUser)) {
			if (!empty($phone)) {
				$checkPhone = User::where('id', '!=', $userId)->where('phone', $phone)->first();
				if (empty($checkPhone)) {
					$updateData = User::where('id', $userId)->update([
						'phone' 		 => $phone,
						'updated_at' 	 => date('Y-m-d H:i:s')
					]);
				}
			}
			if (!empty($email)) {
				$checkEmail = User::where('id', '!=', $userId)->where('email', $email)->first();
				if (empty($checkEmail)) {
					$updateData = User::where('id', $userId)->update([
						'email' 		 => $email,
						'updated_at' 	 => date('Y-m-d H:i:s')
					]);
				}
			}
			$userArray = $this->getuserDetail($userId);
			$message = 'Updated Successfully';
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => $userArray]);
		} else {
			$msg = "OTP not matched!";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}
	}
	public function phoneEmailResendOtp(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("otp"=>"")]);
		}
		$phone   = $request->phone;
		$email   = $request->email;
		if (!empty($userId)) {
			$checkUser = User::where('id', $userId)->first();
			if ($checkUser) {
				$otpnumber = rand(1111, 9999);
				if($phone==''){
					$phone = $checkUser->phone;
				}
				$update = User::where('id', $userId)->update(['otp_match' => $otpnumber]);
				if ($update) {
					$returndata['otp'] = $otpnumber;
					$msg = 'Verification Otp Sent, Please Check.';
					/*
					//ak
					$this->helper->sms($phone, $otpnumber);
					$this->helper->sendEmail($checkUser->email, 'BrainyWood: Verify OTP', $data = array('userName' => $checkUser->name, 'message' => '<p>Thank you for connecting at BrainyWood,</p><p>You have got successfully your OTP: ' . $otpnumber . '</p>'));
					*/
					return response()->json(['statusCode' => 200, 'message' => $msg, "data" => $returndata]);
				} else {
					return response()->json(['statusCode' => 400, 'message' => 'Somthing Went Wrong!', 'data' => array("otp" => "")]);
				}
			} else {
				return response()->json(['statusCode' => 400, 'message' => 'Invaild User Id!', 'data' => array("otp" => "")]);
			}
		} else {
			return response()->json(['statusCode' => 400, 'message' => 'Wrong Paramenter Passed!', 'data' => array("otp" => "")]);
		}
	}
	// public function changePassword(Request $request)
	// {
	// 	$bearerToken = $request->bearerToken();
	// 	$userId   = $request->userId;
	// 	$userStatus = $this->isUserActive($userId,$bearerToken);
	// 	if ($userStatus == 0) {
	// 		$message = "User not Available.";
	// 		return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("id"=>"")]);
	// 	}
	// 	$validator = Validator::make($request->all(), [
	// 		'userId' => 'required|numeric',
	// 		'current_password' => 'required',
	// 		'password' => 'required|min:6',
	// 		'confirm_password' => 'required|min:6|max:20|same:password',
	// 	]);

	// 	if($validator->fails()){
	// 		$msg = $validator->messages()->first();
	// 		//return $this->sendError($validator->messages()->first());
	// 		return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
	// 	}

	// 	$currentPassword = $request->current_password;
	// 	$newPassword = $request->password;
	// 	$confirmPassword = $request->confirm_password;
	// 	$user = User::where("id", $userId)->first();
	// 	if (Hash::check($currentPassword, $user->password)) {
	// 		if ($newPassword != $confirmPassword) {
	// 			$msg = 'Password and Confirm password not matched!';
	// 			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
	// 		}
	// 		$users = User::findOrFail($userId);
	// 		$users->password = bcrypt($newPassword);
	// 		$users->userpass = $newPassword;
	// 		$users->save();

	// 		$msg = 'Password updated successfully.';
	// 		return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => array("id" => $userId)]);
	// 	} else {
	// 		$msg = 'Current Password not matched!';
	// 		return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
	// 	}
	// }

	public function getAllAssignmentsList(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("assignments" => [])]);
		}
		$course_id = $request->course_id;
		if (!empty($course_id)) {
			$assignments = Assignment::where("course_id", $course_id)->where("deleted", 0)->orderBy("sort_id", "ASC")->get();
		} else {
			$teacher = User::where("id", $userId)->first();
			$allowedCourses = $teacher->allow_courses;
			$allowedCourses = explode(",", trim($allowedCourses));
			$assignments = Assignment::whereIn("course_id", $allowedCourses)->where("deleted", 0)->orderBy("sort_id", "ASC")->get();
		}
		$assignmentdata = array();
		if (!empty($assignments)) {
			foreach ($assignments as $key => $val) {
				$assignmentdata[$key]['id'] 	= $val['id'];
				$assignmentdata[$key]['title'] 	= $val['title'];
			}
			$message = "Get All Assignments List Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("assignments" => $assignmentdata)]);
		} else {
			$message = "Assignments List Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("assignments" => "")]);
		}
	}
	public function assignmentSave(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("assignment_id" => "")]);
		}

		$validator = Validator::make($request->all(), [
			'course_id' => 'required',
			'title' => 'required',
			//'image_file' => 'mimes:jpeg,jpg,png,gif,webp',
			//'video_file' => 'mimes:mp4',
			'content' => 'required',
		]);

		if($validator->fails()){
			$msg = $validator->messages()->first();
			//return $this->sendError($validator->messages()->first());
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("assignment_id" => "")]);
		}

		$s3 = AWS::createClient('s3');
		$imageUrl = $videoUrl = $otherUrl = NULL;
		$image_file = $request->file('image_file');
		$video_file = $request->file('video_file');
		$another_file = $request->file('another_file');
			
		$destinationPath = public_path().'/upload/assignments/';
		if($image_file){
			$imageOriginalFile = $image_file->getClientOriginalName();
			$imageFilename = "assignment_0_".time().$imageOriginalFile;
			$image_file->move($destinationPath, $imageFilename);
			//S3 Upload
			$s3->putObject(array(
				'Bucket'     => env('AWS_BUCKET'),
				'Key'        => "assignment_data/".$imageFilename,
				'SourceFile' => $destinationPath.$imageFilename,
			));
			$imageUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $imageFilename;
			if(!empty($image_file) && file_exists( $destinationPath.$imageFilename )) {
				unlink( $destinationPath.$imageFilename );
			}
		}
		if($video_file){
			$videoOriginalFile = $video_file->getClientOriginalName();
			$videoFilename = "assignment_0_".time()."_org.mp4";
			$video_file->move($destinationPath, $videoFilename);
			//S3 Upload
			$s3->putObject(array(
				'Bucket'     => env('AWS_BUCKET'),
				'Key'        => "assignment_data/".$videoFilename,
				'SourceFile' => $destinationPath.$videoFilename,
			));
			$videoUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $videoFilename;
			if(!empty($video_file) && file_exists( $destinationPath.$videoFilename )) {
				unlink( $destinationPath.$videoFilename );
			}
		}
		if($another_file){
			$otherOriginalFile = $another_file->getClientOriginalName();
			$otherFilename = "assignment_other_".time().$otherOriginalFile;
			$another_file->move($destinationPath, $otherFilename);
			//S3 Upload
			$s3->putObject(array(
				'Bucket'     => env('AWS_BUCKET'),
				'Key'        => "assignment_data/".$otherFilename,
				'SourceFile' => $destinationPath.$otherFilename,
			));
			$otherUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $otherFilename;
			if(!empty($another_file) && file_exists( $destinationPath.$otherFilename )) {
				unlink( $destinationPath.$otherFilename );
			}
		}
		
		$assignment = new Assignment();
		$assignment->added_by = $userId;
		$assignment->course_id = $request->input('course_id');
		$assignment->title = $request->input('title');
		$assignment->content = $request->input('content');
		$assignment->image = $imageUrl;
		$assignment->video = $videoUrl;
		$assignment->other_file = $otherUrl;
		$assignment->status = 1;
		$assignment->save();
		$assignmentId=$assignment->id;
		if($assignmentId){
			$assign = Assignment::findOrFail($assignmentId);
			$assign->sort_id = $assignmentId;
			$assign->update();
		}
			
		$msg = 'Assignment Added Successfully.';
		return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => array("assignment_id" => $assignmentId)]);
	}
	public function assignmentList(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("assignments" => [])]);
		}
		$search = $request->search;
		$course_id = $request->course_id;
		$assignments = Assignment::with("user","course")->where("added_by", $userId);
		if (!empty($search)) {
			$assignments = $assignments->where("title", 'like', "%" . $search . "%");
		}
		if (!empty($course_id)) {
			$assignments = $assignments->where("course_id", $course_id);
		}
		$assignments = $assignments->where("deleted", 0)->orderBy("sort_id", "ASC")->get();
		$assignmentdata = array();
		if (!empty($assignments)) {
			foreach ($assignments as $key => $val) {
				$assignmentdata[$key]['id'] 			= $val['id'];
				$assignmentdata[$key]['added_by'] 		= ($val['added_by']>0) ? $val->user->name : '';
				$assignmentdata[$key]['course_name'] 	= ($val['course_id']>0) ? $val->course->name : '';
				$assignmentdata[$key]['title'] 			= $val['title'];
				$assignmentdata[$key]['content'] 		= !empty($val['content']) ? $val['content'] : 'NA';
				$assignmentdata[$key]['image'] 			= !empty($val['image']) ? $val['image'] : 'NA';
				$assignmentdata[$key]['video'] 			= !empty($val['video']) ? $val['video'] : 'NA';
				$assignmentdata[$key]['download_file'] 	= !empty($val['other_file']) ? $val['other_file'] : 'NA';
				$assignmentdata[$key]['status'] 		= ($val['status']==1) ? 'Active' : 'Inactive';
				$assignmentdata[$key]['submitted_count'] = $val->submitted_data->count();
			}
			$message = "All Assignments Data.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("assignments" => $assignmentdata)]);
		} else {
			$message = "Assignment Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("assignments" => "")]);
		}
	}
	public function getAssignmentDetails(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("assignment" => array("id" => ""))]);
		}
		$assignmentId = $request->assignmentId;
		$assignment = Assignment::with("user","course")->where("id", $assignmentId)->where("deleted", 0)->first();
		$assignmentData = array();
		if (!empty($assignment)) {
			$assignmentData['id'] 				= $assignment->id;
			$assignmentData['added_by'] 		= ($assignment->added_by>0) ? $assignment->user->name : '';
			$assignmentData['course_id'] 		= $assignment->course_id;
			$assignmentData['course_name'] 		= ($assignment->course_id>0) ? $assignment->course->name : '';
			$assignmentData['title'] 			= $assignment->title;
			$assignmentData['content'] 			= !empty($assignment->content) ? $assignment->content : 'NA';
			$assignmentData['image']			= !empty($assignment->image) ? $assignment->image : 'NA';
			$assignmentData['video']			= !empty($assignment->video) ? $assignment->video : 'NA';
			$assignmentData['download_file']	= !empty($assignment->other_file) ? $assignment->other_file : 'NA';
			$message = "Assignment Details.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("assignment" => $assignmentData)]);
		} else {
			$message = "Assignment Details Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("assignment" => array("id" => ""))]);
		}
	}
	public function assignmentUpdate(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("assignment_id" => "")]);
		}

		$validator = Validator::make($request->all(), [
			'assignmentId' => 'required|numeric',
			'course_id' => 'required',
			'title' => 'required',
			//'image_file' => 'mimes:jpeg,jpg,png,gif,webp',
			//'video_file' => 'mimes:mp4',
			'content' => 'required',
		]);

		if($validator->fails()){
			$msg = $validator->messages()->first();
			//return $this->sendError($validator->messages()->first());
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("assignment_id" => "")]);
		}

		$assignmentId = $request->assignmentId;
		$assignment = Assignment::findOrFail($assignmentId);
		$s3 = AWS::createClient('s3');
		$imageUrl = $videoUrl = $otherUrl = NULL;
		$image_file = $request->file('image_file');
		$video_file = $request->file('video_file');
		$another_file = $request->file('another_file');
			
		$destinationPath = public_path().'/upload/assignments/';
		if($image_file){
			$imageOriginalFile = $image_file->getClientOriginalName();
			$imageFilename = "assignment_0_".time().$imageOriginalFile;
			$image_file->move($destinationPath, $imageFilename);
			//S3 Upload
			$s3->putObject(array(
				'Bucket'     => env('AWS_BUCKET'),
				'Key'        => "assignment_data/".$imageFilename,
				'SourceFile' => $destinationPath.$imageFilename,
			));
			$imageUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $imageFilename;
			if(!empty($image_file) && file_exists( $destinationPath.$imageFilename )) {
				unlink( $destinationPath.$imageFilename );
			}
			if(!empty($assignment->image) && file_exists( $destinationPath.$assignment->image )) {
				unlink( $destinationPath.$assignment->image );
			}
			if(!empty($assignment->image)){
				$uploadedImageUrl = $assignment->image;
				$uploadedImageArr = explode("https://s3.ap-south-1.amazonaws.com/video.brainywoodindia.com/assignment_data/", $uploadedImageUrl);
				$imageName = $uploadedImageArr[1];
				$result = $s3->deleteObject(array(
					'Bucket' => env('AWS_BUCKET'),
					'Key'    => "assignment_data/".$imageName
				));
			}
		} else {
			$imageUrl = $assignment->image;
		}
		if($video_file){
			$videoOriginalFile = $video_file->getClientOriginalName();
			$videoFilename = "assignment_0_".time()."_org.mp4";
			$video_file->move($destinationPath, $videoFilename);
			//S3 Upload
			$s3->putObject(array(
				'Bucket'     => env('AWS_BUCKET'),
				'Key'        => "assignment_data/".$videoFilename,
				'SourceFile' => $destinationPath.$videoFilename,
			));
			$videoUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $videoFilename;
			if(!empty($video_file) && file_exists( $destinationPath.$videoFilename )) {
				unlink( $destinationPath.$videoFilename );
			}
			if(!empty($assignment->video) && file_exists( $destinationPath.$assignment->video )) {
				unlink( $destinationPath.$assignment->video );
			}
			if(!empty($assignment->video)){
				$uploadedVideoUrl = $assignment->video;
				$uploadedVideoArr = explode("https://s3.ap-south-1.amazonaws.com/video.brainywoodindia.com/assignment_data/", $uploadedVideoUrl);
				$videoName = $uploadedVideoArr[1];
				$result = $s3->deleteObject(array(
					'Bucket' => env('AWS_BUCKET'),
					'Key'    => "assignment_data/".$videoName
				));
			}
		} else {
			$videoUrl = $assignment->video;
		}
		if($another_file){
			$otherOriginalFile = $another_file->getClientOriginalName();
			$otherFilename = "assignment_other_".time().$otherOriginalFile;
			$another_file->move($destinationPath, $otherFilename);
			//S3 Upload
			$s3->putObject(array(
				'Bucket'     => env('AWS_BUCKET'),
				'Key'        => "assignment_data/".$otherFilename,
				'SourceFile' => $destinationPath.$otherFilename,
			));
			$otherUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $otherFilename;
			if(!empty($another_file) && file_exists( $destinationPath.$otherFilename )) {
				unlink( $destinationPath.$otherFilename );
			}
			if(!empty($assignment->other_file) && file_exists( $destinationPath.$assignment->other_file )) {
				unlink( $destinationPath.$assignment->other_file );
			}
			if(!empty($assignment->other_file)){
				$uploadedOtherUrl = $assignment->other_file;
				$uploadedOtherArr = explode("https://s3.ap-south-1.amazonaws.com/video.brainywoodindia.com/assignment_data/", $uploadedOtherUrl);
				$otherName = $uploadedOtherArr[1];
				$result = $s3->deleteObject(array(
					'Bucket' => env('AWS_BUCKET'),
					'Key'    => "assignment_data/".$otherName
				));
			}
		} else {
			$otherUrl = $assignment->other_file;
		}
		$assignment->course_id = $request->input('course_id');
		$assignment->title = $request->input('title');
		$assignment->content = $request->input('content');
		$assignment->image = $imageUrl;
		$assignment->video = $videoUrl;
		$assignment->other_file = $otherUrl;
		$assignment->save();
		$assignmentId=$assignment->id;
			
		$msg = 'Assignment Updated Successfully.';
		return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => array("assignment_id" => $assignmentId)]);
	}
	public function getAssignedGroupList(Request $request)
	{
		//dd($request->all());
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userAssignments" => [], "total_submitted" => 0, "total_approved" => 0, "total_unapproved" => 0)]);
		}
		$search 			= $request->search;
		$course_id 			= $request->course_id;
		$assignment_id 		= $request->assignment_id;
		$user_id 			= $request->user_id;
		$teacher_approved 	= ($request->teacher_approved != '') ? ($request->teacher_approved == 'not') ? 2 : 1 : '';
		$userAssignments = AssignmentSubmission::with('assignment','user')
			->where('assigned_to', $userId)
			->Where(function($query) use ($assignment_id) {
				if (isset($assignment_id) && !empty($assignment_id)) {
					$query->where('assignment_id', $assignment_id);
				}
			})
			->whereHas('assignment', function($query) use ($search) {
				if (isset($search) && !empty($search)) {
					$query->where('title', 'LIKE', "%".$search."%");
				}
			})
			->whereHas('assignment', function($query) use ($course_id) {
				if (isset($course_id) && !empty($course_id)) {
					$query->where('course_id', $course_id);
				}
			})
			->Where(function($query) use ($user_id) {
				if (isset($user_id) && !empty($user_id)) {
					$query->where('user_id', $user_id);
				}
			})
			->Where(function($query) use ($teacher_approved) {
				if (isset($teacher_approved) && !empty($teacher_approved)) {
					$query->where('teacher_approved', $teacher_approved);
				}
			})
			->where('user_status',1)
			->where('deleted',0)
			->groupBy('user_id','assignment_id')
			->orderBy('id','DESC')->get();
		$userAssData = array();
		if (!empty($userAssignments)) {
			foreach ($userAssignments as $key => $val) {
				$userAssData[$key]['assignSubId'] 		= $val['id'];
				$userAssData[$key]['assignment_title'] 	= ($val->assignment_id>0) ? $val->assignment->title : '';
				$userAssData[$key]['user_name'] 		= ($val->user_id>0) ? $val->user->name : '';
				$userAssData[$key]['teacher_approved']	= ($val['teacher_approved']==1) ? 'Approved' : 'Not Approved';
				$userAssData[$key]['created_at'] 		= date('d/m/Y, h:i A', strtotime($val['created_at']));
			}
			$totalCount = count($userAssignments);
			$approved = 0;
			$notAssigned = 0;
			foreach($userAssignments as $single){
				if($single->teacher_approved==1){
					$approved++;
				}
			}
			$notApproved = $totalCount - $approved;
			$message = "Get Assigned Assignments Data.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userAssignments" => $userAssData, "total_submitted" => $totalCount, "total_approved" => $approved, "total_unapproved" => $notApproved)]);
		} else {
			$message = "Assigned Assignment Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userAssignments" => [], "total_submitted" => 0, "total_approved" => 0, "total_unapproved" => 0)]);
		}
	}
	public function userSubmittedAssignmentDetails(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userAssignments" => [])]);
		}
		$assignSubId = $request->assignSubId;
		$user_submitted = AssignmentSubmission::where('id', $assignSubId)->where('assigned_to', $userId)->first();
		if(!empty($user_submitted)){
			$userAssignments = AssignmentSubmission::with('assignment','user')->where('assignment_id', $user_submitted->assignment_id)->where('user_id', $user_submitted->user_id)->where('assigned_to', $userId)->where('user_status',1)->where('deleted',0)->orderBy('id','ASC')->get();
			$userAssignmntdata = array();
			if (!empty($userAssignments)) {
				foreach ($userAssignments as $key => $userAssignmnt) {
					$user_images_array = $user_videos_array = $user_other_files_array = $user_images_videos_array = $user_submitted_files_array = array();
					if($userAssignmnt->user_images!=''){
						$user_images_arr = !empty($userAssignmnt->user_images) ? explode(",", $userAssignmnt->user_images) : [];
						foreach($user_images_arr as $user_image){
							$user_images['file_path'] = $user_image;
							$user_images['file_type'] = 'image';
							array_push($user_images_array, $user_images);
						}
					}
					if($userAssignmnt->user_videos!=''){
						$user_videos_arr = !empty($userAssignmnt->user_videos) ? explode(",", $userAssignmnt->user_videos) : [];
						foreach($user_videos_arr as $user_video){
							$user_videos['file_path'] = $user_video;
							$user_videos['file_type'] = 'video';
							array_push($user_videos_array, $user_videos);
						}
					}
					$user_images_videos_array = array_merge($user_images_array, $user_videos_array);
					if($userAssignmnt->user_other_files!=''){
						$user_other_files_arr = !empty($userAssignmnt->user_other_files) ? explode(",", $userAssignmnt->user_other_files) : [];
						foreach($user_other_files_arr as $user_other_file){
							$user_other_files['file_path'] = $user_other_file;
							$user_other_files['file_type'] = 'other_file';
							array_push($user_other_files_array, $user_other_files);
						}
					}
					$user_submitted_files_array = array_merge($user_images_videos_array, $user_other_files_array);
					$teacher_approved = 0;
					if (!empty($userAssignmnt)) {
						if ($userAssignmnt->teacher_images != '' || $userAssignmnt->teacher_videos != '' || $userAssignmnt->teacher_content != '') {
							$teacher_approved = 2;
						} elseif ($userAssignmnt->teacher_approved == 1) {
							$teacher_approved = 1;
						} else {
							$teacher_approved = 0;
						}
					}
					$userAssignmntdata[$key]['assignSubId']			 = $userAssignmnt->id;
					$userAssignmntdata[$key]['assignment_title']	 = $userAssignmnt->assignment->title;
					$userAssignmntdata[$key]['course_name']			 = $userAssignmnt->assignment->course->name;
					$userAssignmntdata[$key]['created_at'] 			 = $userAssignmnt->created_at->diffForHumans();
					$userAssignmntdata[$key]['user_submitted_files'] = $user_submitted_files_array;
					$userAssignmntdata[$key]['user_content'] 	 	 = !empty($userAssignmnt->user_content) ? $userAssignmnt->user_content : '';
					$userAssignmntdata[$key]['user_status'] 	 	 = $userAssignmnt->user_status;
					$userAssignmntdata[$key]['teacher_approved'] 	 = !empty($userAssignmnt) ? $teacher_approved : 0;
				}
				$message = "Get User Submitted Assignments.";
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userAssignments" => $userAssignmntdata)]);
			} else {
				$message = "User Submitted Assignment Not Found!";
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userAssignments" => [])]);
			}
		} else {
			$message = "Submitted Assignment Details Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userAssignments" => [])]);
		}
	}
	public function teacherRevertAssignment(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("assignSubId" => "")]);
		}
		$assignSubId = $request->assignSubId;
		$s3 = AWS::createClient('s3');
		if (!empty($request->file('teacher_images_file')) || !empty($request->file('teacher_videos_file')) || !empty($request->teacher_content) ) {
			$submittedAsignmnt = AssignmentSubmission::with('user')->findOrFail($assignSubId);
			$image_files = $request->file('teacher_images_file');
			//$video_files = $request->file('teacher_videos_file');
			$video_file = $request->file('teacher_videos_file');
			$another_files = $request->file('teacher_another_file');

			$destinationPath = public_path().'/upload/assignments/';
			$imagesData = [];
			if($request->hasFile('teacher_images_file')){
				foreach($image_files as $image_file){
					if($image_file){
						$imageOriginalFile = $image_file->getClientOriginalName();
						$imageFilename = "teacher_".$assignSubId."_".time().$imageOriginalFile;
						$image_file->move($destinationPath, $imageFilename);
						//S3 Upload
						$s3->putObject(array(
							'Bucket'     => env('AWS_BUCKET'),
							'Key'        => "assignment_data/".$imageFilename,
							'SourceFile' => $destinationPath.$imageFilename,
						));
						$imagesData[] = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $imageFilename;
						if(!empty($image_file) && file_exists( $destinationPath.$imageFilename )) {
							unlink( $destinationPath.$imageFilename );
						}
						if(!empty($submittedAsignmnt->teacher_images)){
							$teacher_images_arr = !empty($submittedAsignmnt->teacher_images) ? explode(",", $submittedAsignmnt->teacher_images) : [];
							foreach($teacher_images_arr as $teacher_image){
								if(!empty($teacher_image) && file_exists( $destinationPath.$teacher_image )) {
									unlink( $destinationPath.$teacher_image );
								}
								$uploadedImageArr = explode("https://s3.ap-south-1.amazonaws.com/video.brainywoodindia.com/assignment_data/", $teacher_image);
								$imageName = $uploadedImageArr[1];
								$result = $s3->deleteObject(array(
									'Bucket' => env('AWS_BUCKET'),
									'Key'    => "assignment_data/".$imageName
								));
							}
						}
					}
				}
			}
			$videosData = [];
			if($request->hasFile('teacher_videos_file')){
				//foreach($video_files as $video_file){
					if($video_file){
						$videoOriginalFile = $video_file->getClientOriginalName();
						//$videoFilename = rand(111,999).strtotime(date('Y-m-d-H:isa')).$videoOriginalFile;
						$videoFilename = "teacher_".$assignSubId."_".time()."_org.mp4";
						$video_file->move($destinationPath, $videoFilename);
						//S3 Upload
						$s3->putObject(array(
							'Bucket'     => env('AWS_BUCKET'),
							'Key'        => "assignment_data/".$videoFilename,
							'SourceFile' => $destinationPath.$videoFilename,
						));
						$videosData[] = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $videoFilename;
						if(!empty($video_file) && file_exists( $destinationPath.$videoFilename )) {
							unlink( $destinationPath.$videoFilename );
						}
						if(!empty($submittedAsignmnt->teacher_videos)){
							$teacher_videos_arr = !empty($submittedAsignmnt->teacher_videos) ? explode(",", $submittedAsignmnt->teacher_videos) : [];
							foreach($teacher_videos_arr as $teacher_video){
								if(!empty($teacher_video) && file_exists( $destinationPath.$teacher_video )) {
									unlink( $destinationPath.$teacher_video );
								}
								$uploadedVideoArr = explode("https://s3.ap-south-1.amazonaws.com/video.brainywoodindia.com/assignment_data/", $teacher_video);
								$videoName = $uploadedVideoArr[1];
								$result = $s3->deleteObject(array(
									'Bucket' => env('AWS_BUCKET'),
									'Key'    => "assignment_data/".$videoName
								));
							}
						}
					}
				//}
			}
			$anotherFilesData = [];
			if($request->hasFile('teacher_another_file')){
				foreach($another_files as $another_file){
					if($another_file){
						$anotherOriginalFile = $another_file->getClientOriginalName();
						$anotherFilename = "teacher_".$assignSubId."_".time().$anotherOriginalFile;
						$another_file->move($destinationPath, $anotherFilename);
						//S3 Upload
						$s3->putObject(array(
							'Bucket'     => env('AWS_BUCKET'),
							'Key'        => "assignment_data/".$anotherFilename,
							'SourceFile' => $destinationPath.$anotherFilename,
						));
						$anotherFilesData[] = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $anotherFilename;
						if(!empty($another_file) && file_exists( $destinationPath.$anotherFilename )) {
							unlink( $destinationPath.$anotherFilename );
						}
						if(!empty($submittedAsignmnt->teacher_other_files)){
							$teacher_other_files_arr = !empty($submittedAsignmnt->teacher_other_files) ? explode(",", $submittedAsignmnt->teacher_other_files) : [];
							foreach($teacher_other_files_arr as $teacher_other_file){
								if(!empty($teacher_other_file) && file_exists( $destinationPath.$teacher_other_file )) {
									unlink( $destinationPath.$teacher_other_file );
								}
								$uploadedAnotherFileArr = explode("https://s3.ap-south-1.amazonaws.com/video.brainywoodindia.com/assignment_data/", $teacher_other_file);
								$anotherFileName = $uploadedAnotherFileArr[1];
								$result = $s3->deleteObject(array(
									'Bucket' => env('AWS_BUCKET'),
									'Key'    => "assignment_data/".$anotherFileName
								));
							}
						}
					}
				}
			}

			$submittedAsignmnt->teacher_content = $request->input('teacher_content');
			$submittedAsignmnt->teacher_images  = !empty($imagesData) ? implode(",", $imagesData) : $submittedAsignmnt->teacher_images;
			$submittedAsignmnt->teacher_videos  = !empty($videosData) ? implode(",", $videosData) : $submittedAsignmnt->teacher_videos;
			$submittedAsignmnt->teacher_other_files  = !empty($anotherFilesData) ? implode(",", $anotherFilesData) : $submittedAsignmnt->teacher_other_files;
			$submittedAsignmnt->save();
			//Send notification
			$userId = $submittedAsignmnt->user_id;
			$token = isset($submittedAsignmnt->user->deviceToken) ? $submittedAsignmnt->user->deviceToken : '';
			if ($token!='') {
				$title = 'GuruAthome';
				$click_action = 'Assignment';
				$module_id = $assignSubId;
				$message = 'Teacher reviewed your submitted Assignment.';
				$this->helper->addNotification($userId,$message,$click_action,$module_id);
			}
			$message = "User Assignment Remarked Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("assignSubId" => $assignSubId)]);
		} else {
			$message = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("assignSubId" => "")]);
		}
	}
	public function getRemarkedAssignment(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userAssignments" => [])]);
		}
		$assignSubId = $request->assignSubId;
		$userAssignmnt = AssignmentSubmission::with('assignment','user')->where('id', $assignSubId)->where('assigned_to', $userId)->where('user_status',1)->where('deleted',0)->first();
		$userAssignmntdata = $teacher_images_array = $teacher_videos_array = $teacher_other_files_array = $teacher_images_videos_array = $teacher_submitted_files_array = array();
		if(!empty($userAssignmnt)){
			if($userAssignmnt->teacher_images!=''){
				$teacher_images_arr = !empty($userAssignmnt->teacher_images) ? explode(",", $userAssignmnt->teacher_images) : [];
				foreach($teacher_images_arr as $teacher_image){
					$teacher_images['file_path'] = $teacher_image;
					$teacher_images['file_type'] = 'image';
					array_push($teacher_images_array, $teacher_images);
				}
			}
			if($userAssignmnt->teacher_videos!=''){
				$teacher_videos_arr = !empty($userAssignmnt->teacher_videos) ? explode(",", $userAssignmnt->teacher_videos) : [];
				foreach($teacher_videos_arr as $teacher_video){
					$teacher_videos['file_path'] = $teacher_video;
					$teacher_videos['file_type'] = 'video';
					array_push($teacher_videos_array, $teacher_videos);
				}
			}
			$teacher_images_videos_array = array_merge($teacher_images_array, $teacher_videos_array);
			if($userAssignmnt->teacher_other_files!=''){
				$teacher_other_files_arr = !empty($userAssignmnt->teacher_other_files) ? explode(",", $userAssignmnt->teacher_other_files) : [];
				foreach($teacher_other_files_arr as $teacher_other_file){
					$teacher_other_files['file_path'] = $teacher_other_file;
					$teacher_other_files['file_type'] = 'other_file';
					array_push($teacher_other_files_array, $teacher_other_files);
				}
			}
			$teacher_submitted_files_array = array_merge($teacher_images_videos_array, $teacher_other_files_array);
			$teacher_approved = 0;
			if (!empty($userAssignmnt)) {
				if ($userAssignmnt->teacher_images != '' || $userAssignmnt->teacher_videos != '' || $userAssignmnt->teacher_content != '') {
					$teacher_approved = 2;
				} elseif ($userAssignmnt->teacher_approved == 1) {
					$teacher_approved = 1;
				} else {
					$teacher_approved = 0;
				}
			}
			$userAssignmntdata['assignSubId']				= $userAssignmnt->id;
			$userAssignmntdata['assignment_title']			= $userAssignmnt->assignment->title;
			$userAssignmntdata['course_name']				= $userAssignmnt->assignment->course->name;
			$userAssignmntdata['teacher_submitted_files']	= $teacher_submitted_files_array;
			$userAssignmntdata['teacher_content']			= !empty($userAssignmnt->teacher_content) ? $userAssignmnt->teacher_content : '';
			$userAssignmntdata['teacher_approved']			= !empty($userAssignmnt) ? $teacher_approved : 0;
			$message = "Teacher Checked Assignment Details.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userAssignments" => $userAssignmntdata)]);
		} else {
			$message = "Teacher Not Checked Assignment Yet!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userAssignments" => [])]);
		}
	}
	public function approvedAssignment(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("assignSubId" => "")]);
		}
		$assignSubId = $request->assignSubId;
		$status = $request->status;
		$userAssignmnt = AssignmentSubmission::with('user')->where('id', $assignSubId)->where('assigned_to', $userId)->where('user_status',1)->where('deleted',0)->first();
		if(!empty($userAssignmnt)){
			$userAssignmnt->teacher_approved = $status;
			$userAssignmnt->update();
			if($status==1){
				//Send notification
				$userId = $userAssignmnt->user_id;
				$token = isset($userAssignmnt->user->deviceToken) ? $userAssignmnt->user->deviceToken : '';
				if ($token!='') {
					$title = 'GuruAthome';
					$click_action = 'Assignment';
					$module_id = $userAssignmnt->id;
					$message = 'Teacher remarked your submitted Assignment.';
					$this->helper->addNotification($userId,$message,$click_action,$module_id);
				}
			}
			$message = "Submitted Assignment Approval Status Updated Successfully";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("assignSubId" => $assignSubId)]);
		} else {
			$message = "Submitted Assignment Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("assignSubId" => "")]);
		}
	}

	public function getSubjectsByMultipleClass(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("subjects" => [])]);
		}
		$class_ids = $request->class_ids;
		if (!empty($class_ids)) {
			$class_ids = !empty($class_ids) ? explode(",", $class_ids) : [];
			$getClassSubjectData = StudentClassSubject::whereIn("class_id", $class_ids)->get();
			$subject_ids_arr = [];
			$i=0;
			foreach ($getClassSubjectData as $classSubject) {
				$subject_ids = !empty($classSubject['subject_ids']) ? explode(",", $classSubject['subject_ids']) : [];
				foreach ($subject_ids as $key => $value) {
					$subject_ids_arr[$i]=$value;
					$i++;
				}
			}
			array_unique($subject_ids_arr);
			$getSubjectData = Subject::whereIn("id",$subject_ids_arr)->orderBy("title", "ASC")->get();
			$subjectdata = array();
			if (!empty($getSubjectData)) {
				foreach ($getSubjectData as $key => $val) {
					$subjectdata[$key]['id'] 	= $val['id'];
					$subjectdata[$key]['title'] = $val['title'];
				}
				$message = "Get All Subjects List Successfully.";
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("subjects" => $subjectdata)]);
			} else {
				$message = "Subjects List Not Found!";
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("subjects" => [])]);
			}
		} else {
			$message = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("subjects" => "")]);
		}
	}
	public function getSubjectsBySingleClass(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("subjects" => [])]);
		}
		$subjectdata = array();
		$class_id = $request->class_id;
		if (!empty($class_id)) {
			$classSubject = StudentClassSubject::where("class_id", $class_id)->first();
			$subject_ids = !empty($classSubject['subject_ids']) ? explode(",", $classSubject['subject_ids']) : [];
			$getSubjectData = Subject::whereIn("id",$subject_ids)->orderBy("title", "ASC")->get();
		} else {
			$getSubjectData = Subject::orderBy("title", "ASC")->get();
		}
		if (!empty($getSubjectData)) {
			foreach ($getSubjectData as $key => $val) {
				$subjectdata[$key]['id'] 	= $val['id'];
				$subjectdata[$key]['title'] = $val['title'];
			}
			$message = "Get All Subjects List Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("subjects" => $subjectdata)]);
		} else {
			$message = "Subjects List Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("subjects" => [])]);
		}
	}

	public function liveClasses(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("liveClasses" => [])]);
		}
		$class_type = ($request->class_type) ? $request->class_type : '';
		$class_id 	= ($request->class_id) ? $request->class_id : '';
		$subject_id = ($request->subject_id) ? $request->subject_id : '';
		$from 	    = ($request->from) ? $request->from : '';
		$to     	= ($request->to) ? $request->to.' 23:59:59' : date('Y-m-d H:i:s');
		$status 	= ($request->status) ? $request->status : ''; //1 OR 2
		$liveClasses = LiveClass::with('user','subject_data')
			 ->where("added_by", $userId)
			 ->Where(function($query) use ($class_type) {
				if (isset($class_type) && !empty($class_type)) {
					$query->where("class_type", $class_type);
				}
			 })
			 ->Where(function($query) use ($class_id) {
				if (isset($class_id) && !empty($class_id)) {
					$query->whereRaw("find_in_set($class_id,class_ids)");
				}
			 })
			 ->Where(function($query) use ($subject_id) {
				if (isset($subject_id) && !empty($subject_id)) {
					$query->where("subject_id", $subject_id);
				}
			 })
			 ->Where(function($query) use ($from, $to) {
				if (isset($from) && !empty($from)) {
					//$query->whereBetween("created_at", [$from, $to]);
					$query->where("class_time", ">=", $from)->where("end_time", "<=", $to);
				}
			 })
			 ->Where(function($query) use ($status) {
				if (isset($status) && !empty($status)) {
					$query->where("status", $status);
				}
			 })
			 ->where("deleted", 0)->orderBy("id", "DESC")->get();
		$liveClsdata = array();
		if (!empty($liveClasses)) {
			foreach ($liveClasses as $key => $val) {
				foreach(config('constant.CLASS_TYPE') as $value => $clstype_key){
					if($val->class_type == $clstype_key){
						$classType = ucwords(str_replace("_", " ", strtolower($value)));
					}
				}
				$class_ids_arr = !empty($val->class_ids) ? explode(",", $val->class_ids) : [];
				//$class_data    = StudentClass::whereIn('id',$class_ids_arr)->pluck('class_name');
				$classes_data  = StudentClass::select('id','class_name')->whereIn('id',$class_ids_arr)->get();
				$liveClsdata[$key]['id'] 			= $val['id'];
				$liveClsdata[$key]['added_by'] 		= ($val['added_by']>0) ? $val->user->name : 'NA';
				$liveClsdata[$key]['title'] 		= $val['title'];
				$liveClsdata[$key]['class_type'] 	= ($classType) ? $classType : '';
				$liveClsdata[$key]['class_data'] 	= !empty($classes_data) ? $classes_data : [];
				$liveClsdata[$key]['subject'] 		= ($val['subject_id']>0) ? $val->subject_data->title : 'NA';
				$liveClsdata[$key]['image'] 		= asset('upload/liveclasses') . "/" . $val['image'];
				$liveClsdata[$key]['class_time'] 	= date('m/d/Y, h:i A', strtotime($val['class_time']));
				$liveClsdata[$key]['end_time'] 		= !empty($val['end_time']) ? date('m/d/Y, h:i A', strtotime($val['end_time'])) : '';
				$liveClsdata[$key]['isFree'] 		= $val['isFree'];
				$liveClsdata[$key]['status'] 		= $val['status'];
				$total_interest = LiveclassNotify::where("class_id", $val['id'])->count();
				$liveClsdata[$key]['total_interest'] = $total_interest;
			}
			$message = "All Live Classes Data.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("liveClasses" => $liveClsdata)]);
		} else {
			$message = "Live Class Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("liveClasses" => "")]);
		}
	}
	public function liveClassSave(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("class_id" => "")]);
		}

		$validator = Validator::make($request->all(), [
			//'added_by' => 'required',
			'title' => 'required',
			'subject_id' => 'required',
			'meeting_id' => 'required|regex:/^\S*$/u',
			'class_time' => 'required',
			'end_time' => 'required',
		]);

		if($validator->fails()){
			$msg = $validator->messages()->first();
			//return $this->sendError($validator->messages()->first());
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("class_id" => "")]);
		}

		//echo '<pre />'; print_r($request->all()); die;
		$class_start_datetime = $class_end_datetime = '';
		$title 		= $request->input('title');
		$subject_id	= $request->input('subject_id');
		$meeting_id = $request->input('meeting_id');
		$pass_code 	= $request->input('pass_code');
		$class_time = ($request->class_time) ? $request->class_time : '';
		$start_datetime = explode(' ', $class_time);
		if(isset($start_datetime) && !empty($start_datetime)) {
			$start_date = explode('/', $start_datetime[0]);
			if(isset($start_date) && $class_time!=''){
				$class_start_datetime = $start_date[2].'-'.$start_date[1].'-'.$start_date[0].' '.$start_datetime[1];
			}
		}
		$end_time = ($request->end_time) ? $request->end_time : '';
		$end_datetime = explode(' ', $end_time);
		if(isset($end_datetime) && !empty($end_datetime)) {
			$end_date = explode('/', $end_datetime[0]);
			if(isset($end_date) && $end_time!=''){
				$class_end_datetime = $end_date[2].'-'.$end_date[1].'-'.$end_date[0].' '.$end_datetime[1];
			}
		}
		if (strtotime($class_end_datetime) < strtotime($class_start_datetime)) {
			$date = strtotime($class_start_datetime);
			$date = strtotime("+1 hours", $date);
			$class_end_datetime = date('Y-m-d H:i:s', $date);
		}
		//echo $class_start_datetime."--------".$class_end_datetime; die;

		$msg = '';
		if (!empty($title) && !empty($subject_id) && !empty($meeting_id) && !empty($class_start_datetime) && !empty($class_end_datetime)) {
			$filename = '';
			$file = $request->file('image');
			if($file){
				$destinationPath = public_path().'/upload/liveclasses/';
				$originalFile = $file->getClientOriginalName();
				$filename=strtotime(date('Y-m-d-H:isa')).$originalFile;
				$file->move($destinationPath, $filename);
			}
			
			$liveClass = new LiveClass();
			$liveClass->added_by 	= $userId;
			$liveClass->title 		= $title;
			$liveClass->class_type 	= $request->input('class_type');
			//$liveClass->class_ids 	= !empty($request->class_ids) ? implode(",", $request->class_ids) : NULL;
			$liveClass->class_ids 	= !empty($request->class_ids) ? $request->class_ids : NULL;
			$liveClass->subject_id 	= $subject_id;
			$liveClass->image 		= $filename;
			$liveClass->meeting_id	= $meeting_id;
			$liveClass->pass_code 	= $pass_code;
			$liveClass->master_class = 0;
			$liveClass->class_time 	= $class_start_datetime;
			$liveClass->end_time 	= $class_end_datetime;
			$liveClass->isFree 		= $request->input('free');
			$liveClass->status 		= 1; //1=Active OR 2=Inactive
			$liveClass->save();
			$liveClassId = $liveClass->id;
			
			$msg = 'Live Class Added Successfully.';
			return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => array("class_id" => $liveClassId)]);
		} else {
			$msg = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("class_id" => "")]);
		}
	}
	public function getLiveClassDetails(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("liveClass" => array("id" => ""))]);
		}
		$classId = $request->classId;
		$liveClass = LiveClass::with('user','subject_data')->where("id", $classId)->where("deleted", 0)->first();
		$liveClassData = array();
		if (!empty($liveClass)) {
			foreach(config('constant.CLASS_TYPE') as $value => $clstype_key){
				if($liveClass->class_type == $clstype_key){
					$classType = ucwords(str_replace("_", " ", strtolower($value)));
				}
			}
			$class_ids_arr = !empty($liveClass->class_ids) ? explode(",", $liveClass->class_ids) : [];
			$classes_data  = StudentClass::select('id','class_name')->whereIn('id',$class_ids_arr)->get();
			$liveClassData['id'] 				= $liveClass->id;
			$liveClassData['added_by'] 			= ($liveClass->added_by>0) ? $liveClass->user->name : 'NA';
			$liveClassData['title'] 			= $liveClass->title;
			$liveClassData['class_type'] 		= ($classType) ? $classType : '';
			$liveClassData['class_data'] 		= !empty($classes_data) ? $classes_data : [];
			$liveClassData['subject_id'] 		= $liveClass->subject_id;
			$liveClassData['subject_name'] 		= ($liveClass->subject_id>0) ? $liveClass->subject_data->title : '';
			$liveClassData['meeting_id'] 		= $liveClass->meeting_id;
			$liveClassData['pass_code'] 		= $liveClass->pass_code;
			$liveClassData['class_time'] 		= date('m/d/Y, h:i A', strtotime($liveClass->class_time));
			$liveClassData['end_time'] 			= !empty($liveClass->end_time) ? date('m/d/Y, h:i A', strtotime($liveClass->end_time)) : '';
			$liveClassData['image']				= !empty($liveClass->image) ? asset('upload/liveclasses') . "/" . $liveClass->image : '';
			if ($liveClass->uploads3==1){
				$video = $liveClass->video;
			} else {
				$video = asset('upload/liveclasses') . "/" . $liveClass->video;
			}
			$liveClassData['original_video']	= isset($liveClass->video) ? $video : 'NA';
			if (!empty($liveClass->video_1) && $liveClass->video_1!='NA') {
				if ($liveClass->uploads3==1){
					$video_1 = $liveClass->video_1;
				} else {
					if(file_exists( public_path().'/upload/liveclasses/'.$liveClass->video_1 )) {
						$video_1 = asset('upload/liveclasses') . "/" . $liveClass->video_1;
					} else {
						$video_1 = $liveClassData['original_video'];
					}
				}
			} else {
				$video_1 = $liveClassData['original_video'];
			}
			if (!empty($liveClass->video_2) && $liveClass->video_2!='NA') {
				if ($liveClass->uploads3==1){
					$video_2 = $liveClass->video_2;
				} else {
					if(file_exists( public_path().'/upload/liveclasses/'.$liveClass->video_2 )) {
						$video_2 = asset('upload/liveclasses') . "/" . $liveClass->video_2;
					} else {
						$video_2 = $liveClassData['original_video'];
					}
				}
			} else {
				$video_2 = $liveClassData['original_video'];
			}
			if (!empty($liveClass->video_3) && $liveClass->video_3!='NA') {
				if ($liveClass->uploads3==1){
					$video_3 = $liveClass->video_3;
				} else {
					if(file_exists( public_path().'/upload/liveclasses/'.$liveClass->video_3 )) {
						$video_3 = asset('upload/liveclasses') . "/" . $liveClass->video_3;
					} else {
						$video_3 = $liveClassData['original_video'];
					}
				}
			} else {
				$video_3 = $liveClassData['original_video'];
			}
			$liveClassData['low_video'] 		= $video_1;
			$liveClassData['medium_video'] 		= $video_2;
			$liveClassData['high_video'] 		= $video_3;
			$liveClassData['isFree']  			= $liveClass->isFree;
			$message = "Get Live Class Details Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("liveClass" => $liveClassData)]);
		} else {
			$message = "Live Class Details Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("liveClass" => array("id" => ""))]);
		}
	}
	public function liveClassUpdate(Request $request)
	{
		$s3 = AWS::createClient('s3');
		
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("class_id" => "")]);
		}

		$validator = Validator::make($request->all(), [
			//'added_by' => 'required',
			'title' => 'required',
			'subject_id' => 'required',
			'meeting_id' => 'required|regex:/^\S*$/u',
			'class_time' => 'required',
			'end_time' => 'required',
		]);

		if($validator->fails()){
			$msg = $validator->messages()->first();
			//return $this->sendError($validator->messages()->first());
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("class_id" => "")]);
		}
		if($request->hasFile('video')){
			$validator = Validator::make($request->all(), [
				'video' => 'mimes:mp4',
			]);

			if($validator->fails()){
				$msg = $validator->messages()->first();
				//return $this->sendError($validator->messages()->first());
				return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("class_id" => "")]);
			}
		}

		$classId   = $request->classId;
		$liveClass = LiveClass::findOrFail($classId);

		$class_start_datetime = $class_end_datetime = '';
		$title 		= $request->input('title');
		$subject_id	= $request->input('subject_id');
		$meeting_id = $request->input('meeting_id');
		$pass_code 	= $request->input('pass_code');
		$class_time = ($request->class_time) ? $request->class_time : '';
		$start_datetime = explode(' ', $class_time);
		if(isset($start_datetime) && !empty($start_datetime)) {
			$start_date = explode('/', $start_datetime[0]);
			if(isset($start_date) && $class_time!=''){
				$class_start_datetime = $start_date[2].'-'.$start_date[1].'-'.$start_date[0].' '.$start_datetime[1];
			}
		}
		$end_time = ($request->end_time) ? $request->end_time : '';
		$end_datetime = explode(' ', $end_time);
		if(isset($end_datetime) && !empty($end_datetime)) {
			$end_date = explode('/', $end_datetime[0]);
			if(isset($end_date) && $end_time!=''){
				$class_end_datetime = $end_date[2].'-'.$end_date[1].'-'.$end_date[0].' '.$end_datetime[1];
			}
		}
		if (strtotime($class_end_datetime) < strtotime($class_start_datetime)) {
			$date = strtotime($class_start_datetime);
			$date = strtotime("+1 hours", $date);
			$class_end_datetime = date('Y-m-d H:i:s', $date);
		}
		//echo $class_start_datetime."--------".$class_end_datetime; die;

		$msg = '';
		if (!empty($title) && !empty($subject_id) && !empty($meeting_id) && !empty($class_start_datetime) && !empty($class_end_datetime)) {

			$file = $request->file('image');
			$video = $request->file('video');
			$pdf = $request->file('pdf');
			
			$destinationPath = public_path().'/upload/liveclasses/';
			if($file){
				$originalFile = $file->getClientOriginalName();
				$filename=strtotime(date('Y-m-d-H:isa')).$originalFile;
				$file->move($destinationPath, $filename);
			}else{
				$filename=$liveClass->image;  
			}
			if($video){
				$originalFilevideo = $video->getClientOriginalName();
				//$filenamevideo = strtotime(date('Y-m-d-H:isa')).$originalFilevideo;
				$filenamevideo = time() . "_org.mp4";
				$video->move($destinationPath, $filenamevideo);
				$uploads3 = 0;
			}else{
				$filenamevideo = $liveClass->video;  
				$uploads3 = $liveClass->uploads3;
			}
			if($pdf){
				$pdfOriginalName = $pdf->getClientOriginalName();
				//$pdffilename = strtotime(date('Y-m-d-H:isa')).$pdfOriginalName;
				$pdffilename = time() . $pdfOriginalName;
				$pdf->move($destinationPath, $pdffilename);
			}else{
				$pdffilename = $liveClass->pdf;
			}

			if($video){
				if(!empty($liveClass->video) && file_exists( public_path().'/upload/liveclasses/'.$liveClass->video )) {
					unlink( public_path().'/upload/liveclasses/'.$liveClass->video );
				}
				if(!empty($liveClass->video)){
					$videoName = $liveClass->video;
					$s3->deleteObject(array(
						'Bucket' => env('AWS_BUCKET'),
						'Key'    => $videoName
					));
					$s3->deleteObject(array(
						'Bucket' => env('AWS_BUCKET'),
						'Key'    => "mobile_app_data/".$videoName
					));
				}
				if(!empty($liveClass->video_1) && file_exists( public_path().'/upload/liveclasses/'.$liveClass->video_1 ) && $liveClass->video_1!='NA') {
					unlink( public_path().'/upload/liveclasses/'.$liveClass->video_1 );
				}
				if(!empty($liveClass->video_1)){
					$videoName1 = $liveClass->video_1;
					$s3->deleteObject(array(
						'Bucket' => env('AWS_BUCKET'),
						'Key'    => "mobile_app_data/".$videoName1
					));
				}
				if(!empty($liveClass->video_2) && file_exists( public_path().'/upload/liveclasses/'.$liveClass->video_2 ) && $liveClass->video_2!='NA') {
					unlink( public_path().'/upload/liveclasses/'.$liveClass->video_2 );
				}
				if(!empty($liveClass->video_2)){
					$videoName2 = $liveClass->video_2;
					$s3->deleteObject(array(
						'Bucket' => env('AWS_BUCKET'),
						'Key'    => "mobile_app_data/".$videoName2
					));
				}
				if(!empty($liveClass->video_3) && file_exists( public_path().'/upload/liveclasses/'.$liveClass->video_3 ) && $liveClass->video_3!='NA') {
					unlink( public_path().'/upload/liveclasses/'.$liveClass->video_3 );
				}
				if(!empty($liveClass->video_3)){
					$videoName3 = $liveClass->video_3;
					$s3->deleteObject(array(
						'Bucket' => env('AWS_BUCKET'),
						'Key'    => "mobile_app_data/".$videoName3
					));
				}
				$deleteVideoTemp = VideoTemp::where('liveclassId',$classId)->delete();
				$liveClass->video_1 = NULL;
				$liveClass->video_2 = NULL;
				$liveClass->video_3 = NULL;
				$liveClass->processtatus = 0;
				$liveClass->starttime = NULL;
				$liveClass->endtime = NULL;
				$liveClass->uploads3 = 0;
				$liveClass->uploads3v1 = 0;
				$liveClass->uploads3v2 = 0;
				$liveClass->uploads3v3 = 0;
			}

			$liveClass->added_by 	= $userId;
			$liveClass->title 		= $title;
			$liveClass->class_type 	= $request->input('class_type');
			//$liveClass->class_ids 	= !empty($request->class_ids) ? implode(",", $request->class_ids) : NULL;
			$liveClass->class_ids 	= !empty($request->class_ids) ? $request->class_ids : NULL;
			$liveClass->subject_id 	= $subject_id;
			$liveClass->image 		= $filename;
			$liveClass->video 		= $filenamevideo;
			$liveClass->pdf 		= $pdffilename;
			$liveClass->meeting_id	= $meeting_id;
			$liveClass->pass_code 	= $pass_code;
			$liveClass->class_time 	= $class_start_datetime;
			$liveClass->end_time 	= $class_end_datetime;
			$liveClass->isFree 		= $request->input('free');
			$liveClass->save();
			$liveClassId = $liveClass->id;
			
			$msg = 'Live Class Updated Successfully.';
			return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => array("class_id" => $liveClassId)]);
		} else {
			$msg = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("class_id" => "")]);
		}
	}

	public function questionAnswers(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("allQuestions" => [], "answeredQuestions" => [], "pendingQuestions" => [])]);
		}
		$search = $request->search;
		$dt = Carbon::now();
		$from = $dt->subMonth();
		$to = date('Y-m-d H:i:s');
		/*$from = date('Y-m-d').' 00:00:00';
		$to = date('Y-m-d').' 23:59:59';*/
		//$allQuestions = QuestionAsk::where("status", 1)->whereBetween("created_at", [$from, $to]);
		$teacher = User::where("id", $userId)->first();
		$allowedCourses = $teacher->allow_courses;
		$allowedCourses = explode(",", trim($allowedCourses));
		$allQuestions = QuestionAsk::whereIn("course_id", $allowedCourses)->where("course_id", "!=", 0)->where("status", 1)->where("deleted", 0);
		if (!empty($allQuestions)) {
			$allQuestions = $allQuestions->where("question", 'like', "%" . $search . "%");
		}
		$allQuestions = $allQuestions->orderBy("id", "DESC")->get();
		$allQuestionArr = $answeredQuestionsArr = $pendingQuestionsArr = array();
		if (!empty($allQuestions)) {
			foreach ($allQuestions as $key => $val) {
				$user = User::where("id", $val['user_id'])->first();
				$course = Courses::where("id", $val['course_id'])->first();
				$lession = Lession::where("id", $val['lession_id'])->first();
				$topic = Chapter::where("id", $val['topic_id'])->first();
				$courseLessionTopicName = '';
				if($val['topic_id'] > 0){
					$courseLessionTopicName = $course->name.' / '.$lession->name.' / '.$topic->name;
				}elseif($val['lession_id'] > 0){
					$courseLessionTopicName = $course->name.' / '.$lession->name;
				}else{
					if($val['course_id'] > 0){
						$courseLessionTopicName = $course->name;
					}
				}
				$images = $images_arr = array();
				if (!empty($val['image'])) {
					$images = explode(',', $val['image']);
				}
				foreach ($images as $queImg) {
					$imgPath['image'] = asset('upload/questionask') . "/" . $queImg;
					array_push($images_arr, $imgPath);
				}

				$allQuestionArr[$key]['id']            = $val['id'];
				$allQuestionArr[$key]['added_by']      = $user->name;
				$allQuestionArr[$key]['profile_image'] = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
				$allQuestionArr[$key]['course_name']   = $courseLessionTopicName;
				$allQuestionArr[$key]['created_at']    = $val['created_at']->diffForHumans();
				$allQuestionArr[$key]['question']      = $val['question'];
				$allQuestionArr[$key]['images']        = $images_arr;
				$allQuestionArr[$key]['total_answers'] = QuestionAnswer::where("ques_id", $val['id'])->count();
				$allQuestionArr[$key]['share_url'] = route('questionAnswerView',$val['id']);
				if ($userId == $val['user_id']) {
					$allQuestionArr[$key]['my_question']  = 1;
				} else {
					$allQuestionArr[$key]['my_question']  = 0;
				}

				$ques_answer = QuestionAnswer::where("user_id", $val['user_id'])->where("ques_id", $val['id'])->count();
				if ($ques_answer > 0) {
					$answeredQuestion['id']            = $val['id'];
					$answeredQuestion['added_by']      = $user->name;
					$answeredQuestion['profile_image'] = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
					$answeredQuestion['course_name']   = $courseLessionTopicName;
					$answeredQuestion['created_at']    = $val['created_at']->diffForHumans();
					$answeredQuestion['question']      = $val['question'];
					$answeredQuestion['answered_ques_images'] = $images_arr;
					$answeredQuestion['total_answers'] = QuestionAnswer::where("ques_id", $val['id'])->count();
					$answeredQuestion['share_url'] = route('questionAnswerView',$val['id']);
					if ($userId == $val['user_id']) {
						$answeredQuestion['my_question']  = 1;
					} else {
						$answeredQuestion['my_question']  = 0;
					}
					array_push($answeredQuestionsArr, $answeredQuestion);
				} else {
					$pendingQuestion['id']            = $val['id'];
					$pendingQuestion['added_by']      = $user->name;
					$pendingQuestion['profile_image'] = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
					$pendingQuestion['course_name']   = $courseLessionTopicName;
					$pendingQuestion['created_at']    = $val['created_at']->diffForHumans();
					$pendingQuestion['question']      = $val['question'];
					$pendingQuestion['pending_ques_images'] = $images_arr;
					$pendingQuestion['total_answers'] = QuestionAnswer::where("ques_id", $val['id'])->count();
					$pendingQuestion['share_url'] = route('questionAnswerView',$val['id']);
					if ($userId == $val['user_id']) {
						$pendingQuestion['my_question']  = 1;
					} else {
						$pendingQuestion['my_question']  = 0;
					}
					array_push($pendingQuestionsArr, $pendingQuestion);
				}
			}
			$message = "Get All Question Answer Data.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("allQuestions" => $allQuestionArr, "answeredQuestions" => $answeredQuestionsArr, "pendingQuestions" => $pendingQuestionsArr)]);
		} else {
			$message = "Question Answer Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("allQuestions" => [], "answeredQuestions" => [], "pendingQuestions" => [])]);
		}
	}
	public function answerAQuestion(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("answer_id" => "")]);
		}
		$quesId 	= $request->quesId;
		$answer 	= $request->answer;
		$msg = '';
		if (!empty($userId)  && !empty($quesId)) {
			$questioncheck = QuestionAsk::where('id', $quesId)->first();
			if (!empty($questioncheck)) {
				$imagesData = [];
				$files = $request->file('image');
				if ($request->hasFile('image')) {
					foreach ($files as $file) {
						if($file){
							$destinationPath = public_path().'/upload/questionask/';
							$originalFile = $file->getClientOriginalName();
							$newImage = rand(111,999).strtotime(date('Y-m-d-H:isa')).$originalFile;
							$file->move($destinationPath, $newImage);
							//$imagesData[] = $newImage;
							$url = 'upload/questionask/' . $newImage;
							$thumb_img = Image::make($url)->resize(200, 200);
							$thumb_img->save('upload/questionask/thumb/'.$newImage,80);
							$imagesData[] =  $thumb_img->basename;
						}
					}
				}
				$data = array(
					'user_id' 	 => $userId,
					'ques_id'    => $quesId,
					'answer'     => $answer,
					//'image'      => $imagess,
					'image'      => implode(",", $imagesData),
					'expert' 	 => 2,
					'status'     => 1,
					'created_at' => date('Y-m-d H:i:s'),
				);
				$insertId = QuestionAnswer::insertGetId($data);
				$user = User::where("id", $userId)->first();
				$ques = QuestionAsk::where("id", $quesId)->first();
				$msg = $user->name.', Answered a question '.$ques->question.' in Q&A, check it now.';
				$this->helper->addNotification($ques->user_id,$msg);
				/*$users = User::where("id", "!=", $userId)->where("role_id", "!=", 1)->get();
				foreach ($users as $userval) {
					$this->helper->addNotification($userval->id,$msg);
				}*/
				$message = 'Your Answer Submitted Successfully.';
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("answer_id" => $insertId)]);
			} else {
				$message = "Question not Found!";
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("answer_id" => "")]);
			}
		} else {
			$message = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("answer_id" => "")]);
		}
	}
	public function viewAnswers(Request $request)
	{
		$quesId   = $request->quesId;
		$question = QuestionAsk::where("id", $quesId)->where("status", 1)->first();
		$questiondata = array();
		if (!empty($question)) {
			$user = User::where("id", $question->user_id)->first();
			$course = Courses::where("id", $question->course_id)->first();
			$lession = Lession::where("id", $question->lession_id)->first();
			$topic = Chapter::where("id", $question->topic_id)->first();
			$courseLessionTopicName = '';
			if($question->topic_id > 0){
				$courseLessionTopicName = $course->name.' / '.$lession->name.' / '.$topic->name;
			}elseif($question->lession_id > 0){
				$courseLessionTopicName = $course->name.' / '.$lession->name;
			}else{
				if($question->course_id > 0){
					$courseLessionTopicName = $course->name;
				}
			}
			$images = $images_arr = array();
			if (!empty($question->image)) {
				$images = explode(',', $question->image);
			}
			foreach ($images as $queImg) {
				$imgPath['image'] = asset('upload/questionask') . "/" . $queImg;
				array_push($images_arr, $imgPath);
			}

			$questiondata['id']            = $question->id;
			$questiondata['added_by']      = isset($user->name) ? $user->name : '';
			$questiondata['profile_image'] = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
			$questiondata['course_name']   = $courseLessionTopicName;
			$questiondata['created_at']    = $question->created_at->diffForHumans();
			$questiondata['question']      = $question->question;
			$questiondata['question_images'] = $images_arr;
			$questiondata['total_answers'] = QuestionAnswer::where("ques_id", $question->id)->count();
			$questiondata['share_url'] = route('questionAnswerView',$question->id);
			$answers = QuestionAnswer::where("ques_id", $quesId)->where("expert", "!=", 0)->where("status", 1)->orderBy("expert", "ASC")->orderBy("ans_like", "DESC")->get();
			$answerdata = array();
			$expertanswerdata = array();
			if (!empty($answers)) {
				foreach ($answers as $key => $val) {
					$user = User::where("id", $val['user_id'])->first();
					$images = $images_arr = array();
					if (!empty($val['image'])) {
						$images = explode(',', $val['image']);
					}
					foreach ($images as $queImg) {
						$imgPath['image'] = asset('upload/questionask') . "/" . $queImg;
						array_push($images_arr, $imgPath);
					}
					
					$expertans['id']            = $val['id'];
					$expertans['added_by']      = isset($user->name) ? $user->name : '';
					$expertans['profile_image'] = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
					$expertans['created_at']    = $val['created_at']->diffForHumans();
					$expertans['answer']        = $val['answer'];
					$expertans['images']        = $images_arr;
					$expertans['ans_like']      = $val['ans_like'];
					$expertans['ans_unlike']    = $val['ans_unlike'];
					$expertans['expert']		= $val['expert'];
					array_push($expertanswerdata, $expertans);
				}
			}
			$useranswers = QuestionAnswer::where("ques_id", $quesId)->where("expert", 0)->where("status", 1)->orderBy("ans_like", "DESC")->get();
			$useranswerdata = array();
			if (!empty($useranswers)) {
				foreach ($useranswers as $key => $val) {
					$user = User::where("id", $val['user_id'])->first();
					$images = $images_arr = array();
					if (!empty($val['image'])) {
						$images = explode(',', $val['image']);
					}
					foreach ($images as $queImg) {
						$imgPath['image'] = asset('upload/questionask') . "/" . $queImg;
						array_push($images_arr, $imgPath);
					}
					
					$userans['id']            = $val['id'];
					$userans['added_by']      = isset($user->name) ? $user->name : '';
					$userans['profile_image'] = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
					$userans['created_at']    = $val['created_at']->diffForHumans();
					$userans['answer']        = $val['answer'];
					$userans['images']        = $images_arr;
					$userans['ans_like']      = $val['ans_like'];
					$userans['ans_unlike']    = $val['ans_unlike'];
					$userans['expert']		  = $val['expert'];
					array_push($useranswerdata, $userans);
				}
			}
			$answerdata = array_merge($expertanswerdata, $useranswerdata);
			$message = "Get All Answers of a Question Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("question" => $questiondata, "answers" => $answerdata)]);
		} else {
			$message = "Question Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("question" => "", "answers" => [])]);
		}
	}
	public function likeAnswer(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("answer_id" => "")]);
		}
		$answerId = $request->answerId;
		$msg = '';
		if (!empty($userId) && !empty($answerId)) {
			$getAnswer = QuestionAnswer::where('id', $answerId)->first();
			if (!empty($getAnswer)) {
				$getAnswerLike = QuestionAnswerLike::where('user_id', $userId)->where('answer_id', $answerId)->first();
				$userLiked = !empty($getAnswerLike) ? $getAnswerLike->ans_like : 0;
				$userDisliked = !empty($getAnswerLike) ? $getAnswerLike->ans_unlike : 0;
				if ($userLiked==0) {
					$preLike = $getAnswer->ans_like;
					$preDislike = $getAnswer->ans_unlike;
					if ($userDisliked==1) {
						$preDislike = $preDislike - 1;
					}
					$like = $preLike + 1;
					$data = array(
						'ans_like' 	 => $like,
						'ans_unlike' => $preDislike,
						'updated_at' => date('Y-m-d H:i:s'),
					);
					$update = QuestionAnswer::where("id", $answerId)->update($data);
					if (!empty($getAnswerLike)) {
						$data1 = array(
							'ans_like' 	 => 1,
							'ans_unlike' => 0,
							'updated_at' => date('Y-m-d H:i:s'),
						);
						$update = QuestionAnswerLike::where("id", $getAnswerLike->id)->update($data1);
					} else {
						$data1 = array(
							'user_id' 	 => $userId,
							'answer_id'  => $answerId,
							'ans_like' 	 => 1,
							'ans_unlike' => 0,
							'created_at' => date('Y-m-d H:i:s'),
						);
						$insert = QuestionAnswerLike::insertGetId($data1);
					}
					$msg = 'Your Like Submitted Successfully.';
					return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => array("answer_id" => $answerId)]);
				} else {
					$msg = 'You have already liked this answer.';
					return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => array("answer_id" => $answerId)]);
				}
			} else {
				$msg = "Answer not Found!";
				return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("answer_id" => "")]);
			}
		} else {
			$msg = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("answer_id" => "")]);
		}
	}
	public function unlikeAnswer(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("answer_id" => "")]);
		}
		$answerId = $request->answerId;
		$msg = '';
		if (!empty($userId) && !empty($answerId)) {
			$getAnswer = QuestionAnswer::where('id', $answerId)->first();
			if (!empty($getAnswer)) {
				$getAnswerLike = QuestionAnswerLike::where('user_id', $userId)->where('answer_id', $answerId)->first();
				$userLiked = !empty($getAnswerLike) ? $getAnswerLike->ans_like : 0;
				$userDisliked = !empty($getAnswerLike) ? $getAnswerLike->ans_unlike : 0;
				if ($userDisliked==0) {
					$preLike = $getAnswer->ans_like;
					if ($userLiked==1) {
						$preLike = $preLike - 1;
					}
					$preUnLike = $getAnswer->ans_unlike;
					$unlike = $preUnLike + 1;
					$data = array(
						'ans_like' 	 => $preLike,
						'ans_unlike' => $unlike,
						'updated_at' => date('Y-m-d H:i:s'),
					);
					$update = QuestionAnswer::where("id", $answerId)->update($data);
					if (!empty($getAnswerLike)) {
						$data1 = array(
							'ans_like' 	 => 0,
							'ans_unlike' => 1,
							'updated_at' => date('Y-m-d H:i:s'),
						);
						$update = QuestionAnswerLike::where("id", $getAnswerLike->id)->update($data1);
					} else {
						$data1 = array(
							'user_id' 	 => $userId,
							'answer_id'  => $answerId,
							'ans_like' 	 => 0,
							'ans_unlike' => 1,
							'created_at' => date('Y-m-d H:i:s'),
						);
						$insert = QuestionAnswerLike::insertGetId($data1);
					}
					$msg = 'Your Unlike Submitted Successfully.';
					return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => array("answer_id" => $answerId)]);
				} else {
					$msg = 'You have already disliked this answer.';
					return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => array("answer_id" => $answerId)]);
				}
			} else {
				$msg = "Answer not Found!";
				return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("answer_id" => "")]);
			}
		} else {
			$msg = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("answer_id" => "")]);
		}
	}

	public function getChatGroupList(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("chatGroups" => [])]);
		}
		$search = $request->search;
		$type = $request->type;
		$status = $request->status;
		$teacher = User::where("id", $userId)->first();
		$allowedGroups = !empty($teacher) ? $teacher->allow_groups : '';
		$allowedGroups = explode(",", trim($allowedGroups));
		if(!empty($allowedGroups)){
			$chatGroups = ChatGroup::whereIn("id", $allowedGroups)
				->Where(function($query) use ($search) {
					if (isset($search) && !empty($search)) {
						$query->where("title", 'like', "%" . $search . "%");
					}
				})
				->Where(function($query) use ($type) {
					if (isset($type) && !empty($type)) {
						$query->where('type', '=', $type);
					}
				})
				->Where(function($query) use ($status) {
					if (isset($status) && !empty($status)) {
						$query->where('status', $status);
					}
				})
				->orderBy('sort_id', 'ASC')->get();
			$groupdata = array();
			if (!empty($chatGroups)) {
				foreach ($chatGroups as $chatkey => $val) {
					foreach(config('constant.CHAT_GROUP_TYPE') as $value => $key){
						if($val->type == $key){$group_type = ucwords(str_replace("_", " ", strtolower($value))); }
					}
					$groupdata[$chatkey]['id'] 			= $val['id'];
					$groupdata[$chatkey]['title'] 		= $val['title'];
					$groupdata[$chatkey]['image'] 		= ($val['image']) ? asset('upload/chat_images/'.$val['image']) : '';
					$groupdata[$chatkey]['description'] = $val['description'];
					$groupdata[$chatkey]['group_type'] 	= $group_type;
					$groupdata[$chatkey]['status'] 		= $val['status'];
					$groupdata[$chatkey]['users_count'] = count($val->joined_user_data->where('block_status', 0));
				}
				$message = "All Chat Groups Data.";
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("chatGroups" => $groupdata)]);
			} else {
				$message = "Chat Group Not Found!";
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("chatGroups" => [])]);
			}
		} else {
			$message = "You have not allowed in Chat Group, Please contact to Team!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("chatGroups" => [])]);
		}
	}
	public function getChatGroupMessages(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("group_data" => "", "message_data" => [])]);
		}
		$groupId  = $request->groupId;
		$page = $request->has('page') ? $request->get('page') : 1;
		$limit = $request->has('limit') ? $request->get('limit') : 10;
		$group = ChatGroup::findOrFail($groupId);
		$groupdata = array();
		$messagedata_arr = array();
		if (!empty($group)) {
			//$userGroup = ChatGroupUser::where("group_id", $groupId)->where("user_id", $userId)->first();
			$groupdata['id'] 			= $group['id'];
			$groupdata['title'] 		= $group['title'];
			$groupdata['image'] 		= !empty($group['image']) ? asset('upload/chat_images/' . $group['image']) : '';
			$groupdata['pin_msg'] 		= $group['pin_msg'];
			$groupdata['description'] 	= $group['description'];
			//$groupdata['mute_status'] 	= !empty($userGroup) ? $userGroup->mute_status : 0;
			$chat_messages_data = ChatMessage::where("group_id", $groupId)->orderBy("id", "DESC")->paginate($limit);
			// dd($group->chat_messages_data);
			if ($group->chat_messages_data->count()>0) {
				$offset = ($page - 1) * $limit;
				//$chat_messages_data = $group->chat_messages_data->slice($offset)->take($limit)->sortByDesc('id');
				foreach ($chat_messages_data as $key => $message) {
					if ($message->parent_id > 0) {
						if ($message->user_id == $userId) {
							$type = 3;
						} else {
							$type = 4;
						}
						if ($message->user_data->role_id == 3) {
						} elseif ($message->user_data->role_id == 2) {
							$type = 7;
						} else {
							$type = 8;
						}
					} else {
						if ($message->user_id == $userId) {
							$type = 1;
						} else {
							$type = 2;
						}
						if ($message->user_data->role_id == 3) {
						} elseif ($message->user_data->role_id == 2) {
							$type = 5;
						} else {
							$type = 6;
						}
					}
					$messagedata['id'] 			= $message->id;
					$messagedata['parent_id'] 	= $message->parent_id;
					//$messagedata['user_id'] 	= $message->user_id;
					$messagedata['user_name'] 	= $message->user_data->name;
					$messagedata['user_image'] 	= ($message->user_data->image) ? asset('upload/profile/' . $message->user_data->image) : '';
					$messagedata['image'] 		= ($message->status==1) ? !empty($message->image) ? $message->image : '' : '';
					$messagedata['video'] 		= ($message->status==1) ? !empty($message->video) ? $message->video : '' : '';
					$messagedata['message'] 	= ($message->status==1) ? $this->helper->checkBadWords($message->message) : 'This Message was Deleted!';
					$messagedata['created_at'] 	= $message->created_at->diffForHumans();
					$messagedata['type'] 		= $type;
					$messagedata['oldmsg_user_name'] 	= ($message->parent_id > 0) ? $message->oldmsg_data->user_data->name : '';
					$messagedata['oldmsg_user_image'] 	= ($message->parent_id > 0) ? ($message->oldmsg_data->user_data->image) ? asset('upload/profile/' . $message->oldmsg_data->user_data->image) : '' : '';
					$messagedata['oldmsg_image'] 		= ($message->status==1) ? ($message->parent_id > 0) ? !empty($message->oldmsg_data->image) ? $message->oldmsg_data->image : '' : '' : '';
					$messagedata['oldmsg_video'] 		= ($message->status==1) ? ($message->parent_id > 0) ? !empty($message->oldmsg_data->video) ? $message->oldmsg_data->video : '' : '' : '';
					$messagedata['oldmsg_message'] 		= ($message->status==1) ? ($message->parent_id > 0) ? $this->helper->checkBadWords($message->oldmsg_data->message) : '' : 'This Message was Deleted!';
					$messagedata['oldmsg_created_at'] 	= ($message->parent_id > 0) ? $message->oldmsg_data->created_at->diffForHumans() : '';
					array_push($messagedata_arr, $messagedata);
				}
			}
		}
		//$this->helper->trackingApi($userId, 'chat', '', $groupId, '', $deviceType);
		$message = "Get Chat Group Messages Successfully.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("group_data" => $groupdata, "message_data" => $messagedata_arr)]);
	}
	public function saveChatGroupMessage(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$deviceType = $request->deviceType;
		$userStatus = $this->isUserActive($userId,$bearerToken,$deviceType);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("messageId" => "")]);
		}
		$messageId  = ($request->messageId) ? $request->messageId : 0;
		$groupId  = $request->groupId;
		//$chatMessage = ($request->message) ? $this->helper->checkBadWords($request->message) : NULL;
		$chatMessage = ($request->message) ? $request->message : NULL;
		$getUserStatus = User::where("id", $userId)->whereRaw("find_in_set($groupId,allow_groups)")->first();
		if (empty($getUserStatus)) {
			$message = "You are not allow to send message here, Please contact to team first!";
			return response()->json(['statusCode' => 201, 'message' => $message, 'data' => array("messageId" => $messageId)]);
		}

		$s3 = AWS::createClient('s3');
		
		if(!empty($request->file('video'))) {
			$validator = Validator::make($request->all(), [
				//'message' => 'required',
				//'image' => 'required',
				'video' => 'mimes:mp4',
			]);

			if($validator->fails()){
				$msg = $validator->messages()->first();
				return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("messageId" => "")]);
			}
		}
		$image = $request->file('image');
		$video = $request->file('video');
		$imageUrl = $videoUrl = NULL;
		$destinationPath = public_path().'/upload/chat_message_images/';
		if($image){
			$originalFile = $image->getClientOriginalName();
			$imageFilename = "chat_".time().$originalFile;
			$image->move($destinationPath, $imageFilename);
			//S3 Upload
			$s3->putObject(array(
				'Bucket'     => env('AWS_BUCKET'),
				'Key'        => "assignment_data/".$imageFilename,
				'SourceFile' => $destinationPath.$imageFilename,
			));
			$imageUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $imageFilename;
			if(!empty($image) && file_exists( $destinationPath.$imageFilename )) {
				unlink( $destinationPath.$imageFilename );
			}
		}
		if($video){
			$originalFile = $video->getClientOriginalName();
			$videoFilename = "chat_".time().".mp4";
			$video->move($destinationPath, $videoFilename);
			//S3 Upload
			$s3->putObject(array(
				'Bucket'     => env('AWS_BUCKET'),
				'Key'        => "assignment_data/".$videoFilename,
				'SourceFile' => $destinationPath.$videoFilename,
			));
			$videoUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $videoFilename;
			if(!empty($video_file) && file_exists( $destinationPath.$videoFilename )) {
				unlink( $destinationPath.$videoFilename );
			}
		}
		$data = array(
			'parent_id'		=> $messageId,
			'group_id'		=> $groupId,
			'user_id'		=> $userId,
			'image'			=> $imageUrl,
			'video'			=> $videoUrl,
			'message'		=> $chatMessage,
			'created_at'	=> date('Y-m-d H:i:s'),
		);
		$insertId = ChatMessage::insertGetId($data);
		$chatMessage = ChatMessage::with('user_data','group_data')->where("id", $insertId)->first();
		if (!empty($chatMessage)) {
			$userName = $chatMessage->user_data->name;
			$groupName = $chatMessage->group_data->title;
			$getGroupUsers = ChatGroupUser::select("user_id")->where("group_id", $groupId)->where("block_status", 0)->where("mute_status", 0)->get();
			if (!empty($getGroupUsers) && !empty($getGroupUsers->toArray())) {
				foreach ($getGroupUsers as $value) {
					//$groupUserIds[] = $value['user_id'];
					$user = User::select("deviceToken")->where("id", "!=", $userId)->where("id", $value['user_id'])->first();
					$token = isset($user->deviceToken) ? $user->deviceToken : '';
					if ($token!='') {
						$title = 'GuruAthome';
						$click_action = 'Chat';
						$module_id = $groupId;
						$message = $userName . ' sent a message in ' . $groupName . ' ' . $chatMessage->message;
					//ak	$this->helper->notificationsend($token, $title, $message, $click_action, $module_id);
					}
				}
			}
		}
		$message = "Your chat message submitted successfully.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("messageId" => $insertId)]);
	}

	public function getNotifications(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("notifications" => [])]);
		}
		$notifications = Notification::where("user_id",$userId)->orderBy("id","DESC")->get();
		$notificationdata = array();
		if (!empty($notifications)) {
			foreach ($notifications as $key => $value) {
				$notificationdata[$key]['id']    = $value['id'];
				$notificationdata[$key]['message'] = $value['message'];
				$notificationdata[$key]['created_at'] = $value['created_at']->diffForHumans();
			}
			$message = "Get Notification List.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("notifications" => $notificationdata)]);
		} else {
			$message = "Notification not Found.";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("notifications" => "")]);
		}
	}
	public function notificationstatus(Request $request)
	{
		$userId = $request->header("userId");
		$noti = Notification::where('user_id', $userId)->where('status', 0)->update(['status' => 1]);
		$msg = "notification seen successfully";
		return response()->json(['statusCode' => 200, 'message' => $msg]);
	}
	public function sendMail($email, $stubject = NULL, $message = NULL)
    {

        require base_path("vendor/autoload.php");
        $mail = new PHPMailer(true);     // Passing `true` enables exceptions
        try {
            $mail->SMTPDebug = 0;
            $mail->isSMTP();
            $mail->Host = "smtp.gmail.com";
            $mail->Port = 587;
            $mail->SMTPSecure = "tls";
            $mail->SMTPAuth = true;
            $mail->Username = "<EMAIL>";
            $mail->Password = "trbcxmazrubpkfzt";
            $mail->addAddress($email, "User Name");
            $mail->Subject = $stubject;
            $mail->isHTML();
            $mail->Body = $message;
            $mail->setFrom("<EMAIL>");
            $mail->FromName = "Guru At Home";

            if ($mail->send()) {
                return 1;
            } else {
                return 0;
            }
        } catch (Exception $e) {
            return 0;
        }
    }

   


}
