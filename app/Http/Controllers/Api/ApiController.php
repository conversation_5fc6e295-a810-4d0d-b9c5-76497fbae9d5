<?php

namespace App\Http\Controllers\Api;
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Mail;
use App\Models\UserTemp;
//use App\Mail\SendMail;
use App\Models\AboutUs;
use App\Models\Chapter;
use App\Models\Contactus;
use App\Models\ContinueStudy;
use App\Models\CouponCode;
use App\Models\Coursefeature;
use App\Models\Coursefeq;
use App\Models\Courses;
use App\Models\Lession;
use App\Models\LiveClass;
use App\Models\LiveclassNotify;
use App\Models\Notification;
use App\Models\Page;
use App\Models\Popularvideo;
use App\Models\Portfolio;
use App\Models\QuestionAnswer;
use App\Models\QuestionAnswerLike;
use App\Models\QuestionAsk;
use App\Models\Quiz;
use App\Models\Quizoption;
use App\Models\Quizquestions;
use App\Models\RatingMessage;
use App\Models\RatingType;
use App\Models\RatingUser;
use App\Models\State;
use App\Models\StudentClass;
use App\Models\StudentExam;
use App\Models\StudentExamAnswer;
use App\Models\Subscription;
use App\Models\User;
use App\Models\UserSubscription;
use Image;
use DateTime;
use App\Models\Subject;
use App\Models\StudentApplyForDemo;
date_default_timezone_set('Asia/Kolkata');
use DB;
use Carbon\Carbon;
use PDF;
include public_path().'/razorpay-php/Razorpay.php';
use Razorpay\Api\Api;
use  App\Models\Category;
use App\Models\Banner;
	
use App\Models\StudentBookingFrees;
use App\Models\TeacherAvailabiltyOfSlotsDetails;
use App\Models\TeachingDetails;
use App\Models\TeacherAvailabiltyOfSlots;
use App\Models\StudentDemoClassFeedback;

use App\Models\Enroll;
use App\Models\StudentEnrollSchedule;
use App\Models\PurchaseClass;
use App\Models\Payment;
use App\Models\StudentContactSupportEnroll;



  use JWTAuth;

    use Tymon\JWTAuth\Exceptions\JWTException;
class ApiController extends Controller
{
	 function __construct($foo = null)
	 {
	 	
	 }

	public function getCitiesByState(Request $request)
	{
		$token='sss';
		return response()->json(compact('token'))->header("Access-Control-Allow-Origin",  "*");

	}
	public function sendmailtoemp(){

					$too="<EMAIL>";
					$subject="subject";
					$message="message message";
                    $to =$too;// ;$too.",<EMAIL>"                   //,{"email":"<EMAIL>"}
                    //dd($to);
                    $subject = $subject;
                    $from='<EMAIL>';
                    $headers = "From: " . strip_tags($from) . "\r\n";
                    $headers .= "MIME-Version: 1.0\r\n";
                    $headers .= "Content-Type: text/html; charset=ISO-8859-1\r\n";
                    $curl = curl_init();
                    curl_setopt_array($curl, array(
                    CURLOPT_URL => 'https://api.sendgrid.com/v3/mail/send',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS =>'{"personalizations":[{"to":[{"email":"'.$to.'","name":"No Limit"}],"subject":"'.$subject.'"}],"content": [{"type": "text/html", "value": "'.$message.'"}],"from":{"email":"<EMAIL>","name":"No Limit"}}',
                    CURLOPT_HTTPHEADER => array(
                    'Authorization: Bearer *********************************************************************',
                    'Content-Type: application/json'
                    ),
                    ));
                    $response = curl_exec($curl);
                    // print_r($response);die;
                    curl_close($curl);
         return 1;
    }

	public function authenticate(Request $request)
        {
        	     $credentials = $request->only('email', 'password');

        	 $validator=Validator::make($request->all(),[
                'email'=>'required',
                'password'=>'required',
            ]);
            if ($validator->fails()) {
                $result=['status'=>false,'message'=>$validator->errors()];
            }else{
            	
            	 $admin=User::where('email',$request->email)->first();
            	   if (!empty($admin)) {
            	   	if (Hash::check($request->password, $admin->password)) {  
            	   	      $token = JWTAuth::fromUser($admin);
            	   	           $result=['status'=>true,'token'=>$token,'data'=>$admin];
            	   	 }else{
                        $result=['status'=>false,'message'=>'Password do not match'];
                    }
                }else{
                        $result=['status'=>false,'message'=>'User not exist'];
                    }

            }
               return response()->json($result);
        }
       public function get_user(Request $request)
    {
    	//dd($request->header());
       
 
        $user = JWTAuth::user();
 
        return response()->json(['user' => $user]);
    }
	public function generateRandomString($length = 50)
	{
		$characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
		$charactersLength = strlen($characters);
		$randomString = '';
		for ($i = 0; $i < $length; $i++) {
			$randomString .= $characters[rand(0, $charactersLength - 1)];
		}
		return $randomString;
	}

	public function convertTimeinMiliseconds($hour,$minute,$seconds)
	{
	    $sec_to_milli = $seconds * 1000;            //seconds to milliseconds
	    $min_to_milli = $minute * 60 * 1000;        //minutes to milliseconds
	    $hrs_to_milli = $hour * 60 * 60 * 1000;     //hours to milliseconds

	    $milliseconds = $hrs_to_milli + $min_to_milli + $sec_to_milli;

	    return $milliseconds;
	}

	public function getStudentClass()
	{
		$studentClasses = StudentClass::where('status',1)->where('deleted',0)->orderBy('id', 'ASC')->get();
		$classdata = array();
		foreach ($studentClasses as $key => $val) {
			$classdata[$key]['id'] = $val['id'];
			$classdata[$key]['class_name'] = $val['class_name'];
		}
		$message = "Get All Student Classes List.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("student_classes" => $classdata)]);
	}

	public function getStates()
	{
		$states = State::orderBy('state', 'ASC')->get();
		$statedata = array();
		foreach ($states as $key => $val) {
			$statedata[$key]['id'] = $val['id'];
			$statedata[$key]['state_name'] = $val['state'];
		}
		$message = "Get All States List.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("states" => $statedata)]);
	}

	public function getuserDetail($userid)
	{
		$user = User::where('id', '=', $userid)->first();
		if ($user->role_id == 1) {
			$role = "Administrator";
		}else if ($user->role_id == 2) {
			$role = "Teacher";
		}else{
			$role = "User";
		}
		//$features = UserFeature::where("user_id", $userid)->get();
		$featureArray = array();
		if (!empty($features)) {
			foreach ($features as $val) {
				$featureArray[] = array("name" => $val->name);
			}
		}
		$data = array(
			'userId' => ($user->id) ? $user->id : '',
			//'role_id' => ($role) ? $role : '',
			'name' => ($user->name) ? $user->name : '',
			'email' => ($user->email) ? $user->email : '',
			'phone' => ($user->phone) ? $user->phone : '',
			'gender' => ($user->gender) ? $user->gender : '',
			'dob' => ($user->dob) ? date('m/d/Y', strtotime($user->dob)) : '',
			'class_name' => ($user->class_name) ? $user->class_name : '',
			'school_college' => ($user->school_college) ? $user->school_college : '',
			'state' => ($user->state) ? $user->state : '',
			'city' => ($user->city) ? $user->city : '',
			'image' => ($user->image) ? asset('upload/profile') . '/' . $user->image : '',
			'api_token' => ($user->api_token) ? $user->api_token : '',
			'otp_match' => ($user->otp_match) ? $user->otp_match : '',
			//'firebase_user_id' => ($user->firebase_user_id) ? $user->firebase_user_id : '',
			//'features' => $featureArray,
		);
		return $data;
	}

	public function getStudentDetails($userid){

			$user= JWTAuth::user();
			if(!is_null($user))
			{
				//		if(is_numeric($userid)){
				//	$user = User::where('id', '=', $userid)->first();
				$code =200;
				$msg ="Data";
				$data = $user;
				$returndata = array("data" => $data);
		}
		else
		{
			$code = 400;
			$msg = 'Invalid Request';
			$returndata = array("id" => "");
		}
		return response()->json(['statusCode' => $code, 'message' => $msg, 'data' => $returndata]);
	}
	//get all active class
	public function getallClass(){
		if(is_numeric($userid)){
			$user = User::where('id', '=', $userid)->first();
			$code =200;
			$msg ="Data";
			$data = $user;
			$returndata = array("data" => $data);
		}
		else
		{
			$code = 400;
			$msg = 'Invalid Request';
			$returndata = array("id" => "");
		}
		return response()->json(['statusCode' => $code, 'message' => $msg, 'data' => $returndata]);
	}
	//student login
	public function login(Request $request)
	{
	    //$this->sms("8448323559",1234);die;
		$validator = Validator::make($request->all(), [
			'email' => 'required',
			'password' => 'required',
			
		]);

		if($validator->fails()){
        	$msg = $validator->messages()->first();
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}
		//dd($request->all());
		$email		        = !empty($_REQUEST['email']) ? trim($_REQUEST['email']) : '';
		$password           = !empty($_REQUEST['password']) ? trim($_REQUEST['password']) : '';
		//firebase_user_id
		if (!empty($email) && !empty($password)) {
			if (is_numeric($email)) {
				$checkUser = User::where("phone", $email)->where("deleted",0)->where('role_id',6)->first();
				if (!empty($checkUser)) {
					if (Hash::check($password, $checkUser->password)) {
						if (Auth::attempt(['phone' => $email, 'password' => $password,"deleted"=>0])) {
							$user = Auth::user();
						//	dd($user);
							if($user->role_id != 6){
								$msg = "You are not allowed to login here!";
								return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
							}
							if($user->status != 1){
								$msg = "Your account not activated, Please contact to Team!";
								return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
							}
							if($user->deleted != 0){
								$msg = "Your account deleted, Please contact to Team!";
								return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
							}
							
							$code = 200;
							$msg = 'Login successfully.';
							$returndata = $user;
						}else{
							$code = 400;
							$msg = 'Mobile number and Password not matched!';
							$returndata = array("id" => "");
						}
					}else{
						$code = 400;
						$msg = 'Password not matched!';
						$returndata = array("id" => "");
					}
				}else{
					$code = 400;
					$msg = 'Mobile number not matched!';
					$returndata = array("id" => "");
				}
			} else {
				$checkUser =User::where("email", $email)->where("deleted",0)->where('role_id',6)->first();
				if (!empty($checkUser)) {
					if (Hash::check($password, $checkUser->password)) {
						if(Auth::attempt(['email' => $email, 'password' => $password,"deleted"=>0])){
							$user = Auth::user();
						//	dd($user);
								   	      $token = JWTAuth::fromUser($checkUser);
							if($user->role_id != 6){
								$msg = "You are not allowed to login here!";
								return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
							}
							if($user->status != 1){
								$msg = "Your account not activated, Please contact to Team!";
								return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
							}
							if($user->deleted != 0){
								$msg = "Your account deleted, Please contact to Team!";
								return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
							}
							//dd($user);

							//$otp = rand(1111, 9999);
							//$user['token'] =  $user->createToken('MyApp')->accessToken;
							//$user->generateToken();
							//$userss = User::find($user->id);
							//$userss->deviceToken = $deviceToken;
							//$userss->otp_match = $otp;
							//$userss->save();
							//$this->sms($user->phone, $otp);
							//$this->sendEmail($user->email, 'Guruathome: Verify OTP', $data = array('userName' => $user->name, 'message' => '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: ' . $otp . '</p>'));

							$code = 200;
							$msg = 'Login successfully.';
						
							$user->api_token = $token;
							$returndata = $user;

							//$returndata['image'] = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
							//return response()->json(['success' => $success], 200);
						}else{
							$code = 400;
							$msg = 'Email id and Password not matched!';
							$returndata = array("id" => "");
						}
					}else{
						$code = 400;
						$msg = 'Password not matched!';
						$returndata = array("id" => "");
					}
				}else{
					$code = 400;
					$msg = 'Email id not matched!';
					$returndata = array("id" => "");
				}
			}
		}else{
			$code = 400;
			$msg = 'Email id or Mobile number not found!';
			$returndata = array("id" => "");
		}
		return response()->json(['statusCode' => $code, 'message' => $msg, 'data' => $returndata]);
	}

	// public function login(Request $request)
	// {
	//     //$this->sms("8448323559",1234);die;
	// 	$validator = Validator::make($request->all(), [
	// 		'email' => 'required',
	// 		'password' => 'required',
	// 		'deviceToken' => 'required',
	// 	]);

	// 	if($validator->fails()){
 //        	$msg = $validator->messages()->first();
	// 		return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
	// 	}
	// 	//dd($request->all());
	// 	$email		        = !empty($_REQUEST['email']) ? trim($_REQUEST['email']) : '';
	// 	//$mobile		    = !empty($_REQUEST['mobile']) ? trim($_REQUEST['mobile']) : '';
	// 	$password           = !empty($_REQUEST['password']) ? trim($_REQUEST['password']) : '';
	// 	$deviceToken        = !empty($_REQUEST['deviceToken']) ? trim($_REQUEST['deviceToken']) : '';
	// 	//$firebase_user_id = !empty($_REQUEST['firebase_user_id']) ? trim($_REQUEST['firebase_user_id']) : '';
	// 	//firebase_user_id
	// 	if (!empty($email) && !empty($password)) {
	// 		if (is_numeric($email)) {
	// 			$checkUser = User::where("phone", $email)->first();
	// 			if (!empty($checkUser)) {
	// 				if (Hash::check($password, $checkUser->password)) {
	// 					if (Auth::attempt(['phone' => $email, 'password' => $password])) {
	// 						$user = Auth::user();
	// 						if($user->role_id != 3){
	// 							$msg = "You are not allowed to login here!";
	// 							return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
	// 						}
	// 						if($user->status != 1){
	// 							$msg = "Your account not activated, Please contact to Team!";
	// 							return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
	// 						}
	// 						$otp = rand(1111, 9999);
	// 						$user->generateToken();
	// 						$userss = User::find($user->id);
	// 						$userss->deviceToken = $deviceToken;
	// 						$userss->otp_match = $otp;
	// 						$userss->save();
	// 					//	$this->sms($user->phone, $otp);
	// 				//		$this->sendEmail($user->email, 'Guruathome: Verify OTP', $data = array('userName' => $user->name, 'message' => '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: ' . $otp . '</p>'));

	// 						$code = 200;
	// 						$msg = 'Login successfully.';
	// 						$returndata = $user;
	// 						$returndata['image'] = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
	// 					}else{
	// 						$code = 400;
	// 						$msg = 'Mobile number and Password not matched!';
	// 						$returndata = array("id" => "");
	// 					}
	// 				}else{
	// 					$code = 400;
	// 					$msg = 'Password not matched!';
	// 					$returndata = array("id" => "");
	// 				}
	// 			}else{
	// 				$code = 400;
	// 				$msg = 'Mobile number not matched!';
	// 				$returndata = array("id" => "");
	// 			}
	// 		} else {
	// 			$checkUser = User::where("email", $email)->first();
	// 			if (!empty($checkUser)) {
	// 				if (Hash::check($password, $checkUser->password)) {
	// 					if(Auth::attempt(['email' => $email, 'password' => $password])){
	// 						$user = Auth::user();
	// 						if($user->role_id != 3){
	// 							$msg = "You are not allowed to login here!";
	// 							return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
	// 						}
	// 						if($user->status != 1){
	// 							$msg = "Your account not activated, Please contact to Team!";
	// 							return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
	// 						}
	// 						$otp = rand(1111, 9999);
	// 						//$user['token'] =  $user->createToken('MyApp')->accessToken;
	// 						$user->generateToken();
	// 						$userss = User::find($user->id);
	// 						$userss->deviceToken = $deviceToken;
	// 						$userss->otp_match = $otp;
	// 						$userss->save();
	// 						$this->sms($user->phone, $otp);
	// 						$this->sendEmail($user->email, 'Guruathome: Verify OTP', $data = array('userName' => $user->name, 'message' => '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: ' . $otp . '</p>'));

	// 						$code = 200;
	// 						$msg = 'Login successfully.';
	// 						$returndata = $user;
	// 						$returndata['image'] = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
	// 						//return response()->json(['success' => $success], 200);
	// 					}else{
	// 						$code = 400;
	// 						$msg = 'Email id and Password not matched!';
	// 						$returndata = array("id" => "");
	// 					}
	// 				}else{
	// 					$code = 400;
	// 					$msg = 'Password not matched!';
	// 					$returndata = array("id" => "");
	// 				}
	// 			}else{
	// 				$code = 400;
	// 				$msg = 'Email id not matched!';
	// 				$returndata = array("id" => "");
	// 			}
	// 		}
	// 	}else{
	// 		$code = 400;
	// 		$msg = 'Email id or Mobile number not found!';
	// 		$returndata = array("id" => "");
	// 	}
	// 	return response()->json(['statusCode' => $code, 'message' => $msg, 'data' => $returndata]);
	// }


	//create by akhil register student
	public function register(Request $request)
	{
			//dd($request->all());
		$validator = Validator::make($request->all(), [
			//'name' => 'required|regex:/^[\pL\s\-]+$/u',
			//'email' => 'required|email|unique:users,email',
			'email' => 'required|email',
			
			//'phone' => 'numeric|min:10|unique:users,phone',
			'password' => 'required|min:6',
			'confirm_password' => 'required|min:6|max:20|same:password',
		]);

		if($validator->fails()){
        	$msg = $validator->messages()->first();
			//return $this->sendError($validator->messages()->first());
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}

	    //echo '<pre />'; print_r($request->all()); die;
		$role 				= 6;//ucwords($request->input('role'));//2 for teacher 6 for student
		//$name 				= ucwords($request->input('name'));
		$email 				= $request->input('email');
		//$phone 				= $request->input('phone');
		$phone="";
		$password 			= $request->input('password');
		$confirm_password 	= $request->input('confirm_password');
		$coupon_code = $request->input('coupon_code');
		//	$gender 			= $request->input('gender');
		///$class_name 		= $request->input('class_name');
		//$state 				= $request->input('state');
		//$city 				= $request->input('city');
		//$deviceToken 		= $request->input('deviceToken');
		//$firebase_user_id = $request->input('firebase_user_id');
		$msg = '';
		if ( !empty($email)  && !empty($password)) {
			if ($password != $confirm_password) {
				$msg = "Password and Confirm password not matched!";
				return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
			}
			$usercheck = User::where('email', $email)->where("deleted",0)->first();
			//dd($usercheck);
			if (!empty($usercheck)) {
				$msg = "Email id already exists. Please login directly.";
				return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
			} else {
				
				if ($phone!=null) {
					
					$phonecheck = User::where('phone', $phone)->where("deleted",0)->first();
					if(!empty($phonecheck))
					{
						$msg = "Phone number already exists. Please login directly.";
						return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
					}
					else
					{
						$imagess = '';
						if (isset($_FILES['image']['name']) && $_FILES['image']['name']!='') {
							$profileimagename = $_FILES['image']['name'];
							$tmpimage1 = $_FILES['image']['tmp_name'];
							$newprofileImage = rand(00000, 99999) . date('d') . $profileimagename;
							$location = "upload/profile/";
							move_uploaded_file($tmpimage1, $location . $newprofileImage);
							$url = 'upload/profile/' . $newprofileImage;
							$img = Image::make($url)->resize(200, 200);
							$imagess =  $img->basename;
						}
							$otp = rand(1111, 9999);
							$data = array(
							'role_id' 	=> $role,
							'name' 		=> $name,
							'email' 	=> $email,
							'phone' 	=> $phone,
							'password' 	=> bcrypt($password),
							'userpass' 	=> $password,
							'otp'=>$otp,
							'coupon_code'=>$coupon_code,
								//'image'   => $imagess,
							//	'gender' 	=> $gender,
								//'dob'     => $dob,
							//	'class_name' => $class_name,
								//'school_college' => $school_college,
							//	'state' 	=> $state,
							//	'city' 		=> $city,
							//	'remember_token' => Str::random(60),
							//	'api_token' => Str::random(60),
							//	'devicetoken' => $deviceToken,
							//'otp_match' => $otp,
							'status'    => 1,
							//'firebase_user_id' => $firebase_user_id,
							//'isDevice'=>'App',
							'created_at' => date('Y-m-d H:i:s'),
							);
									$msg = ' Otp Send Successfully Your Email address.';
								$userId= UserTemp::insertGetId($data);
								$refercode = strtoupper(substr($this->generateRandomString(), 0, 3));
				//	$refercode = strtoupper(substr($name, 0, 3));
					$refercode = $refercode . $user->id;
					$user->refer_code  = $refercode;
					$user->save(); 
										$subject ='Guruathome: Verify your account';
								 $message ="Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: "  .$otp  ;


									$this->sendMail($request->email, $subject,$message);
						
								// $this->sendMail($email, 'Guruathome: Verify your account', $data = array('userName' => $name, 'message' => '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: ' . $otp . '</p>'));

							$returndata = array();
							//	$returndata = $this->getuserDetail($userId);
							//	$returndata['otp'] = $otp;
							return response()->json(['statusCode' => 200,"id"=>$userId, 'message' => $msg]);
						}
					}
				 else 
				 {
				
					$imagess = '';
					if (isset($_FILES['image']['name']) && $_FILES['image']['name']!='') {
						$profileimagename = $_FILES['image']['name'];
						$tmpimage1 = $_FILES['image']['tmp_name'];
						$newprofileImage = rand(00000, 99999) . date('d') . $profileimagename;
						$location = "upload/profile/";
						move_uploaded_file($tmpimage1, $location . $newprofileImage);
						$url = 'upload/profile/' . $newprofileImage;
						$img = Image::make($url)->resize(200, 200);
						$imagess =  $img->basename;
					}
					$otp = rand(1111, 9999);
					$data = array(
						'role_id' 	=> $role,
					//	'name' 		=> $name,
						'email' 	=> $email,
					//	'phone' 	=> $phone,
						'password' 	=>bcrypt($password), //Hash::make($password),
						'userpass' 	=> $password,
						'coupon_code'=>$coupon_code,
						'otp'=>$otp,
						//'image'   => $imagess,
					//	'gender' 	=> $gender,
						//'dob'     => $dob,
					//	'class_name' => $class_name,
						//'school_college' => $school_college,
					//	'state' 	=> $state,
					//	'city' 		=> $city,
					//	'remember_token' => Str::random(60),
					//	'api_token' => Str::random(60),
					//	'devicetoken' => $deviceToken,
						//'otp_match' => $otp,
						'status'    => 1,
						//'firebase_user_id' => $firebase_user_id,
						//'isDevice'=>'App',
						'created_at' => date('Y-m-d H:i:s'),
						'updated_at' => date('Y-m-d H:i:s'),	
					);

				
				$userId=	UserTemp::insertGetId($data);
				//	$userId = User::insertGetId($data);

					 $user=UserTemp::find($userId);
					$refercode = strtoupper(substr($this->generateRandomString(), 0, 3));
				//	$refercode = strtoupper(substr($name, 0, 3));
					$refercode = $refercode . $user->id;
					$user->refer_code = $refercode;
					$user->save(); 
					//$msg = 'User Registration Completed Successfully.';
					$msg = ' Otp Send Successfully Your Email address.';
		//			$this->sms($phone, $otp);
				//	$this->addNotification($userId,$msg);
					/*$data = array('username' => $name, 'OTP' =>  $otp, 'msg' =>  $msg);
					Mail::send('emails.register', $data, function ($message) {
						$email = $_POST['email'];
						$message->to($email, 'From Guruathome')->subject('Guruathome: Verify your account');
						$message->from('<EMAIL>', 'Guruathome');
					});*/
					$subject ='Guruathome: Verify your account';
				 $message ="Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: "  .$otp  ;


			
				$this->sendMail($request->email, $subject,$message);
			
					// $this->sendMail($email, 'Guruathome: Verify your account', $data = array('userName' => $name, 'message' => '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: ' . $otp . '</p>'));

					$returndata = array();
				//	$returndata = $this->getuserDetail($userId);
				//	$returndata['otp'] = $otp;
					return response()->json(['statusCode' => 200,"id"=>$userId, 'message' => $msg,'OTP'=>$otp]);
				}
			}
		} else {
			$msg = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}
		//$token = auth()->login($user);
		//return $this->respondWithToken($token);
	}
	// public function registerold(Request $request) //19-10-2022
	// {
	// 	//dd($request->all());
	// 	$validator = Validator::make($request->all(), [
	// 		'name' => 'required|regex:/^[\pL\s\-]+$/u',
	// 		'email' => 'required|email|unique:users,email',
	// 		'phone' => 'numeric|min:10|unique:users,phone',
	// 		'password' => 'required|min:6',
	// 		'confirm_password' => 'required|min:6|max:20|same:password',
	// 	]);

	// 	if($validator->fails()){
 //        	$msg = $validator->messages()->first();
	// 		//return $this->sendError($validator->messages()->first());
	// 		return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
	// 	}

	//     //echo '<pre />'; print_r($request->all()); die;
	// 	$role 				= ucwords($request->input('role'));//2 for teacher 3 for student
	// 	$name 				= ucwords($request->input('name'));
	// 	$email 				= $request->input('email');
	// 	$phone 				= $request->input('phone');
	// 	$password 			= $request->input('password');
	// 	$confirm_password 	= $request->input('confirm_password');
	// 	$gender 			= $request->input('gender');
	// 	$class_name 		= $request->input('class_name');
	// 	$state 				= $request->input('state');
	// 	$city 				= $request->input('city');
	// 	$deviceToken 		= $request->input('deviceToken');
	// 	//$firebase_user_id = $request->input('firebase_user_id');
	// 	$msg = '';
	// 	if (!empty($name)  && !empty($email)  && !empty($password)) {
	// 		if ($password != $confirm_password) {
	// 			$msg = "Password and Confirm password not matched!";
	// 			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
	// 		}
	// 		$usercheck = User::where('email', $email)->first();
	// 		//dd($usercheck);
	// 		if (!empty($usercheck)) {
	// 			$msg = "Email id already exists. Please login directly.";
	// 			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
	// 		} else {
				
				
	// 			if ($phone!=null) {
	// 				$phonecheck = User::where('phone', $phone)->first();
	// 				if(!empty($phonecheck))
	// 				{
	// 					$msg = "Phone number already exists. Please login directly.";
	// 					return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
	// 				}
	// 				else
	// 				{
	// 					$imagess = '';
	// 				if (isset($_FILES['image']['name']) && $_FILES['image']['name']!='') {
	// 					$profileimagename = $_FILES['image']['name'];
	// 					$tmpimage1 = $_FILES['image']['tmp_name'];
	// 					$newprofileImage = rand(00000, 99999) . date('d') . $profileimagename;
	// 					$location = "upload/profile/";
	// 					move_uploaded_file($tmpimage1, $location . $newprofileImage);
	// 					$url = 'upload/profile/' . $newprofileImage;
	// 					$img = Image::make($url)->resize(200, 200);
	// 					$imagess =  $img->basename;
	// 				}
	// 				$otp = rand(1111, 9999);
	// 				$data = array(
	// 					'role_id' 	=> $role,
	// 					'name' 		=> $name,
	// 					'email' 	=> $email,
	// 					'phone' 	=> $phone,
	// 					'password' 	=> bcrypt($password),
	// 					'userpass' 	=> $password,
	// 					'otp'=>$otp,
	// 					//'image'   => $imagess,
	// 				//	'gender' 	=> $gender,
	// 					//'dob'     => $dob,
	// 				//	'class_name' => $class_name,
	// 					//'school_college' => $school_college,
	// 				//	'state' 	=> $state,
	// 				//	'city' 		=> $city,
	// 				//	'remember_token' => Str::random(60),
	// 				//	'api_token' => Str::random(60),
	// 				//	'devicetoken' => $deviceToken,
	// 					//'otp_match' => $otp,
	// 					'status'    => 1,
	// 					//'firebase_user_id' => $firebase_user_id,
	// 					//'isDevice'=>'App',
	// 					'created_at' => date('Y-m-d H:i:s'),
	// 				);
	// 				$msg = ' Otp Send Successfully Your Email address.';
	// 			$userId= UserTemp::insertGetId($data);
	// 					$subject ='Guruathome: Verify your account';
	// 			 $message ="Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: "  .$otp  ;


	// 				$this->sendMail($request->email, $subject,$message);
			
	// 				// $this->sendMail($email, 'Guruathome: Verify your account', $data = array('userName' => $name, 'message' => '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: ' . $otp . '</p>'));

	// 				$returndata = array();
	// 			//	$returndata = $this->getuserDetail($userId);
	// 				$returndata['otp'] = $otp;
	// 				return response()->json(['statusCode' => 200,"id"=>$userId, 'message' => $msg]);
	// 				}
	// 			} else {
	// 				$imagess = '';
	// 				if (isset($_FILES['image']['name']) && $_FILES['image']['name']!='') {
	// 					$profileimagename = $_FILES['image']['name'];
	// 					$tmpimage1 = $_FILES['image']['tmp_name'];
	// 					$newprofileImage = rand(00000, 99999) . date('d') . $profileimagename;
	// 					$location = "upload/profile/";
	// 					move_uploaded_file($tmpimage1, $location . $newprofileImage);
	// 					$url = 'upload/profile/' . $newprofileImage;
	// 					$img = Image::make($url)->resize(200, 200);
	// 					$imagess =  $img->basename;
	// 				}
	// 				$otp = rand(1111, 9999);
	// 				$data = array(
	// 					'role_id' 	=> $role,
	// 					'name' 		=> $name,
	// 					'email' 	=> $email,
	// 					'phone' 	=> $phone,
	// 					'password' 	=>bcrypt($password), //Hash::make($password),
	// 					'userpass' 	=> $password,
	// 					'otp'=>$otp,
	// 					//'image'   => $imagess,
	// 				//	'gender' 	=> $gender,
	// 					//'dob'     => $dob,
	// 				//	'class_name' => $class_name,
	// 					//'school_college' => $school_college,
	// 				//	'state' 	=> $state,
	// 				//	'city' 		=> $city,
	// 				//	'remember_token' => Str::random(60),
	// 				//	'api_token' => Str::random(60),
	// 				//	'devicetoken' => $deviceToken,
	// 					//'otp_match' => $otp,
	// 					'status'    => 1,
	// 					//'firebase_user_id' => $firebase_user_id,
	// 					//'isDevice'=>'App',
	// 					'created_at' => date('Y-m-d H:i:s'),
	// 					'updated_at' => date('Y-m-d H:i:s'),	
	// 				);

	// 			$userId=	UserTemp::insertGetId($data);
	// 			//	$userId = User::insertGetId($data);
	// 				/* $user=User::find($userId);
	// 				$refercode = strtoupper(substr($name, 0, 3));
	// 				$refercode = $refercode . $user->id;
	// 				$user->refercode = $refercode;
	// 				$user->save(); */
	// 				//$msg = 'User Registration Completed Successfully.';
	// 				$msg = ' Otp Send Successfully Your Email address.';
	// 	//			$this->sms($phone, $otp);
	// 			//	$this->addNotification($userId,$msg);
	// 				/*$data = array('username' => $name, 'OTP' =>  $otp, 'msg' =>  $msg);
	// 				Mail::send('emails.register', $data, function ($message) {
	// 					$email = $_POST['email'];
	// 					$message->to($email, 'From Guruathome')->subject('Guruathome: Verify your account');
	// 					$message->from('<EMAIL>', 'Guruathome');
	// 				});*/
	// 				$subject ='Guruathome: Verify your account';
	// 			 $message ="Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: "  .$otp  ;


	// 				$this->sendMail($request->email, $subject,$message);
			
	// 				// $this->sendMail($email, 'Guruathome: Verify your account', $data = array('userName' => $name, 'message' => '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: ' . $otp . '</p>'));

	// 				$returndata = array();
	// 			//	$returndata = $this->getuserDetail($userId);
	// 				$returndata['otp'] = $otp;
	// 				return response()->json(['statusCode' => 200,"id"=>$userId, 'message' => $msg]);
	// 			}
	// 		}
	// 	} else {
	// 		$msg = "Wrong Paramenter Passed!";
	// 		return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
	// 	}
	// 	//$token = auth()->login($user);
	// 	//return $this->respondWithToken($token);
	// }

	// public function register(Request $request)
	// {
	// 	$validator = Validator::make($request->all(), [
	// 		'name' => 'required|regex:/^[\pL\s\-]+$/u',
	// 		'email' => 'required|email|unique:users,email',
	// 		'phone' => 'required|numeric|min:10|unique:users,phone',
	// 		'password' => 'required|min:6',
	// 		'confirm_password' => 'required|min:6|max:20|same:password',
	// 	]);

	// 	if($validator->fails()){
 //        	$msg = $validator->messages()->first();
	// 		//return $this->sendError($validator->messages()->first());
	// 		return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
	// 	}

	//     //echo '<pre />'; print_r($request->all()); die;
	// 	$name 				= ucwords($request->input('name'));
	// 	$email 				= $request->input('email');
	// 	$phone 				= $request->input('phone');
	// 	$password 			= $request->input('password');
	// 	$confirm_password 	= $request->input('confirm_password');
	// 	$gender 			= $request->input('gender');
	// 	$class_name 		= $request->input('class_name');
	// 	$state 				= $request->input('state');
	// 	$city 				= $request->input('city');
	// 	$deviceToken 		= $request->input('deviceToken');
	// 	//$firebase_user_id = $request->input('firebase_user_id');
	// 	$msg = '';
	// 	if (!empty($name)  && !empty($email) && !empty($phone) && !empty($password)) {
	// 		if ($password != $confirm_password) {
	// 			$msg = "Password and Confirm password not matched!";
	// 			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
	// 		}
	// 		$usercheck = User::where('email', $email)->first();
	// 		if (!empty($usercheck)) {
	// 			$msg = "Email id already exists. Please login directly.";
	// 			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
	// 		} else {
	// 			$phonecheck = User::where('phone', $phone)->first();
	// 			if (!empty($phonecheck)) {
	// 				$msg = "Phone number already exists. Please login directly.";
	// 				return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
	// 			} else {
	// 				$imagess = '';
	// 				if (isset($_FILES['image']['name']) && $_FILES['image']['name']!='') {
	// 					$profileimagename = $_FILES['image']['name'];
	// 					$tmpimage1 = $_FILES['image']['tmp_name'];
	// 					$newprofileImage = rand(00000, 99999) . date('d') . $profileimagename;
	// 					$location = "upload/profile/";
	// 					move_uploaded_file($tmpimage1, $location . $newprofileImage);
	// 					$url = 'upload/profile/' . $newprofileImage;
	// 					$img = Image::make($url)->resize(200, 200);
	// 					$imagess =  $img->basename;
	// 				}
	// 				$otp = rand(1111, 9999);
	// 				$data = array(
	// 					'role_id' 	=> 3,
	// 					'name' 		=> $name,
	// 					'email' 	=> $email,
	// 					'phone' 	=> $phone,
	// 					'password' 	=> bcrypt($password),
	// 					'userpass' 	=> $password,
	// 					//'image'   => $imagess,
	// 					'gender' 	=> $gender,
	// 					//'dob'     => $dob,
	// 					'class_name' => $class_name,
	// 					//'school_college' => $school_college,
	// 					'state' 	=> $state,
	// 					'city' 		=> $city,
	// 					'remember_token' => Str::random(60),
	// 					'api_token' => Str::random(60),
	// 					'devicetoken' => $deviceToken,
	// 					'otp_match' => $otp,
	// 					'status'    => 1,
	// 					//'firebase_user_id' => $firebase_user_id,
	// 					//'isDevice'=>'App',
	// 					'created_at' => date('Y-m-d H:i:s'),
	// 				);
	// 				$userId = User::insertGetId($data);
	// 				/* $user=User::find($userId);
	// 				$refercode = strtoupper(substr($name, 0, 3));
	// 				$refercode = $refercode . $user->id;
	// 				$user->refercode = $refercode;
	// 				$user->save(); */
	// 				$msg = 'User Registration Completed Successfully.';
	// 				$this->sms($phone, $otp);
	// 			//	$this->addNotification($userId,$msg);
	// 				/*$data = array('username' => $name, 'OTP' =>  $otp, 'msg' =>  $msg);
	// 				Mail::send('emails.register', $data, function ($message) {
	// 					$email = $_POST['email'];
	// 					$message->to($email, 'From Guruathome')->subject('Guruathome: Verify your account');
	// 					$message->from('<EMAIL>', 'Guruathome');
	// 				});*/
	// 				$this->sendEmail($email, 'Guruathome: Verify your account', $data = array('userName' => $name, 'message' => '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: ' . $otp . '</p>'));
	// 				$returndata = array();
	// 				$returndata = $this->getuserDetail($userId);
	// 				$returndata['otp'] = $otp;
	// 				return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => $returndata]);
	// 			}
	// 		}
	// 	} else {
	// 		$msg = "Wrong Paramenter Passed!";
	// 		return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
	// 	}
	// 	//$token = auth()->login($user);
	// 	//return $this->respondWithToken($token);
	// }
	//student match otp for login
	public function otpMatch(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'userId' => 'required|numeric',
			'otp' => 'required|numeric',
		]);

		if($validator->fails()){
        	$msg = $validator->messages()->first();
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}
		$userId = $request->input('userId');
		$otp = $request->input('otp');
		if (empty($otp) || empty($userId)) {
			return response()->json(['statusCode' => 400, 'message' => 'Wrong parameter Passed!', 'data' => array("id" => "")]);
		}
		$user = UserTemp::where('otp', $otp)->where("id", $userId)->first();
		//	dd($user);
		$status=1;
		// if( $user->role_id==6)
		// {
		// 	$status = 0;
		// }
		if ($user) {
			$data = array(
				"name"=>$user->name,
				"role_id"=>$user->role_id,
				"email"=>$user->email,
				"country_code"=>$user->country_code,
				"country_flag"=>$user->country_flag,
				"phone"=>$user->phone,
				"password"=>$user->password,
				"userpass"=>$user->userpass,
				"status"=>$status,
				"refer_code"=>$user->refer_code,
				'api_token' => Str::random(60),
				'created_at' => date('Y-m-d H:i:s'),
				'updated_at' => date('Y-m-d H:i:s'),			
			);
		//	dd($data);
		//	DB::table('users')->where('id', $userId)->update(array('status' => 1));
		$users=	User::insertGetId($data);
		UserTemp::where("id",$user->id)->delete();
			$returndata = array();
			$returndata = $this->getuserDetail($users);


			$select_type="";
			$data =[];
			//$AllData['banners_url'] =asset('upload/banners/');
			$AllData['class_images'] =url("/upload/class/");
			$AllData['course_images'] =url('/course/');
			$AllData['subject_images'] =url('/upload/subject/');
			//$AllData['banner'] = Banner::where("deleted",0)->where("status",1)->get();
			$AllData['classes'] = StudentClass::where("deleted",0)->where("status",1)->get();
			$AllData['category'] = Category::where("deleted",0)->where("status",1)->get();
			$message = "";
				$select_type.="All";
				if(!empty($AllData['classes']))
				{
					foreach($AllData['classes'] as $cls){
						$cls['subject_Details'] = Subject::where("class_id",$cls->id)->where('status',1)->get();

					}

				}
				else
				{
					$cls['subject_Details'] = Subject::where('status',1)->get();
				}
				if(!empty($AllData['category']))
				{
					foreach($AllData['category'] as $cat){
						$cat['course_Details'] = Courses::where("category_id",$cat->id)->where('status',1)->get();
					}
				}
				else
				{
				$cat['course_Details'] = Courses::where('status',1)->get();

				}
				array_push($data,$AllData);
			//	$returndata->class_subject_details = $data;
		//	$this->smsWithTemplate($user->phone, 'AfterVerificationAccount', '+************', '<EMAIL>');//ak
			return response()->json(['statusCode' => 200, 'message' => 'Account Verified Successfully.',"class_subject_details"=>$data, 'data' => $returndata]);
		} else {
			return response()->json(['statusCode' => 400, 'message' => 'otp does not match!', 'data' =>  array("id" => "")]);
		}
	}
	//student resendotp
	public function resendOtp(Request $request)
	{
		$userId = $request->input('userId');
		if (!empty($userId)) {
			$checkUser = User::where('id', $userId)->where("deleted",0)->where("role_id",6)->first();
			$checktemp = 0;
		$UserTemp =	UserTemp::where('id', $userId)->first();
		
		if(!is_null($UserTemp))
		{
			$checktemp=1;
			$checkUser = $UserTemp;
		}
		
			if ($checkUser) {
				$otpnumber = rand(1111, 9999);
				$phone = $checkUser->phone;
				if($checktemp==0)
				{
					$update = DB::table('users')->where("deleted",0)->where('id', $userId)->update(['otp_match' => $otpnumber]);
				}
				else if($checktemp==1)
				{
					$update = UserTemp::where('id', $userId)->update(['otp' => $otpnumber]);

				}

				if ($update) {
					$returndata['otp'] = $otpnumber;
					$msg = 'Verification Otp Send, Please Check.';
					//$this->sms($phone, $otpnumber);
					/*$data = array('username' => $checkUser->name, 'OTP' => $otpnumber, 'msg' => $msg);
					Mail::send('emails.otpmail', $data, function ($message) {
						$checkUser = User::where('id', $_POST['userId'])->first();
						$email = $checkUser->email;
						$message->to($email, 'From Guruathome')->subject('Guruathome: Verify OTP');
						$message->from('<EMAIL>', 'Guruathome');
					});*/
					$message = '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: ' . $otpnumber . '</p>';
					$this->sendMail($checkUser->email, 'Guruathome: Verify OTP',$message );
					
					return response()->json(['statusCode' => 200, 'message' => $msg, "data" => $returndata]);
				} else {
					return response()->json(['statusCode' => 400, 'message' => 'Somthing Went Wrong!', 'data' => array("otp" => "")]);
				}
			} else {
				return response()->json(['statusCode' => 400, 'message' => 'Invaild User Id!', 'data' => array("otp" => "")]);
			}
		} else {
			return response()->json(['statusCode' => 400, 'message' => 'Wrong Paramenter Passed!', 'data' => array("otp" => "")]);
		}
	}
	//student forget password
	public function forgetpassword(Request $request)
	{
		$returndata = array();
		$email = $request->email;
		if ($email != '') {
			if (is_numeric($email)) {
				//$user = User::where('phone', '=', $phone)->where('status', '=', 1)->where("role_id", $role_id)->first();
				$user = User::where('phone', $email)->where("deleted",0)->where("role_id",6)->first();
			} else {
				$user = User::where('email', $email)->where("deleted",0)->where("role_id",6)->first();

			}
		} else {
			$msg = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("type" => "forgetpassword")]);
		}

		if (!empty($user) && $user!=null) {
			$otp = rand(1111, 9999);
			$user->otp_match = $otp;
			$user->save();
			$msg = 'Verification OTP send on email address, Please Check.';
			//$this->sms($user->phone, $otp);
			/*$data = array('username' => $user->name, 'OTP' =>  $otp, 'msg' =>  $msg);
			Mail::send('emails.app_forgot_password', $data, function ($message) {
				$email = $_POST['email'];
				if ($email != '') {
					if (is_numeric($email)) {
						$user = User::where('phone', $email)->first();
					} else {
						$user = User::where('email', $email)->first();
					}
				}
				$email = $user->email;
				$message->to($email, 'From Guruathome')->subject('Guruathome: Forgot Password');
				$message->from('<EMAIL>', 'Guruathome');
			});*/
			//	$this->sendEmail($user->email, 'Guruathome: Forgot Password', $data = array('userName' => $user->name, 'message' => '<p>You have been forgotton your password, don\'t worry, Please reset your password </p><p>You have got successfully your OTP: '. $otp . '</p>'));
				
				$message = 'You have been forgotton your password, don\'t worry, Please reset your password You have got successfully your OTP: '. $otp ;

			//	$this->sendMail($request->email, $subject,$message);

				$this->sendMail($user->email,"Guruathome: Forgot Password",$message);
			$returndata['type'] = 'forgetpassword';
			$returndata['userId'] = $user->id;
			$returndata['otp'] = $otp;
			$checkUser = User::where('id', $user->id)->update(["otp_match"=>$otp]);
	
			return response()->json(['statusCode' => 200, 'message' => $msg, "data" => $returndata]);
		} else {
			$msg = "Email Not Exist!";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("type" => "forgetpassword")]);
		}
	}
	//student checkForgetPasswordOtp 
	public function checkForgetPasswordOtp(Request $request)
	{
		$userId 			= $request->userId;
		$otp 				= $request->otp;
		$checkUser = User::where('id', $userId)->where("otp_match",$otp)->where('role_id',6)->first();
		//			dd($checkUser);
		if($checkUser!=null)
		{
			$message = 'Verification otp  Successfully';
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userId" => $userId)]);
			} else {
				$message = 'Invalid otp Please try again';
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userId" => $userId)]);
			}

	}
	// student resetPassword 
	public function resetPassword(Request $request)
	{
//		$checkUser=	JWTAuth::user();

		$userId 			= $request->userId;
		$newPassword 		= $request->newPassword;
		$confirmPassword 	= $request->confirmPassword;
		$checkUser = User::where('id', $userId)->where("role_id",6)->first();
		if (!empty($checkUser)) {
			if (!empty($newPassword)) {
				if ($newPassword != $confirmPassword) {
					$msg = "Password and Confirm password not matched!";
					return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("userId" => $userId)]);
				}
				$updateData = User::where('id', $userId)->update([
					'password'		=> hash::make($newPassword),
					'userpass'		=> $newPassword,
					'updated_at' 	=> date('Y-m-d H:i:s')
				]);
				$message = 'Password Reset Successfully';
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userId" => $userId)]);
			} else {
				$message = 'Please enter new password!';
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userId" => $userId)]);
			}
		} else {
			$msg = "Invalid User Id ";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("userId" => $userId)]);
		}
	}
	//student Verfy Otp 
	public function verifyOtp(Request $request)
	{
		$userId 			= $request->userId;
		$otp 				= $request->otp;
		$checkUser = User::where('id', $userId)->where("otp_match",$otp)->where('role_id',6)->first();
		//			dd($checkUser);
		if($checkUser!=null)
		{
			$message = 'Verification otp  Successfully';
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userId" => $userId)]);
			} else {
				$message = 'Invalid otp Please try again';
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userId" => $userId)]);
			}

	}
	//student changepassword
	public function changePassword(Request $request)
	{
		if(!empty($request->All()))
		{
			$user =JWTAuth::user();
	
			//$bearerToken = $request->bearerToken();
			$userId   = $user->id;
			//$user = User::where("id", $userId)->where("role_id",6)->first();
			if($user!=null)
			{
				$userStatus = $user->status;
				if ($userStatus == 0) {
					$message = "User not Available.";
					return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("id"=>"")]);
				}
				$validator = Validator::make($request->all(), [
					'userId' => 'required|numeric',
					'current_password' => 'required',
					'newPassword' => 'required|min:6',
					'confirm_password' => 'required|min:6|max:20|same:newPassword',
				]);

				if($validator->fails()){
		        	$msg = $validator->messages()->first();
					//return $this->sendError($validator->messages()->first());
					return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
				}

				$currentPassword = $request->current_password;
				$newPassword = $request->newPassword;
				$confirmPassword = $request->confirm_password;
				//$user = User::where("id", $userId)->first();
				if (Hash::check($currentPassword, $user->password)) {
					if ($newPassword != $confirmPassword) {
						$msg = 'Password and Confirm password not matched!';
						return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
					}
					$users = User::findOrFail($userId);
					$users->password = hash::make($newPassword);
					$users->userpass = $newPassword;
					$users->save();

					$msg = 'Password updated successfully.';
					return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => array("id" => $userId)]);
				} else {
					$msg = 'Current Password not matched!';
					return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
				}
			}
			else
			{
				$msg = 'Invalid User!';
					return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
			}
		}
	}
	//studentupdateProfile
	public function studentupdateProfile(Request $request)
	{
		$user = JWTAuth::user();

			//$bearerToken = $request->bearerToken();
		$userId   = $user->id;
		//$user = User::where("id", $userId)->where("role_id",6)->first();
		if($user!=null)
		{
			$userStatus =$user->status;
			if ($userStatus == 0) {
				$message = "User not Available.";
				return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("id"=>"")]);
			}
			$userArray = array();
			$validator = Validator::make($request->all(), [
				'userId' => 'required|numeric',
				'name' => 'required|regex:/^[\pL\s\-]+$/u',
				'parent_name'=>'required',
				'email' => 'required|string|email',
				'phone' => 'required|min:10|max:10',
				'country_code' => 'required|min:2',
				'timezone'=>'required',
				'currency'=>'required'

			]);

			if($validator->fails()){
	        	$msg = $validator->messages()->first();
				return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
			}
			$address = $request->address;
			$name 		    = ucwords($request->name);
			$email 			= $request->email;
			$timezone 		= $request->timezone;
			$phone 			= $request->phone;
			$parent_name 		= $request->parent_name;
			$class_name 	= $request->class_id;
			$subject_id 	= $request->subject_id;
			$currency 		= $request->currency;
			// $school_college = $request->school_college;
			// $state 			= $request->state;
			// $city 			= $request->city;
			$country_code 	= $request->country_code;
			$checkUser = User::where('id', $userId)->first();
			if (!empty($checkUser)) {
				$imagess = '';
				//print_r($_FILES); exit;
				if (isset($_FILES['image']['name']) && $_FILES['image']['name']!='') {
					$profileimagename = $_FILES['image']['name'];
					$tmpimage1 = $_FILES['image']['tmp_name'];
					$newprofileImage = rand(00000, 99999) . date('d') . $profileimagename;
					$location = "upload/profile/";
					move_uploaded_file($tmpimage1, $location . $newprofileImage);
					$url = 'upload/profile/' . $newprofileImage;
					$img = Image::make($url)->resize(200, 200);
					$imagess =  $img->basename;
				} else {
					$imagess = $checkUser->image;
				}
				//	$otp = rand(1111, 9999);
				$updateData = User::where('id', $userId)->update([
					'name' 			 => $name,
					//'email' 		 => $email,
					'phone' 		 => $phone,
					'parent_name' 		 => $parent_name,
					//'dob' 			 => date('Y-m-d H:i:s', strtotime($dob)),
					'image' 		 => $imagess,
					'class_id' 	 => $class_name,
					'currency'=>$currency,
				//	'school_college' => $school_college,
				//	'state' 		 => $state,
				//	'city' 			 => $city,
					'country_code' 	 => $country_code,
					'address'		=>$address,
				//	'otp_match' 	 => $otp,
					'timezone'=>$timezone,
					'subject_id'=>$subject_id,
					'updated_at' 	 => date('Y-m-d H:i:s')
				]);
				if($updateData){
					$message = 'Updated Successfully';
					$update = User::where('id',$userId)->first();
					unset($update->api_token);
					return response()->json(['statusCode' => 200, 'message' => $message, 'data' => $update]);
				}else {
					$msg = "Updated Failed";
					return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
				}
			}
		 	else {
					$msg = "Invalid User Id ";
					return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
			}
		}
		else {
					$msg = "User Not Found ";
					return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
			}


	}
	//Banner
	public function Banner(Request $request){

		$url = asset('upload/banners/');
		$bearerToken = $request->bearerToken();
		$userId 	= $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);

		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "",  "portfolios" => [])]);
		}
		else
		{
			$data =Banner::where("deleted",0)->where("status",1)->get();
			if(isset($data))
			{
				$message = "Banner Available.";
				return response()->json(['statusCode' => 200, 'message' => $message,'base_path'=>$url, 'data' => array("data"=>$data)]);
			}
			else
			{
				$message = "Banner Not Available.";
				return response()->json(['statusCode' => 203, 'message' => $message, 'base_path'=>$url,'data' => array("data"=>"")]);
			}
		}
	}

	//gethomeScreen
	public function gethomeScreen(Request $request){
	//	$bearerToken = $request->bearerToken();
	
		$type 		= $request->type;
		$user= JWTAuth::user();

		$userId 	= $user->id;
		$userStatus =$user->status; //$this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "",  "portfolios" => [])]);
		}
		else
		{
			$select_type="";
			$data =[];
			$AllData['banners_url'] =asset('upload/banners/');
			$AllData['class_images'] =url("/upload/class/");
			$AllData['course_images'] =url('/course/');
			$AllData['subject_images'] =url('/upload/subject/');
			$AllData['banner'] = Banner::select('image')->where("deleted",0)->where("status",1)->get();
			$AllData['classes'] = StudentClass::where("deleted",0)->where("status",1)->get();
			$AllData['category'] = Category::where("deleted",0)->where("status",1)->get();
			$message = "";
				$select_type.="All";
				if(!empty($AllData['classes']))
				{
					foreach($AllData['classes'] as $cls){
						$cls['subject_Details'] = Subject::where("class_id",$cls->id)->where('status',1)->get();

					}
				}
				if(!empty($AllData['category']))
				{
					foreach($AllData['category'] as $cat){
						$cat['course_Details'] = Courses::where("category_id",$cat->id)->where('status',1)->get();
					}
				}
				$StudentApplyForDemo = StudentApplyForDemo::where("user_id",$userId)->get();
				if(!is_null($StudentApplyForDemo))
				{
					foreach ($StudentApplyForDemo as  $value) {
						if($value->class_id!=0)
						{
							$value->class_name = $value->class->class_name;
							$value->class_image = $value->class->image;
							$value->class_description = $value->class->description;
							$value->class_status = $value->class->status;
							$value->class_deleted = $value->class->deleted;
							unset($value->class);
						}
						if($value->subject_id!=0)
						{
							$value->subject_name = $value->subject->title;
							$value->subject_image = $value->subject->image;
							$value->subject_description = $value->subject->description;
							$value->subject_status = $value->subject->status;
							$value->subject_deleted = $value->subject->deleted;
							unset($value->subject);
						}
					
						if($value->category_id!=0)
						{
							$value->category_name = $value->category->name;
							$value->category_image = $value->category->image;
							$value->category_description = $value->category->description;
							$value->category_status = $value->category->status;
							$value->category_deleted = $value->category->deleted;
							unset($value->category);
						}
						if($value->course_id!=0)
						{
							$value->courses_name = $value->courses->name;
							$value->courses_image = $value->courses->image;
							$value->courses_description = $value->courses->overview;
							$value->course_sort_description = $value->courses->sort_description;
							$value->courses_status = $value->courses->status;
							$value->courses_deleted = $value->courses->deleted;
							unset($value->courses);
						}
					}
				
				
				}
				$AllData['demo_request'] = $StudentApplyForDemo;
				unset($StudentApplyForDemo['class']);
				unset($StudentApplyForDemo['category']);
				unset($StudentApplyForDemo['subject']);
				unset($StudentApplyForDemo['courses']);
				array_push($data,$AllData);
		
			return response()->json(['statusCode' => 200, 'select_type'=>$select_type,'message' => $message, 'data' => $data]);
		}
	}
	
	public function studentapplyforademo(Request $request){
		$user  =JWTAuth::user();
		//	dd($request->all());

		// $bearerToken 	= $request->bearerToken();
		 $userId 		= $user->id;
		 $requestData = json_decode($request->data);
		// dd($requestData);
		 $userStatus = $user->status;//$this->isUserActive($userId,$bearerToken);
	  if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "",  "portfolios" => [])]);
		}
		else{

			$booking_free =$requestData;
			$range = explode("-", $request->select_price_range);

			$booking_free_select_price_range =isset($range[0]) ? $range[0] :0; 
			$booking_free_end_select_range =isset($range[1]) ? $range[1] :0; 

			$booking_free_preferred_topic = isset($request->preferred_topic) ? $request->preferred_topic :'';	

			if(!empty($requestData) && isset($requestData))
			{
				//StudentApplyForDemo

				$StudentApplyForDemo = new StudentApplyForDemo();
				$StudentApplyForDemo->user_id 		= $userId;
				$StudentApplyForDemo->class_id 		=isset($request->class_id) ? $request->class_id :0; 
				$StudentApplyForDemo->subject_id 	=isset($request->subject_id) ? $request->subject_id :0;
				$StudentApplyForDemo->category_id 	=isset($request->category_id) ? $request->category_id :0;
				$StudentApplyForDemo->course_id   	=isset($request->course_id) ? $request->course_id :0;
				$StudentApplyForDemo->start_price_range   	=isset($booking_free_select_price_range) ? $booking_free_select_price_range :0;
				$StudentApplyForDemo->end_price_range   	=isset($booking_free_end_select_range) ? $booking_free_end_select_range :0;
				$StudentApplyForDemo->save();
				$StudentApplyForDemoLastInsertedId = $StudentApplyForDemo->id;
				if(!empty($requestData))
				{
					foreach ($requestData as $key => $value) {
						
						//	student_booking_frees
						$StudentBookingFrees = new StudentBookingFrees();
						$StudentBookingFrees->user_id = $userId;
						$StudentBookingFrees->studentapplyfordemo_id = $StudentApplyForDemoLastInsertedId;
						$StudentBookingFrees->select_date = $value->select_date;
						$StudentBookingFrees->select_time = $value->select_time;
						$StudentBookingFrees->select_end_time = $value->select_end_time;
						$StudentBookingFrees->preferred_topic = $booking_free_preferred_topic;
						$StudentBookingFrees->save();							
				}
					$message = "Booking Free Apply Successfully.";
					return response()->json(['statusCode' => 200, 'message' => $message,'data'=>$this->get_studentapplyforademo($StudentApplyForDemoLastInsertedId)]);
				}
				else
				{
					$message = "Booking Free Apply Failed.";
					return response()->json(['statusCode' => 203, 'message' => $message,'data'=>'']);
				}

			}
		}

	}

	//Show demo class done 

		public function get_demo_class_done(Request $request){
		$getAdmin=User::where("id",1)->first();
		$teacher_available_slot  = [];
		$teacher_available_slotss = array();

		$percentage_commission = $getAdmin->percentage_commission;
		$user = JWTAuth::user();
		$getApplyfordemo= StudentApplyForDemo::join("student_booking_frees","student_booking_frees.studentapplyfordemo_id","=","studentapplyfordemo.id")
		->join('teacher_availabilty_of_slots_details',"teacher_availabilty_of_slots_details.id","=","studentapplyfordemo.teacher_availabilty_of_slots_details_id")
		->select([
				'teacher_availabilty_of_slots_details.start_time as teacher_start_time',
				'teacher_availabilty_of_slots_details.end_time as teacher_end_time',
				'teacher_availabilty_of_slots_details.teacher_id',
				'teacher_availabilty_of_slots_details.request_status as request_status',
				'teacher_availabilty_of_slots_details.id as teacher_availabilty_of_slots_details_id',
				'teacher_availabilty_of_slots_details.zoom_link as teqcher_zoom_link',
				'studentapplyfordemo.*',
				'student_booking_frees.select_time',
				'student_booking_frees.select_end_time',
				'student_booking_frees.select_date'
			])->where([
				'studentapplyfordemo.user_id'=>$user->id,
				'studentapplyfordemo.class_end_status'=>1
		])->get();
		
			
		$subject_id_arr=[];
		$class_id_arr=[];	
		$response=array();
		$new_data = [];

		$matchData = [];
		if(!empty($getApplyfordemo))
		{
			foreach ($getApplyfordemo as  $value) {
				$student_start_time= $value->select_time;
				$student_end_time= $value->select_end_time;
				if(($value->teacher_start_time == $student_start_time))
				{	
					$matchData[] = $value;
				}
			}
		}
	

		if($matchData)
		{
			foreach ($matchData as $key=>  $value) {
					$value->student_apply_for_demo_id =$value->id;
 					$value->teacher_availabilty_of_slots_details_id = $value->teacher_availabilty_of_slots_details_id;
					$value->student_name=$value->getUser->name;
					$value->preferred_topic = $value->preferred_topic;
					$teacher_fees= TeachingDetails::where("user_id",$value->teacher_id)->first()->fees;
					$admin_percentage_commission = ($teacher_fees * $percentage_commission)/100;
					$value->fees =$teacher_fees+$admin_percentage_commission; 
					$value->zoom_link= isset($value->teqcher_zoom_link) ? $value->teqcher_zoom_link : $value->class_url;

					if($value->class_id!=0)
					{
						$value->class_name = $value->class->class_name;
						$value->subject_name = $value->subject->title;
						$class_id_arr[] = $value->class_id;	
						// $response[$key]['class_name'] = $value->class->class_name;
						// $response[$key]['subject_name'] = $value->subject->title;
						unset($value->class);
						unset($value->subject);
					}
					if($value->course_id!=0)
					{

						$subject_id_arr[] = $value->course_id;	
						
						$value->category_name = $value->category->name;
						$value->courses_name= $value->courses->name;
						

						// $response[$key]['category_name'] = $value->category->name;
						// $response[$key]['courses_name'] = $value->courses->name;
						unset($value->category);
						unset($value->courses);	
						
					}
					unset($value->get_user);
					array_push($new_data,$value);

					//get Available slot
					$teacher_id = $value->teacher_id;

					$teachingDetails= TeacherAvailabiltyOfSlots::where('user_id',$value->teacher_assign_id)->get();
					if(!empty($teachingDetails))
					{
						foreach($teachingDetails as $k=> $te)
						{
						 $TeacherAvailabiltyOfSlotsDetails = TeacherAvailabiltyOfSlotsDetails::where("teacher_availabilty_of_slots_id",$te->id)->where('request_status',0) ->get();
							if(!empty($TeacherAvailabiltyOfSlotsDetails))
							{
								$te->teacher_available_slot = $TeacherAvailabiltyOfSlotsDetails;
//								$teacher_available_slotss[$k]= $TeacherAvailabiltyOfSlotsDetails;
								array_push($teacher_available_slot,$te);
							}

						}
					}

			}
		}


		if(!empty($new_data))
				{
					return response()->json(['statusCode'=>200,"message"=>"","data"=>$new_data,'teacher_available_slot'=>$teacher_available_slot]);
				}
				else
				{
					return response()->json(['statusCode'=>400,"message"=>"No Record Found","data"=>'','teacher_available_slot'=>'']);

				}
	}

	public function check_teacher_slot_available_or_not(Request $request)
	{
		$user = JWTAuth::user();
		 $userStatus = $user->status;//$this->isUserActive($userId,$bearerToken);
	  if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "",  "portfolios" => [])]);
		}
		else
		{
			 $TeacherAvailabiltyOfSlotsDetails = TeacherAvailabiltyOfSlotsDetails::where("id",$request->id)->first();

			 $request_status = false;
			
			 	if(!empty($TeacherAvailabiltyOfSlotsDetails))
				{
					$request_status = ($TeacherAvailabiltyOfSlotsDetails->request_status==0) ?  true : false;
					return response()->json(['statusCode'=>200,"message"=>"","data"=>$request_status]);
				}
				else
				{
					return response()->json(['statusCode'=>400,"message"=>"No Record Found","data"=>'']);

				}

		}

	}

	public function get_demo_class_doneold(Request $request){

		$getAdmin=User::where("id",1)->first();
		
		$percentage_commission = $getAdmin->percentage_commission;

		$user = JWTAuth::user();
		
		//  $getApplyfordemo  =StudentApplyForDemo::join("student_booking_frees","student_booking_frees.studentapplyfordemo_id","=","studentapplyfordemo.id")
		//  ->join('teacher_availabilty_of_slots_details','teacher_availabilty_of_slots_details.id',"=","studentapplyfordemo.teacher_availabilty_of_slots_details_id")
		//  ->where('studentapplyfordemo.user_id',$user->id)
		// //->where('student_booking_frees.select_time','03:10:00')
		// //->where('student_booking_frees.select_end_time','04:00:00')
		// // ->where('student_booking_frees.select_end_time','=','teacher_availabilty_of_slots_details.start_time')
		// // ->where('student_booking_frees.select_time',"=","teacher_availabilty_of_slots_details.start_time")
		 	// 	 //->where('teacher_availabilty_of_slots_details.end_time',"=","student_booking_frees.select_end_time")
		 	// 	 ->where('studentapplyfordemo.class_end_status',1)
		//  ->select(
		//  	['studentapplyfordemo.*',
		//  	'student_booking_frees.select_time',
		//  	'student_booking_frees.select_end_time',
		//  	'student_booking_frees.select_date',
		//  	'teacher_availabilty_of_slots_details.start_time as teacher_start_time',
		//  	'teacher_availabilty_of_slots_details.end_time as teacher_end_time'
		//  ])->get();

		$getApplyfordemo= StudentApplyForDemo::join("student_booking_frees","student_booking_frees.studentapplyfordemo_id","=","studentapplyfordemo.id")
		->join('teacher_availabilty_of_slots_details',"teacher_availabilty_of_slots_details.id","=","studentapplyfordemo.teacher_availabilty_of_slots_details_id")
		->select(
			[
				'teacher_availabilty_of_slots_details.start_time as teacher_start_time',
				'teacher_availabilty_of_slots_details.end_time as teacher_end_time',

					'teacher_availabilty_of_slots_details.teacher_id',
						'teacher_availabilty_of_slots_details.request_status as request_status',
						'teacher_availabilty_of_slots_details.id as teacher_availabilty_of_slots_details_id',
						'teacher_availabilty_of_slots_details.zoom_link as teqcher_zoom_link',
				

				'studentapplyfordemo.*',
				'student_booking_frees.select_time',
				'student_booking_frees.select_end_time',
				'student_booking_frees.select_date'
			])
		->where([
				'studentapplyfordemo.user_id'=>$user->id,
				'studentapplyfordemo.class_end_status'=>1
		])
		
		
		->get();
		
				//	print_r($getApplyfordemo->toarray());
				///	 	die;
			// 		 	$getApplyfordemos= DB::select("select `studentapplyfordemo`.*, `student_booking_frees`.`select_time`, `student_booking_frees`.`select_end_time`, `student_booking_frees`.`select_date`, `teacher_availabilty_of_slots_details`.`start_time` as `teacher_start_time`, `teacher_availabilty_of_slots_details`.`end_time` as `teacher_end_time` from `studentapplyfordemo` inner join `student_booking_frees` on `student_booking_frees`.`studentapplyfordemo_id` = `studentapplyfordemo`.`id` inner join `teacher_availabilty_of_slots_details` on `teacher_availabilty_of_slots_details`.`id` = `studentapplyfordemo`.`teacher_availabilty_of_slots_details_id` 
			// where `studentapplyfordemo`.`user_id` = ".$user->id." and `studentapplyfordemo`.`class_end_status` = 1 and `student_booking_frees`.`select_time` = teacher_availabilty_of_slots_details.start_time and `teacher_availabilty_of_slots_details`.`end_time` = student_booking_frees.select_end_time");
			// print_r($getApplyfordemos);

		 // $getApplyfordemos= DB::select("select `studentapplyfordemo`.*, `student_booking_frees`.`select_time`, `student_booking_frees`.`select_end_time`, `student_booking_frees`.`select_date`, `teacher_availabilty_of_slots_details`.`start_time` as `teacher_start_time`, `teacher_availabilty_of_slots_details`.`end_time` as `teacher_end_time` from `studentapplyfordemo` inner join `student_booking_frees` on `student_booking_frees`.`studentapplyfordemo_id` = `studentapplyfordemo`.`id` inner join `teacher_availabilty_of_slots_details` on `teacher_availabilty_of_slots_details`.`id` = `studentapplyfordemo`.`teacher_availabilty_of_slots_details_id` where `studentapplyfordemo`.`user_id` = ".$user->id." and `studentapplyfordemo`.`class_end_status` = 1 and `student_booking_frees`.`select_time` = teacher_availabilty_of_slots_details.start_time and `teacher_availabilty_of_slots_details`.`end_time` = student_booking_frees.select_end_time");

		//dd($getApplyfordemos);
		$subject_id_arr=[];
		$class_id_arr=[];	
		$response=array();
		$new_data = [];

		$matchData = [];
		if(!empty($getApplyfordemo))
		{
			foreach ($getApplyfordemo as  $value) {
				$student_start_time= $value->select_time;
				$student_end_time= $value->select_end_time;
				if(($value->teacher_start_time == $student_start_time))
				{	
					$matchData[] = $value;
				}
			}
		}
	

		if($matchData)
		{
			foreach ($matchData as $key=>  $value) {
				$value->student_apply_for_demo_id =$value->id;
 					$value->teacher_availabilty_of_slots_details_id = $value->teacher_availabilty_of_slots_details_id;
					$value->student_name=$value->getUser->name;
					$value->preferred_topic = $value->preferred_topic;
					$teacher_fees= TeachingDetails::where("user_id",$value->teacher_id)->first()->fees;
					$admin_percentage_commission = ($teacher_fees * $percentage_commission)/100;
					$value->fees =$teacher_fees+$admin_percentage_commission; 
					$value->zoom_link= isset($value->teqcher_zoom_link) ? $value->teqcher_zoom_link : $value->class_url;

					if($value->class_id!=0)
					{
						$value->class_name = $value->class->class_name;
						$value->subject_name = $value->subject->title;

						$response[$key]['class_name'] = $value->class->class_name;
						$response[$key]['subject_name'] = $value->subject->title;
						unset($value->class);
						unset($value->subject);
					}
					if($value->course_id!=0)
					{

						$value->category_name = $value->category->name;
						$value->courses_name= $value->courses->name;
						

						$response[$key]['category_name'] = $value->category->name;
						$response[$key]['courses_name'] = $value->courses->name;
						unset($value->category);
						unset($value->courses);	
						unset($value->get_user);
					}
					array_push($new_data,$response[$key]);

			}
		}
	
		//	if(!empty($getApplyfordemo))
		//	{
			// foreach ($getApplyfordemo as $key => $value) {

			// 	$student_start_time= $value->select_time;
			// 	$student_end_time= $value->select_end_time;
			// 	if(($value->teacher_start_time == $student_start_time) && ($value->teacher_end_time == $student_end_time))
			// 	{

			// 		if($value->class_id!=0)
			// 		{
			// 			$class_id_arr[]=$value->class_id;
			// 			$subject_id_arr[]=$value->subject_id;
			// 		}
			// 		else if($value->course_id!=0)
			// 		{
			// 			$class_id_arr[]=$value->category_id;
			// 			$subject_id_arr[]=$value->course_id;
			// 		}
			// 		$student_start_time= $value->select_time;
			// 	 	$student_end_time= $value->select_end_time;

			// 		$student_select_price_range= $value->start_price_range;
			// 		$student_end_select_range= $value->end_price_range;

			// 		$timestamp = strtotime($value->created_at);
			// 		$day = date('w', $timestamp);
		
			// 	$get_TeacherAvailabiltyOfSlots=	TeacherAvailabiltyOfSlots::join('teacher_availabilty_of_slots_details','teacher_availabilty_of_slots_details.teacher_availabilty_of_slots_id', '=','teacher_availabilty_of_slots.id')
			// 	->join('teaching_details',"teaching_details.user_id","=","teacher_availabilty_of_slots.user_id")
			// 	->join('users','users.id',"=","teaching_details.user_id")
			// 	->where('teacher_availabilty_of_slots_details.request_status',2)
			// 	->where('teacher_availabilty_of_slots.day',$day)
			// 	->where('teacher_availabilty_of_slots_details.start_time',">=",$student_start_time)
			// 	->where('teacher_availabilty_of_slots_details.end_time',"<=",$student_end_time)

			// 	->where('teaching_details.fees',">=",$student_select_price_range)
			// 	->where('teaching_details.fees',"<=",$student_end_select_range)

			// 	->select([
			// 		'teacher_availabilty_of_slots_details.teacher_id',
			// 			'teacher_availabilty_of_slots_details.request_status as request_status',
			// 			'teacher_availabilty_of_slots_details.id as teacher_availabilty_of_slots_details_id',
			// 			'teacher_availabilty_of_slots_details.zoom_link as teqcher_zoom_link',
			// 			'teacher_availabilty_of_slots_details.start_time',
			// 			'teacher_availabilty_of_slots_details.end_time',
			// 			'teacher_availabilty_of_slots.*',
			// 			'users.class_url',
			// 			'teaching_details.fees as teaching_details_fees'])
			// 	->first();

			// 	if($get_TeacherAvailabiltyOfSlots)
			// 	{

			// 		$value->teacher_availabilty_of_slots_details_id = $get_TeacherAvailabiltyOfSlots->teacher_availabilty_of_slots_details_id;
			// 		$value->student_name=$value->getUser->name;
			// 		$value->preferred_topic = $value->preferred_topic;
					

			// 		$admin_percentage_commission = ($get_TeacherAvailabiltyOfSlots->teaching_details_fees * $percentage_commission)/100;
			// 		$response[$key]['teacher_id']=$get_TeacherAvailabiltyOfSlots->teacher_id;
			// 		$response[$key]['teaching_details_fees']=$get_TeacherAvailabiltyOfSlots->teaching_details_fees+$admin_percentage_commission;
			// 		$response[$key]['select_date']=$value->select_date;
			// 		$response[$key]['start_time']=$get_TeacherAvailabiltyOfSlots->start_time;
			// 		$response[$key]['end_time']=$get_TeacherAvailabiltyOfSlots->end_time;
			// 		$response[$key]['request_status']=$get_TeacherAvailabiltyOfSlots->request_status;
					

			// 		$response[$key]['student_apply_for_demo_id']=$value->id;
			// 		$response[$key]['teacher_availabilty_of_slots_details_id']=$get_TeacherAvailabiltyOfSlots->teacher_availabilty_of_slots_details_id;
			// 		$response[$key]['student_name']=$value->getUser->name;
			// 		$response[$key]['preferred_topic'] = $value->preferred_topic;
			// 		$response[$key]['zoom_link'] = isset($get_TeacherAvailabiltyOfSlots->teqcher_zoom_link) ? $get_TeacherAvailabiltyOfSlots->teqcher_zoom_link : $get_TeacherAvailabiltyOfSlots->class_url;

			// 		if($value->class_id!=0)
			// 		{
			// 			$value->class_name = $value->class->class_name;
			// 			$value->subject_name = $value->subject->title;

			// 			$response[$key]['class_name'] = $value->class->class_name;
			// 			$response[$key]['subject_name'] = $value->subject->title;
			// 			unset($value->class);
			// 			unset($value->subject);
			// 		}
			// 		 if($value->course_id!=0)
			// 		{

			// 			$value->category_name = $value->category->name;
			// 			$value->courses_name= $value->courses->name;
						

			// 			$response[$key]['category_name'] = $value->category->name;
			// 			$response[$key]['courses_name'] = $value->courses->name;
			// 			unset($value->category);
			// 			unset($value->courses);	
			// 			unset($value->get_user);
			// 		}
			// 			array_push($new_data,$response[$key]);
			// 		}
			// }
		
		//}

		// }
		// if(!empty($new_data))
		// 		{
		// 			return response()->json(['statusCode'=>200,"message"=>"","data"=>$new_data]);
		// 		}
		// 		else
		// 		{
		// 			return response()->json(['statusCode'=>400,"message"=>"No Record Found","data"=>'']);

		// 		}
	}
	//studentapplyforademo

	public function studentapplyforademoolds(Request $request){
		$user  =JWTAuth::user();

		// $bearerToken 	= $request->bearerToken();
		 $userId 		= $user->id;
		 $requestData = json_decode($request->data);
		 $userStatus = $user->status;//$this->isUserActive($userId,$bearerToken);
	  if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "",  "portfolios" => [])]);
		}
		else{
		//dd($requestData);
			$apply_for_demo =$requestData->apply_for_demo;
			$booking_free = isset($apply_for_demo) ?  $apply_for_demo[0]->booking_free :'';
			
			$booking_free_select_price_range = isset($booking_free) ?  $booking_free[3]->select_price_range :'';
			$booking_free_preferred_topic = 	isset($booking_free) ?  $booking_free[3]->preferred_topic :'';
			//$booking_free_currency = !isset($booking_free) ?  $booking_free[3]->currency :'₹';
			//dd($booking_free_currency);
			if(!empty($apply_for_demo) && isset($apply_for_demo))
			{
				//StudentApplyForDemo

				$StudentApplyForDemo = new StudentApplyForDemo();
				$StudentApplyForDemo->user_id 		= $userId;
				$StudentApplyForDemo->class_id 		= isset($apply_for_demo) ? $apply_for_demo[0]->class_id :0;
				$StudentApplyForDemo->subject_id 	= isset($apply_for_demo) ? $apply_for_demo[0]->subject_id :0;
				$StudentApplyForDemo->category_id 	= isset($apply_for_demo) ? $apply_for_demo[0]->category_id :0 ;
				$StudentApplyForDemo->course_id   	= isset($apply_for_demo) ? $apply_for_demo[0]->course_id :0 ;
				$StudentApplyForDemo->save();
				$StudentApplyForDemoLastInsertedId = $StudentApplyForDemo->id;
				if(!empty($booking_free))
				{
					foreach ($booking_free as $key => $value) {
						if($key!=3)
						{
							//	student_booking_frees
							$StudentBookingFrees = new StudentBookingFrees();
							$StudentBookingFrees->user_id = $userId;
							$StudentBookingFrees->studentapplyfordemo_id = $StudentApplyForDemoLastInsertedId;
							$StudentBookingFrees->select_date = $value->select_date;
							$StudentBookingFrees->select_time = $value->select_time;
							$StudentBookingFrees->select_price_range = $booking_free_select_price_range;
							$StudentBookingFrees->preferred_topic = $booking_free_preferred_topic;
						//	$StudentBookingFrees->currency = $booking_free_currency;
							$StudentBookingFrees->save();							
						}
					}
					$message = "Booking Free Apply Successfully.";
					return response()->json(['statusCode' => 200, 'message' => $message,'data'=>$this->get_studentapplyforademo($StudentApplyForDemoLastInsertedId)]);
				}
				else
				{
					$message = "Booking Free Apply Failed.";
					return response()->json(['statusCode' => 203, 'message' => $message,'data'=>'']);
				}

			}
		}

	}
	//availableteacher
	public function availableteacher(Request $request){
		$user = JWTAuth::user();

		//$bearerToken 	= $request->bearerToken();
		$userId 		= $user->id;
		//	$requestData = json_decode($request->data);

		$userStatus =$user->status; //$this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "",  "portfolios" => [])]);
		}
		else
		{
			$get_StudentApplyForDemo = StudentApplyForDemo::where('user_id',$userId)->get(['id','user_id','category_id','class_id','subject_id','category_id','course_id','start_price_range','end_price_range','teacher_assign_id','teacher_availabilty_of_slots_details_id','zoom_link']);
			if(!is_null($get_StudentApplyForDemo))
			{

				foreach($get_StudentApplyForDemo as $get)
				{
					if(($get->class_id!=0) && ($get->subject_id!=0))
					{
						$get->type=1;
					}
					else if(($get->category_id!=0) && ($get->course_id!=0))
					{
						$get->type=2;
					}
				 	$StudentBookingFrees=	StudentBookingFrees::where("studentapplyfordemo_id",$get->id)->where(['deleted'=>0,'status'=>1])->get(['id','select_date','select_time','select_end_time']);
				$get->booking_fees_details = $StudentBookingFrees;
				}
			$message = "";
					return response()->json(['statusCode' => 200, 'message' => $message,'data'=>$get_StudentApplyForDemo]);
			}
			else
			{
			$message = "No Data Found";
					return response()->json(['statusCode' => 203, 'message' => $message,'data'=>'']);				
			}

		}

	}
	public function get_studentapplyforademo($StudentApplyForDemoLastInsertedId)
	{
		//dd($StudentApplyForDemoLastInsertedId);
		$StudentApplyForDemo = StudentApplyForDemo::where("id",$StudentApplyForDemoLastInsertedId)->orderBy("id","desc")->first();
		$userId=$StudentApplyForDemo->user_id; 
		if(!is_null($StudentApplyForDemo))
		{
			
			$getcurrent_student  =DB::table('users')->where('id',$StudentApplyForDemo->user_id)->first()->currency;
			$StudentApplyForDemo->currency = $getcurrent_student;
			$class_id = "";
			$categor_id = "";	
			//select class
			if($StudentApplyForDemo->class_id!=0)
			{
				$StudentApplyForDemo->class_image_path = url("/upload/class/");
	
				$StudentApplyForDemo->class_data = $StudentApplyForDemo->class;
			$class_id .= $StudentApplyForDemo->class_id;
	

				if($StudentApplyForDemo->subject_id!=0)
				{
					$StudentApplyForDemo->subject_image_path = url("/upload/subject/");
					$StudentApplyForDemo->subject_data = $StudentApplyForDemo->subject;
				}
				unset($StudentApplyForDemo['class']);
				unset($StudentApplyForDemo['subject']);
			}
			
			//select category
			if($StudentApplyForDemo->category_id!=0)
			{
				$StudentApplyForDemo->class_image_path = url("/upload/class/");
				$categor_id .= $StudentApplyForDemo->category_id;

				$StudentApplyForDemo->category_data = $StudentApplyForDemo->category->name;
				if($StudentApplyForDemo->courses!=0)
				{
					$StudentApplyForDemo->courses_data = $StudentApplyForDemo->courses;
				}
				unset($StudentApplyForDemo['category']);
				unset($StudentApplyForDemo['courses']);
			}
			$get_Assigned_teacher = TeacherAvailabiltyOfSlotsDetails::join('users',"users.id","=","teacher_availabilty_of_slots_details.teacher_id")
			->where('teacher_availabilty_of_slots_details.id',$StudentApplyForDemo->teacher_availabilty_of_slots_details_id)->where('teacher_availabilty_of_slots_details.request_status',2)->select([
				'users.name as teacher_name',
				'users.class_url as class_url',
				'teacher_availabilty_of_slots_details.*',

			])->first();

			if(!empty($get_Assigned_teacher))
			{
				$percentage_commission = DB::table('users')->where("id",1)->first()->percentage_commission;
				$get_teacherDetails = TeachingDetails::orWhere("class_or_category_id",$class_id)->orWhere("subject_or_course_id",$categor_id)->first()->fees;

				$admin_amount = ($get_teacherDetails*$percentage_commission)/100;
				$get_Assigned_teacher->teacher_price = ($get_teacherDetails+ $admin_amount);
			
		//	dd($get_Assigned_teacher->teacher_availabilty_of_slots_id);
			$get_Assigned_teacher->update_zoom_link = isset($get_Assigned_teacher->zoom_link) ? $get_Assigned_teacher->zoom_link :$get_Assigned_teacher->class_url;
			$StudentApplyForDemo->StudentBookingFrees_data = StudentBookingFrees::where("user_id",$userId)->where("studentapplyfordemo_id",$StudentApplyForDemo->id)->get();
			
			$StudentApplyForDemo->Assigned_teacher = $get_Assigned_teacher;
				}
			
			return $StudentApplyForDemo;

		}
		else
		{
			return false;

		}

	}

	//studentapplyforademoold
	public function studentapplyforademoold(Request $request){
		
		$bearerToken 	= $request->bearerToken();
		$userId 		= $request->userId;
		$class_id 		= $request->class_id;
		$course_id 		= $request->course_id;
		$subject_id 	= $request->subject_id;
		$category_id    = $request->category_id;

		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "",  "portfolios" => [])]);
		}
		else
		{
			$updateData ="";
			$StudentApplyForDemo = new StudentApplyForDemo();
			$StudentApplyForDemo->user_id 		= $userId;
			$StudentApplyForDemo->class_id 		= isset($class_id) ? $class_id :0;
			$StudentApplyForDemo->course_id 	= isset($course_id) ? $course_id :0;
			$StudentApplyForDemo->subject_id 	= isset($subject_id) ? $subject_id : 0 ;
			$StudentApplyForDemo->category_id   = isset($category_id) ? $category_id : 0 ;
			$StudentApplyForDemo->save();
			// StudentApplyForDemo::create(array("user_id",$userId,'class_id'=>$class_id,'course_id'=>$course_id,'subject_id'=>$subject_id));

			if($StudentApplyForDemo){
					$message = 'Apply For A Demo Successfully';
					return response()->json(['statusCode' => 200, 'message' => $message]);
				}else {
					$msg = "Apply For A Demo Failed";
					return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
				}
		}

	}
	//demo class feedback
	public function demo_class_feedback(Request $request)
	{
		$user = JWTAuth::user();
		$student_id = $user->id;
		$teacher_id = $request->teacher_id;
		$message 	= $request->message;
		$student_apply_for_demo_id = $request->student_apply_for_demo_id;

		$res=StudentDemoClassFeedback::create(array(
			"student_id"=>$user->id,
			"teacher_id"=>$teacher_id,
			'rating'=>$request->rating,
			"message"=>$message,
			"student_apply_for_demo_id"=>$student_apply_for_demo_id
		));
		if($res)
		{
			$message = 'Your FeedBack Submitted Successfully';
			return response()->json(['statusCode' => 200, 'message' => $message]);
		}
		else
		{
			$message = 'Your FeedBack Submitted Failed';
			return response()->json(['statusCode' => 400, 'message' => $message]);
		}
	}

	public function enrollStudent(Request $request)
	{	

				/*
		use App\Models\Enroll;
		use App\Models\StudentEnrollSchedule;
		use App\Models\PurchaseClass;
		use App\Models\Payment;
				*/
		 $user = JWTAuth::user();
		 if($request->all())
		 {
			 $student_apply_for_demo_id = $request->student_apply_for_demo_id;
			 $no_of_class = $request->no_of_class;
			 $student_id = $user->id;
			$actual_price = $request->actual_price;
			$coupon_code = $request->coupon_code;
			$actual_amount = $request->actual_amount;
			$gst_amount = $request->gst_amount;
			$amount = $request->amount;
			$discount_amount = $request->discount_amount;
			$amount_to_pay = $request->amount_to_pay;
			$teacher_id = $request->teacher_id;
			$requestData = json_decode($request->data);
			

			$insertedEnrollId=Enroll::create(
					array(
						"student_id"=>$student_id,
						'student_apply_for_demo_id'=>$student_apply_for_demo_id,
						'teacher_id'=>$teacher_id,
						'no_of_class'=>$no_of_class)
				)->id;

				// for($i=0;$i< $no_of_class;$i++)
				// {
					if(!empty($requestData))
					{
						foreach ($requestData as $key => $value) {
							
						$studentEnrollSchedule =	StudentEnrollSchedule::create(array(
								'select_date'=>$value->select_date,
								'type'=>$value->type,
								'enroll_id'=>$insertedEnrollId,
								'start_time'=>$value->start_time,
								'end_time'=>$value->end_time,
								'teacher_availabilty_of_slots_id'=>$value->teacher_availabilty_of_slots_id,
								'teacher_availabilty_of_slots_details_id'=>$value->teacher_availabilty_of_slots_details_id,
								'student_id'=>$student_id,
								'teacher_id'=>$teacher_id
							))->id;

						}
					}
				//}
			$insertPurchaseClassId=	PurchaseClass::create(
					array(
						'enroll_id' =>$insertedEnrollId,
						'student_id'=>$student_id,
						'teacher_id'=>$teacher_id,
						'coupon_code'=>$coupon_code,
						'gst_amount'=>$gst_amount,
						"actual_amount"=>$actual_amount,
						'discount_amount'=>$discount_amount,
						"amount"=>$amount,
						"actual_price"=>$actual_price,
						"amount_to_pay"=>$amount_to_pay
						 ))->id;
		
			 $payment = Payment::create(array(
			 	'enroll_id' =>$insertedEnrollId,
			 	'purchase_class_id'=>$insertPurchaseClassId,
			 	'transation_id'=>"terewrewrwerwerwr",
			 	'payment_status'=>"Successfully"
			 ));
			
			if($payment)
			{
				$message = "Class will be added  to available class";
				return response()->json(array("statusCode"=>200,'message'=>$message));
			}
			else
			{
				$message = "Will Show the failure nessage";
				return response()->json(array("statusCode"=>400,'message'=>$message));
			}
		}
	}

	public function get_UpcomingClasses(Request $request){

		$user = JWTAuth::user();

		$userId = $user->id;
		$currentDate = date('Y-m-d');

		$teacher_available_slot=[];
		
		$getEnroll =Enroll::join('student_enroll_schedule','student_enroll_schedule.enroll_id',"=",'enrolls.id')
		->join('users',"users.id","=","enrolls.teacher_id")
		->join('teacher_availabilty_of_slots_details',"teacher_availabilty_of_slots_details.id","=","student_enroll_schedule.teacher_availabilty_of_slots_details_id")
		->join('studentapplyfordemo',"studentapplyfordemo.id","=","enrolls.student_apply_for_demo_id")
		->where("enrolls.student_id",$userId)
		->where("enrolls.status",0)
		->where('student_enroll_schedule.select_date',">=",$currentDate)
		->select([
			'studentapplyfordemo.class_id',
			'studentapplyfordemo.subject_id',
			'studentapplyfordemo.course_id',
			'studentapplyfordemo.category_id',
			'studentapplyfordemo.course_id',
			'student_enroll_schedule.id as student_enroll_schedule_id',
			'student_enroll_schedule.cancel_status',
			'users.name as teacher_name',
			'users.class_url as class_url',
			'student_enroll_schedule.teacher_availabilty_of_slots_details_id',
				'enrolls.id as enroll_id',
				'enrolls.student_id as user_id',
				'enrolls.student_apply_for_demo_id',
				'enrolls.no_of_class',
				'enrolls.status as enroll_status',
				'student_enroll_schedule.id as student_enroll_schedule_id',
				'student_enroll_schedule.type',
				'student_enroll_schedule.start_time',
				'student_enroll_schedule.end_time',
				'student_enroll_schedule.select_date',
				'student_enroll_schedule.teacher_id',
				'teacher_availabilty_of_slots_details.zoom_link'
		])
		->get();
		if(!empty($getEnroll))
		{
			foreach($getEnroll as $value)
			{
					$value->student_enroll_schedule_id = $value->student_enroll_schedule_id;
				if($value->class_id!=0)
				{
					$value->class_name= 	StudentClass::where("id",$value->class_id)->first()->class_name;
					$value->subject_name= 	Subject::where("id",$value->subject_id)->first()->title;
				}
				if($value->course_id!=0)
				{
					$value->category_name= 	Category::where("id",$value->category_id)->first()->name;
					$value->course_name= 	Courses::where("id",$value->course_id)->first()->name;
				}
				
				$value->zoome_class_link = isset($value->zoom_link) ? $value->zoom_link :$value->class_url;

				//get Available slot
					$teacher_id = $value->teacher_id;

					$teachingDetails= TeacherAvailabiltyOfSlots::where('user_id',$value->teacher_id)->get();
					if(!empty($teachingDetails))
					{
						foreach($teachingDetails as $k=> $te)
						{
						 $TeacherAvailabiltyOfSlotsDetails = TeacherAvailabiltyOfSlotsDetails::where("teacher_availabilty_of_slots_id",$te->id)->where('request_status',0) ->get();
							if(!empty($TeacherAvailabiltyOfSlotsDetails))
							{
								$te->teacher_available_slot = $TeacherAvailabiltyOfSlotsDetails;
								array_push($teacher_available_slot,$te);
							}

						}
					}
			}
			return response()->json(array("statusCode"=>200,'message'=>'',"data"=>$getEnroll,"teacher_available_slot"=>$teacher_available_slot));
		}
		else
		{
			$message = "Will Show the failure nessage";
			return response()->json(array("statusCode"=>400,'message'=>$message));
		}
	}

	public function UpcomingCancelClasses(Request $request){
		$user = JWTAuth::user();
		$enroll_id = $request->enroll_id;
		$student_enroll_schedule_id = $request->student_enroll_schedule_id;
		$studentEnrollSchedule = StudentEnrollSchedule::where('id',$student_enroll_schedule_id)->where('enroll_id',$enroll_id)->update(array('cancel_status'=>1));
		if($studentEnrollSchedule)
		{
			$message = "Schedule Cancel Successfully";
			return response()->json(array("statusCode"=>200,'message'=>$message));
		}
		else
		{
			$message = "Schedule Cancel Failed";
			return response()->json(array("statusCode"=>400,'message'=>$message));
		}
	}

	public function  enrollCompletedClasses(Request $request){// api for end of upcomming class 
		$user = JWTAuth::user();
		$enroll_id = $request->enroll_id;
		$student_enroll_schedule_id = $request->student_enroll_schedule_id;
		Enroll::where("id",$enroll_id)->update(array("status"=>1));
		$studentEnrollSchedule = StudentEnrollSchedule::where('id',$student_enroll_schedule_id)->where('enroll_id',$enroll_id)->update(array('status'=>1));
		if($studentEnrollSchedule)
		{
			$message = "Class End Successfully";
			return response()->json(array("statusCode"=>200,'message'=>$message));
		}
		else
		{
			$message = "Class End Failed";
			return response()->json(array("statusCode"=>400,'message'=>$message));
		}
	}

	public function get_EnrollConpletedClasses(Request $request){

		$user = JWTAuth::user();

		$userId = $user->id;
		$currentDate = date('Y-m-d');

		$teacher_available_slot=[];
		
		$getEnroll =Enroll::join('student_enroll_schedule','student_enroll_schedule.enroll_id',"=",'enrolls.id')
		->join('users',"users.id","=","enrolls.teacher_id")
		->join('teacher_availabilty_of_slots_details',"teacher_availabilty_of_slots_details.id","=","student_enroll_schedule.teacher_availabilty_of_slots_details_id")
		->join('studentapplyfordemo',"studentapplyfordemo.id","=","enrolls.student_apply_for_demo_id")
		->where("enrolls.student_id",$userId)
		->where("enrolls.status",1)
		->where('student_enroll_schedule.select_date',">=",$currentDate)
		->select([
			'studentapplyfordemo.class_id',
			'studentapplyfordemo.subject_id',
			'studentapplyfordemo.course_id',
			'studentapplyfordemo.category_id',
			'studentapplyfordemo.course_id',
			'student_enroll_schedule.id as student_enroll_schedule_id',
			'student_enroll_schedule.cancel_status',
			'users.name as teacher_name',
			'users.class_url as class_url',
			'student_enroll_schedule.teacher_availabilty_of_slots_details_id',
				'enrolls.id as enroll_id',
				'enrolls.student_id as user_id',
				'enrolls.student_apply_for_demo_id',
				'enrolls.no_of_class',
				'enrolls.status as enroll_status',
				'student_enroll_schedule.id as student_enroll_schedule_id',
				'student_enroll_schedule.type',
				'student_enroll_schedule.start_time',
				'student_enroll_schedule.end_time',
				'student_enroll_schedule.select_date',
				'student_enroll_schedule.teacher_id',
				'teacher_availabilty_of_slots_details.zoom_link'
		])
		->get();
		if(!empty($getEnroll))
		{
			foreach($getEnroll as $value)
			{
					$value->student_enroll_schedule_id = $value->student_enroll_schedule_id;
				if($value->class_id!=0)
				{
					$value->class_name= 	StudentClass::where("id",$value->class_id)->first()->class_name;
					$value->subject_name= 	Subject::where("id",$value->subject_id)->first()->title;
				}
				if($value->course_id!=0)
				{
					$value->category_name= 	Category::where("id",$value->category_id)->first()->name;
					$value->course_name= 	Courses::where("id",$value->course_id)->first()->name;
				}
				
				$value->zoome_class_link = isset($value->zoom_link) ? $value->zoom_link :$value->class_url;

			
			}
			return response()->json(array("statusCode"=>200,'message'=>'',"data"=>$getEnroll));
		}
		else
		{
			$message = "Will Show the failure nessage";
			return response()->json(array("statusCode"=>400,'message'=>$message));
		}
	}

	public function craditdebitAvailableClass(Request $request)
	{
		$user = JWTAuth::user();

		$userId = $user->id;
		$currentDate = date('Y-m-d');

		$teacher_available_slot=[];
		
		$getEnroll =Enroll::join('student_enroll_schedule','student_enroll_schedule.enroll_id',"=",'enrolls.id')
		->join('users',"users.id","=","enrolls.teacher_id")
		->join('teacher_availabilty_of_slots_details',"teacher_availabilty_of_slots_details.id","=","student_enroll_schedule.teacher_availabilty_of_slots_details_id")
		->join('studentapplyfordemo',"studentapplyfordemo.id","=","enrolls.student_apply_for_demo_id")
		->join('payments','payments,enroll_id',"=","enrolls.id")
		->where("enrolls.student_id",$userId)
		->where("enrolls.status",1)
		//->where('student_enroll_schedule.select_date',">=",$currentDate)
		->select([
			'payments.transation_id',
			'payments.payment_status',
			'studentapplyfordemo.class_id',
			'studentapplyfordemo.subject_id',
			'studentapplyfordemo.course_id',
			'studentapplyfordemo.category_id',
			'studentapplyfordemo.course_id',
			'student_enroll_schedule.id as student_enroll_schedule_id',
			'student_enroll_schedule.cancel_status',
			'users.name as teacher_name',
			'users.class_url as class_url',
			'student_enroll_schedule.teacher_availabilty_of_slots_details_id',
				'enrolls.id as enroll_id',
				'enrolls.student_id as user_id',
				'enrolls.student_apply_for_demo_id',
				'enrolls.no_of_class',
				'enrolls.status as enroll_status',
				'student_enroll_schedule.id as student_enroll_schedule_id',
				'student_enroll_schedule.type',
				'student_enroll_schedule.start_time',
				'student_enroll_schedule.end_time',
				'student_enroll_schedule.select_date',
				'student_enroll_schedule.teacher_id',
				'teacher_availabilty_of_slots_details.zoom_link'
		])
		->get();
		if(!empty($getEnroll))
		{
			foreach($getEnroll as $value)
			{
					$value->student_enroll_schedule_id = $value->student_enroll_schedule_id;
				if($value->class_id!=0)
				{
					$value->class_name= 	StudentClass::where("id",$value->class_id)->first()->class_name;
					$value->subject_name= 	Subject::where("id",$value->subject_id)->first()->title;
				}
				if($value->course_id!=0)
				{
					$value->category_name= 	Category::where("id",$value->category_id)->first()->name;
					$value->course_name= 	Courses::where("id",$value->course_id)->first()->name;
				}
				
				$value->zoome_class_link = isset($value->zoom_link) ? $value->zoom_link :$value->class_url;

			
			}
			return response()->json(array("statusCode"=>200,'message'=>'',"data"=>$getEnroll));
		}
		else
		{
			$message = "Will Show the failure nessage";
			return response()->json(array("statusCode"=>400,'message'=>$message));
		}
	}
	public function student_contact_support(Request $request){
		$user = JWTAuth::user();
		$StudentContactSupport = StudentContactSupportEnroll::create(array("student_id"=>$request->student_id,"teacher_id"=>$request->teacher_id,'enroll_id'=>$request->enroll_id,"message"=>$request->message));
		if($StudentContactSupport)
			{
				$message = "Contact Support Added Successfully";
				return response()->json(array("statusCode"=>200,'message'=>$message));
			}
			else
			{
				$message = "Contact Support Added Failed";
				return response()->json(array("statusCode"=>400,'message'=>$message));
			}
	}
	//enroll_rescheduleclass
	public function enroll_rescheduleclass(Request $request){
			$user = JWTAuth::user();
			$id= $request->teacher_availabilty_of_slots_details_id;
			$student_enroll_schedule_id= $request->student_enroll_schedule_id;
			
			TeacherAvailabiltyOfSlotsDetails::where('id',$id)->update(array('reschedule_status'=>1));
			$get_StudentEnrollSchedule = StudentEnrollSchedule::where('id',$student_enroll_schedule_id)->update(array('reschedule_status'=>1));
			if(!empty($get_StudentEnrollSchedule))
			{
				$message = "Reschedule Apply Successfully";
				return response()->json(array("statusCode"=>200,'message'=>$message));
			}
			else
			{
				$message = "Reschedule Apply Added Failed";
				return response()->json(array("statusCode"=>400,'message'=>$message));
			}	
		
	}
	public function checkUserToken(Request $request)
	{
		$userId 	= $request->userId;
		$bearerToken = $request->bearerToken();
		if ($userId > 0) {
			$user = User::where("id", $userId)->where("deleted", 0)->first();
			$userStatus = !empty($user) ? $user->status : 0;
			if ($userStatus == 1) {
				if ($bearerToken != $user->api_token) {
					$userStatus = 0;
				}
			}
		} else {
			$userStatus = 0;
		}
		//return $userStatus;
		if ($userStatus == 0) {
			$message = "User not Available.";
        	return response()->json(['statusCode' => 203, 'message' => $message]);
        } else {
        	$message = "User Available.";
        	return response()->json(['statusCode' => 200, 'message' => $message]);
        }
	}

	public function isUserActive($userId,$bearerToken=NULL)
	{

		if ($userId > 0) {
			$user = User::where("id", $userId)->where("deleted", 0)->first();
			$userStatus = !empty($user) ? $user->status : 0;

			if ($userStatus == 1) {
				if ($bearerToken != $user->api_token) {
					$userStatus = 0;
				}
			}
		} else {
			$userStatus = 0;
		}
		return $userStatus;
	}


	public function getAboutUsDetails(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId 	= $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "aboutus" => array("id" => ""), "portfolios" => [])]);
		}
		$today	  = date('Y-m-d');
		$subscription = UserSubscription::where("user_id", $userId)->where("end_date", ">=", $today)->orderBy('id', 'DESC')->first();
		$userSubscription = !empty($subscription) ? 1 : 0;
		$aboutUs = AboutUs::where("id", 1)->first();
		$aboutdata = array();
		if (!empty($aboutUs)) {
			$aboutdata['id']  			= $aboutUs->id;
			$aboutdata['title'] 		= $aboutUs->title;
			$aboutdata['content']		= $aboutUs->content;
			$aboutdata['organization']	= $aboutUs->organization;
			$aboutdata['vision']		= $aboutUs->vision;
			$aboutdata['mission']		= $aboutUs->mission;
			$aboutdata['process']		= $aboutUs->process;
			$aboutdata['video']			= asset('upload/aboutus') . "/" . $aboutUs->video; //'https://multiplatform-f.akamaihd.net/i/multi/april11/sintel/sintel-hd_,512x288_450_b,640x360_700_b,768x432_1000_b,1024x576_1400_m,.mp4.csmil/master.m3u8';
			$interesting_facts_arr = array();
			$interesting_facts = json_decode($aboutUs->interesting_facts, true);
			if (!empty($interesting_facts)) {
				foreach ($interesting_facts as $key => $value) {
					$fact['fact_icon'] = asset('upload/aboutus') . "/" . $value['fact_icon'];
					$fact['fact_title'] = $value['fact_title'];
					$fact['fact_sub_title'] = $value['fact_sub_title'];
					array_push($interesting_facts_arr, $fact);
				}
			}
			$aboutdata['interesting_facts']	= $interesting_facts_arr;

			$portfolios = Portfolio::where("status", 1)->where('deleted', 0)->get();
			$portfoliodata = array();
			if (!empty($portfolios)) {
				foreach ($portfolios as $key => $value) {
					$portfoliodata[$key]['id'] 			= $value['id'];
					$portfoliodata[$key]['title'] 		= $value['title'];
					$portfoliodata[$key]['sub_title'] 	= $value['sub_title'];
					$portfoliodata[$key]['image'] 		= asset('upload/portfolios') . "/" . $value['image'];
				}
			}
			$message = "About Us Details.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "aboutus" => $aboutdata, "portfolios" => $portfoliodata)
			]);
		} else {
			$message = "About Us Details Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "aboutus" => "", "portfolios" => "")]);
		}
	}

	public function userHomepage(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "popular_videos" => [], "continue_studying" => [], "courses" => [])]);
		}
		$search   = $request->search;
		$today	  = date('Y-m-d');
		$subscription = UserSubscription::where("user_id", $userId)->where("end_date", ">=", $today)->orderBy('id', 'DESC')->first();
		$userSubscription = !empty($subscription) ? 1 : 0;
		$lessions = Lession::where("status", 1)->where('deleted', 0);
		$popularvideos = Popularvideo::where("status", 1)->where('deleted', 0);
		$continueStudy = ContinueStudy::where("user_id", $userId)->where('is_complete', 0);
		$courses  = Courses::where("status", 1)->where('deleted', 0);
		if (!empty($lessions)) {
			$lessions = $lessions->where("name", 'like', "%" . $search . "%");
		}
		$lessions = $lessions->orderBy('sort_id', 'ASC')->get();
		if (!empty($popularvideos)) {
			$popularvideos = $popularvideos->where("name", 'like', "%" . $search . "%");
		}
		$popularvideos = $popularvideos->orderBy('sort_id', 'ASC')->limit(10)->get();
		$continueStudy = $continueStudy->orderBy('updated_at', 'DESC')->limit(10)->get();
		if (!empty($courses)) {
			$courses = $courses->where("name", 'like', "%" . $search . "%");
		}
		$courses = $courses->orderBy('sort_id', 'ASC')->get();
		$videodata = array();
		$studydata = array();
		$coursedata = array();

		foreach ($popularvideos as $key => $val) {
			$videodata[$key]['id'] 				= $val['id'];
			$videodata[$key]['name'] 			= $val['name'];
			$videodata[$key]['video_thumb'] 	= asset('upload/popularvideos') . "/" . $val['video_thumb'];
			if ($val['uploads3']==1){
				$video = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$val['video'];
			} else {
				$video = asset('upload/popularvideos') . "/" . $val['video'];
			}
			$videodata[$key]['original_video'] 	= isset($val['video']) ? $video : 'NA'; //'https://multiplatform-f.akamaihd.net/i/multi/april11/sintel/sintel-hd_,512x288_450_b,640x360_700_b,768x432_1000_b,1024x576_1400_m,.mp4.csmil/master.m3u8';
			if (!empty($val['video_1']) && $val['video_1']!='NA') {
				if ($val['uploads3']==1){
					$video_1 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$val['video_1'];
				} else {
					if(file_exists( public_path().'/upload/popularvideos/'.$val['video_1'] )) {
						$video_1 = asset('upload/popularvideos') . "/" . $val['video_1'];
					} else {
						$video_1 = $videodata[$key]['original_video'];
					}
				}
			} else {
				$video_1 = $videodata[$key]['original_video'];
			}
			if (!empty($val['video_2']) && $val['video_2']!='NA') {
				if ($val['uploads3']==1){
					$video_2 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$val['video_2'];
				} else {
					if(file_exists( public_path().'/upload/popularvideos/'.$val['video_2'] )) {
						$video_2 = asset('upload/popularvideos') . "/" . $val['video_2'];
					} else {
						$video_2 = $videodata[$key]['original_video'];
					}
				}
			} else {
				$video_2 = $videodata[$key]['original_video'];
			}
			if (!empty($val['video_3']) && $val['video_3']!='NA') {
				if ($val['uploads3']==1){
					$video_3 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$val['video_3'];
				} else {
					if(file_exists( public_path().'/upload/popularvideos/'.$val['video_3'] )) {
						$video_3 = asset('upload/popularvideos') . "/" . $val['video_3'];
					} else {
						$video_3 = $videodata[$key]['original_video'];
					}
				}
			} else {
				$video_3 = $videodata[$key]['original_video'];
			}
			$videodata[$key]['low_video'] 		= $video_1;
			$videodata[$key]['medium_video'] 	= $video_2;
			$videodata[$key]['high_video'] 		= $video_3;
			$videodata[$key]['paid'] 			= $val['paid'];
		}
		foreach ($continueStudy as $key => $study) {
			if ($study['lession_id']==0) {
				$course  = Courses::where("id", $study['course_id'])->where("status", 1)->where('deleted', 0)->first();
				$studydata[$key]['id']  = $study['id'];
				$studydata[$key]['courseId']  		= $study['course_id'];
				$studydata[$key]['lessionId'] 		= 0;
				$studydata[$key]['topicId'] 		= 0;
				$studydata[$key]['name'] 			= isset($course->name) ? $course->name : '';
				$studydata[$key]['image'] 			= isset($course->image) ? asset('course') . "/" . $course->image : '';
				if ($course->uploads3==1){
					$video = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$course->video;
				} else {
					$video = asset('course') . "/" . $course->video;
				}
				$studydata[$key]['original_video'] 	= isset($course->video) ? $video : 'NA'; //'https://multiplatform-f.akamaihd.net/i/multi/april11/sintel/sintel-hd_,512x288_450_b,640x360_700_b,768x432_1000_b,1024x576_1400_m,.mp4.csmil/master.m3u8';
				if (!empty($course->video_1) && $course->video_1!='NA') {
					if ($course->uploads3==1){
						$video_1 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$course->video_1;
					} else {
						if(file_exists( public_path().'/course/'.$course->video_1 )) {
							$video_1 = asset('course') . "/" . $course->video_1;
						} else {
							$video_1 = $studydata[$key]['original_video'];
						}
					}
				} else {
					$video_1 = $studydata[$key]['original_video'];
				}
				if (!empty($course->video_2) && $course->video_2!='NA') {
					if ($course->uploads3==1){
						$video_2 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$course->video_2;
					} else {
						if(file_exists( public_path().'/course/'.$course->video_2 )) {
							$video_2 = asset('course') . "/" . $course->video_2;
						} else {
							$video_2 = $studydata[$key]['original_video'];
						}
					}
				} else {
					$video_2 = $studydata[$key]['original_video'];
				}
				if (!empty($course->video_3) && $course->video_3!='NA') {
					if ($course->uploads3==1){
						$video_3 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$course->video_3;
					} else {
						if(file_exists( public_path().'/course/'.$course->video_3 )) {
							$video_3 = asset('course') . "/" . $course->video_3;
						} else {
							$video_3 = $studydata[$key]['original_video'];
						}
					}
				} else {
					$video_3 = $studydata[$key]['original_video'];
				}
				$studydata[$key]['low_video'] 		= $video_1;
				$studydata[$key]['medium_video'] 	= $video_2;
				$studydata[$key]['high_video'] 		= $video_3;
				$total_lessions = Lession::where("courseId", $study['course_id'])->where("status", 1)->where('deleted', 0)->count();
				$studydata[$key]['total_lessions'] 	= $total_lessions;
				$studydata[$key]['isFree'] 			= isset($course->isFree) ? $course->isFree : '';
			} else {
				if ($study['lession_id']>0) {
					if ($study['topic_id']==0) {
						$lession  = Lession::where("id", $study['lession_id'])->where("status", 1)->where('deleted', 0)->first();
						$studydata[$key]['id'] 				= $study['id'];
						$studydata[$key]['courseId']  		= $study['course_id'];
						$studydata[$key]['lessionId'] 		= $study['lession_id'];
						$studydata[$key]['topicId'] 		= 0;
						$studydata[$key]['name'] 			= isset($lession->name) ? $lession->name : '';
						$studydata[$key]['image'] 			= isset($lession->video_thumb) ? asset('lessions') . "/" . $lession->video_thumb : '';
						if ($lession->uploads3==1){
							$video = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$lession->fullvideo;
						} else {
							$video = asset('lessions') . "/" . $lession->fullvideo;
						}
						$studydata[$key]['original_video'] 	= isset($lession->fullvideo) ? $video : 'NA'; //'https://multiplatform-f.akamaihd.net/i/multi/april11/sintel/sintel-hd_,512x288_450_b,640x360_700_b,768x432_1000_b,1024x576_1400_m,.mp4.csmil/master.m3u8';
						if (!empty($lession->video_1) && $lession->video_1!='NA') {
							if ($lession->uploads3==1){
								$video_1 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$lession->video_1;
							} else {
								if(file_exists( public_path().'/lessions/'.$lession->video_1 )) {
									$video_1 = asset('lessions') . "/" . $lession->video_1;
								} else {
									$video_1 = $studydata[$key]['original_video'];
								}
							}
						} else {
							$video_1 = $studydata[$key]['original_video'];
						}
						if (!empty($lession->video_2) && $lession->video_2!='NA') {
							if ($lession->uploads3==1){
								$video_2 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$lession->video_2;
							} else {
								if(file_exists( public_path().'/lessions/'.$lession->video_2 )) {
									$video_2 = asset('lessions') . "/" . $lession->video_2;
								} else {
									$video_2 = $studydata[$key]['original_video'];
								}
							}
						} else {
							$video_2 = $studydata[$key]['original_video'];
						}
						if (!empty($lession->video_3) && $lession->video_3!='NA') {
							if ($lession->uploads3==1){
								$video_3 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$lession->video_3;
							} else {
								if(file_exists( public_path().'/lessions/'.$lession->video_3 )) {
									$video_3 = asset('lessions') . "/" . $lession->video_3;
								} else {
									$video_3 = $studydata[$key]['original_video'];
								}
							}
						} else {
							$video_3 = $studydata[$key]['original_video'];
						}
						$studydata[$key]['low_video'] 		= $video_1;
						$studydata[$key]['medium_video'] 	= $video_2;
						$studydata[$key]['high_video'] 		= $video_3;
						$total_lessions = Lession::where("courseId", $study['course_id'])->where("status", 1)->where('deleted', 0)->count();
						$studydata[$key]['total_lessions'] 	= $total_lessions;
						$studydata[$key]['isFree']			= isset($lession->isFree) ? $lession->isFree : '';
					} else {
						$topic = Chapter::where("id", $study['topic_id'])->where("status", 1)->where('deleted', 0)->first();
						$studydata[$key]['id'] 				= $study['id'];
						$studydata[$key]['courseId']  		= $study['course_id'];
						$studydata[$key]['lessionId'] 		= $study['lession_id'];
						$studydata[$key]['topicId'] 		= $study['topic_id'];
						$studydata[$key]['name'] 			= isset($topic->name) ? $topic->name : '';
						$studydata[$key]['image'] 			= isset($topic->video_thumb) ? asset('lessions') . "/" . $topic->video_thumb : '';
						if ($topic->uploads3==1){
							$video = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$topic->fullvideo;
						} else {
							$video = asset('lessions') . "/" . $topic->fullvideo;
						}
						$studydata[$key]['original_video'] 	= isset($topic->fullvideo) ? $video : 'NA'; //'https://multiplatform-f.akamaihd.net/i/multi/april11/sintel/sintel-hd_,512x288_450_b,640x360_700_b,768x432_1000_b,1024x576_1400_m,.mp4.csmil/master.m3u8';
						if (!empty($topic->video_1) && $topic->video_1!='NA') {
							if ($topic->uploads3==1){
								$video_1 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$topic->video_1;
							} else {
								if(file_exists( public_path().'/lessions/'.$topic->video_1 )) {
									$video_1 = asset('lessions') . "/" . $topic->video_1;
								} else {
									$video_1 = $studydata[$key]['original_video'];
								}
							}
						} else {
							$video_1 = $studydata[$key]['original_video'];
						}
						if (!empty($topic->video_2) && $topic->video_2!='NA') {
							if ($topic->uploads3==1){
								$video_2 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$topic->video_2;
							} else {
								if(file_exists( public_path().'/lessions/'.$topic->video_2 )) {
									$video_2 = asset('lessions') . "/" . $topic->video_2;
								} else {
									$video_2 = $studydata[$key]['original_video'];
								}
							}
						} else {
							$video_2 = $studydata[$key]['original_video'];
						}
						if (!empty($topic->video_3) && $topic->video_3!='NA') {
							if ($topic->uploads3==1){
								$video_3 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$topic->video_3;
							} else {
								if(file_exists( public_path().'/lessions/'.$topic->video_3 )) {
									$video_3 = asset('lessions') . "/" . $topic->video_3;
								} else {
									$video_3 = $studydata[$key]['original_video'];
								}
							}
						} else {
							$video_3 = $studydata[$key]['original_video'];
						}
						$studydata[$key]['low_video'] 		= $video_1;
						$studydata[$key]['medium_video'] 	= $video_2;
						$studydata[$key]['high_video'] 		= $video_3;
						$total_lessions = Chapter::where("courseId", $study['course_id'])->where("lessionId", $study['lession_id'])->where("status", 1)->where('deleted', 0)->count();
						$studydata[$key]['total_lessions'] 	= $total_lessions;
						$studydata[$key]['isFree']			= isset($topic->isFree) ? $topic->isFree : '';
					}
				}
			}
			$checkStudentExam = StudentExam::where("user_id", $userId)->where("course_id", $study['course_id'])->where("lession_id", $study['lession_id'])->orderBy("id","DESC")->first();
			if (!empty($checkStudentExam)) {
				$is_complete = $checkStudentExam->is_complete + 1;
			} else {
				$is_complete = 0;
			}
			$studydata[$key]['quizStatus']	= $is_complete;
			/*$total_time = strtotime($study['video_total_time']) - strtotime('00:00:00');
			$viewed_time = strtotime($study['video_viewed_time']) - strtotime('00:00:00');*/
			$total_time = !empty($study['video_total_time']) ? $study['video_total_time'] : 0;
			$viewed_time = !empty($study['video_viewed_time']) ? $study['video_viewed_time'] : 0;
			$view_percent = $percentLeft = 0;
			if ($total_time > 0) {
				$view_percent = ($viewed_time / $total_time) * 100;
				if ($view_percent > 99) {
					$data = array(
						'is_complete' => 1,
						'updated_at'  => date('Y-m-d H:i:s'),
					);
					$update = ContinueStudy::where("id", $study['id'])->update($data);
					$view_percent = 100;
				}
				if ($view_percent < 0) {
					$view_percent = 0;
				}
				$percentLeft = (($total_time - $viewed_time) / $total_time) * 100;
			}
			//echo 'TT '.$total_time.' VT '.$viewed_time.' VP '.$view_percent.' LP '.$percentLeft; die;
			$studydata[$key]['video_total_time']  = !empty($study['video_total_time']) ? $study['video_total_time'] : 0;
			$studydata[$key]['video_viewed_time'] = !empty($study['video_viewed_time']) ? $study['video_viewed_time'] : 0;
			$studydata[$key]['view_percent']      = round($view_percent);
		}
		foreach ($courses as $key => $value) {
			$coursedata[$key]['id'] 			= $value['id'];
			$coursedata[$key]['name'] 			= $value['name'];
			$coursedata[$key]['image'] 			= asset('course') . "/" . $value['image'];
			if ($value['uploads3']==1){
				$video = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$value['video'];
			} else {
				$video = asset('course') . "/" . $value['video'];
			}
			$coursedata[$key]['original_video'] = isset($value['video']) ? $video : 'NA'; //'https://multiplatform-f.akamaihd.net/i/multi/april11/sintel/sintel-hd_,512x288_450_b,640x360_700_b,768x432_1000_b,1024x576_1400_m,.mp4.csmil/master.m3u8';
			if (!empty($value['video_1']) && $value['video_1']!='NA') {
				if ($value['uploads3']==1){
					$video_1 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$value['video_1'];
				} else {
					if(file_exists( public_path().'/course/'.$value['video_1'] )) {
						$video_1 = asset('course') . "/" . $value['video_1'];
					} else {
						$video_1 = $coursedata[$key]['original_video'];
					}
				}
			} else {
				$video_1 = $coursedata[$key]['original_video'];
			}
			if (!empty($value['video_2']) && $value['video_2']!='NA') {
				if ($value['uploads3']==1){
					$video_2 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$value['video_2'];
				} else {
					if(file_exists( public_path().'/course/'.$value['video_2'] )) {
						$video_2 = asset('course') . "/" . $value['video_2'];
					} else {
						$video_2 = $coursedata[$key]['original_video'];
					}
				}
			} else {
				$video_2 = $coursedata[$key]['original_video'];
			}
			if (!empty($value['video_3']) && $value['video_3']!='NA') {
				if ($value['uploads3']==1){
					$video_3 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$value['video_3'];
				} else {
					if(file_exists( public_path().'/course/'.$value['video_3'] )) {
						$video_3 = asset('course') . "/" . $value['video_3'];
					} else {
						$video_3 = $coursedata[$key]['original_video'];
					}
				}
			} else {
				$video_3 = $coursedata[$key]['original_video'];
			}
			$coursedata[$key]['low_video'] 		= $video_1;
			$coursedata[$key]['medium_video'] 	= $video_2;
			$coursedata[$key]['high_video'] 	= $video_3;
			$coursedata[$key]['pdf'] 			= asset('course') . "/" . $value['pdf'];
			$total_lessions = Lession::where("courseId", $value['id'])->where("status", 1)->where('deleted', 0)->count();
			$coursedata[$key]['total_lessions'] = $total_lessions;
			$coursedata[$key]['isFree'] 		= $value['isFree'];
		}
		$message = "User Home Page Data.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "popular_videos" => $videodata, "continue_studying" => $studydata, "courses" => $coursedata)]);
	}
	public function allPopularVideos(Request $request)
	{
		$search   = $request->search;
		$popularvideos = Popularvideo::where("status", 1)->where('deleted', 0);
		if (!empty($popularvideos)) {
			$popularvideos = $popularvideos->where("name", 'like', "%" . $search . "%");
		}
		$popularvideos = $popularvideos->orderBy('sort_id', 'ASC')->get();
		$videodata = array();
		foreach ($popularvideos as $key => $val) {
			$videodata[$key]['id'] 				= $val['id'];
			$videodata[$key]['name'] 			= $val['name'];
			$videodata[$key]['video_thumb'] 	= asset('upload/popularvideos') . "/" . $val['video_thumb'];
			if ($val['uploads3']==1){
				$video = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$val['video'];
			} else {
				$video = asset('upload/popularvideos') . "/" . $val['video'];
			}
			$videodata[$key]['original_video'] 	= isset($val['video']) ? $video : 'NA';
			if (!empty($val['video_1']) && $val['video_1']!='NA') {
				if ($val['uploads3']==1){
					$video_1 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$val['video_1'];
				} else {
					if(file_exists( public_path().'/upload/popularvideos/'.$val['video_1'] )) {
						$video_1 = asset('upload/popularvideos') . "/" . $val['video_1'];
					} else {
						$video_1 = $videodata[$key]['original_video'];
					}
				}
			} else {
				$video_1 = $videodata[$key]['original_video'];
			}
			if (!empty($val['video_2']) && $val['video_2']!='NA') {
				if ($val['uploads3']==1){
					$video_2 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$val['video_2'];
				} else {
					if(file_exists( public_path().'/upload/popularvideos/'.$val['video_2'] )) {
						$video_2 = asset('upload/popularvideos') . "/" . $val['video_2'];
					} else {
						$video_2 = $videodata[$key]['original_video'];
					}
				}
			} else {
				$video_2 = $videodata[$key]['original_video'];
			}
			if (!empty($val['video_3']) && $val['video_3']!='NA') {
				if ($val['uploads3']==1){
					$video_3 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$val['video_3'];
				} else {
					if(file_exists( public_path().'/upload/popularvideos/'.$val['video_3'] )) {
						$video_3 = asset('upload/popularvideos') . "/" . $val['video_3'];
					} else {
						$video_3 = $videodata[$key]['original_video'];
					}
				}
			} else {
				$video_3 = $videodata[$key]['original_video'];
			}
			$videodata[$key]['low_video'] 		= $video_1;
			$videodata[$key]['medium_video']	= $video_2;
			$videodata[$key]['high_video'] 		= $video_3;
			$videodata[$key]['paid'] 			= $val['paid'];
		}
		$message = "Get All Popular Videos List.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("popular_videos" => $videodata)]);
	}
	public function ourCourses(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "courses" => [])]);
		}
		$search   = $request->search;
		$today	  = date('Y-m-d');
		$subscription = UserSubscription::where("user_id", $userId)->where("end_date", ">=", $today)->orderBy('id', 'DESC')->first();
		$userSubscription = !empty($subscription) ? 1 : 0;
		$courses  = Courses::where("status", 1)->where('deleted', 0);
		if (!empty($courses)) {
			$courses = $courses->where("name", 'like', "%" . $search . "%");
		}
		$courses = $courses->orderBy('sort_id', 'ASC')->get();
		$coursedata = array();
		foreach ($courses as $key => $value) {
			$coursedata[$key]['id'] 			= $value['id'];
			$coursedata[$key]['name']			= $value['name'];
			$coursedata[$key]['image'] 			= asset('course') . "/" . $value['image'];
			if ($value['uploads3']==1){
				$video = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$value['video'];
			} else {
				$video = asset('course') . "/" . $value['video'];
			}
			$coursedata[$key]['original_video'] = isset($value['video']) ? $video : 'NA';
			if (!empty($value['video_1']) && $value['video_1']!='NA') {
				if ($value['uploads3']==1){
					$video_1 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$value['video_1'];
				} else {
					if(file_exists( public_path().'/course/'.$value['video_1'] )) {
						$video_1 = asset('course') . "/" . $value['video_1'];
					} else {
						$video_1 = $coursedata[$key]['original_video'];
					}
				}
			} else {
				$video_1 = $coursedata[$key]['original_video'];
			}
			if (!empty($value['video_2']) && $value['video_2']!='NA') {
				if ($value['uploads3']==1){
					$video_2 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$value['video_2'];
				} else {
					if(file_exists( public_path().'/course/'.$value['video_2'] )) {
						$video_2 = asset('course') . "/" . $value['video_2'];
					} else {
						$video_2 = $coursedata[$key]['original_video'];
					}
				}
			} else {
				$video_2 = $coursedata[$key]['original_video'];
			}
			if (!empty($value['video_3']) && $value['video_3']!='NA') {
				if ($value['uploads3']==1){
					$video_3 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$value['video_3'];
				} else {
					if(file_exists( public_path().'/course/'.$value['video_3'] )) {
						$video_3 = asset('course') . "/" . $value['video_3'];
					} else {
						$video_3 = $coursedata[$key]['original_video'];
					}
				}
			} else {
				$video_3 = $coursedata[$key]['original_video'];
			}
			$coursedata[$key]['low_video']		= $video_1;
			$coursedata[$key]['medium_video']	= $video_2;
			$coursedata[$key]['high_video']		= $video_3;
			$coursedata[$key]['pdf']			= asset('course') . "/" . $value['pdf'];
			$total_lessions = Lession::where("courseId", $value['id'])->where("status", 1)->where('deleted', 0)->count();
			$coursedata[$key]['total_lessions'] = $total_lessions;
			$coursedata[$key]['isFree']			= $value['isFree'];
		}
		$message = "Our Courses List.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "courses" => $coursedata)]);
	}
	public function getCourseDetails(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "course" => array("id" => ""), "course_features" => [], "course_faqs" => [], "course_videos" => [], "course_pdfs" => [])]);
		}
		$courseId = $request->courseId;
		$today	  = date('Y-m-d');
		$subscription = UserSubscription::where("user_id", $userId)->where("end_date", ">=", $today)->orderBy('id', 'DESC')->first();
		$userSubscription = !empty($subscription) ? 1 : 0;
		$course   = Courses::where("id", $courseId)->where("status", 1)->where('deleted', 0)->first();
		$coursedata = array();
		if (!empty($course)) {
			$coursedata['id'] 				= $course->id;
			$coursedata['name'] 			= $course->name;
			$coursedata['image']			= asset('course') . "/" . $course->image;
			if ($course->uploads3==1){
				$video = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$course->video;
			} else {
				$video = asset('course') . "/" . $course->video;
			}
			$coursedata['original_video']	= isset($course->video) ? $video : 'NA';
			if (!empty($course->video_1) && $course->video_1!='NA') {
				if ($course->uploads3==1){
					$video_1 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$course->video_1;
				} else {
					if(file_exists( public_path().'/course/'.$course->video_1 )) {
						$video_1 = asset('course') . "/" . $course->video_1;
					} else {
						$video_1 = $coursedata['original_video'];
					}
				}
			} else {
				$video_1 = $coursedata['original_video'];
			}
			if (!empty($course->video_2) && $course->video_2!='NA') {
				if ($course->uploads3==1){
					$video_2 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$course->video_2;
				} else {
					if(file_exists( public_path().'/course/'.$course->video_2 )) {
						$video_2 = asset('course') . "/" . $course->video_2;
					} else {
						$video_2 = $coursedata['original_video'];
					}
				}
			} else {
				$video_2 = $coursedata['original_video'];
			}
			if (!empty($course->video_3) && $course->video_3!='NA') {
				if ($course->uploads3==1){
					$video_3 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$course->video_3;
				} else {
					if(file_exists( public_path().'/course/'.$course->video_3 )) {
						$video_3 = asset('course') . "/" . $course->video_3;
					} else {
						$video_3 = $coursedata['original_video'];
					}
				}
			} else {
				$video_3 = $coursedata['original_video'];
			}
			$coursedata['low_video'] 		= $video_1;
			$coursedata['medium_video'] 	= $video_2;
			$coursedata['high_video'] 		= $video_3;
			$course_videos = Lession::where("courseId", $courseId)->where("fullvideo", "!=", "")->where("status", 1)->where('deleted', 0)->orderBy('sort_id', 'ASC')->get();
			$coursedata['total_videos'] 	= count($course_videos);
			$coursedata['video_duration']	= $course->video_duration;
			//$coursedata['total_duration'] = 0;
			foreach ($course_videos as $key => $val) {
				//$coursedata['total_duration'] += $val['id'];
			}
			$courseComplete = ContinueStudy::where("user_id", $userId)->where("course_id", $courseId)->get();
			$study_total_time = $study_viewed_time = 0;
			foreach ($courseComplete as $key => $val) {
				$study_total_time += $val['video_total_time'];
				$study_viewed_time += $val['video_viewed_time'];
			}
			if ($study_total_time > 0) {
				$course_complete = ($study_viewed_time / $study_total_time) * 100;
				if ($course_complete > 99) {
					$course_complete = 100;
				}
			} else {
				$course_complete = 0;
			}
			$coursedata['course_complete'] = round($course_complete);
			$coursedata['overview'] = $course->overview;
			$coursedata['course_certificate'] = '';
			$coursedata['isFree']  = $course->isFree;
			$checkStudentExam = StudentExam::where("user_id", $userId)->where("course_id", $courseId)->where("lession_id", 0)->where("topic_id", 0)->orderBy("id", "DESC")->first();
			if (!empty($checkStudentExam)) {
				if ($checkStudentExam->start_time != '00:00:00') {
					$certificate_status = $checkStudentExam->is_complete + 1;
				} else {
					$certificate_status = 0;
				}
			} else {
				$certificate_status = 0;
			}
			$coursedata['certificateStatus']	= $certificate_status;
			$continueStudy = ContinueStudy::where("user_id", $userId)->where("course_id", $courseId)->where("lession_id", 0)->where('is_complete', 0)->first();
			$study['video_total_time'] = !empty($continueStudy->video_total_time) ? $continueStudy->video_total_time : 0;
			$study['video_viewed_time'] = !empty($continueStudy->video_viewed_time) ? $continueStudy->video_viewed_time : 0;
			$total_time = $study['video_total_time'];
			$viewed_time = $study['video_viewed_time'];
			if ($total_time > 0) {
				$view_percent = ($viewed_time / $total_time) * 100;
				if ($view_percent > 99) {
					$view_percent = 100;
				}
				if ($view_percent < 0) {
					$view_percent = 0;
				}
			} else {
				$view_percent = 0;
			}
			$coursedata['video_total_time']  = $study['video_total_time'];
			$coursedata['video_viewed_time'] = $study['video_viewed_time'];
			$coursedata['view_percent']      = round($view_percent);
			$course_features = Coursefeature::where("courseId", $courseId)->get();
			$featuredata = array();
			foreach ($course_features as $key => $val) {
				$featuredata[$key]['feature'] = $val['feature'];
			}
			$course_faqs = Coursefeq::where("courseId", $courseId)->get();
			$coursefaqdata = array();
			foreach ($course_faqs as $key => $val) {
				$coursefaqdata[$key]['id']       = $val['id'];
				$coursefaqdata[$key]['question'] = $val['title'];
				$coursefaqdata[$key]['answer']   = $val['contant'];
			}
			$lessions = Lession::where("courseId", $courseId)->where("status", 1)->where('deleted', 0)->orderBy('sort_id', 'ASC')->get();
			$videodata = array();
			foreach ($lessions as $key => $value) {
				if($value['fullvideo'] != ''){
					$videodata[$key]['lession_id']		= $value['id'];
					$videodata[$key]['name']			= $value['name'];
					$videodata[$key]['image']			= asset('lessions') . "/" . $value['video_thumb'];
					if ($value['uploads3']==1){
						$video = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$value['fullvideo'];
					} else {
						$video = asset('lessions') . "/" . $value['fullvideo'];
					}
					$videodata[$key]['original_video']	= isset($value['fullvideo']) ? $video : 'NA';
					if (!empty($value['video_1']) && $value['video_1']!='NA') {
						if ($value['uploads3']==1){
							$video_1 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$value['video_1'];
						} else {
							if(file_exists( public_path().'/lessions/'.$value['video_1'] )) {
								$video_1 = asset('lessions') . "/" . $value['video_1'];
							} else {
								$video_1 = $videodata[$key]['original_video'];
							}
						}
					} else {
						$video_1 = $videodata[$key]['original_video'];
					}
					if (!empty($value['video_2']) && $value['video_2']!='NA') {
						if ($value['uploads3']==1){
							$video_2 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$value['video_2'];
						} else {
							if(file_exists( public_path().'/lessions/'.$value['video_2'] )) {
								$video_2 = asset('lessions') . "/" . $value['video_2'];
							} else {
								$video_2 = $videodata[$key]['original_video'];
							}
						}
					} else {
						$video_2 = $videodata[$key]['original_video'];
					}
					if (!empty($value['video_3']) && $value['video_3']!='NA') {
						if ($value['uploads3']==1){
							$video_3 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$value['video_3'];
						} else {
							if(file_exists( public_path().'/lessions/'.$value['video_3'] )) {
								$video_3 = asset('lessions') . "/" . $value['video_3'];
							} else {
								$video_3 = $videodata[$key]['original_video'];
							}
						}
					} else {
						$video_3 = $videodata[$key]['original_video'];
					}
					$videodata[$key]['low_video']		= $video_1;
					$videodata[$key]['medium_video']	= $video_2;
					$videodata[$key]['high_video']		= $video_3;
					$videodata[$key]['content']			= $value['content'];
					$videodata[$key]['isFree']			= $value['isFree'];
					$checkStudentExam = StudentExam::where("user_id", $userId)->where("course_id", $courseId)->where("lession_id", $value['id'])->where("topic_id", 0)->orderBy("id","DESC")->first();
					if (!empty($checkStudentExam)) {
						$is_complete = $checkStudentExam->is_complete + 1;
					} else {
						$is_complete = 0;
					}
					$videodata[$key]['quizStatus']		= $is_complete;
					$continueStudy = ContinueStudy::where("user_id", $userId)->where("course_id", $courseId)->where("lession_id", $value['id'])->where('is_complete', 0)->first();
					$study['video_total_time'] = !empty($continueStudy->video_total_time) ? $continueStudy->video_total_time : 0;
					$study['video_viewed_time'] = !empty($continueStudy->video_viewed_time) ? $continueStudy->video_viewed_time : 0;
					$total_time = $study['video_total_time'];
					$viewed_time = $study['video_viewed_time'];
					if ($total_time > 0) {
						$view_percent = ($viewed_time / $total_time) * 100;
						if ($view_percent > 99) {
							$view_percent = 100;
						}
						if ($view_percent < 0) {
							$view_percent = 0;
						}
					} else {
						$view_percent = 0;
					}
					$videodata[$key]['video_total_time']  = $study['video_total_time'];
					$videodata[$key]['video_viewed_time'] = $study['video_viewed_time'];
					$videodata[$key]['view_percent']      = round($view_percent);
				}
			}
			$pdfdata = array();
			foreach ($lessions as $key => $value) {
				if($value['pdf'] != ''){
					$pdfs['lession_id']	= $value['id'];
					$pdfs['name']			= $value['name'];
					$pdfs['image']			= asset('lessions') . "/" . $value['video_thumb'];
					$pdfs['pdf']			= asset('lessions') . "/" . $value['pdf'];
					$pdfs['content']		= $value['content'];
					$pdfs['isFree']		= $value['isFree'];
					$checkStudentExam = StudentExam::where("user_id", $userId)->where("course_id", $courseId)->where("lession_id", $value['id'])->where("topic_id", 0)->orderBy("id","DESC")->first();
					if (!empty($checkStudentExam)) {
						$is_complete = $checkStudentExam->is_complete + 1;
					} else {
						$is_complete = 0;
					}
					$pdfs['quizStatus']	= $is_complete;
					array_push($pdfdata, $pdfs);
				}
			}
			$message = "Course Details.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "course" => $coursedata, "course_features" => $featuredata, "course_faqs" => $coursefaqdata, "course_videos" => $videodata, "course_pdfs" => $pdfdata)]);
		} else {
			$message = "Course Details Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "course" => "", "course_features" => "", "course_faqs" => "", "course_videos" => "", "course_pdfs" => "")]);
		}
	}
	public function getLessionDetails(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "lession" => array("id" => ""), "topics" => [])]);
		}
		$lessionId  = $request->lessionId;
		$today	  = date('Y-m-d');
		$subscription = UserSubscription::where("user_id", $userId)->where("end_date", ">=", $today)->orderBy('id', 'DESC')->first();
		$userSubscription = !empty($subscription) ? 1 : 0;
		$lession = Lession::where("id", $lessionId)->where("status", 1)->where('deleted', 0)->first();
		$lessiondata = array();
		if (!empty($lession)) {
			$lessiondata['course_id']		= $lession->courseId;
			$lessiondata['lession_id']		= $lession->id;
			$lessiondata['name']			= $lession->name;
			$lessiondata['image']			= asset('lessions') . "/" . $lession->video_thumb;
			if ($lession->uploads3==1){
				$video = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$lession->fullvideo;
			} else {
				$video = asset('lessions') . "/" . $lession->fullvideo;
			}
			$lessiondata['original_video']	= isset($lession->fullvideo) ? $video : 'NA';
			if (!empty($lession->video_1) && $lession->video_1!='NA') {
				if ($lession->uploads3==1){
					$video_1 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$lession->video_1;
				} else {
					if(file_exists( public_path().'/lessions/'.$lession->video_1 )) {
						$video_1 = asset('lessions') . "/" . $lession->video_1;
					} else {
						$video_1 = $lessiondata['original_video'];
					}
				}
			} else {
				$video_1 = $lessiondata['original_video'];
			}
			if (!empty($lession->video_2) && $lession->video_2!='NA') {
				if ($lession->uploads3==1){
					$video_2 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$lession->video_2;
				} else {
					if(file_exists( public_path().'/lessions/'.$lession->video_2 )) {
						$video_2 = asset('lessions') . "/" . $lession->video_2;
					} else {
						$video_2 = $lessiondata['original_video'];
					}
				}
			} else {
				$video_2 = $lessiondata['original_video'];
			}
			if (!empty($lession->video_3) && $lession->video_3!='NA') {
				if ($lession->uploads3==1){
					$video_3 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$lession->video_3;
				} else {
					if(file_exists( public_path().'/lessions/'.$lession->video_3 )) {
						$video_3 = asset('lessions') . "/" . $lession->video_3;
					} else {
						$video_3 = $lessiondata['original_video'];
					}
				}
			} else {
				$video_3 = $lessiondata['original_video'];
			}
			$lessiondata['low_video']		= $video_1;
			$lessiondata['medium_video']	= $video_2;
			$lessiondata['high_video']		= $video_3;
			$lessiondata['pdf']				= asset('lessions') . "/" . $lession->pdf;
			$lessiondata['content']			= $lession->content;
			$lessiondata['isFree']			= $lession->isFree;

			$checkStudentExam = StudentExam::where("user_id", $userId)->where("course_id", $lession->courseId)->where("lession_id", $lession->id)->where("topic_id", 0)->orderBy("id","DESC")->first();
			if (!empty($checkStudentExam)) {
				$is_complete = $checkStudentExam->is_complete + 1;
			} else {
				$is_complete = 0;
			}
			$lessiondata['quizStatus']	= $is_complete;

			$continueStudy = ContinueStudy::where("user_id", $userId)->where("course_id", $lession->courseId)->where("lession_id", $lession->id)->where('is_complete', 0)->first();
			$study['video_total_time'] = !empty($continueStudy->video_total_time) ? $continueStudy->video_total_time : 0;
			$study['video_viewed_time'] = !empty($continueStudy->video_viewed_time) ? $continueStudy->video_viewed_time : 0;
			$total_time = $study['video_total_time'];
			$viewed_time = $study['video_viewed_time'];
			if ($total_time > 0) {
				$view_percent = ($viewed_time / $total_time) * 100;
				if ($view_percent > 99) {
					$view_percent = 100;
				}
				if ($view_percent < 0) {
					$view_percent = 0;
				}
			} else {
				$view_percent = 0;
			}
			$lessiondata['video_total_time']  = $study['video_total_time'];
			$lessiondata['video_viewed_time'] = $study['video_viewed_time'];
			$lessiondata['view_percent']      = round($view_percent);

			$topics = Chapter::where("lessionId", $lession->id)->where("status", 1)->where('deleted', 0)->orderBy('sort_id', 'ASC')->get();
			$topicdata = array();
			if (!empty($topics)) {
				foreach ($topics as $key => $value) {
					$chapterNum = $key + 1;
					$topicdata[$key]['chapter_id']		= $value['lessionId'].'.'.$chapterNum;
					$topicdata[$key]['course_id']		= $value['courseId'];
					$topicdata[$key]['lession_id']		= $value['lessionId'];
					$topicdata[$key]['topic_id']		= $value['id'];
					$topicdata[$key]['name']			= $value['name'];
					$topicdata[$key]['image']			= asset('lessions') . "/" . $value['video_thumb'];
					if ($value['uploads3']==1){
						$video = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$value['fullvideo'];
					} else {
						$video = asset('lessions') . "/" . $value['fullvideo'];
					}
					$topicdata[$key]['original_video']	= isset($value['fullvideo']) ? $video : 'NA';
					if (!empty($value['video_1']) && $value['video_1']!='NA') {
						if ($value['uploads3']==1){
							$video_1 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$value['video_1'];
						} else {
							if(file_exists( public_path().'/lessions/'.$value['video_1'] )) {
								$video_1 = asset('lessions') . "/" . $value['video_1'];
							} else {
								$video_1 = $topicdata[$key]['original_video'];
							}
						}
					} else {
						$video_1 = $topicdata[$key]['original_video'];
					}
					if (!empty($value['video_2']) && $value['video_2']!='NA') {
						if ($value['uploads3']==1){
							$video_2 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$value['video_2'];
						} else {
							if(file_exists( public_path().'/lessions/'.$value['video_2'] )) {
								$video_2 = asset('lessions') . "/" . $value['video_2'];
							} else {
								$video_2 = $topicdata[$key]['original_video'];
							}
						}
					} else {
						$video_2 = $topicdata[$key]['original_video'];
					}
					if (!empty($value['video_3']) && $value['video_3']!='NA') {
						if ($value['uploads3']==1){
							$video_3 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$value['video_3'];
						} else {
							if(file_exists( public_path().'/lessions/'.$value['video_3'] )) {
								$video_3 = asset('lessions') . "/" . $value['video_3'];
							} else {
								$video_3 = $topicdata[$key]['original_video'];
							}
						}
					} else {
						$video_3 = $topicdata[$key]['original_video'];
					}
					$topicdata[$key]['low_video']		= $video_1;
					$topicdata[$key]['medium_video']	= $video_2;
					$topicdata[$key]['high_video']		= $video_3;
					$topicdata[$key]['pdf']				= asset('lessions') . "/" . $value['pdf'];
					$topicdata[$key]['content']			= $value['content'];
					$topicdata[$key]['isFree']			= $value['isFree'];
					
					$checkStudentExam = StudentExam::where("user_id", $userId)->where("course_id", $value['courseId'])->where("lession_id", $value['lessionId'])->where("topic_id", $value['id'])->orderBy("id","DESC")->first();
					if (!empty($checkStudentExam)) {
						$is_complete = $checkStudentExam->is_complete + 1;
					} else {
						$is_complete = 0;
					}
					$topicdata[$key]['quizStatus']	= $is_complete;
					
					$continueStudy = ContinueStudy::where("user_id", $userId)->where("course_id", $value['courseId'])->where("lession_id", $value['lessionId'])->where("topic_id", $value['id'])->where('is_complete', 0)->first();
					$study['video_total_time'] = !empty($continueStudy->video_total_time) ? $continueStudy->video_total_time : 0;
					$study['video_viewed_time'] = !empty($continueStudy->video_viewed_time) ? $continueStudy->video_viewed_time : 0;
					$total_time = $study['video_total_time'];
					$viewed_time = $study['video_viewed_time'];
					if ($total_time > 0) {
						$view_percent = ($viewed_time / $total_time) * 100;
						if ($view_percent > 99) {
							$view_percent = 100;
						}
						if ($view_percent < 0) {
							$view_percent = 0;
						}
					} else {
						$view_percent = 0;
					}
					$topicdata[$key]['video_total_time']  = $study['video_total_time'];
					$topicdata[$key]['video_viewed_time'] = $study['video_viewed_time'];
					$topicdata[$key]['view_percent']      = round($view_percent);
				}
			}
			$message = "Lession Details.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "lession" => $lessiondata, "topics" => $topicdata)
			]);
		} else {
			$message = "Lession Details Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "lession" => "", "topics" => "")]);
		}
	}
	public function getRatingTypes(Request $request)
	{
		//$ratingType 	= $request->ratingType;
		$ratingTypes = RatingType::get();
		$ratingTypedata = array();
		if (!empty($ratingTypes)) {
			foreach ($ratingTypes as $key => $value) {
				$ratingTypedata[$key]['id']	= $value['id'];
				$ratingTypedata[$key]['ratingType']	= $value['ratingType'];
			}
			$message = "Get Rating Types Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("ratingTypes" => $ratingTypedata)
			]);
		} else {
			$message = "Rating Types Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("ratingTypes" => "")]);
		}
	}
	public function getRatingMessages(Request $request)
	{
		/*$ratingType 	= $request->ratingType;
		$ratingMessages = RatingMessage::where('ratingType', $ratingType)->get();*/
		/*echo '<pre />'; print_r($ratingMessages); die;*/
		//$ratingMessages = RatingMessage::groupBy("message")->get();
		$ratingMessages = RatingMessage::get();
		$sadMsgdata = array();
		$scepticMsgdata = array();
		$happyMsgdata = array();
		if (!empty($ratingMessages)) {
			foreach ($ratingMessages as $key => $value) {
				//$ratingMsgdata[$key]['message']	= $value['message'];
				if ($value['ratingType']==1) {
					$sadMsg['message']	= $value['message'];
					array_push($sadMsgdata, $sadMsg);
				} else if ($value['ratingType']==2) {
					$scepticMsg['message']	= $value['message'];
					array_push($scepticMsgdata, $scepticMsg);
				} else if ($value['ratingType']==3) {
					$happyMsg['message']	= $value['message'];
					array_push($happyMsgdata, $happyMsg);
				} else {
				}
			}
			$message = "Get Rating Messages Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("sadMessages" => $sadMsgdata, "scepticMessages" => $scepticMsgdata, "happyMessages" => $happyMsgdata)
			]);
		} else {
			$message = "Rating Messages Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("sadMessages" => "", "scepticMessages" => "", "happyMessages" => "")]);
		}
	}
	public function saveRatingsbyUser(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("rating_id" => "")]);
		}
		$courseId 	= $request->courseId;
		$lessionId  = $request->lessionId;
		$topicId 	= $request->topicId;
		$ratingType 	= $request->ratingType;
		$ratingMessage  = $request->ratingMessage;
		$message 		= !empty($request->message) ? $request->message : '';
		$msg = '';
		if (!empty($userId)  && !empty($courseId) && !empty($ratingMessage)) {
			$data = array(
				'userId' 	=> $userId,
				'courseId'  => $courseId,
				'lessionId' => $lessionId,
				'topicId' 	=> $topicId,
				'ratingType' => $ratingType,
				'ratingMessage' => $ratingMessage,
				'message'    => $message,
				'status'     => 0,
				'created_at' => date('Y-m-d H:i:s'),
			);
			$insertId = RatingUser::insertGetId($data);
			$message = 'Rating Submitted Successfully.';
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array('rating_id' => $insertId)]);
		} else {
			$message = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("rating_id" => "")]);
		}
	}
	public function userContinueStudyHistory(Request $request)
	{
		$search   = $request->search;
		$userId   = $request->userId;
		$today	  = date('Y-m-d');
		$subscription = UserSubscription::where("user_id", $userId)->where("end_date", ">=", $today)->orderBy('id', 'DESC')->first();
		$userSubscription = !empty($subscription) ? 1 : 0;
		$continueStudy = ContinueStudy::where("user_id", $userId)->where('is_complete', 0);
		$continueStudy = $continueStudy->orderBy('updated_at', 'DESC')->get();
		$studydata = array();
		foreach ($continueStudy as $key => $study) {
			if ($study['lession_id']==0) {
				$course = Courses::where("id", $study['course_id'])->where("status", 1)->where('deleted', 0)->first();
				$studydata[$key]['id']				= $study['id'];
				$studydata[$key]['name']			= $course->name;
				$studydata[$key]['image']			= asset('course') . "/" . $course->image;
				if ($course->uploads3==1){
					$video = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$course->video;
				} else {
					$video = asset('course') . "/" . $course->video;
				}
				$studydata[$key]['original_video']	= isset($course->video) ? $video : 'NA';
				if (!empty($course->video_1) && $course->video_1!='NA') {
					if ($course->uploads3==1){
						$video_1 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$course->video_1;
					} else {
						if(file_exists( public_path().'/course/'.$course->video_1 )) {
							$video_1 = asset('course') . "/" . $course->video_1;
						} else {
							$video_1 = $studydata[$key]['original_video'];
						}
					}
				} else {
					$video_1 = $studydata[$key]['original_video'];
				}
				if (!empty($course->video_2) && $course->video_2!='NA') {
					if ($course->uploads3==1){
						$video_2 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$course->video_2;
					} else {
						if(file_exists( public_path().'/course/'.$course->video_2 )) {
							$video_2 = asset('course') . "/" . $course->video_2;
						} else {
							$video_2 = $studydata[$key]['original_video'];
						}
					}
				} else {
					$video_2 = $studydata[$key]['original_video'];
				}
				if (!empty($course->video_3) && $course->video_3!='NA') {
					if ($course->uploads3==1){
						$video_3 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$course->video_3;
					} else {
						if(file_exists( public_path().'/course/'.$course->video_3 )) {
							$video_3 = asset('course') . "/" . $course->video_3;
						} else {
							$video_3 = $studydata[$key]['original_video'];
						}
					}
				} else {
					$video_3 = $studydata[$key]['original_video'];
				}
				$studydata[$key]['low_video']		= $video_1;
				$studydata[$key]['medium_video']	= $video_2;
				$studydata[$key]['high_video']		= $video_3;
				$total_lessions = Lession::where("courseId", $study['course_id'])->where("status", 1)->where('deleted', 0)->count();
				$studydata[$key]['total_lessions']  = $total_lessions;
			} else {
				if ($study['topic_id']==0) {
					$lession = Lession::where("id", $study['lession_id'])->where("status", 1)->where('deleted', 0)->first();
					$studydata[$key]['id']				= $study['id'];
					$studydata[$key]['name']			= $lession->name;
					$studydata[$key]['image']			= asset('lessions') . "/" . $lession->video_thumb;
					if ($lession->uploads3==1){
						$video = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$lession->fullvideo;
					} else {
						$video = asset('lessions') . "/" . $lession->fullvideo;
					}
					$studydata[$key]['original_video']	= isset($lession->fullvideo) ? $video : 'NA';
					if (!empty($lession->video_1) && $lession->video_1!='NA') {
						if ($lession->uploads3==1){
							$video_1 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$lession->video_1;
						} else {
							if(file_exists( public_path().'/lessions/'.$lession->video_1 )) {
								$video_1 = asset('lessions') . "/" . $lession->video_1;
							} else {
								$video_1 = $studydata[$key]['original_video'];
							}
						}
					} else {
						$video_1 = $studydata[$key]['original_video'];
					}
					if (!empty($lession->video_2) && $lession->video_2!='NA') {
						if ($lession->uploads3==1){
							$video_2 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$lession->video_2;
						} else {
							if(file_exists( public_path().'/lessions/'.$lession->video_2 )) {
								$video_2 = asset('lessions') . "/" . $lession->video_2;
							} else {
								$video_2 = $studydata[$key]['original_video'];
							}
						}
					} else {
						$video_2 = $studydata[$key]['original_video'];
					}
					if (!empty($lession->video_3) && $lession->video_3!='NA') {
						if ($lession->uploads3==1){
							$video_3 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$lession->video_3;
						} else {
							if(file_exists( public_path().'/lessions/'.$lession->video_3 )) {
								$video_3 = asset('lessions') . "/" . $lession->video_3;
							} else {
								$video_3 = $studydata[$key]['original_video'];
							}
						}
					} else {
						$video_3 = $studydata[$key]['original_video'];
					}
					$studydata[$key]['low_video']		= $video_1;
					$studydata[$key]['medium_video']	= $video_2;
					$studydata[$key]['high_video']		= $video_3;
					$total_lessions = Lession::where("courseId", $study['course_id'])->where("status", 1)->where('deleted', 0)->count();
					$studydata[$key]['total_lessions']	= $total_lessions;
				} else {
					$topic = Chapter::where("id", $study['topic_id'])->where("status", 1)->where('deleted', 0)->first();
					$studydata[$key]['id']				= $study['id'];
					$studydata[$key]['name']			= $topic->name;
					$studydata[$key]['image']			= asset('lessions') . "/" . $topic->video_thumb;
					if ($topic->uploads3==1){
						$video = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$topic->fullvideo;
					} else {
						$video = asset('lessions') . "/" . $topic->fullvideo;
					}
					$studydata[$key]['original_video']	= isset($topic->fullvideo) ? $video : 'NA';
					if (!empty($topic->video_1) && $topic->video_1!='NA') {
						if ($topic->uploads3==1){
							$video_1 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$topic->video_1;
						} else {
							if(file_exists( public_path().'/lessions/'.$topic->video_1 )) {
								$video_1 = asset('lessions') . "/" . $topic->video_1;
							} else {
								$video_1 = $studydata[$key]['original_video'];
							}
						}
					} else {
						$video_1 = $studydata[$key]['original_video'];
					}
					if (!empty($topic->video_2) && $topic->video_2!='NA') {
						if ($topic->uploads3==1){
							$video_2 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$topic->video_2;
						} else {
							if(file_exists( public_path().'/lessions/'.$topic->video_2 )) {
								$video_2 = asset('lessions') . "/" . $topic->video_2;
							} else {
								$video_2 = $studydata[$key]['original_video'];
							}
						}
					} else {
						$video_2 = $studydata[$key]['original_video'];
					}
					if (!empty($topic->video_3) && $topic->video_3!='NA') {
						if ($topic->uploads3==1){
							$video_3 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$topic->video_3;
						} else {
							if(file_exists( public_path().'/lessions/'.$topic->video_3 )) {
								$video_3 = asset('lessions') . "/" . $topic->video_3;
							} else {
								$video_3 = $studydata[$key]['original_video'];
							}
						}
					} else {
						$video_3 = $studydata[$key]['original_video'];
					}
					$studydata[$key]['low_video']		= $video_1;
					$studydata[$key]['medium_video']	= $video_2;
					$studydata[$key]['high_video']		= $video_3;
					$total_chapters = Chapter::where("courseId", $study['course_id'])->where("lessionId", $study['lession_id'])->where("status", 1)->where('deleted', 0)->count();
					$studydata[$key]['total_lessions']	= $total_chapters;
				}
			}
			/*$total_time = strtotime($study['video_total_time']) - strtotime('00:00:00');
			$viewed_time = strtotime($study['video_viewed_time']) - strtotime('00:00:00');*/
			$total_time = !empty($study['video_total_time']) ? $study['video_total_time'] : 0;
			$viewed_time = !empty($study['video_viewed_time']) ? $study['video_viewed_time'] : 0;
			$view_percent = $percentLeft = 0;
			if ($total_time > 0) {
				$view_percent = ($viewed_time / $total_time) * 100;
				if ($view_percent > 99) {
					$view_percent = 100;
				}
				if ($view_percent < 0) {
					$view_percent = 0;
				}
				$percentLeft = (($total_time - $viewed_time) / $total_time) * 100;
			}
			//echo 'TT '.$total_time.' VT '.$viewed_time.' VP '.$view_percent.' LP '.$percentLeft; die;
			$studydata[$key]['video_total_time']  = $study['video_total_time'];
			$studydata[$key]['video_viewed_time'] = $study['video_viewed_time'];
			$studydata[$key]['view_percent']      = round($view_percent);
		}
		$message = "User Continue Study History Data.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "continue_studying" => $studydata)]);
	}
	public function postContinueStudy(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("continueStudyId" => "")]);
		}
		$courseId 			= $request->courseId;
		$lessionId 			= $request->lessionId;
		$topicId 			= $request->topicId;
		$videoTotalTime 	= $request->videoTotalTime;
		$videoViewedTime 	= $request->videoViewedTime;
		if ($videoTotalTime == $videoViewedTime) {
			$is_complete = 1;
		} else {
			$is_complete = 0;
		}
		$checkContStudy = ContinueStudy::where("user_id", $userId)->where("course_id", $courseId)->where("lession_id", $lessionId)->where("topic_id", $topicId)->where('is_complete', 0)->first();
		if (!empty($checkContStudy)) {
			$cshId = $checkContStudy->id;
			$data = array(
				/*'video_total_time'  => date('H:i:s',strtotime($videoTotalTime)),
				'video_viewed_time' => date('H:i:s',strtotime($videoViewedTime)),*/
				'video_total_time'  => $videoTotalTime,
				'video_viewed_time' => $videoViewedTime,
				'is_complete'  		=> $is_complete,
				'updated_at'        => date('Y-m-d H:i:s'),
			);
			$update = ContinueStudy::where("id", $cshId)->update($data);
			if ($is_complete==1) {
				if ($lessionId > 0) {
					$lession = Lession::where("id", $lessionId)->first();
					$msg = 'You have successfully completed your '.$lession->name.' lession.';
				} else {
					$course = Courses::where("id", $courseId)->first();
					$msg = 'You have successfully completed your '.$course->name.' course.';
				}
				$this->addNotification($userId,$msg);
			}
			$delete = ContinueStudy::where("is_complete", 1)->delete();
			$message = "Continue Study Updated Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("continueStudyId" => $cshId)]);
		} else {
			$data = array(
				'user_id'  			=> $userId,
				'course_id'  		=> $courseId,
				'lession_id'  		=> $lessionId,
				'topic_id'  		=> $topicId,
				/*'video_total_time'  => date('H:i:s',strtotime($videoTotalTime)),
				'video_viewed_time' => date('H:i:s',strtotime($videoViewedTime)),*/
				'video_total_time'  => $videoTotalTime,
				'video_viewed_time' => $videoViewedTime,
				'is_complete'  		=> $is_complete,
				'created_at'        => date('Y-m-d H:i:s'),
			);
			$cshId = ContinueStudy::insertGetId($data);
			$message = "Continue Study Added Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("continueStudyId" => $cshId)]);
		}
	}

	public function quizGuideline(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("quiz" => array("id" => ""))]);
		}
		$courseId = $request->courseId;
		$lessionId = $request->lessionId;
		if ($lessionId > 0) {
			$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 0)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
		} else {
			$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 1)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
		}
		if (!empty($getQuiz)) {
			$course   = Courses::where("id", $courseId)->where("status", 1)->where('deleted', 0)->first();
			$lession   = Lession::where("id", $lessionId)->where("status", 1)->where('deleted', 0)->first();
			$quiz['id'] = $getQuiz->id;
			$quiz['name'] = $getQuiz->name;
			$quiz['courseId'] = $courseId;
			$quiz['lessionId'] = $lessionId;
			$quiz['courseName'] = $course->name;
			$quiz['lessionName'] = !empty($lession->name) ? $lession->name : '';

			//$quiz['total_time'] = strtotime($getQuiz->duration);
			$hour         = date('H',strtotime($getQuiz->duration));
		    $minute       = date('i',strtotime($getQuiz->duration));
		    $seconds      = date('s',strtotime($getQuiz->duration));

		    $milliseconds = $this->convertTimeinMiliseconds($hour,$minute,$seconds);
			$quiz['total_time'] = $milliseconds;

			$examQuestions = Quizquestions::where("quizId", $getQuiz->id)->get();
			$total_marks = 0;
			foreach ($examQuestions as $ques) {
				$total_marks += $ques['marking'];
			}
			$quiz['total_questions'] = count($examQuestions);
			$quiz['total_marks'] = $total_marks;
			$quiz['guideline'] = $getQuiz->guideline;
			$checkStudentExam = StudentExam::where("user_id", $userId)->where("course_id", $courseId)->where("lession_id", $lessionId)->where('is_complete', 0)->orderBy("id","DESC")->first();
			$remaining_time = "";
			if (!empty($checkStudentExam)) {
				$examId = $checkStudentExam->id;
				$examTotalTime = $checkStudentExam->total_time;
				/*if (!empty($examTotalTime) && $examTotalTime!='00:00:00') {
					$remain_time = strtotime($getQuiz->duration) - strtotime($examTotalTime);
					$remain_time = date('H:i:s', $remain_time);
					$hour        = date('H',strtotime($remain_time));
				    $minute      = date('i',strtotime($remain_time));
				    $seconds     = date('s',strtotime($remain_time));

				    $remaining_time = $this->convertTimeinMiliseconds($hour,$minute,$seconds);
				} else {
					$remaining_time = "";
				}*/
				$remaining_time = $checkStudentExam->remaining_time;
				$quiz['exam_id'] = $examId;
			} else {
				$quiz['exam_id'] = 0;
			}
			$quiz['remaining_time'] = !empty($remaining_time) ? $remaining_time : '';
			$message = "Get quiz details successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("quiz" => $quiz)]);
		} else {
			$message = "Quiz Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("quiz" => array("id" => ""))]);
		}
	}
	public function letStartQuiz(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("quiz" => array("quiz_id" => ""), "quizdata" => [])]);
		}
		$courseId = $request->courseId;
		$lessionId = $request->lessionId;
		if ($lessionId > 0) {
			$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 0)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
		} else {
			$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 1)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
		}
		if (!empty($getQuiz)) {
			$quiz['quiz_id'] = $getQuiz->id;
			$quiz['name'] = $getQuiz->name;
			$checkStudentExam = StudentExam::where("user_id", $userId)->where("course_id", $courseId)->where("lession_id", $lessionId)->where('is_complete', 0)->orderBy("id","DESC")->first();
			if (!empty($checkStudentExam)) {
				$examId = $checkStudentExam->id;
				$data = array(
					'start_time' => date('H:i:s'),
					'end_time'   => date('H:i:s'),
					'updated_at' => date('Y-m-d H:i:s'),
				);
				$update = StudentExam::where("id", $examId)->update($data);
				$quiz['exam_id'] = $examId;
				$quizdata = array();
				$examQuestions = Quizquestions::where("quizId", $getQuiz->id)->get();
				$total_questions = count($examQuestions);
				foreach ($examQuestions as $key => $val) {
					$quizdata[$key]['id'] = $val->id;
					$quizdata[$key]['questions'] = $val->questions;
					$quizdata[$key]['image'] = !empty($val->image) ? asset('upload/quizquestions') . "/" . $val->image : '';
					$quizdata[$key]['marking'] = $val->marking;
					$queoptions = Quizoption::where("questionId", $val->id)->orderBy("id", "ASC")->get();
					$optiondata = array();
					foreach ($queoptions as $key1 => $value) {
						if ($key1 <= 3) {
							$optiondata[$key1]['id'] = $value->id;
							if ($value->val_type==0) {
								$optiondata[$key1]['option'] = !empty($value->quizoption) ? asset('upload/quizquestions') . "/" . $value->quizoption : '';
							} else {
								$optiondata[$key1]['option'] = $value->quizoption;
							}
							$optiondata[$key1]['val_type'] = $value->val_type;
						}
					}
					$quizdata[$key]['answers'] = $optiondata;
					$stAnswer = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $val->id)->first();
					$quizdata[$key]['given_answer'] = !empty($stAnswer->answer) ? $stAnswer->answer : 0;
				}
				$total_attemped = StudentExamAnswer::where("exam_id", $examId)->where("attemp", 1)->count();
				$quiz['total_attemped'] = $total_attemped;
				$quiz['total_unattemped'] = $total_questions - $total_attemped;
				$message = "Get all quiz questions list with previous exam.";
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("quiz" => $quiz, "quizdata" => $quizdata)]);
			} else {
				$data = array(
					'user_id'     => $userId,
					'course_id'   => $courseId,
					'lession_id'  => $lessionId,
					'start_time'  => date('H:i:s'),
					'is_complete' => 0,
					'created_at'  => date('Y-m-d H:i:s'),
				);
				$examId = StudentExam::insertGetId($data);
				$quiz['exam_id'] = $examId;
				$quizdata = array();
				$examQuestions = Quizquestions::where("quizId", $getQuiz->id)->get();
				$total_questions = count($examQuestions);
				foreach ($examQuestions as $key => $val) {
					$quizdata[$key]['id'] = $val->id;
					$quizdata[$key]['questions'] = $val->questions;
					$quizdata[$key]['image'] = !empty($val->image) ? asset('upload/quizquestions') . "/" . $val->image : '';
					$quizdata[$key]['marking'] = $val->marking;
					$queoptions = Quizoption::where("questionId", $val->id)->get();
					$optiondata = array();
					foreach ($queoptions as $key1 => $value) {
						if ($key1 <= 3) {
							$optiondata[$key1]['id'] = $value->id;
							if ($value->val_type==0) {
								$optiondata[$key1]['option'] = !empty($value->quizoption) ? asset('upload/quizquestions') . "/" . $value->quizoption : '';
							} else {
								$optiondata[$key1]['option'] = $value->quizoption;
							}
							$optiondata[$key1]['val_type'] = $value->val_type;
						}
					}
					$quizdata[$key]['answers'] = $optiondata;
					$quizdata[$key]['given_answer'] = 0;
				}
				$total_attemped = StudentExamAnswer::where("exam_id", $examId)->where("attemp", 1)->count();
				$quiz['total_attemped'] = $total_attemped;
				$quiz['total_unattemped'] = $total_questions - $total_attemped;
				$message = "Get all quiz questions list.";
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("quiz" => $quiz, "quizdata" => $quizdata)]);
			}
		} else {
			$message = "Quiz Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("quiz" => "", "quizdata" => "")]);
		}
	}
	public function pauseQuiz(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("exam_id" => "")]);
		}
		$examId = $request->examId;
		$remainingTime = $request->remainingTime;
		$studentExam = StudentExam::where("id", $examId)->where("user_id", $userId)->where('is_complete', 0)->orderBy("id","DESC")->first();
		if (!empty($studentExam)) {
			$start_time = $studentExam->start_time;
			$end_time = date('H:i:s');
			$start = Carbon::parse($start_time);
    		$end = Carbon::parse($end_time);
			/*$hours = $end->diffInHours($start);
			$minutes = $end->diffInMinutes($start);*/
			$seconds = $end->diffInSeconds($start);
    		$total_time = sprintf('%02d:%02d:%02d', ($seconds/3600),($seconds/60%60), $seconds%60);
			$before_total_time = $studentExam->total_time;
			if (!empty($before_total_time)) {
    			$newDateTime = Carbon::parse($before_total_time)->addSeconds($seconds);
    			$total_time = date('H:i:s', strtotime($newDateTime));
    		}
    		//echo $total_time; die;
			$data = array(
				'end_time'   => $end_time,
				'total_time' => $total_time,
				'remaining_time' => $remainingTime,
				'updated_at' => date('Y-m-d H:i:s'),
			);
			$update = StudentExam::where("id", $examId)->update($data);
			$message = "Exam paused successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("exam_id" => $examId)]);
		} else {
			$message = "Quiz Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("exam_id" => "")]);
		}
	}
	public function nextQuiz(Request $request)
	{
		$examId = $request->examId;
		$quesId = $request->quesId;
		$answer = $request->answer;
		if (!empty($answer)) {
			$attemp = 1;
		} else {
			$attemp = 0;
		}
		$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $quesId)->first();
		if (!empty($studentAns)) {
			$studentAnsId = $studentAns->id;
			$data = array(
				'answer'     => $answer,
				'attemp'     => $attemp,
				'updated_at' => date('Y-m-d H:i:s'),
			);
			$update = StudentExamAnswer::where("id", $studentAnsId)->update($data);
			$message = "Answer updated successfully.";
		} else {
			$data = array(
				'exam_id'    => $examId,
				'ques_id'    => $quesId,
				'answer'     => $answer,
				'attemp'     => $attemp,
				'created_at' => date('Y-m-d H:i:s'),
			);
			$studentAnsId = StudentExamAnswer::insertGetId($data);
			$message = "Answer inserted successfully.";
		}
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("ans_id" => $studentAnsId)]);
	}
	public function endQuiz(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("exam_id" => "")]);
		}
		$examId = $request->examId;
		$studentExam = StudentExam::where("id", $examId)->where("user_id", $userId)->where('is_complete', 0)->orderBy("id","DESC")->first();
		if (!empty($studentExam)) {
			$start_time = $studentExam->start_time;
			$end_time = date('H:i:s');
			$start = Carbon::parse($start_time);
    		$end = Carbon::parse($end_time);
			/*$hours = $end->diffInHours($start);
			$minutes = $end->diffInMinutes($start);*/
			$seconds = $end->diffInSeconds($start);
    		$total_time = sprintf('%02d:%02d:%02d', ($seconds/3600),($seconds/60%60), $seconds%60);
			$before_total_time = $studentExam->total_time;
			if (!empty($before_total_time)) {
    			$newDateTime = Carbon::parse($before_total_time)->addSeconds($seconds);
    			$total_time = date('H:i:s', strtotime($newDateTime));
    		}
    		//echo $total_time; die;
			$data = array(
				'end_time'   => $end_time,
				'total_time' => $total_time,
				'is_complete' => 1,
				'updated_at' => date('Y-m-d H:i:s'),
			);
			$update = StudentExam::where("id", $examId)->update($data);
			
			$getStudentExam = StudentExam::where("id", $examId)->where("user_id", $userId)->where("is_complete", 1)->first();
			if (!empty($getStudentExam)) {
				$courseId = $getStudentExam->course_id;
				$lessionId = $getStudentExam->lession_id;
				$total_time = $getStudentExam->total_time;
				$attemped_date = date('d M, Y', strtotime($getStudentExam->created_at));
				if ($lessionId > 0) {
					$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 0)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
				} else {
					$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 1)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
				}
				if (!empty($getQuiz)) {
					$passing_percent = $getQuiz->passing_percent;
					$quizdata = array();
					$examQuestions = Quizquestions::where("quizId", $getQuiz->id)->get();
					$total_questions = count($examQuestions);
					$total_marking = $score = $right = $wrong = $total_solved = $not_solved = 0;
					foreach ($examQuestions as $key => $val) {
						$quizdata[$key]['id'] = $val->id;
						$quizdata[$key]['questions'] = $val->questions;
						$quizdata[$key]['image'] = !empty($val->image) ? asset('upload/quizquestions') . "/" . $val->image : '';
						$quizdata[$key]['marking'] = $val->marking;
						$total_marking += $val->marking;

						$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $val->id)->first();
						if (!empty($studentAns)) {
							$quizAnswers = Quizoption::where("questionId", $val->id)->orderBy("id", "ASC")->get();
							$answer = '';
							foreach ($quizAnswers as $key1 => $value) {
								if ($key1 == ($val->currect_option - 1)){
									$answer = $value->id;
								}
							}
							if ($answer == $studentAns->answer) {
								$right++;
								$score += $val->marking;
							} else {
								$wrong++;
							}
						}
					}
					$total_solved = $right + $wrong;
					$not_solved = $total_questions - $total_solved;
					//$percentage = number_format((($right * 100) / $total_questions), 2);
					$percentage = number_format((($score * 100) / $total_marking), 2);
					if ($percentage >= $passing_percent) {
						$download_status = 1;
					} else {
						$download_status = 0;
					}
					
					$user = User::where("id", $userId)->first();
					if ($download_status == 1) {
						$msg = 'Hi, '.$user->name.' Wonderful score on the recent quiz. Keep learning';
					} else {
						//$msg = 'Hi, '.$user->name.' Less score on the recent quiz. Keep learning';
						$msg = 'Sorry, Minimum '.$passing_percent.'% was needed to get the certificate. Please re-attempt the test again. All the best.';
					}
					$this->addNotification($userId,$msg);
				}
			}
			$message = "Exam ended successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("exam_id" => $examId)]);
		} else {
			$message = "Quiz Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("exam_id" => "")]);
		}
	}
	public function cancelQuiz(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("quiz" => array("id" => ""))]);
		}
		$courseId = $request->courseId;
		$lessionId = $request->lessionId;
		if ($lessionId > 0) {
			$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 0)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
		} else {
			$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 1)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
		}
		if (!empty($getQuiz)) {
			$quiz['id'] = $getQuiz->id;
			$quiz['name'] = $getQuiz->name;
			$quiz['courseId'] = $courseId;
			$quiz['lessionId'] = $lessionId;
			//$cancelExam = StudentExam::where("user_id", $userId)->where("course_id", $courseId)->where("lession_id", $lessionId)->where('is_complete', 0)->delete();
			$studentExam = StudentExam::where("user_id", $userId)->where("course_id", $courseId)->where("lession_id", $lessionId)->where('is_complete', 0)->orderBy("id","DESC")->first();
			$examId = $studentExam->id;
			//$answerDel = StudentExamAnswer::where("exam_id", $examId)->delete();
			$countStudentExam = StudentExam::where("user_id", $userId)->where("course_id", $courseId)->where("lession_id", $lessionId)->where('is_complete', 1)->orderBy("id","DESC")->count();
			$completeStatus = -1;
			if ($countStudentExam > 1) {
				$completeStatus = 2; //Retake exam cancelled status
			}
			$data = array(
				'start_time' => '00:00:00',
				'end_time'   => '00:00:00',
				'total_time' => '00:00:00',
				'is_complete' => $completeStatus,
				'updated_at' => date('Y-m-d H:i:s'),
			);
			$update = StudentExam::where("id", $examId)->update($data);
			$message = "Quiz cancelled successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("quiz" => $quiz)]);
		} else {
			$message = "Quiz Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("quiz" => array("id" => ""))]);
		}
	}

	public function attempedQuiz(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("quiz" => array("total_questions" => ""), "allquiz" => [], "attempedquiz" => [], "unattempedquiz" => [])]);
		}
		$examId = $request->examId;
		$quizId = $request->quizId;
		$quizdata = array();
		$attempeddata = array();
		$unattempeddata = array();
		$examQuestions = Quizquestions::where("quizId", $quizId)->get();
		$total_questions = count($examQuestions);
		if (!empty($examQuestions)) {
			foreach ($examQuestions as $key => $val) {
				$quizdata[$key]['id'] = $val->id;
				//$quizdata[$key]['questions'] = $val->questions;
				$quizdata[$key]['ques_num'] = $key + 1;
				$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $val->id)->first();
				$quizdata[$key]['attemp'] = isset($studentAns->attemp) ? $studentAns->attemp : 2;
			}
			foreach ($examQuestions as $key1 => $val) {
				$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $val->id)->first();
				if (!empty($studentAns) && $studentAns->attemp == 1) {
					$attemp['id'] = $val->id;
					//$attemp['questions'] = $val->questions;
					$attemp['ques_num'] = $key1 + 1;
					$attemp['attemp'] = $studentAns->attemp;
					array_push($attempeddata, $attemp);
				/*} else {
					$unattempeddata[$key1]['id'] = $val->id;
					//$unattempeddata[$key1]['questions'] = $val->questions;
					$unattempeddata[$key1]['ques_num'] = $key1 + 1;
					$unattempeddata[$key1]['attemp'] = 2;*/
				}
			}
			foreach ($examQuestions as $key2 => $val) {
				$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $val->id)->first();
				if (empty($studentAns) || $studentAns->attemp == 0) {
					/*$unattempeddata[$key2]['id'] = $val->id;
					//$unattempeddata[$key2]['questions'] = $val->questions;
					$unattempeddata[$key2]['ques_num'] = $key2 + 1;
					$unattempeddata[$key2]['attemp'] = 2;*/
					$unattemp['id'] = $val->id;
					$unattemp['ques_num'] = $key2 + 1;
					$unattemp['attemp'] = isset($studentAns->attemp) ? 0 : 2;
					array_push($unattempeddata, $unattemp);
				}
			}
			$quiz['total_questions'] = $total_questions;
			$total_attemped = StudentExamAnswer::where("exam_id", $examId)->where("attemp", 1)->count();
			$quiz['total_attemped'] = $total_attemped;
			$quiz['total_unattemped'] = $total_questions - $total_attemped;
			$message = "Get all quiz questions list with attemped/unattemped data.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("quiz" => $quiz, "allquiz" => $quizdata, "attempedquiz" => $attempeddata, "unattempedquiz" => $unattempeddata)]);
		} else {
			$message = "Quiz Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("quiz" => "", "allquiz" => "", "attempedquiz" => "", "unattempedquiz" => "")]);
		}
	}
	public function getAllAttempedQuiz(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("attemped" => [])]);
		}
		$courseId = $request->courseId;
		$lessionId = $request->lessionId;
		$getStudentExams = StudentExam::where("user_id", $userId)->where("course_id", $courseId)->where("lession_id", $lessionId)->where("is_complete", 1)->get();
		$attempdata = array();
		if (!empty($getStudentExams)) {
			foreach ($getStudentExams as $key => $val) {
				$attemp = $key + 1;
				$attempdata[$key]['attemp'] = 'Attempt ('.$attemp.')';
				$attempdata[$key]['examId'] = $val->id;
			}
			$message = "Get all quiz attemped list by user.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("attemped" => $attempdata)]);
		} else {
			$message = "Quiz Attemped Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("attemped" => [])]);
		}
	}
	public function getQuizHistory(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("quiz" => array("quiz_id" => ""), "quizhistory" => [])]);
		}
		$examId = $request->examId;
		/*$quizId = $request->quizId;
		$getQuiz = Quiz::where("id", $quizId)->where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->first();
		$courseId = $request->courseId;
		$lessionId = $request->lessionId;*/
		$getStudentExam = StudentExam::where("id", $examId)->where("user_id", $userId)->where("is_complete", 1)->first();
		if (!empty($getStudentExam)) {
			$examId = $getStudentExam->id;
			$courseId = $getStudentExam->course_id;
			$lessionId = $getStudentExam->lession_id;
			if ($lessionId > 0) {
				$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 0)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
			} else {
				$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 1)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
			}
			if (!empty($getQuiz)) {
				$quiz['quiz_id'] = $getQuiz->id;
				$quiz['name'] = $getQuiz->name;
				$quiz['courseId'] = $courseId;
				$quiz['lessionId'] = $lessionId;
				$quizdata = array();
				$examQuestions = Quizquestions::where("quizId", $getQuiz->id)->get();
				$total_questions = count($examQuestions);
				$quiz['total_questions'] = $total_questions;
				foreach ($examQuestions as $key => $val) {
					$quizdata[$key]['id'] = $val->id;
					$quizdata[$key]['questions'] = $val->questions;
					$quizdata[$key]['image'] = !empty($val->image) ? asset('upload/quizquestions') . "/" . $val->image : '';
					$quizdata[$key]['marking'] = $val->marking;
					$quizdata[$key]['solution'] = $val->solution;
					$queoptions = Quizoption::where("questionId", $val->id)->get();
					$optiondata = array();
					foreach ($queoptions as $key1 => $value) {
						if ($key1 <= 3) {
							$optiondata[$key1]['id'] = $value->id;
							if ($value->val_type==0) {
								$optiondata[$key1]['option'] = !empty($value->quizoption) ? asset('upload/quizquestions') . "/" . $value->quizoption : '';
							} else {
								$optiondata[$key1]['option'] = $value->quizoption;
							}
							$optiondata[$key1]['val_type'] = $value->val_type;
						}
					}
					$quizdata[$key]['answers'] = $optiondata;
					$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $val->id)->first();
					if (!empty($studentAns)) {
						$quizStAnswer = Quizoption::where("id", $studentAns->answer)->first();
					}
					//$quizdata[$key]['given_answer'] = isset($quizStAnswer->quizoption) ? $quizStAnswer->quizoption : 0;
					$quizdata[$key]['given_answer'] = isset($studentAns->answer) ? $studentAns->answer : 0;
					$quizAnswers = Quizoption::where("questionId", $val->id)->orderBy("id", "ASC")->get();
					$answer = '';
					foreach ($quizAnswers as $key1 => $value) {
						if ($key1 == ($val->currect_option - 1)){
							$answer = $value->id;
						}
					}
					$quizAnswer = Quizoption::where("id", $answer)->first();
					//$quizdata[$key]['correct_answer'] = isset($quizAnswer->quizoption) ? $quizAnswer->quizoption : 'NA';
					$quizdata[$key]['correct_answer'] = isset($answer) ? $answer : 'NA';
				}
				$message = "Get all quiz questions list history.";
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("quiz" => $quiz, "quizhistory" => $quizdata)]);
			} else {
				$message = "Quiz Not Found!";
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("quiz" => "", "quizhistory" => "")]);
			}
		} else {
			$message = "Quiz Attemped Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("quiz" => "", "quizhistory" => "")]);
		}
	}
	public function getQuizResult(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("quiz" => array("quiz_id" => ""))]);
		}
		$examId = $request->examId;
		/*$quizId = $request->quizId;
		$courseId = $request->courseId;
		$lessionId = $request->lessionId;*/
		$getStudentExam = StudentExam::where("id", $examId)->where("user_id", $userId)->where("is_complete", 1)->first();
		if (!empty($getStudentExam)) {
			//$examId = $getStudentExam->id;
			$courseId = $getStudentExam->course_id;
			$lessionId = $getStudentExam->lession_id;
			$total_time = $getStudentExam->total_time;
			$certificate = $getStudentExam->certificate;
			$attemped_date = date('d M, Y', strtotime($getStudentExam->created_at));
			//$getQuiz = Quiz::where("id", $quizId)->where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->first();
			if ($lessionId > 0) {
				$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 0)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
			} else {
				$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 1)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
			}
			if (!empty($getQuiz)) {
				$quiz['quiz_id'] = $getQuiz->id;
				$quiz['name'] = $getQuiz->name;
				$islession = $getQuiz->islession;
				$passing_percent = $getQuiz->passing_percent;
				$coursename = !empty($getQuiz->courses->name) ? $getQuiz->courses->name : '';
				$quiz['courseId'] = $courseId;
				$quiz['lessionId'] = $lessionId;
				$quizdata = array();
				$examQuestions = Quizquestions::where("quizId", $getQuiz->id)->get();
				$total_questions = count($examQuestions);
				$quiz['total_questions'] = $total_questions;
				$quiz['time_efficiency'] = $total_time;
				$quiz['attemped_date'] = $attemped_date;
				$total_marking = $score = $right = $wrong = $total_solved = $not_solved = 0;
				foreach ($examQuestions as $key => $val) {
					$quizdata[$key]['id'] = $val->id;
					$quizdata[$key]['questions'] = $val->questions;
					$quizdata[$key]['image'] = !empty($val->image) ? asset('upload/quizquestions') . "/" . $val->image : '';
					$quizdata[$key]['marking'] = $val->marking;
					$total_marking += $val->marking;
					/*$queoptions = Quizoption::where("questionId", $val->id)->get();
					$optiondata = array();
					foreach ($queoptions as $key1 => $value) {
						$optiondata[$key1]['id'] = $value->id;
						$optiondata[$key1]['option'] = $value->quizoption;
					}
					$quizdata[$key]['answers'] = $optiondata;*/
					$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $val->id)->first();
					if (!empty($studentAns)) {
						$quizAnswers = Quizoption::where("questionId", $val->id)->orderBy("id", "ASC")->get();
						$answer = '';
						foreach ($quizAnswers as $key1 => $value) {
							if ($key1 == ($val->currect_option - 1)){
								$answer = $value->id;
							}
						}
						//if ($val->currect_option == $studentAns->answer) {
						if ($answer == $studentAns->answer) {
							$right++;
							$score += $val->marking;
						} else {
							$wrong++;
						}
					}
				}
				$total_solved = $right + $wrong;
				$not_solved = $total_questions - $total_solved;
				$quiz['total_attemped'] = $total_solved;
				$quiz['right_answer'] = $right;
				$quiz['wrong_answer'] = $wrong + $not_solved;
				$quiz['total_score'] = $score.' out of '.$total_marking;
				//$percentage = number_format((($right * 100) / $total_questions), 2);
				$percentage = number_format((($score * 100) / $total_marking), 2);
				$quiz['percentage'] = $percentage;
				if ($percentage >= $passing_percent) {
					$download_status = 1;
					$quiz['remark_status'] = 'Passed';
				} else {
					$download_status = 0;
					$quiz['remark_status'] = 'Failed';
				}
				if ($islession==1){
					$download_status = $download_status;
				} else {
					$download_status = 0;
				}
				$quiz['download_status'] = $download_status;
				if ($download_status==1) {
					if ($certificate !='') {
						//$quiz['certificate_url'] = asset('lessions/978material.pdf');
						$quiz['certificate_url'] = asset('upload/generatedPDF') . "/" . $certificate;
					} else {
						$user = User::where("id", $userId)->first();
						$username = $user->name;
						$newCertificate = $this->generatePDF($examId, $username, $coursename, $attemped_date);
						$quiz['certificate_url'] = asset('upload/generatedPDF') . "/" . $newCertificate;
					}
				} else {
					$quiz['certificate_url'] = '';
				}

				$message = "Get all quiz questions list score.";
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("quiz" => $quiz)]);
			} else {
				$message = "Quiz Not Found!";
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("quiz" => array("quiz_id" => ""))]);
			}
		} else {
			$message = "Quiz Attemped Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("quiz" => array("quiz_id" => ""))]);
		}
	}
	public function getQuizResultDetails(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("quiz" => array("total_questions" => ""), "allquiz" => [], "correctquiz" => [], "incorrectquiz" => [], "skipedquiz" => [])]);
		}
		$examId = $request->examId;
		/*$quizId = $request->quizId;
		$courseId = $request->courseId;
		$lessionId = $request->lessionId;*/
		$getStudentExam = StudentExam::where("id", $examId)->where("user_id", $userId)->where("is_complete", 1)->first();
		if (!empty($getStudentExam)) {
			//$examId = $getStudentExam->id;
			$courseId = $getStudentExam->course_id;
			$lessionId = $getStudentExam->lession_id;
			$total_time = $getStudentExam->total_time;
			$attemped_date = date('d M, Y', strtotime($getStudentExam->created_at));
			//$getQuiz = Quiz::where("id", $quizId)->where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->first();
			if ($lessionId > 0) {
				$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 0)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
			} else {
				$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 1)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
			}
			if (!empty($getQuiz)) {
				$quizdata = array();
				$correctdata = array();
				$incorrectdata = array();
				$unattempeddata = array();
				$examQuestions = Quizquestions::where("quizId", $getQuiz->id)->get();
				$total_questions = count($examQuestions);
				$quiz['total_questions'] = $total_questions;
				/*$quiz['time_efficiency'] = $total_time;
				$quiz['attemped_date'] = $attemped_date;*/
				$score = $right = $wrong = $total_solved = $not_solved = 0;
				foreach ($examQuestions as $key => $val) {
					$quizdata[$key]['id'] = $val->id;
					// $quizdata[$key]['questions'] = $val->questions;
					// $quizdata[$key]['image'] = !empty($val->image) ? asset('upload/quizquestions') . "/" . $val->image : '';
					// $quizdata[$key]['marking'] = $val->marking;
					$quizdata[$key]['ques_num'] = $key + 1;
					$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $val->id)->first();
					$quizdata[$key]['attemp'] = isset($studentAns->attemp) ? $studentAns->attemp : 2;
					/*$queoptions = Quizoption::where("questionId", $val->id)->get();
					$optiondata = array();
					foreach ($queoptions as $key1 => $value) {
						$optiondata[$key1]['id'] = $value->id;
						$optiondata[$key1]['option'] = $value->quizoption;
					}
					$quizdata[$key]['answers'] = $optiondata;*/
					$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $val->id)->first();
					if (!empty($studentAns)) {
						$quizAnswers = Quizoption::where("questionId", $val->id)->orderBy("id", "ASC")->get();
						$answer = '';
						foreach ($quizAnswers as $arr_key => $value) {
							if ($arr_key == ($val->currect_option - 1)){
								$answer = $value->id;
							}
						}
						//if ($val->currect_option == $studentAns->answer) {
						if ($answer == $studentAns->answer) {
							$right++;
							$score += $val->marking;
						} else {
							if ($studentAns->attemp == 1) {
								$wrong++;
							}
						}
					}
				}
				$total_solved = $right + $wrong;
				$not_solved = $total_questions - $total_solved;
				$quiz['total_attemped'] = $total_solved;
				$quiz['right_answer'] = $right;
				$quiz['wrong_answer'] = $wrong;
				$quiz['skiped_answer'] = $not_solved;
				$quiz['total_score'] = $score;
				
				foreach ($examQuestions as $key1 => $val) {
					$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $val->id)->first();
					if (!empty($studentAns)) {
						$quizAnswers = Quizoption::where("questionId", $val->id)->orderBy("id", "ASC")->get();
						$answer = '';
						foreach ($quizAnswers as $arr_key => $value) {
							if ($arr_key == ($val->currect_option - 1)){
								$answer = $value->id;
							}
						}
						if ($answer == $studentAns->answer) {
							$correct['id'] = $val->id;
							//$correct['questions'] = $val->questions;
							$correct['ques_num'] = $key1 + 1;
							$correct['attemp'] = $studentAns->attemp;
							array_push($correctdata, $correct);
						} else {
							if ($studentAns->attemp == 1) {
								$incorrect['id'] = $val->id;
								//$incorrect['questions'] = $val->questions;
								$incorrect['ques_num'] = $key1 + 1;
								$incorrect['attemp'] = $studentAns->attemp;
								array_push($incorrectdata, $incorrect);
							}
						}
					}
				}
				foreach ($examQuestions as $key2 => $val) {
					$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $val->id)->first();
					if (empty($studentAns) || $studentAns->attemp == 0) {
						$unattemp['id'] = $val->id;
						$unattemp['ques_num'] = $key2 + 1;
						$unattemp['attemp'] = 2;
						array_push($unattempeddata, $unattemp);
					}
				}

				$message = "Get all quiz questions list result details.";
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("quiz" => $quiz, "allquiz" => $quizdata, "correctquiz" => $correctdata, "incorrectquiz" => $incorrectdata, "skipedquiz" => $unattempeddata)]);
			} else {
				$message = "Quiz Not Found!";
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("quiz" => "", "allquiz" => "", "correctquiz" => "", "incorrectquiz" => "", "skipedquiz" => "")]);
			}
		} else {
			$message = "Quiz Attemped Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("quiz" => "", "allquiz" => "", "correctquiz" => "", "incorrectquiz" => "", "skipedquiz" => "")]);
		}
	}

	public function liveClasses(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "liveClassNow" => [], "liveClasses" => [])]);
		}
		$search = $request->search;
		$from = date('Y-m-d').' 00:00:00';
		$to = date('Y-m-d').' 23:59:59';
		//$dt = Carbon::now();
		$today	  = date('Y-m-d');
		$subscription = UserSubscription::where("user_id", $userId)->where("end_date", ">=", $today)->orderBy('id', 'DESC')->first();
		$userSubscription = !empty($subscription) ? 1 : 0;
		$liveClass = LiveClass::where("status", 1)->where("deleted", 0)->whereBetween('class_time', [$from, date('Y-m-d H:i:s')])->get();
		$liveClassNow = array();
		if (!empty($liveClass)) {
			foreach ($liveClass as $key => $val) {
				$user = User::where("id", $val['added_by'])->first();
				$liveClassNow[$key]['id'] = $val['id'];
				$liveClassNow[$key]['added_by'] = $user->name;
				$liveClassNow[$key]['title'] = $val['title'];
				$liveClassNow[$key]['subject'] = $val['subject'];
				$liveClassNow[$key]['image'] = asset('upload/liveclasses') . "/" . $val['image'];
				$liveClassNow[$key]['meeting_id'] = $val['meeting_id'];
				$liveClassNow[$key]['pass_code'] = $val['pass_code'];
				$liveClassNow[$key]['master_class'] = $val['master_class'];
				$liveClassNow[$key]['class_time'] = date('m/d/Y, h:i A', strtotime($val['class_time']));
				$liveClassNow[$key]['isFree'] 		= $val['isFree'];
				$check_interest = LiveclassNotify::where("user_id", $userId)->where("class_id", $val['id'])->first();
				$my_interest = !empty($check_interest) ? 1 : 0;
				$liveClassNow[$key]['my_interest'] = $my_interest;
				$total_interest = LiveclassNotify::where("class_id", $val['id'])->count();
				$liveClassNow[$key]['total_interest'] = $total_interest;
			}
			$liveClasses = LiveClass::where("status", 1)->where("deleted", 0)->whereBetween('class_time', [$from, $to]);
			if (!empty($liveClasses)) {
				$liveClasses = $liveClasses->where("title", 'like', "%" . $search . "%");
			}
			$liveClasses = $liveClasses->orderBy('class_time', 'ASC')->limit(10)->get();
			$liveClsdata = array();
			foreach ($liveClasses as $key => $val) {
				$user = User::where("id", $val['added_by'])->first();
				$liveClsdata[$key]['id'] = $val['id'];
				$liveClsdata[$key]['added_by'] = $user->name;
				$liveClsdata[$key]['title'] = $val['title'];
				$liveClsdata[$key]['subject'] = $val['subject'];
				$liveClsdata[$key]['image'] = asset('upload/liveclasses') . "/" . $val['image'];
				$liveClsdata[$key]['meeting_id'] = $val['meeting_id'];
				$liveClsdata[$key]['pass_code'] = $val['pass_code'];
				$liveClsdata[$key]['master_class'] = $val['master_class'];
				$liveClsdata[$key]['class_time'] = date('m/d/Y, h:i A', strtotime($val['class_time']));
				$liveClsdata[$key]['isFree'] 		= $val['isFree'];
				$check_interest = LiveclassNotify::where("user_id", $userId)->where("class_id", $val['id'])->first();
				$my_interest = !empty($check_interest) ? 1 : 0;
				$liveClsdata[$key]['my_interest'] = $my_interest;
				$total_interest = LiveclassNotify::where("class_id", $val['id'])->count();
				$liveClsdata[$key]['total_interest'] = $total_interest;
			}
			$message = "All Live Classes Data.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "liveClassNow" => $liveClassNow, "liveClasses" => $liveClsdata)]);
		} else {
			$message = "Live Class Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "liveClassNow" => "", "liveClasses" => "")]);
		}
	}
	public function pastUpcomingClasses(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "pastClasses" => [], "upcomingClasses" => [])]);
		}
		$search = $request->search;
		$now = date('Y-m-d H:i:s');
		//$dt = Carbon::now();
		$today	  = date('Y-m-d');
		$subscription = UserSubscription::where("user_id", $userId)->where("end_date", ">=", $today)->orderBy('id', 'DESC')->first();
		$userSubscription = !empty($subscription) ? 1 : 0;
		$pastClasses = LiveClass::where("status", 1)->where("deleted", 0)->where('class_time', '<', $now)->orderBy('class_time', 'ASC')->get();
		$pastClass = array();
		if (!empty($pastClasses)) {
			foreach ($pastClasses as $key => $val) {
				$user = User::where("id", $val['added_by'])->first();
				$pastClass[$key]['id']				= $val['id'];
				$pastClass[$key]['added_by']		= $user->name;
				$pastClass[$key]['title']			= $val['title'];
				$pastClass[$key]['subject']			= $val['subject'];
				$pastClass[$key]['image']			= asset('upload/liveclasses') . "/" . $val['image'];
				if ($val['uploads3']==1){
					$video = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$val['video'];
				} else {
					$video = asset('upload/liveclasses') . "/" . $val['video'];
				}
				$pastClass[$key]['original_video']	= isset($val['video']) ? $video : 'NA';
				if (!empty($val['video_1']) && $val['video_1']!='NA') {
					if ($val['uploads3']==1){
						$video_1 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$val['video_1'];
					} else {
						if(file_exists( public_path().'/upload/liveclasses/'.$val['video_1'] )) {
							$video_1 = asset('upload/liveclasses') . "/" . $val['video_1'];
						} else {
							$video_1 = $pastClass[$key]['original_video'];
						}
					}
				} else {
					$video_1 = $pastClass[$key]['original_video'];
				}
				if (!empty($val['video_2']) && $val['video_2']!='NA') {
					if ($val['uploads3']==1){
						$video_2 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$val['video_2'];
					} else {
						if(file_exists( public_path().'/upload/liveclasses/'.$val['video_2'] )) {
							$video_2 = asset('upload/liveclasses') . "/" . $val['video_2'];
						} else {
							$video_2 = $pastClass[$key]['original_video'];
						}
					}
				} else {
					$video_2 = $pastClass[$key]['original_video'];
				}
				if (!empty($val['video_3']) && $val['video_3']!='NA') {
					if ($val['uploads3']==1){
						$video_3 = "https://s3.ap-south-1.amazonaws.com/video.Guruathomeindia.com/mobile_app_data/".$val['video_3'];
					} else {
						if(file_exists( public_path().'/upload/liveclasses/'.$val['video_3'] )) {
							$video_3 = asset('upload/liveclasses') . "/" . $val['video_3'];
						} else {
							$video_3 = $pastClass[$key]['original_video'];
						}
					}
				} else {
					$video_3 = $pastClass[$key]['original_video'];
				}
				$pastClass[$key]['low_video']		= $video_1;
				$pastClass[$key]['medium_video']	= $video_2;
				$pastClass[$key]['high_video']		= $video_3;
				$pastClass[$key]['master_class']	= $val['master_class'];
				$pastClass[$key]['class_time']		= date('m/d/Y, h:i A', strtotime($val['class_time']));
				$pastClass[$key]['isFree'] 			= $val['isFree'];
				$check_interest = LiveclassNotify::where("user_id", $userId)->where("class_id", $val['id'])->first();
				$my_interest = !empty($check_interest) ? 1 : 0;
				$pastClass[$key]['my_interest']		= $my_interest;
				$total_interest = LiveclassNotify::where("class_id", $val['id'])->count();
				$pastClass[$key]['total_interest']	= $total_interest;
			}
		}
		$upcomingClasses = LiveClass::where("status", 1)->where("deleted", 0)->where('class_time', '>=', $now)->orderBy('class_time', 'ASC')->get();
		$upcomingClass = array();
		if (!empty($upcomingClasses)) {
			foreach ($upcomingClasses as $key => $val) {
				$user = User::where("id", $val['added_by'])->first();
				$upcomingClass[$key]['id'] 				= $val['id'];
				$upcomingClass[$key]['added_by'] 		= $user->name;
				$upcomingClass[$key]['title'] 			= $val['title'];
				$upcomingClass[$key]['subject'] 		= $val['subject'];
				$upcomingClass[$key]['image'] 			= asset('upload/liveclasses') . "/" . $val['image'];
				$upcomingClass[$key]['meeting_id'] 		= $val['meeting_id'];
				$upcomingClass[$key]['pass_code'] 		= $val['pass_code'];
				$upcomingClass[$key]['master_class'] 	= $val['master_class'];
				$upcomingClass[$key]['class_time'] 		= date('m/d/Y, h:i A', strtotime($val['class_time']));
				$upcomingClass[$key]['isFree'] 			= $val['isFree'];
				$check_interest = LiveclassNotify::where("user_id", $userId)->where("class_id", $val['id'])->first();
				$my_interest = !empty($check_interest) ? 1 : 0;
				$upcomingClass[$key]['my_interest'] 	= $my_interest;
				$total_interest = LiveclassNotify::where("class_id", $val['id'])->count();
				$upcomingClass[$key]['total_interest'] 	= $total_interest;
			}
		}
		$message = "All Past and Upcoming Live Classes Data.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "pastClasses" => $pastClass, "upcomingClasses" => $upcomingClass)]);
	}
	public function notifyClass(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("notifyId" => "")]);
		}
		$classId = $request->classId;
		if (!empty($userId) && !empty($classId)) {
			$user = User::where("id", $userId)->first();
			$check_interest = LiveclassNotify::where("user_id", $userId)->where("class_id", $classId)->first();
			if (empty($check_interest)) {
				$data = array(
					'user_id'  		=> $userId,
					'class_id'  	=> $classId,
					'status'  		=> 0,
					'created_at'    => date('Y-m-d H:i:s'),
				);
				$insertId = LiveclassNotify::insertGetId($data);
			} else {
				$data = array(
					'status'  		=> 0,
					'updated_at'    => date('Y-m-d H:i:s'),
				);
				$insertId = LiveclassNotify::where("id", $check_interest->id)->update($data);
			}
			$liveClass = LiveClass::where("id", $classId)->where("status", 1)->where("deleted", 0)->first();
			if ($liveClass) {
			//	$this->smsWithTemplate($user->phone, 'LiveClassSMS', $user->name, $liveClass->subject, $liveClass->class_time);//ak
			}
			$message = "Your Class Added Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("notifyId" => $insertId)]);
		} else {
			$message = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("notifyId" => "")]);
		}
	}

	public function getCourses(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("userSubscription" => "", "askFreeQuestions" => "", "askedQuestionCount" => "", "courses" => [])]);
		}
		$today	  = date('Y-m-d');
		$subscription = UserSubscription::where("user_id", $userId)->where("end_date", ">=", $today)->orderBy('id', 'DESC')->first();
		$userSubscription = !empty($subscription) ? 1 : 0;
		$askFreeQuestions = 3;
		$askedQuestionCount = QuestionAsk::where('user_id', $userId)->count();
		$courses  = Courses::where("status", 1)->where('deleted', 0)->orderBy('sort_id', 'ASC')->get();
		$coursedata = array();
		foreach ($courses as $key => $value) {
			$coursedata[$key]['id']  = $value['id'];
			$coursedata[$key]['name']  = $value['name'];
		}
		$message = "All Courses List.";
		return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("userSubscription" => $userSubscription, "askFreeQuestions" => $askFreeQuestions, "askedQuestionCount" => $askedQuestionCount, "courses" => $coursedata)]);
	}
	public function getLessionsBycourse(Request $request)
	{
		$courseId = $request->courseId;
		$lessions = Lession::where("courseId", $courseId)->where("status", 1)->where('deleted', 0)->orderBy('sort_id', 'ASC')->get();
		$lessiondata = array();
		if (!empty($lessions)) {
			foreach ($lessions as $key => $value) {
				$lessiondata[$key]['id']  = $value['id'];
				$lessiondata[$key]['name']  = $value['name'];
			}
			$message = "Lessions List by Course.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("lessions" => $lessiondata)]);
		} else {
			$message = "Lessions List not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("lessions" => "")]);
		}
	}
	public function getTopicsByLession(Request $request)
	{
		$lessionId = $request->lessionId;
		$topics = Chapter::where("lessionId", $lessionId)->where("status", 1)->where('deleted', 0)->orderBy('sort_id', 'ASC')->get();
		$topicdata = array();
		if (!empty($topics)) {
			foreach ($topics as $key => $value) {
				$topicdata[$key]['id']  = $value['id'];
				$topicdata[$key]['name']  = $value['name'];
			}
			$message = "Topics List by Lession.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("topics" => $topicdata)]);
		} else {
			$message = "Topics List not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("topics" => "")]);
		}
	}
	public function askQuestion(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("ques_id" => "")]);
		}
		$courseId 	= $request->courseId;
		$lessionId  = $request->lessionId;
		$topicId 	= $request->topicId;
		$question 	= $request->question;
		$msg = '';
		if (!empty($userId)  && !empty($courseId) && !empty($lessionId) || !empty($question) || isset($_FILES['image']['name']) ) {
			$questioncount = QuestionAsk::where('user_id', $userId)->count();
			if ($questioncount < 3) {
				$imagess = '';
				if (isset($_FILES['image']['name']) && $_FILES['image']['name']!='') {
					$quesimagename = $_FILES['image']['name'];
					$tmpimage1 = $_FILES['image']['tmp_name'];
					$newImage = rand(00000, 99999) . date('d') . $quesimagename;
					$location = "upload/questionask/";
					move_uploaded_file($tmpimage1, $location . $newImage);
					$url = 'upload/questionask/' . $newImage;
					$img = Image::make($url)->resize(200, 200);
					$imagess =  $img->basename;
				}
				$data = array(
					'user_id' 	 => $userId,
					'course_id'  => $courseId,
					'lession_id' => $lessionId,
					'topic_id' 	 => $topicId,
					'question'   => $question,
					'image'      => $imagess,
					'status'     => 0,
					'created_at' => date('Y-m-d H:i:s'),
				);
				$insertId = QuestionAsk::insertGetId($data);
				/*$user = User::where("id", $userId)->first();
				$msg = $user->name.' Asked a question '.$question.' in Q&A, check it now.';
				$users = User::where("id", "!=", $userId)->where("role_id", "!=", 1)->get();
				foreach ($users as $userval) {
					$this->addNotification($userval->id,$msg);
				}*/
				$message = 'Your Question Submitted Successfully, Team will approve it.';
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array('ques_id' => $insertId)]);
			} else {
				//Check subcription by user later work will be do.
				$subcriptionCheck = User::where('id', $userId)->where('status', 1)->first();
				if (!empty($subcriptionCheck)) {
					$imagess = '';
					if (isset($_FILES['image']['name']) && $_FILES['image']['name']!='') {
						$quesimagename = $_FILES['image']['name'];
						$tmpimage1 = $_FILES['image']['tmp_name'];
						$newImage = rand(00000, 99999) . date('d') . $quesimagename;
						$location = "upload/questionask/";
						move_uploaded_file($tmpimage1, $location . $newImage);
						$url = 'upload/questionask/' . $newImage;
						$img = Image::make($url)->resize(200, 200);
						$imagess =  $img->basename;
					}
					$data = array(
						'user_id' 	 => $userId,
						'course_id'  => $courseId,
						'lession_id' => $lessionId,
						'topic_id' 	 => $topicId,
						'question'   => $question,
						'image'      => $imagess,
						'status'     => 0,
						'created_at' => date('Y-m-d H:i:s'),
					);
					$insertId = QuestionAsk::insertGetId($data);
					/*$user = User::where("id", $userId)->first();
					$msg = $user->name.', Asked a question '.$question.' in Q&A, check it now.';
					$users = User::where("id", "!=", $userId)->where("role_id", "!=", 1)->get();
					foreach ($users as $userval) {
						$this->addNotification($userval->id,$msg);
					}*/
					$message = 'Your Question Submitted Successfully, Team will approve it.';
					return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("ques_id" => $insertId)]);
				} else {
					$message = "Please subscribed a package first to ask more questions!";
					return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("ques_id" => "")]);
				}
			}
		} else {
			$message = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("ques_id" => "")]);
		}
	}
	public function latestQuestion(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("latestQuestions" => [])]);
		}
		$search = $request->search;
		$dt = Carbon::now();
		$from = $dt->subMonth();
		$to = date('Y-m-d H:i:s');
		/*$from = date('Y-m-d').' 00:00:00';
		$to = date('Y-m-d').' 23:59:59';*/
		//$latestQuesAsks = QuestionAsk::where("status", 1)->whereBetween("created_at", [$from, $to]);
		$latestQuesAsks = QuestionAsk::where("status", 1)->where("deleted", 0);
		if (!empty($latestQuesAsks)) {
			$latestQuesAsks = $latestQuesAsks->where("question", 'like', "%" . $search . "%");
		}
		$latestQuesAsks = $latestQuesAsks->orderBy("id", "DESC")->get();
		$latestQuestion = array();
		if (!empty($latestQuesAsks)) {
			foreach ($latestQuesAsks as $key => $val) {
				$user = User::where("id", $val['user_id'])->first();
				$course = Courses::where("id", $val['course_id'])->first();
				$lession = Lession::where("id", $val['lession_id'])->first();
				$topic = Chapter::where("id", $val['topic_id'])->first();
				$courseLessionTopicName = '';
				if($val['topic_id'] > 0){
					$courseLessionTopicName = $course->name.' / '.$lession->name.' / '.$topic->name;
				}elseif($val['lession_id'] > 0){
					$courseLessionTopicName = $course->name.' / '.$lession->name;
				}else{
					if($val['course_id'] > 0){
						$courseLessionTopicName = $course->name;
					}
				}
				$latestQuestion[$key]['id']            = $val['id'];
				$latestQuestion[$key]['added_by']      = $user->name;
				$latestQuestion[$key]['profile_image'] = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
				$latestQuestion[$key]['course_name']   = $courseLessionTopicName;
				//$latestQuestion[$key]['lession_name']  = isset($lession->name) ? $lession->name : '';
				$latestQuestion[$key]['created_at']    = $val['created_at']->diffForHumans(); //date('h:i A', strtotime($val['created_at']));
				$latestQuestion[$key]['question']      = $val['question'];
				$latestQuestion[$key]['image']         = !empty($val['image']) ? asset('upload/questionask') . "/" . $val['image'] : '';
				$latestQuestion[$key]['total_answers'] = QuestionAnswer::where("ques_id", $val['id'])->count();
				$latestQuestion[$key]['share_url'] = route('questionAnswerView',$val['id']);
				if ($userId == $val['user_id']) {
					$latestQuestion[$key]['my_question']  = 1;
				} else {
					$latestQuestion[$key]['my_question']  = 0;
				}
			}
			$message = "All Latest Questions Data.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("latestQuestions" => $latestQuestion)]);
		} else {
			$message = "Latest Question Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("latestQuestions" => "")]);
		}
	}
	public function myQuestion(Request $request)
	{
		//echo $token = $request->bearerToken(); die;
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this-> isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("myQuestions" => [])]);
		}
		/*$dt = Carbon::now();
		$from = $dt->subMonth();
		$to = date('Y-m-d H:i:s');*/
		$myQuestions = QuestionAsk::where("user_id", $userId)->where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->get();
		$myQuestion = array();
		if (!empty($myQuestions)) {
			foreach ($myQuestions as $key => $val) {
				$user = User::where("id", $val['user_id'])->first();
				$course = Courses::where("id", $val['course_id'])->first();
				$lession = Lession::where("id", $val['lession_id'])->first();
				$topic = Chapter::where("id", $val['topic_id'])->first();
				$courseLessionTopicName = '';
				if($val['topic_id'] > 0){
					$courseLessionTopicName = $course->name.' / '.$lession->name.' / '.$topic->name;
				}elseif($val['lession_id'] > 0){
					$courseLessionTopicName = $course->name.' / '.$lession->name;
				}else{
					if($val['course_id'] > 0){
						$courseLessionTopicName = $course->name;
					}
				}
				$myQuestion[$key]['id']            = $val['id'];
				$myQuestion[$key]['added_by']      = $user->name;
				$myQuestion[$key]['profile_image'] = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
				$myQuestion[$key]['course_name']   = $courseLessionTopicName;
				//$myQuestion[$key]['lession_name']  = isset($lession->name) ? $lession->name : '';
				$myQuestion[$key]['created_at']    = $val['created_at']->diffForHumans();
				$myQuestion[$key]['question']      = $val['question'];
				$myQuestion[$key]['image']         = !empty($val['image']) ? asset('upload/questionask') . "/" . $val['image'] : '';
				$myQuestion[$key]['total_answers'] = QuestionAnswer::where("ques_id", $val['id'])->count();
				$myQuestion[$key]['share_url'] = route('questionAnswerView',$val['id']);
			}
			$message = "All My Questions Data.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("myQuestions" => $myQuestion)]);
		} else {
			$message = "My Question Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("myQuestions" => "")]);
		}
	}
	public function answerAQuestion(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("answer_id" => "")]);
		}
		$quesId 	= $request->quesId;
		$answer 	= $request->answer;
		$msg = '';
		if (!empty($userId)  && !empty($quesId)) {
			$questioncheck = QuestionAsk::where('id', $quesId)->first();
			if (!empty($questioncheck)) {
				$imagess = '';
				if (isset($_FILES['image']['name']) && $_FILES['image']['name']!='') {
					$ansimagename = $_FILES['image']['name'];
					$tmpimage1 = $_FILES['image']['tmp_name'];
					$newImage = rand(00000, 99999) . date('d') . $ansimagename;
					$location = "upload/questionask/";
					move_uploaded_file($tmpimage1, $location . $newImage);
					$url = 'upload/questionask/' . $newImage;
					$img = Image::make($url)->resize(200, 200);
					$imagess =  $img->basename;
				}
				$data = array(
					'user_id' 	 => $userId,
					'ques_id'    => $quesId,
					'answer'     => $answer,
					'image'      => $imagess,
					'status'     => 1,
					'created_at' => date('Y-m-d H:i:s'),
				);
				$insertId = QuestionAnswer::insertGetId($data);
				$user = User::where("id", $userId)->first();
				$ques = QuestionAsk::where("id", $quesId)->first();
				$msg = $user->name.', Answered a question '.$ques->question.' in Q&A, check it now.';
				$this->addNotification($ques->user_id,$msg);
				/*$users = User::where("id", "!=", $userId)->where("role_id", "!=", 1)->get();
				foreach ($users as $userval) {
					$this->addNotification($userval->id,$msg);
				}*/
				$message = 'Your Answer Submitted Successfully.';
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("answer_id" => $insertId)]);
			} else {
				$message = "Question not Found!";
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("answer_id" => "")]);
			}
		} else {
			$message = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("answer_id" => "")]);
		}
	}
	public function viewAnswers(Request $request)
	{
		$quesId   = $request->quesId;
		$question = QuestionAsk::where("id", $quesId)->where("status", 1)->first();
		$questiondata = array();
		if (!empty($question)) {
			$user = User::where("id", $question->user_id)->first();
			$course = Courses::where("id", $question->course_id)->first();
			$lession = Lession::where("id", $question->lession_id)->first();
			$topic = Chapter::where("id", $question->topic_id)->first();
			$courseLessionTopicName = '';
			if($question->topic_id > 0){
				$courseLessionTopicName = $course->name.' / '.$lession->name.' / '.$topic->name;
			}elseif($question->lession_id > 0){
				$courseLessionTopicName = $course->name.' / '.$lession->name;
			}else{
				if($question->course_id > 0){
					$courseLessionTopicName = $course->name;
				}
			}
			$questiondata['id']            = $question->id;
			$questiondata['added_by']      = isset($user->name) ? $user->name : '';
			$questiondata['profile_image'] = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
			$questiondata['course_name']   = $courseLessionTopicName;
			//$questiondata['lession_name']  = isset($lession->name) ? $lession->name : '';
			$questiondata['created_at']    = $question->created_at->diffForHumans();
			$questiondata['question']      = $question->question;
			$questiondata['image']         = !empty($question->image) ? asset('upload/questionask') . "/" . $question->image : '';
			$questiondata['total_answers'] = QuestionAnswer::where("ques_id", $question->id)->count();
			$questiondata['share_url'] = route('questionAnswerView',$question->id);
			$answers = QuestionAnswer::where("ques_id", $quesId)->where("expert", "!=", 0)->where("status", 1)->orderBy("expert", "ASC")->orderBy("ans_like", "DESC")->get();
			$answerdata = array();
			$expertanswerdata = array();
			if (!empty($answers)) {
				foreach ($answers as $key => $val) {
					$user = User::where("id", $val['user_id'])->first();
					/*$answerdata[$key]['id']            = $val['id'];
					$answerdata[$key]['added_by']      = isset($user->name) ? $user->name : '';
					$answerdata[$key]['profile_image'] = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
					$answerdata[$key]['created_at']    = $val['created_at']->diffForHumans();
					$answerdata[$key]['answer']        = $val['answer'];
					$answerdata[$key]['image']         = !empty($val['image']) ? asset('upload/questionask') . "/" . $val['image'] : '';
					$answerdata[$key]['ans_like']      = $val['ans_like'];
					$answerdata[$key]['ans_unlike']    = $val['ans_unlike'];
					$answerdata[$key]['expert'] 	   = $val['expert'];*/
					$expertans['id']            = $val['id'];
					$expertans['added_by']      = isset($user->name) ? $user->name : '';
					$expertans['profile_image'] = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
					$expertans['created_at']    = $val['created_at']->diffForHumans();
					$expertans['answer']        = $val['answer'];
					$expertans['image']         = !empty($val['image']) ? asset('upload/questionask') . "/" . $val['image'] : '';
					$expertans['ans_like']      = $val['ans_like'];
					$expertans['ans_unlike']    = $val['ans_unlike'];
					$expertans['expert']		  = $val['expert'];
					array_push($expertanswerdata, $expertans);
				}
			}
			$useranswers = QuestionAnswer::where("ques_id", $quesId)->where("expert", 0)->where("status", 1)->orderBy("ans_like", "DESC")->get();
			$useranswerdata = array();
			if (!empty($useranswers)) {
				foreach ($useranswers as $key => $val) {
					$user = User::where("id", $val['user_id'])->first();
					$userans['id']            = $val['id'];
					$userans['added_by']      = isset($user->name) ? $user->name : '';
					$userans['profile_image'] = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
					$userans['created_at']    = $val['created_at']->diffForHumans();
					$userans['answer']        = $val['answer'];
					$userans['image']         = !empty($val['image']) ? asset('upload/questionask') . "/" . $val['image'] : '';
					$userans['ans_like']      = $val['ans_like'];
					$userans['ans_unlike']    = $val['ans_unlike'];
					$userans['expert']		  = $val['expert'];
					array_push($useranswerdata, $userans);
				}
			}
			$answerdata = array_merge($expertanswerdata, $useranswerdata);
			$message = "Get All Answers of a Question Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("question" => $questiondata, "answers" => $answerdata)]);
		} else {
			$message = "Question Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("question" => "", "answers" => "")]);
		}
	}
	public function likeAnswer(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("answer_id" => "")]);
		}
		$answerId = $request->answerId;
		$msg = '';
		if (!empty($userId) && !empty($answerId)) {
			$getAnswer = QuestionAnswer::where('id', $answerId)->first();
			if (!empty($getAnswer)) {
				$getAnswerLike = QuestionAnswerLike::where('user_id', $userId)->where('answer_id', $answerId)->first();
				$userLiked = !empty($getAnswerLike) ? $getAnswerLike->ans_like : 0;
				$userDisliked = !empty($getAnswerLike) ? $getAnswerLike->ans_unlike : 0;
				if ($userLiked==0) {
					$preLike = $getAnswer->ans_like;
					$preDislike = $getAnswer->ans_unlike;
					if ($userDisliked==1) {
						$preDislike = $preDislike - 1;
					}
					$like = $preLike + 1;
					$data = array(
						'ans_like' 	 => $like,
						'ans_unlike' => $preDislike,
						'updated_at' => date('Y-m-d H:i:s'),
					);
					$update = QuestionAnswer::where("id", $answerId)->update($data);
					if (!empty($getAnswerLike)) {
						$data1 = array(
							'ans_like' 	 => 1,
							'ans_unlike' => 0,
							'updated_at' => date('Y-m-d H:i:s'),
						);
						$update = QuestionAnswerLike::where("id", $getAnswerLike->id)->update($data1);
					} else {
						$data1 = array(
							'user_id' 	 => $userId,
							'answer_id'  => $answerId,
							'ans_like' 	 => 1,
							'ans_unlike' => 0,
							'created_at' => date('Y-m-d H:i:s'),
						);
						$insert = QuestionAnswerLike::insertGetId($data1);
					}
					$msg = 'Your Like Submitted Successfully.';
					return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => array("answer_id" => $answerId)]);
				} else {
					$msg = 'You have already liked this answer.';
					return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => array("answer_id" => $answerId)]);
				}
			} else {
				$msg = "Answer not Found!";
				return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("answer_id" => "")]);
			}
		} else {
			$msg = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("answer_id" => "")]);
		}
	}
	public function unlikeAnswer(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("answer_id" => "")]);
		}
		$answerId = $request->answerId;
		$msg = '';
		if (!empty($userId) && !empty($answerId)) {
			$getAnswer = QuestionAnswer::where('id', $answerId)->first();
			if (!empty($getAnswer)) {
				$getAnswerLike = QuestionAnswerLike::where('user_id', $userId)->where('answer_id', $answerId)->first();
				$userLiked = !empty($getAnswerLike) ? $getAnswerLike->ans_like : 0;
				$userDisliked = !empty($getAnswerLike) ? $getAnswerLike->ans_unlike : 0;
				if ($userDisliked==0) {
					$preLike = $getAnswer->ans_like;
					if ($userLiked==1) {
						$preLike = $preLike - 1;
					}
					$preUnLike = $getAnswer->ans_unlike;
					$unlike = $preUnLike + 1;
					$data = array(
						'ans_like' 	 => $preLike,
						'ans_unlike' => $unlike,
						'updated_at' => date('Y-m-d H:i:s'),
					);
					$update = QuestionAnswer::where("id", $answerId)->update($data);
					if (!empty($getAnswerLike)) {
						$data1 = array(
							'ans_like' 	 => 0,
							'ans_unlike' => 1,
							'updated_at' => date('Y-m-d H:i:s'),
						);
						$update = QuestionAnswerLike::where("id", $getAnswerLike->id)->update($data1);
					} else {
						$data1 = array(
							'user_id' 	 => $userId,
							'answer_id'  => $answerId,
							'ans_like' 	 => 0,
							'ans_unlike' => 1,
							'created_at' => date('Y-m-d H:i:s'),
						);
						$insert = QuestionAnswerLike::insertGetId($data1);
					}
					$msg = 'Your Unlike Submitted Successfully.';
					return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => array("answer_id" => $answerId)]);
				} else {
					$msg = 'You have already disliked this answer.';
					return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => array("answer_id" => $answerId)]);
				}
			} else {
				$msg = "Answer not Found!";
				return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("answer_id" => "")]);
			}
		} else {
			$msg = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("answer_id" => "")]);
		}
	}

	public function userDetail(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("user" => array("id"=>""))]);
		}
		$user     = User::where("id", $userId)->where("status", 1)->first();
		$userdata = array();
		if (!empty($user)) {
			$userdata['id']             = $user->id;
			$userdata['name']           = isset($user->name) ? $user->name : '';
			$userdata['email']          = isset($user->email) ? $user->email : '';
			$userdata['phone']          = isset($user->phone) ? $user->phone : '';
			$userdata['gender']         = isset($user->gender) ? $user->gender : '';
			$userdata['dob']            = isset($user->dob) ? date('m/d/Y', strtotime($user->dob)) : '';
			$userdata['image']          = !empty($user->image) ? asset('upload/profile') . "/" . $user->image : '';
			$userdata['class_name']     = isset($user->class_name) ? $user->class_name : '';
			$userdata['school_college'] = isset($user->school_college) ? $user->school_college : '';
			$userdata['state']          = isset($user->state) ? $user->state : '';
			$userdata['city']           = isset($user->city) ? $user->city : '';
			$userdata['postal_code']    = isset($user->postal_code) ? $user->postal_code : '';
			$userdata['earned_point']   = 0;
			$userdata['created_at']     = $user->created_at->diffForHumans();
			$message = "Get User Detail Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("user" => $userdata)]);
		} else {
			$message = "User Not Found!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("user" => "")]);
		}
	}
	public function updateProfile(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("id"=>"")]);
		}
		$userArray = array();
		$validator = Validator::make($request->all(), [
			'userId' => 'required|numeric',
			'name' => 'required|regex:/^[\pL\s\-]+$/u',
			'email' => 'required|string|email',
			'phone' => 'required|min:10|max:10',
			'postal_code' => 'numeric|min:6',
		]);

		if($validator->fails()){
        	$msg = $validator->messages()->first();
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}

		$name 		    = ucwords($request->name);
		$email 			= $request->email;
		$phone 			= $request->phone;
		$gender 		= $request->gender;
		$dob 			= $request->dob;
		$class_name 	= $request->class_name;
		$school_college = $request->school_college;
		$state 			= $request->state;
		$city 			= $request->city;
		$postal_code 	= $request->postal_code;
		$checkUser = User::where('id', $userId)->first();
		if (!empty($checkUser)) {
			$imagess = '';
			//print_r($_FILES); exit;
			if (isset($_FILES['image']['name']) && $_FILES['image']['name']!='') {
				$profileimagename = $_FILES['image']['name'];
				$tmpimage1 = $_FILES['image']['tmp_name'];
				$newprofileImage = rand(00000, 99999) . date('d') . $profileimagename;
				$location = "upload/profile/";
				move_uploaded_file($tmpimage1, $location . $newprofileImage);
				$url = 'upload/profile/' . $newprofileImage;
				$img = Image::make($url)->resize(200, 200);
				$imagess =  $img->basename;
			} else {
				$imagess = $checkUser->image;
			}
			$otp = rand(1111, 9999);
			$updateData = User::where('id', $userId)->update([
				'name' 			 => $name,
				//'email' 		 => $email,
				//'phone' 		 => $phone,
				'gender' 		 => $gender,
				'dob' 			 => date('Y-m-d H:i:s', strtotime($dob)),
				'image' 		 => $imagess,
				'class_name' 	 => $class_name,
				'school_college' => $school_college,
				'state' 		 => $state,
				'city' 			 => $city,
				'postal_code' 	 => $postal_code,
				'otp_match' 	 => $otp,
				'updated_at' 	 => date('Y-m-d H:i:s')
			]);
			$userArray = $this->getuserDetail($userId);
			//echo $userArray['email']; die;
			if ($userArray['email']!=$email && $userArray['phone']!=$phone) {
				$changeStatus = 3;
				$newPhone = $phone;
				$newEmail = $email;
			} elseif ($userArray['email']!=$email && $userArray['phone']==$phone) {
				$changeStatus = 2;
				$newPhone = '';
				$newEmail = $email;
			} elseif ($userArray['email']==$email && $userArray['phone']!=$phone) {
				$changeStatus = 1;
				$newPhone = $phone;
				$newEmail = '';
			} else {
				$changeStatus = 0;
				$newPhone = '';
				$newEmail = '';
			}
			$userArray['changeStatus'] = $changeStatus;
			$userArray['newPhone'] = $newPhone;
			$userArray['newEmail'] = $newEmail;
			if ($changeStatus > 0) {
				//send otp
				$msg = 'Verification Otp Send, Please Check.';
				$this->sms($phone, $otp);
				/*$data = array('username' => $userArray['name'], 'OTP' => $otp, 'msg' => $msg);
				Mail::send('emails.otpmail', $data, function ($message) {
					$checkUser = User::where('id', $_POST['userId'])->first();
					$email = $checkUser->email;
					$message->to($email, 'From Guruathome')->subject('Guruathome: Verify OTP');
					$message->from('<EMAIL>', 'Guruathome');
				});*/
				$this->sendEmail($userArray['email'], 'Guruathome: Verify OTP', $data = array('userName' => $userArray['name'], 'message' => '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: ' . $otp . '</p>'));

			}
			$message = 'Updated Successfully';
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => $userArray]);
		} else {
			$msg = "Invalid User Id ";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}
	}
	public function updatePhoneEmail(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("id"=>"")]);
		}
		$userArray = array();
		$validator = Validator::make($request->all(), [
			'userId' => 'required|numeric',
			'otp' => 'required|numeric',
			// 'email' => 'string|email',
			// 'phone' => 'min:10|max:13',
		]);

		if($validator->fails()){
        	$msg = $validator->messages()->first();
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}

		$phone 	= $request->phone;
		$email 	= $request->email;
		$otp 	= $request->otp;
		$checkUser = User::where('id', $userId)->where('otp_match', $otp)->first();
		if (!empty($checkUser)) {
			if (!empty($phone)) {
				$checkPhone = User::where('id', '!=', $userId)->where('phone', $phone)->first();
				if (empty($checkPhone)) {
					$updateData = User::where('id', $userId)->update([
						'phone' 		 => $phone,
						'updated_at' 	 => date('Y-m-d H:i:s')
					]);
				}
			}
			if (!empty($email)) {
				$checkEmail = User::where('id', '!=', $userId)->where('email', $email)->first();
				if (empty($checkEmail)) {
					$updateData = User::where('id', $userId)->update([
						'email' 		 => $email,
						'updated_at' 	 => date('Y-m-d H:i:s')
					]);
				}
			}
			$userArray = $this->getuserDetail($userId);
			$message = 'Updated Successfully';
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => $userArray]);
		} else {
			$msg = "User Id or OTP not matched!";
			return response()->json(['statusCode' => 400, 'message' => $msg, 'data' => array("id" => "")]);
		}
	}
	public function phoneEmailResendOtp(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("otp"=>"")]);
		}
		$phone   = $request->phone;
		$email   = $request->email;
		if (!empty($userId)) {
			$checkUser = User::where('id', $userId)->first();
			if ($checkUser) {
				$otpnumber = rand(1111, 9999);
				if($phone==''){
					$phone = $checkUser->phone;
				}
				$update = DB::table('users')->where('id', $userId)->update(['otp_match' => $otpnumber]);
				if ($update) {
					$returndata['otp'] = $otpnumber;
					$msg = 'Verification Otp Send, Please Check.';
					$this->sms($phone, $otpnumber);
					/*$data = array('username' => $checkUser->name, 'OTP' => $otpnumber, 'msg' => $msg);
					Mail::send('emails.otpmail', $data, function ($message) {
						if (isset($_POST['email']) && $_POST['email']!=''){
							$email = $_POST['email'];
						} else {
							$checkUser = User::where('id', $_POST['userId'])->first();
							$email = $checkUser->email;
						}
						$message->to($email, 'From Guruathome')->subject('Guruathome: Verify OTP');
						$message->from('<EMAIL>', 'Guruathome');
					});*/
					$this->sendEmail($checkUser->email, 'Guruathome: Verify OTP', $data = array('userName' => $checkUser->name, 'message' => '<p>Thank you for connecting at Guruathome,</p><p>You have got successfully your OTP: ' . $otpnumber . '</p>'));
					
					return response()->json(['statusCode' => 200, 'message' => $msg, "data" => $returndata]);
				} else {
					return response()->json(['statusCode' => 400, 'message' => 'Somthing Went Wrong!', 'data' => array("otp" => "")]);
				}
			} else {
				return response()->json(['statusCode' => 400, 'message' => 'Invaild User Id!', 'data' => array("otp" => "")]);
			}
		} else {
			return response()->json(['statusCode' => 400, 'message' => 'Wrong Paramenter Passed!', 'data' => array("otp" => "")]);
		}
	}



	public function getSubscriptions(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("subscriptions" => [])]);
		}
		//$subscriptions  = Subscription::where("status", 1)->where("deleted", 0)->get();
		$subscriptions  = Subscription::where("deleted", 0)->get();
		$subscriptiondata = array();
		if (!empty($subscriptions)) {
			foreach ($subscriptions as $key => $value) {
				if ($value['status']==1) {
					$sub_plan['id']    = $value['id'];
					$sub_plan['name']  = $value['name'];
					$sub_plan['month'] = $value['month'];
					$sub_plan['price'] = $value['price'];
					$sub_plan['image'] = isset($value['image']) ? asset('upload/subscriptions') . "/" . $value['image'] : 'NA';
					$sub_plan['video'] = isset($value['video']) ? asset('upload/subscriptions') . "/" . $value['video'] : 'NA';
					$sub_plan['description'] = $value['description'];
					$faqs_arr = array();
					$faqs = json_decode($value['faqs'], true);
					if (!empty($faqs)) {
						foreach ($faqs as $val) {
							$faq['question'] = $val['question'];
							$faq['answer'] = $val['answer'];
							array_push($faqs_arr, $faq);
						}
					}
					$sub_plan['faqs']	= $faqs_arr;
					$subscription = UserSubscription::where("user_id", $userId)->where("subscription_id", $value['id'])->orderBy('id', 'DESC')->first();
					$sub_plan['start_date'] = !empty($subscription->start_date) ? $subscription->start_date : '';
					$sub_plan['end_date'] = !empty($subscription->end_date) ? $subscription->end_date : '';
					$sub_plan['status'] = !empty($subscription) ? 1 : 0;
					array_push($subscriptiondata, $sub_plan);
				} else {
					$subscription = UserSubscription::where("user_id", $userId)->where("subscription_id", $value['id'])->orderBy('id', 'DESC')->first();
					if (!empty($subscription)) {
						$inactive_sub_plan['id']    = $value['id'];
						$inactive_sub_plan['name']  = $value['name'];
						$inactive_sub_plan['month'] = $value['month'];
						$inactive_sub_plan['price'] = $value['price'];
						$inactive_sub_plan['image'] = isset($value['image']) ? asset('upload/subscriptions') . "/" . $value['image'] : 'NA';
						$inactive_sub_plan['video'] = isset($value['video']) ? asset('upload/subscriptions') . "/" . $value['video'] : 'NA';
						$inactive_sub_plan['description'] = $value['description'];
						$faqs_arr = array();
						$faqs = json_decode($value['faqs'], true);
						if (!empty($faqs)) {
							foreach ($faqs as $val) {
								$faq['question'] = $val['question'];
								$faq['answer'] = $val['answer'];
								array_push($faqs_arr, $faq);
							}
						}
						$inactive_sub_plan['faqs']	= $faqs_arr;
						$subscription = UserSubscription::where("user_id", $userId)->where("subscription_id", $value['id'])->orderBy('id', 'DESC')->first();
						$inactive_sub_plan['start_date'] = !empty($subscription->start_date) ? $subscription->start_date : '';
						$inactive_sub_plan['end_date'] = !empty($subscription->end_date) ? $subscription->end_date : '';
						$inactive_sub_plan['status'] = !empty($subscription) ? 1 : 0;
						array_push($subscriptiondata, $inactive_sub_plan);
					}
				}
			}
			$message = "Get Subscription List.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("subscriptions" => $subscriptiondata)]);
		} else {
			$message = "Subscription not Found.";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("subscriptions" => "")]);
		}
	}

	public function applyCoupon(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("subscriptionId" => "", "orderId" => "", "paymentGateway" => "", "couponCode" => "")]);
		}
		$subscriptionId = $request->subscriptionId;
		$couponCode		= $request->couponCode;
		$discount = $discountAmt = $payableAmt = $coupon_no_of_users = $coupon_user_id = $coupon_subscription_id = 0;
		if (!empty($userId) && !empty($subscriptionId) && !empty($couponCode)) {
			$today	  = date('Y-m-d');
			$subscription = UserSubscription::where("user_id", $userId)->where("end_date", ">=", $today)->orderBy('id', 'DESC')->first();
			$userSubscription = !empty($subscription) ? 1 : 0;
			if ($userSubscription==1) {
				$message = "You have already subscribed a plan, after expired you can take new subscription!";
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("subscriptionId" => $subscriptionId, "orderId" => "", "paymentGateway" => "", "couponCode" => $couponCode)]);
			}
			//$getCoupon = CouponCode::where("user_id", $userId)->where("coupon", $couponCode)->where("status", 1)->where("deleted", 0)->first();
			$getCoupon = CouponCode::where("coupon", $couponCode)->where("end_date", ">=", $today)->where("status", 1)->where("deleted", 0)->first();
			if (!empty($getCoupon) && $getCoupon->discount > 0) {
				$discount = $getCoupon->discount;
			} else {
				$message = "This Coupon Code not found!";
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("subscriptionId" => $subscriptionId, "orderId" => "", "paymentGateway" => "", "couponCode" => $couponCode)]);
			}
			$checkSubscription = UserSubscription::where("user_id", $userId)->where("coupon_code", $couponCode)->first();
			if (!empty($checkSubscription)) {
				$message = "This Coupon Code already in used!";
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("subscriptionId" => $subscriptionId, "orderId" => "", "paymentGateway" => "", "couponCode" => $couponCode)]);
			} else {
				if ($getCoupon->condition_1 == 1){
					if ($getCoupon->user_id > 0){
						$coupon_user_id = $getCoupon->user_id;
						if ($userId != $coupon_user_id) {
							$message = "This Coupon Code not made for you!";
							return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("subscriptionId" => $subscriptionId, "orderId" => "", "paymentGateway" => "", "couponCode" => $couponCode)]);
						}
					}
				} elseif ($getCoupon->condition_1 == 2){
					if ($getCoupon->no_of_users > 0){
						$coupon_no_of_users = $getCoupon->no_of_users;
					}
				} else {
					if ($getCoupon->no_of_users > 0){
						$coupon_no_of_users = $getCoupon->no_of_users;
					}
				}
				if ($getCoupon->subscription_id > 0){
					$coupon_subscription_id = $getCoupon->subscription_id;
					if ($subscriptionId != $coupon_subscription_id) {
						$message = "This Coupon Code not made for this subscription plan!";
						return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("subscriptionId" => $subscriptionId, "orderId" => "", "paymentGateway" => "", "couponCode" => $couponCode)]);
					}
				}
				if ($coupon_no_of_users > 0) {
					$totalUsedCouponCode = UserSubscription::where("coupon_code", $couponCode)->count();
					if ($totalUsedCouponCode >= $coupon_no_of_users) {
						$message = "This Coupon Code is expired!";
						return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("subscriptionId" => $subscriptionId, "orderId" => "", "paymentGateway" => "", "couponCode" => $couponCode)]);
					}
				}
			}
			$getSubscription = Subscription::where("id", $subscriptionId)->where("status", 1)->where("deleted", 0)->first();
			if (!empty($getSubscription) && $getSubscription->price > 1) {
				$amount = $getSubscription->price;
				if ($discount > 0) {
					$discountAmt = ($amount * $discount) / 100;
				}
				if ($discountAmt > 0) {
					if ($amount > $discountAmt) {
						$payableAmt = $amount - $discountAmt;
					} else {
						$payableAmt = 0;
					}
				}
				if ($payableAmt > 0) {
					$genString = $this->generateRandomString(35);
					$key_id = '***********************';
					$secret = 'qIB1iMh3EIR6VyzycKhAzNzh';
					$api = new Api($key_id, $secret);

					$order  = $api->order->create([
					  'receipt' => 'order_rcptid_11',
					  'amount'  => $payableAmt * 100,
					  'currency' => 'INR'
					]);
					//echo '<pre />'; print_r($order); die;
					$orderId = $order['id'];
					$payment_gateway = 1;
					$message = "Order Id Created Successfully.";
				} else {
					$month = !empty($getSubscription->month) ? $getSubscription->month : 0;
					$today = date('Y-m-d');
					$end_date = date('Y-m-d', strtotime('+'.$month.' months'));
					$data1 = array(
						'user_id'			=> $userId,
						'subscription_id'	=> $subscriptionId,
						'start_date'		=> $today,
						'end_date'			=> $end_date,
						'coupon_code'		=> $couponCode,
						'mode'				=> 'Coupon',
						'created_at'		=> date('Y-m-d H:i:s'),
					);
					$orderId = UserSubscription::insertGetId($data1);
					$user = User::where("id", $userId)->first();
					$msg = 'Subscription plan activated';
				//	$this->smsWithTemplate($user->phone, 'Mambership', $user->name, $getSubscription->name);//ak
					$this->addNotification($userId,$msg);
					/*$data = array('username' => $user->name, 'payment_id' => $couponCode, 'msg' => $msg);
					Mail::send('emails.payment', $data, function ($message) {
						$user = User::where("id", $_POST['userId'])->first();
						$email = $user->email;
						$message->to($email, 'From Guruathome')->subject('Guruathome: Subscription plan activated');
						$message->from('<EMAIL>', 'Guruathome');
					});*/
					$this->sendEmail($user->email, 'Guruathome: Subscription payment initiated', $data = array('userName' => $user->name, 'message' => '<p>Thank you for payment initiated at Guruathome,</p><p>You have subscribed a plan successfully with Coupon Code: ' . $couponCode . '</p>'));

					$payment_gateway = 0;
					$message = "Subscription Plan Activated Successfully.";
				}
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("subscriptionId" => $subscriptionId, "orderId" => $orderId, "paymentGateway" => $payment_gateway, "couponCode" => $couponCode)]);
			} else {
				$message = "Subscription not found!";
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("subscriptionId" => $subscriptionId, "orderId" => "", "paymentGateway" => "", "couponCode" => $couponCode)]);
			}
		} else {
			$message = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("subscriptionId" => "", "orderId" => "", "paymentGateway" => "", "couponCode" => $couponCode)]);
		}
	}

	public function getRazorpayOrderid(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("subscriptionId" => "", "orderId" => "")]);
		}
		$subscriptionId = $request->subscriptionId;
		if (!empty($userId) && !empty($subscriptionId)) {
			$today	  = date('Y-m-d');
			$subscription = UserSubscription::where("user_id", $userId)->where("end_date", ">=", $today)->orderBy('id', 'DESC')->first();
			$userSubscription = !empty($subscription) ? 1 : 0;
			if ($userSubscription==1) {
				$message = "You have already subscribed a plan, after expired you can take new subscription!";
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("subscriptionId" => $subscriptionId, "orderId" => "")]);
			}
			$getSubscription = Subscription::where("id", $subscriptionId)->where("status", 1)->where("deleted", 0)->first();
			if (!empty($getSubscription) && $getSubscription->price > 1) {
				$amount = $getSubscription->price;
				$genString = $this->generateRandomString(35);
				$key_id = '***********************';
				$secret = 'qIB1iMh3EIR6VyzycKhAzNzh';
				$api = new Api($key_id, $secret);

				$order  = $api->order->create([
				  'receipt' => 'order_rcptid_11',
				  'amount'  => $amount * 100,
				  'currency' => 'INR'
				]);
				//echo '<pre />'; print_r($order); die;
				$orderId = $order['id'];
				$message = "Order Id Created Successfully.";
				return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("subscriptionId" => $subscriptionId, "orderId" => $orderId)]);
			} else {
				$message = "Subscription not found!";
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("subscriptionId" => $subscriptionId, "orderId" => "")]);
			}
		} else {
			$message = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("subscriptionId" => "", "orderId" => "")]);
		}
	}

	public function takeSubscription(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("id" => "","payment_status" => "","txn_id" => "")]);
		}
		$subscriptionId = $request->subscriptionId;
		$orderId 		= $request->orderId;
		$couponCode		= !empty($request->couponCode) ? $request->couponCode : '';
		if (!empty($userId) && !empty($subscriptionId) && !empty($orderId)) {
			$key_id = '***********************';
			$secret = 'qIB1iMh3EIR6VyzycKhAzNzh';
			$api = new Api($key_id, $secret);

			$payments = $api->order->fetch($orderId)->payments(); // Returns array of payment objects against an order
			//echo '<pre />'; print_r($payments); die;
			$txn_id = '';
			if (!empty($payments)) {
				$paymentStatus = isset($payments['items'][0]['status']) ? $payments['items'][0]['status'] : 'failed';
				if ($paymentStatus!='failed') {
					$txn_id = isset($payments['items'][0]['id']) ? $payments['items'][0]['id'] : '';
					if ($txn_id != '') {
						$paymentMode = 'Online';
						if(!empty($couponCode)){
							$paymentMode = 'Coupon & Online';
						}

						$getSubscription = Subscription::where("id", $subscriptionId)->where("status", 1)->where("deleted", 0)->first();
						$month = !empty($getSubscription->month) ? $getSubscription->month : 0;
						$today = date('Y-m-d');
						$end_date = date('Y-m-d', strtotime('+'.$month.' months'));
						$data1 = array(
							'user_id'			=> $userId,
							'subscription_id'	=> $subscriptionId,
							'start_date'		=> $today,
							'end_date'			=> $end_date,
							'txn_id'			=> $txn_id,
							'coupon_code'		=> $couponCode,
							'mode'				=> $paymentMode,
							'created_at'		=> date('Y-m-d H:i:s'),
						);
						$inserId = UserSubscription::insertGetId($data1);
						$user = User::where("id", $userId)->first();
						$msg = 'Subscription payment initiated';
						$this->addNotification($userId,$msg);
					//	$this->smsWithTemplate($user->phone, 'Mambership', $user->name, $getSubscription->name);//ak
						/*$data = array('username' => $user->name, 'payment_id' =>  $txn_id, 'msg' =>  $msg);
						Mail::send('emails.payment', $data, function ($message) {
							$user = User::where("id", $_POST['userId'])->first();
							$email = $user->email;
							$message->to($email, 'From Guruathome')->subject('Guruathome: Subscription payment initiated');
							$message->from('<EMAIL>', 'Guruathome');
						});*/
						$this->sendEmail($user->email, 'Guruathome: Subscription payment initiated', $data = array('userName' => $user->name, 'message' => '<p>Thank you for payment initiated at Guruathome,</p><p>You have subscribed a plan successfully with Payment id: ' . $txn_id . '</p>'));
						
						$message = "Your Subscription Added Successfully.";
						return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("id" => $inserId,"payment_status" => "success","txn_id" => $txn_id)]);
					} else {
						$message = "Payment not initiated!";
						return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("id" => "","payment_status" => "failed","txn_id" => "")]);
					}
				} else {
					$message = "Payment failed!";
					return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("id" => "","payment_status" => "failed","txn_id" => "")]);
				}
			} else {
				$message = "Payment not initiated!";
				return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("id" => "","payment_status" => "failed","txn_id" => "")]);
			}
		} else {
			$message = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("id" => "","payment_status" => "","txn_id" => "")]);
		}
	}

	public function contactUs(Request $request)
	{
		//echo '<pre />'; print_r($request->all()); die;
		$userId  = $request->userId;
		$message = $request->message;
		if (!empty($userId) && !empty($message)) {
			$data = array(
				'user_id'  		=> $userId,
				'message'  		=> $message,
				'created_at'    => date('Y-m-d H:i:s'),
			);
			$inserId = Contactus::insertGetId($data);
			$message = "Your Message Added Successfully.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("contactId" => $inserId)]);
		} else {
			$message = "Wrong Paramenter Passed!";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("contactId" => "")]);
		}
	}

	public function getNotifications(Request $request)
	{
		$bearerToken = $request->bearerToken();
		$userId   = $request->userId;
		$userStatus = $this->isUserActive($userId,$bearerToken);
		if ($userStatus == 0) {
			$message = "User not Available.";
			return response()->json(['statusCode' => 203, 'message' => $message, 'data' => array("notifications" => [])]);
		}
		$notifications = Notification::where("user_id",$userId)->orderBy("id","DESC")->get();
		$notificationdata = array();
		if (!empty($notifications)) {
			foreach ($notifications as $key => $value) {
				$notificationdata[$key]['id']    = $value['id'];
				$notificationdata[$key]['message'] = $value['message'];
				$notificationdata[$key]['created_at'] = $value['created_at']->diffForHumans();
			}
			$message = "Get Notification List.";
			return response()->json(['statusCode' => 200, 'message' => $message, 'data' => array("notifications" => $notificationdata)]);
		} else {
			$message = "Notification not Found.";
			return response()->json(['statusCode' => 400, 'message' => $message, 'data' => array("notifications" => "")]);
		}
	}

	public function addNotification($userId,$message)
	{
		$data = array(
			'user_id'		=> $userId,
			'message'		=> $message,
			'created_at'	=> date('Y-m-d H:i:s'),
		);
		$inserId = Notification::insertGetId($data);
		$user = User::where("id", $userId)->first();
		$token = isset($user->deviceToken) ? $user->deviceToken : '';
		if ($token!='') {
			$title = 'Guruathome';
			//$this->notificationsend($token, $title, $message);//ak
		}
	}
	public function notificationsend($token, $title, $body)
	{
		$url = "https://fcm.googleapis.com/fcm/send";
		$token = $token;
		$serverKey = 'AAAAqt_CbVU:APA91bH1KAkCHGHbgQEtQYxUldBupx4_7y42dNa1hOPGz8IFePdzSXWu4uC1CudCTuowek2O01KScKbHgoROAscE8mCiy-53rcxcQOABsLvrp1JB14kbGNVT7sGqT53Qh1sjeAflTqC2';
		$title = $title;
		$body = $body;
		$notification = array('title' => $title, 'body' => $body, 'sound' => 'default', 'badge' => '1');
		$arrayToSend = array('to' => $token, 'notification' => $notification, 'priority' => 'high');
		$json = json_encode($arrayToSend);
		$headers = array();
		$headers[] = 'Content-Type: application/json';
		$headers[] = 'Authorization: key=' . $serverKey;
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
		curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
		curl_setopt($ch, CURLOPT_POSTFIELDS, $json);
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		//Send the request
		curl_exec($ch);
		curl_close($ch);
	}
	public function notificationstatus(Request $request)
	{
		$userId = $request->header("userId");
		$noti = Notification::where('user_id', $userId)->where('status', 0)->update(['status' => 1]);
		$msg = "notification seen successfully";
		return response()->json(['statusCode' => 200, 'message' => $msg]);
	}

	public function generatePDF($examId, $username, $coursename, $date)
	{
		//$username = 'Rajendra Kataria';
		//$content = 'Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, remaining essentially unchanged.';
		$date = date('d/m/Y',strtotime($date));
		$data = ['username' => $username, 'coursename' => $coursename, 'date' => $date];
		$pdf = PDF::loadView('front.myPDF', $data);

		//return $pdf->stream();
  
		//return $pdf->download('certificate.pdf');
		$destinationPath = public_path().'/upload/generatedPDF/';
		$filename = $examId.'_'.time().'.pdf';
		if($pdf->save($destinationPath.$filename)->stream('download.pdf')){
			$update = StudentExam::where("id", $examId)->update(array("certificate" => $filename));
			return $filename;
		} else {
			return 0;
		}
	}

    public function sms($phone, $otp)
	{
		/*$curl = curl_init();
		curl_setopt_array($curl, array(
			CURLOPT_URL => 'https://2factor.in/API/V1/e0268e22-d023-11eb-8089-0200cd936042/SMS/' . $phone . '/' . $otp . '/Template',

			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => "",
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 30,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => "GET",
			CURLOPT_POSTFIELDS => "{}",
		));

		$response = curl_exec($curl);
		$err = curl_error($curl);
		curl_close($curl);*/

		$curl = curl_init();

		curl_setopt_array($curl, array(
			CURLOPT_URL => 'http://2factor.in/API/V1/e0268e22-d023-11eb-8089-0200cd936042/ADDON_SERVICES/SEND/TSMS',
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'POST',
			CURLOPT_POSTFIELDS => array('From' => 'BRNYWD','To' => $phone,'TemplateName' => 'customtemplate','VAR1' => $otp),
			CURLOPT_HTTPHEADER => array(
				'token: gfLOYYpyKqGfyZ@vGMbeUhTcP%Erta#JnoDKWZffIVM@IVR'
			),
		));

		$response = curl_exec($curl);

		curl_close($curl);
		//echo $response;
	}

    public function smsWithTemplate($phone, $templateName, $var1='', $var2='', $var3='', $var4='')
	{
		/*$curl = curl_init();

		curl_setopt_array($curl, array(
			CURLOPT_URL => 'http://2factor.in/API/V1/e0268e22-d023-11eb-8089-0200cd936042/ADDON_SERVICES/SEND/TSMS',
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'POST',
			CURLOPT_POSTFIELDS => array('From'=>'BRNYWD', 'To'=>$phone, 'TemplateName'=>$templateName, 'VAR1'=>$var1, 'VAR2'=>$var2, 'VAR3'=>$var3, 'VAR4'=>$var4),
			CURLOPT_HTTPHEADER => array(
				'token: gfLOYYpyKqGfyZ@vGMbeUhTcP%Erta#JnoDKWZffIVM@IVR'
			),
		));
		$response = curl_exec($curl);
		curl_close($curl);*/

		$senderId = 'BRNYWD';

		$ch = curl_init();
		
		curl_setopt($ch, CURLOPT_URL, "http://2factor.in/API/V1/e0268e22-d023-11eb-8089-0200cd936042/ADDON_SERVICES/SEND/TSMS");
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, "From=$senderId&To=$phone&TemplateName=$templateName&VAR1=$var1&VAR2=$var2&VAR3=$var3&VAR4=$var4");
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		$response = curl_exec($ch);
		//print_r($response); exit;
		curl_close($ch);
		//echo $response;
	}

	public function sendEmail($to, $subject, $data)
	{
		$curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://api.sendgrid.com/v3/mail/send',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{"personalizations":[{"to":[{"email":"' . $to . '","name":"ByWood"}],"subject":"' . $subject . '"}],"content": [{"type": "text/html", "value": "<div style=\'background-color:#272b34 !important; text-align:center;display:block ;height: 43px;padding-top: 29px;\'>  <img src=\'http://brainscienceindia.in/public/front/assets/img/web-logo.png\' hight=\'50px\' width=\'100px\'> </div> <br>Hi  ' . $data['userName'] . ', <br><br> ' . $data['message'] . ' <br> <br>The Team <br> <br><span style=\'font-size:9px\'>You are receiving this email because  <br> <br><center style=\'font-size:9px\'>  Copyright © 2021 | Guruathome | All rights reserved </center></span>"}],"from":{"email":"<EMAIL>","name":"Guruathome"}}',
            CURLOPT_HTTPHEADER => array(
                'Authorization: Bearer *********************************************************************',
                'Content-Type: application/json'
            ),
        ));
        $response = curl_exec($curl);


        curl_close($curl);
	}



	public function sendMailtest($email, $stubject = NULL, $message = NULL)
    {

        require base_path("vendor/autoload.php");
        $mail = new PHPMailer(true);     // Passing `true` enables exceptions
       
        try {
            $mail->SMTPDebug = 0;
            $mail->isSMTP();
            $mail->Host = "smtp.gmail.com";
            $mail->Port = 587;
            // if(!empty($$attachment))
            // {
            //  $mail->addAttachment($attachment);
            // }
            $mail->SMTPSecure = "tls";
            $mail->SMTPAuth = true;
            $mail->Username = "<EMAIL>";
            $mail->Password = "trbcxmazrubpkfzt";
            $mail->addAddress($email, "User Name");
            $mail->Subject = $stubject;
            $mail->isHTML();
            $mail->Body = $message;
            $mail->setFrom("<EMAIL>");
            $mail->FromName = "Guru At Home ";

            if ($mail->send()) {
                return 1;
            } else {
                return 0;
            }
        } catch (Exception $e) {
            return 0;
        }
    }


	public function sendMail($email, $stubject = NULL, $message = NULL)
    {

        require base_path("vendor/autoload.php");
        $mail = new PHPMailer(true);     // Passing `true` enables exceptions
        try {
            $mail->SMTPDebug = 0;
            $mail->isSMTP();
            $mail->Host = "smtp.gmail.com";
            $mail->Port = 587;
            // if(!empty($$attachment))
            // {
            //  $mail->addAttachment($attachment);
            // }
            $mail->SMTPSecure = "tls";
            $mail->SMTPAuth = true;
            $mail->Username = "<EMAIL>";
            $mail->Password = "trbcxmazrubpkfzt";
            $mail->addAddress($email, "User Name");
            $mail->Subject = $stubject;
            $mail->isHTML();
            $mail->Body = $message;
            $mail->setFrom("<EMAIL>");
            $mail->FromName = "Guru At Home ";

            if ($mail->send()) {
                return 1;
            } else {
                return 0;
            }
        } catch (Exception $e) {
            return 0;
        }
    }

	  public function sendMailsss($email, $stubject = NULL, $message = NULL)
    {

        require base_path("vendor/autoload.php");
        $mail = new PHPMailer(true);     // Passing `true` enables exceptions
      //	dd($mail);
        try {
            $mail->SMTPDebug = 0;
            $mail->isSMTP();
            $mail->Host = "smtp.gmail.com";
            $mail->Port = 587;
            $mail->SMTPSecure = "tls";
            $mail->SMTPAuth = true;
            $mail->Username = "<EMAIL>";
            $mail->Password = "trbcxmazrubpkfzt";
            $mail->addAddress($email, "User Name");
            $mail->Subject = $stubject;
            $mail->isHTML();
            $mail->Body = $message;
            $mail->setFrom("<EMAIL>");
            $mail->FromName = "Guru At Home";
        //    print_r($mail->send());die;
            if ($mail->send()) {

                return 1;
            } else {
                return 0;
            }
        } catch (Exception $e) {
            return 0;
        }
    }
    public function mailsendtest()
    {
    	$email="<EMAIL>";
    	    	$stubject="stubject";
    	    	$message="message";
		dd($this->sendMailtest($email, $stubject, $message));
    }

	public function testemail(Request $request)
	{
		$otp = 1234;
		/*$msg = 'Email Send Successfully.';
		$data = array('username' => "User", 'remember_token' =>  $otp, 'msg' =>  $msg);
		Mail::send('emails.forgot_password', $data, function ($message) {
			$email = $_POST['email'];
			$message->to($email, 'From Guruathome')->subject('This is your testing mail');
			$message->from('<EMAIL>', 'Guruathome');
		});*/
		//$this->sendEmail('<EMAIL>', 'Guruathome: Testing', $data = array('userName' => 'Rajendra', 'message' => '<p>Thank you for connected at Guruathome,</p>'));

		$phone = '919588841525';
		$msg = 'SMS Send Successfully.';
		$this->sms($phone, $otp, $msg);
		return response()->json(['statusCode' => 200, 'message' => $msg, 'data' => ""]);
	}














	
	
	/*public function sms($phone, $otp)
	{
		$curl = curl_init();
		curl_setopt_array($curl, array(
			CURLOPT_URL => 'https://2factor.in/API/V1/42e8defc-0910-11ea-9fa5-0200cd936042/SMS/' . $phone . '/' . $otp . '/Template',
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => "",
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 30,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => "GET",
			CURLOPT_POSTFIELDS => "{}",
		));
		$response = curl_exec($curl);
		$err = curl_error($curl);
		curl_close($curl);
	}*/
	public function bulksms($message, $phone,$template='insecuresmsAT')
	{
		$To               = $phone;
		$VAR1             = $message;
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, "http://2factor.in/API/V1/42e8defc-0910-11ea-9fa5-0200cd936042/ADDON_SERVICES/SEND/TSMS");
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, "From=SECURE&To=$To&TemplateName=$template&VAR1=$VAR1");
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		$server_output = curl_exec($ch);
		curl_close($ch);
	}
	public function sendtemplatesms($phone,$template='insecuresmsAT',$var1,$var2,$var3)
	{
		$senderId="SECURE";
		if($template=="medicalsmsAT"  || $template=="medicalsmsUT" )
		{
			$senderId="MEDICL";
		}
		if($template=="insecuresmsAT"  || $template=="insecuresmsUT" )
		{
			$senderId="SECURE";
		}
		
		//	$var3=strval($var3);
		$ch = curl_init();
		
		curl_setopt($ch, CURLOPT_URL, "http://2factor.in/API/V1/42e8defc-0910-11ea-9fa5-0200cd936042/ADDON_SERVICES/SEND/TSMS");
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, "From=$senderId&To=$phone&TemplateName=$template&VAR1=$var1&VAR2=$var2&VAR3= $var3");
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		$server_output = curl_exec($ch);
		//print_r($server_output); exit;
		curl_close($ch);
	}
	public function gpslocation(Request $request)
	{
		$id=$request->id;
		$users = DB::table('shorturl')->where("id",$id)->first();
		echo "<script> window.location.href='".$users->fullurl."'; </script>";
	}




}
