<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Courses;
use App\Models\Coursefeature;
use App\Models\Coursefeq;
use App\Models\StudentClass;
use App\Models\StudentClassSubject;
use App\Models\Subject;
use App\Models\VideoTemp;
use App\Models\CoursePdf;
use App\Models\StudentApplyForDemo;
use AWS;
use App\Models\Category;
use App\Models\Planfrequencys;
use App\Models\Plan;

class CoursesController extends Controller
{
	public function index(Request $request)
	{
		//dd($request->all());
		$course = Courses::where('sort_id',0)->orderBy('sort_id', 'ASC')->first();
		if($course){
			$Course = Courses::findOrFail($course->id);
			$Course->sort_id = $course->id;
			$Course->update();
		}

		$class_type = ($request->class_type) ? $request->class_type : '';
		$class_id 	= ($request->class_id) ? $request->class_id : '';
		$subject_id = ($request->subject_id) ? $request->subject_id : '';
		$search		= ($request->search) ? $request->search : '';
		$classes = StudentClass::where('deleted',0)->where('status',1)->orderBy("id", "ASC")->get();
		if (!empty($class_id)) {
			$classSubject = StudentClassSubject::where("class_id", $class_id)->first();
			$subject_ids = !empty($classSubject['subject_ids']) ? explode(",", $classSubject['subject_ids']) : [];
			$subjects = Subject::whereIn("id",$subject_ids)->orderBy("title", "ASC")->get();
		} else {
			$subjects = Subject::orderBy("title", "ASC")->get();
		}
		$data = Courses::with('subject_data')
				 ->Where(function($query) use ($class_type) {
					if (isset($class_type) && !empty($class_type)) {
						$query->where("class_type", $class_type);
					}
				 })
				 ->Where(function($query) use ($class_id) {
					if (isset($class_id) && !empty($class_id)) {
						$query->whereRaw("find_in_set($class_id,class_ids)");
					}
				 })
				 ->Where(function($query) use ($subject_id) {
					if (isset($subject_id) && !empty($subject_id)) {
						$query->where("subject_id", $subject_id);
					}
				 })
				 ->Where(function($query) use ($search) {
					if (isset($search) && !empty($search)) {
						$query->where("name", 'like', "%" . $search . "%");
					}
				 })
				 ->where('deleted',0)->orderBy('id', 'desc');

		$totalData = $data->count();
		$data = $data->paginate(10);
		if(!empty($data)){

			foreach ($data as $d) {

				if($d->category_id!=0)
				{
					$d->category_name = isset($d->category->name) ? $d->category->name :'-';
				}
				else
				{
					$d->category_name = "-";
				}
			}
		}
		
		return view('admin.courses.index', compact('classes','subjects','data','totalData'));
	}

	public function create()
	{
		$classes = StudentClass::where('deleted',0)->where('status',1)->orderBy("id", "ASC")->get();
		$category =Category::where('deleted',0)->where('status',1)->orderBy("id", "ASC")->get();
		return view('admin.courses.create', compact('classes','category'));
	}

	public function store(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'image' => 'required',
			'name' => 'required',
			'overview' => 'required',
			'category_id'=>'required',
			//'video' => 'required|mimes:mp4',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			//dd($request->all());
			$file = $request->file('image');
			$video = $request->file('video');
			$pdf = $request->file('pdf');
			$category_id = $request->category_id;
			$filename = $videofilename = $filenamepdf = NULL;
			$destinationPath = public_path().'/course/';
			if($file){
				$originalFile = $file->getClientOriginalName();
				$filename = strtotime(date('Y-m-d-H:isa')).$originalFile;
				$file->move($destinationPath, $filename);
			}
			if($video){
				$videoOriginalName = $video->getClientOriginalName();
				//$videofilename=rand(111,999).strtotime(date('Y-m-d-H:isa')).$videoOriginalName;
				$videofilename = time() . "_org.mp4";
				$video->move($destinationPath, $videofilename);
			}
			if($pdf){
				$originalFilepdf = $pdf->getClientOriginalName();
				$filenamepdf=rand(111,999).strtotime(date('Y-m-d-H:isa')).$originalFilepdf;
				$pdf->move($destinationPath, $filenamepdf);
			}	
				
			$Courses = new Courses();
			$Courses->category_id = !empty($request->category_id) ? $request->category_id :'0';
			$Courses->class_type = !empty($request->get('class_type')) ?$request->get('class_type') :'301';
			$Courses->class_ids = !empty($request->class_ids) ? implode(",", $request->class_ids) : '';
			$Courses->subject_id = !empty($request->get('subject_id')) ? $request->get('subject_id') :'';
			$Courses->name = $request->get('name');
			$Courses->sort_description = $request->get('sort_description');
			$Courses->overview = $request->get('overview');
			$Courses->video_duration = !empty($request->get('video_duration')) ?$request->get('video_duration') :'';
			$Courses->image = $filename;
			$Courses->video = $videofilename;
			$Courses->pdf = $filenamepdf;
			$Courses->category_id = $category_id;
			$Courses->isFree = !empty($request->get('free')) ? $request->get('free') :'';
			$Courses->save();
			$courseId=$Courses->id;
			if($courseId){
				$Course = Courses::findOrFail($courseId);
				$Course->sort_id = $courseId;
				$Course->update();
			}

			foreach ($request->plan as $fkey => $fvalue) {
			$countryarr = array();
			$plan = new Plan();
			$plan->course_id = $Courses->id;
			$plan->category_id = $category_id;
			$plan->title = $request->title[$fkey];
			$plan->description = $request->plan_description[$fkey];
			$plan->plan_type = $request->plan_type[$fkey];
			$plan->save();
			foreach ($request->one_session[$fkey+1] as $okey => $ovalue) {
				if(!in_array($request->currency[$fkey+1][$okey], $countryarr)){
				   $planfreq = new Planfrequencys();
				   $planfreq->course_id = $Courses->id;
				   $planfreq->category_id = $category_id;
				   $planfreq->plan_id = $plan->id;
				   $planfreq->start_range = $request->start_range[$fkey+1][$okey];
			       $planfreq->end_range = $request->end_range[$fkey+1][$okey];
				   $planfreq->currency = $request->currency[$fkey+1][$okey];
				   $planfreq->one_session = $ovalue;
				   $planfreq->second_session = $request->second_session[$fkey+1][$okey];
				   $planfreq->third_session = $request->third_session[$fkey+1][$okey];
				   $planfreq->four_session = $request->four_session[$fkey+1][$okey];
				   $planfreq->five_session = $request->five_session[$fkey+1][$okey];
				   $planfreq->six_session = $request->six_session[$fkey+1][$okey];
				   $planfreq->save();
				   $countryarr[] = $request->currency[$fkey+1][$okey];
				}
				
			}
		}
		    
			// $feature = !empty($request->get('featu')) ? $request->get('featu') : '';
			// dd(($feature));
			// if(!empty($feature))
			// {
			// 	for ($i=0; $i<count($feature); $i++){
			// 		$Coursefeature=new Coursefeature();
			// 		$Coursefeature->courseId =$courseId;    
			// 		$Coursefeature->feature =$request->input('featu')[$i];    
			// 		$Coursefeature->save(); 
			// 	}
			// }
			// $faqTitle = ($request->get('faqTitle')) ? $request->get('faqTitle') : [];
			// if(!empty($faqTitle))
			// {
			// 	for ($i=0; $i<count($faqTitle); $i++){
			// 		$Coursefeq=new Coursefeq();
			// 		$Coursefeq->courseId =$courseId;    
			// 		$Coursefeq->title =$request->input('faqTitle')[$i];
			// 		$Coursefeq->contant =$request->input('faqcontant')[$i];    
			// 		$Coursefeq->save(); 
			// 	}
			// }
			
			\Session::flash('msg', 'Course Added Successfully.');
			return back();
		}
	}

	public function show(Courses $courses)
	{
		//
	}

	public function edit(Courses $courses, $id)
	{
		// dd($id);
		$Coursefeq = Coursefeq::where('courseId',$id)->get();
		$Coursefeature = Coursefeature::where('courseId',$id)->get();
	  
		$course = Courses::findOrFail($id);
		//$course = Courses::with('course_features_data','course_faqs_data')->findOrFail($id);
		$classes = StudentClass::orderBy("id", "ASC")->get();
		$class_ids = !empty($course->class_ids) ? explode(",", $course->class_ids) : NULL;
		//$class_ids = $course->class_ids;
		if (!empty($class_ids)) {
			$getClassSubjectData = StudentClassSubject::whereIn("class_id", $class_ids)->get();
			$subject_ids_arr = [];
			$i=0;
			foreach ($getClassSubjectData as $classSubject) {
				$subject_ids = !empty($classSubject['subject_ids']) ? explode(",", $classSubject['subject_ids']) : [];
				foreach ($subject_ids as $key => $value) {
					$subject_ids_arr[$i]=$value;
					$i++;
				}
			}
			array_unique($subject_ids_arr);
			$subjects = Subject::whereIn("id",$subject_ids_arr)->orderBy("title", "ASC")->get();
		} else {
			$subjects = Subject::orderBy("title", "ASC")->get();
		}
		//dd($course);
		$category =Category::where('deleted',0)->where('status',1)->orderBy("id", "ASC")->get();
		$plans1 = Plan::where('course_id',$id)->where('plan_type',1)->first();
		$plans2 = Plan::where('course_id',$id)->where('plan_type',2)->first();
		$plans3 = Plan::where('course_id',$id)->where('plan_type',3)->first();
		$pid = isset($plans1->id) ? $plans1->id:"";
		$pid2 = isset($plans2->id) ? $plans2->id:"";
		$pid3 = isset($plans3->id) ? $plans3->id:"";
		$frequency1 = Planfrequencys::where(['course_id'=>$course->id,'plan_id'=>$pid])->get();
		$frequency2 = Planfrequencys::where(['course_id'=>$course->id,'plan_id'=>$pid2])->get();
		$frequency3 = Planfrequencys::where(['course_id'=>$course->id,'plan_id'=>$pid3])->get();
	 // dd($frequency3);
		return view('admin.courses.edit',compact('frequency1','frequency2','frequency3','course','Coursefeq','Coursefeature','classes','subjects','category','plans1','plans2','plans3'));
	}

	public function update(Request $request, Courses $courses, $id)
	{

		//ak $s3 = AWS::createClient('s3');
		
		$validator = Validator::make($request->all(), [
		
			'name' => 'required',
			'overview' => 'required',
			'category_id'=>'required',
			//'video' => 'mimes:mp4',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$Courses = Courses::findOrFail($id);
			//dd($Courses);
			$file = $request->file('image');
			$video = $request->file('video');
			$pdf = $request->file('pdf');
			$category_id  = $request->category_id;
			$destinationPath = public_path().'/course/';
			if($file){
				$originalFile = $file->getClientOriginalName();
				$filename = time() . $originalFile;
				$file->move($destinationPath, $filename);
			}else{
			   $filename=$Courses->image;  
			}
			if($video){
				$videoOriginalName = $video->getClientOriginalName();
				//$videofilename=rand(111,999).strtotime(date('Y-m-d-H:isa')).$videoOriginalName;
				$videofilename = time() . "_org.mp4";
				$video->move($destinationPath, $videofilename);
			//ak	$video_1filename = $video_2filename = $video_3filename = NULL;
			//ak	$uploads3 = $uploads3v1 = $uploads3v2 = $uploads3v3 = 0;
			}else{
			   $videofilename = $Courses->video;
			   $video_1filename = $Courses->video_1;
			   $video_2filename = $Courses->video_2;
			   $video_3filename = $Courses->video_3;
			   $uploads3 = $Courses->uploads3;
			   $uploads3v1 = $Courses->uploads3v1;
			   $uploads3v2 = $Courses->uploads3v2;
			   $uploads3v3 = $Courses->uploads3v3;
			}
			if($pdf){
				$originalFilepdf = $pdf->getClientOriginalName();
				$filenamepdf= time() . $originalFilepdf;
				$pdf->move($destinationPath, $filenamepdf);
			}else{
				$filenamepdf=$Courses->pdf;
			}
			
			if($video){
				if(!empty($Courses->video) && file_exists( public_path().'/course/'.$Courses->video )) {
					unlink( public_path().'/course/'.$Courses->video );
				}
				/*
				if(!empty($Courses->video)){
					$videoName = $Courses->video;
					$s3->deleteObject(array(
				        'Bucket' => env('AWS_BUCKET'),
				        'Key'    => $videoName
				    ));
					$s3->deleteObject(array(
				        'Bucket' => env('AWS_BUCKET'),
				        'Key'    => "mobile_app_data/".$videoName
				    ));
				}*/
				if(!empty($Courses->video_1) && file_exists( public_path().'/course/'.$Courses->video_1 ) && $Courses->video_1!='NA') {
					unlink( public_path().'/course/'.$Courses->video_1 );
				}
				/*
				if(!empty($Courses->video_1)){
					$videoName1 = $Courses->video_1;
					$s3->deleteObject(array(
				        'Bucket' => env('AWS_BUCKET'),
				        'Key'    => "mobile_app_data/".$videoName1
				    ));
				}
				*/
				if(!empty($Courses->video_2) && file_exists( public_path().'/course/'.$Courses->video_2 ) && $Courses->video_2!='NA') {
					unlink( public_path().'/course/'.$Courses->video_2 );
				}
				/*
				if(!empty($Courses->video_2)){
					$videoName2 = $Courses->video_2;
					$s3->deleteObject(array(
				        'Bucket' => env('AWS_BUCKET'),
				        'Key'    => "mobile_app_data/".$videoName2
				    ));
				}
				*/
				if(!empty($Courses->video_3) && file_exists( public_path().'/course/'.$Courses->video_3 ) && $Courses->video_3!='NA') {
					unlink( public_path().'/course/'.$Courses->video_3 );
				}
				/*
				if(!empty($Courses->video_3)){
					$videoName3 = $Courses->video_3;
					$s3->deleteObject(array(
				        'Bucket' => env('AWS_BUCKET'),
				        'Key'    => "mobile_app_data/".$videoName3
				    ));
				}*/

				$deleteVideoTemp = VideoTemp::where('courseId',$id)->where('lessionId',0)->where('topicId',0)->delete();
			}
			$Courses->category_id = !empty($request->category_id) ? $request->category_id :'0';
			$Courses->class_type = !empty($request->get('class_type')) ?$request->get('class_type') :'';
			$Courses->class_ids = !empty($request->class_ids) ? implode(",", $request->class_ids) : NULL;
			$Courses->subject_id = empty($request->get('subject_id')) ?$request->get('subject_id') :'';
			$Courses->name = $request->get('name');
			$Courses->overview = $request->get('overview');
			$Courses->video_duration = !empty($request->get('video_duration')) ?$request->get('video_duration') :'';
			$Courses->image = $filename;
			$Courses->video = $videofilename;
			$Courses->video_1 = $video_1filename;
			$Courses->video_2 = $video_2filename;
			$Courses->video_3 = $video_3filename;
			$Courses->pdf = $filenamepdf;
				$Courses->category_id = $category_id;
			$Courses->isFree = !empty($request->get('free')) ? $request->get('free') :'';
			$Courses->uploads3 = $uploads3;
			$Courses->uploads3v1 = $uploads3v1;
			$Courses->uploads3v2 = $uploads3v2;
			$Courses->uploads3v3 = $uploads3v3;
			$Courses->sort_description = !empty($request->get('sort_description')) ? $request->get('sort_description') : $Courses->sort_description;
			$Courses->save();
			 $courseId=$id;
			 $planid_arr = Plan::where("course_id",$courseId)->pluck('id')->toArray();
			 $courses_session = Plan::where("course_id",$courseId)->delete();
			 $courses_session = Planfrequencys::where("course_id",$courseId)->delete();
			 if(isset($request->plan)){
			 	foreach ($request->plan as $fkey => $fvalue) {
			 


			$countryarr = array();
			$plan = new Plan();
			$plan->course_id = $Courses->id;
			$plan->title = $request->title[$fkey];
			$plan->description = $request->plan_description[$fkey];
			$plan->plan_type = $request->plan_type[$fkey];
			$plan->save();
			$studentapp = StudentApplyForDemo::where('plan_id',$planid_arr[$fkey])->update(['plan_id'=>$plan->id]);
			foreach ($request->one_session[$fkey+1] as $okey => $ovalue) {
				// if(!in_array($request->currency[$fkey+1][$okey], $countryarr)){
				   $planfreq = new Planfrequencys();
				   $planfreq->course_id = $Courses->id;
				   $planfreq->plan_id = $plan->id;
				   $planfreq->start_range = $request->start_range[$fkey+1][$okey];
			       $planfreq->end_range = $request->end_range[$fkey+1][$okey];
				   $planfreq->currency = $request->currency[$fkey+1][$okey];
				   $planfreq->one_session = $ovalue;
				   $planfreq->second_session = $request->second_session[$fkey+1][$okey];
				   $planfreq->third_session = $request->third_session[$fkey+1][$okey];
				   $planfreq->four_session = $request->four_session[$fkey+1][$okey];
				   $planfreq->five_session = $request->five_session[$fkey+1][$okey];
				   $planfreq->six_session = $request->six_session[$fkey+1][$okey];
				   $planfreq->save();
				   // $countryarr[] = $request->currency[$fkey+1][$okey];
				
				
			}
		}
			 }
			
		   
			// Coursefeature::where('courseId',$id)->delete();
			// Coursefeq::where('courseId',$id)->delete();
			// $feature = ($request->get('featu')) ? $request->get('featu') : [];
		
			// for ($i=0; $i<count($feature); $i++){
			// 	$Coursefeature=new Coursefeature();
			// 	$Coursefeature->courseId =$courseId;    
			// 	$Coursefeature->feature =$request->input('featu')[$i];    
			// 	$Coursefeature->save(); 
			// }
			// $faqTitle = ($request->get('faqTitle')) ? $request->get('faqTitle') : [];
			// for ($i=0; $i<count($faqTitle); $i++){
			// 	$Coursefeq=new Coursefeq();
			// 	$Coursefeq->courseId =$courseId;    
			// 	$Coursefeq->title =$request->input('faqTitle')[$i];
			// 	$Coursefeq->contant =$request->input('faqcontant')[$i];    
			// 	$Coursefeq->save(); 
			// }

			\Session::flash('msg', 'Course Updated Successfully.');
			return redirect('/admin/courses');
		}

	}

	public function delete($id)
	{
		$data = Courses::findOrFail($id);
		$data->deleted=1;
		$data->update();

	    return redirect()->back();
	}

	public function updateStatus($id,$status)
	{
		$data = Courses::findOrFail($id);
		$data->status=$status;
		$data->update();

	    return redirect()->back();
	}

	public function sortUp($id,$sort_id)
	{
		$sort_id = $sort_id - 1;
		$course = Courses::where("sort_id", $sort_id);
		//$course->sort_id = $sort_id + 1;
		$course->update(array("sort_id"=>$sort_id + 1));
		$course1 = Courses::findOrFail($id);
		$course1->sort_id = $sort_id;
		$course1->save();

	    return redirect()->back();
	}

	public function sortDown($id,$sort_id)
	{
		$sort_id = $sort_id + 1;
		$course = Courses::where("sort_id", $sort_id);
		//$course->sort_id = $sort_id - 1;
		$course->update(array("sort_id"=>$sort_id - 1));
		$course1 = Courses::findOrFail($id);
		$course1->sort_id = $sort_id;
		$course1->save();

	    return redirect()->back();
	}

	public function convertVideo()
	{
		$data = VideoTemp::where('courseId','!=',0)->where('lessionId',0)->where('topicId',0)->orderBy('id', 'ASC')->get();
		
		return view('admin.courses.convertVideo', compact('data'));
	}

	public function approveVideo($id)
	{
		$videoTemp = VideoTemp::where('id',$id)->first();
		$courseId = $videoTemp->courseId;
		$low_status = $videoTemp->low_status;
		$med_status = $videoTemp->med_status;
		$high_status = $videoTemp->high_status;
		$course = Courses::findOrFail($courseId);
		if ($low_status == 1) {
			$course->video_1 = $videoTemp->low_video;
		}
		if ($med_status == 1) {
			$course->video_2 = $videoTemp->med_video;
		}
		if ($high_status == 1) {
			$course->video_3 = $videoTemp->high_video;
		}
		$course->update();
		
		$delete = VideoTemp::where('id',$id)->delete();

		\Session::flash('msg', 'Converted Video Approved Successfully.');
	    return redirect('/admin/courses/convertVideo');
	}

	public function imgremove($id)
	{
		$course = Courses::findOrFail($id);
		if(file_exists( public_path().'/course/'.$course->image )) {
			unlink( public_path().'/course/'.$course->image );
		}
		$course->image = NULL;
		$course->update();

		\Session::flash('msg', 'Course Image Removed Successfully.');
	    return redirect()->back();
	}

	public function vidremove($id, $video_type)
	{
		$s3 = AWS::createClient('s3');
		
		$course = Courses::findOrFail($id);
		if(!empty($course->video) && file_exists( public_path().'/course/'.$course->video )) {
			unlink( public_path().'/course/'.$course->video );
		}
		if(!empty($course->video_1) && file_exists( public_path().'/course/'.$course->video_1 ) && $course->video_1!='NA') {
			unlink( public_path().'/course/'.$course->video_1 );
		}
		if(!empty($course->video_2) && file_exists( public_path().'/course/'.$course->video_2 ) && $course->video_2!='NA') {
			unlink( public_path().'/course/'.$course->video_2 );
		}
		if(!empty($course->video_3) && file_exists( public_path().'/course/'.$course->video_3 ) && $course->video_3!='NA') {
			unlink( public_path().'/course/'.$course->video_3 );
		}
		if(!empty($course->video)){
			$videoName = $course->video;
			$s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => $videoName
		    ));
			$s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => "mobile_app_data/".$videoName
		    ));
		}
		if(!empty($course->video_1)){
			$videoName1 = $course->video_1;
			$s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => "mobile_app_data/".$videoName1
		    ));
		}
		if(!empty($course->video_2)){
			$videoName2 = $course->video_2;
			$s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => "mobile_app_data/".$videoName2
		    ));
		}
		if(!empty($course->video_3)){
			$videoName3 = $course->video_3;
			$s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => "mobile_app_data/".$videoName3
		    ));
		}
		$course->video = NULL;
		$course->video_1 = NULL;
		$course->video_2 = NULL;
		$course->video_3 = NULL;
		$course->uploads3 = 0;
		$course->uploads3v1 = 0;
		$course->uploads3v2 = 0;
		$course->uploads3v3 = 0;
		$course->processtatus = 0;
		$course->starttime = NULL;
		$course->endtime = NULL;
		$course->update();

		$deleteVideoTemp = VideoTemp::where('courseId',$id)->where('lessionId',0)->where('topicId',0)->delete();

		\Session::flash('msg', 'Course Video Removed Successfully.');
	    return redirect()->back();
	}

	public function pdfremove($id)
	{
		$course = Courses::findOrFail($id);
		if(file_exists( public_path().'/course/'.$course->pdf )) {
			unlink( public_path().'/course/'.$course->pdf );
		}
		$course->pdf = NULL;
		$course->update();

		\Session::flash('msg', 'Course PDF Removed Successfully.');
	    return redirect()->back();
	}

	public function updateishome(Request $request)
	{
		$id = $request->courseId;
		$course = Courses::findOrFail($id);
		if($course->isHome==501){
			$course->isHome=502;
		}else{
			$course->isHome=501;
		}
		$course->update();
		$message = '<div class="alert alert-info"><a class="close" data-dismiss="alert">×</a><strong>Course IsHome Status Updated Successfully.</strong></div>';
	    return $message;
	}

	public function getCoursePdfs(Request $request)
	{
		$courses = Courses::orderBy('sort_id', 'ASC')->get();
		
		$course_id = ($request->course_id) ? $request->course_id : '';

		$data = CoursePdf::with('course_data')
				 ->Where(function($query) use ($course_id) {
					if (isset($course_id) && !empty($course_id)) {
						$query->where("course_id", $course_id);
					}
				 })
				 ->orderBy('sort_id', 'ASC');
		$totalData = $data->count();
		$data = $data->paginate(50);

		return view('admin.courses.course_pdfs', compact('courses','data','totalData'));
	}

	public function storePdf(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'course_id' => 'required',
			//'pdf' => 'required|mimes:pdf',
			'pdf' => 'required',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$pdf = $request->file('pdf');

			$pdfUrl = NULL;
			$destinationPath = public_path().'/course/';
			if($pdf){
				$originalFilepdf = $pdf->getClientOriginalName();
				$pdfFilename = 'course_'.time().$originalFilepdf;
				$pdf->move($destinationPath, $pdfFilename);
				//S3 Upload
				$s3 = AWS::createClient('s3');
				$s3->putObject(array(
					'Bucket'     => env('AWS_BUCKET'),
					'Key'        => "mobile_app_data/".$pdfFilename,
					'SourceFile' => $destinationPath.$pdfFilename,
				));
				$pdfUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/mobile_app_data/'. $pdfFilename;
				if(!empty($pdf) && file_exists( $destinationPath.$pdfFilename )) {
					unlink( $destinationPath.$pdfFilename );
				}
			}
				
			$coursePdf = new CoursePdf();
			$coursePdf->course_id = $request->get('course_id');
			$coursePdf->pdf = $pdfUrl;
			$coursePdf->save();
			$coursePdfId=$coursePdf->id;
			if($coursePdfId){
				$Course = CoursePdf::findOrFail($coursePdfId);
				$Course->sort_id = $coursePdfId;
				$Course->update();
			}
			
			\Session::flash('msg', 'Course PDF Added Successfully.');
			return back();
		}
	}

	public function pdfedit($id)
	{
		$courses = Courses::orderBy('sort_id', 'ASC')->get();
		$coursePdf = CoursePdf::findOrFail($id);
		//dd($courses);

		return view('admin.courses.course_pdf_edit',compact('courses','coursePdf'));
	}

	public function updatePdf(Request $request, $id)
	{
		$s3 = AWS::createClient('s3');
		
		$validator = Validator::make($request->all(), [
			'course_id' => 'required',
			//'pdf' => 'mimes:pdf',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$coursePdf = CoursePdf::findOrFail($id);

			$pdf = $request->file('pdf');

			$destinationPath = public_path().'/course/';
			if($pdf){
				$originalFilepdf = $pdf->getClientOriginalName();
				$pdfFilename = 'course_'.time().$originalFilepdf;
				$pdf->move($destinationPath, $pdfFilename);
				//S3 Upload
				$s3 = AWS::createClient('s3');
				$s3->putObject(array(
					'Bucket'     => env('AWS_BUCKET'),
					'Key'        => "mobile_app_data/".$pdfFilename,
					'SourceFile' => $destinationPath.$pdfFilename,
				));
				$pdfUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/mobile_app_data/'. $pdfFilename;
				if(!empty($pdf) && file_exists( $destinationPath.$pdfFilename )) {
					unlink( $destinationPath.$pdfFilename );
				}
			}else{
				$pdfUrl = $coursePdf->pdf;
			}
			
			if($pdf){
				if(!empty($coursePdf->pdf) && file_exists( public_path().'/course/'.$coursePdf->pdf )) {
					unlink( public_path().'/course/'.$coursePdf->pdf );
				}
				if(!empty($coursePdf->pdf)){
					$pdfName = $coursePdf->pdf;
					$s3->deleteObject(array(
				        'Bucket' => env('AWS_BUCKET'),
				        'Key'    => "mobile_app_data/".$pdfName
				    ));
				}
			}

			$coursePdf->course_id = $request->get('course_id');
			$coursePdf->pdf = $pdfUrl;
			$coursePdf->save();

			\Session::flash('msg', 'Course PDF Updated Successfully.');
			return redirect('/admin/courses/pdfs');
		}

	}

	public function coursepdfremove($id)
	{
		$s3 = AWS::createClient('s3');
		$coursePdf = CoursePdf::findOrFail($id);
		if(file_exists( public_path().'/course/'.$coursePdf->pdf )) {
			unlink( public_path().'/course/'.$coursePdf->pdf );
		}
		if(!empty($coursePdf->pdf)){
			$pdfUrl = explode("https://s3.ap-south-1.amazonaws.com/video.brainywoodindia.com/mobile_app_data/", $coursePdf->pdf);
			$pdfName = $pdfUrl[1];
			//dd($pdfName);
			$s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => "mobile_app_data/".$pdfName
		    ));
		}
		$coursePdf->pdf = NULL;
		$coursePdf->update();

		\Session::flash('msg', 'Course PDF Removed Successfully.');
	    return redirect()->back();
	}

	public function updateStatusPdf($id,$status)
	{
		$data = CoursePdf::findOrFail($id);
		$data->status=$status;
		$data->update();

	    return redirect()->back();
	}

	public function sortUpPdf($id,$sort_id)
	{
		$sort_id = $sort_id - 1;
		$course = CoursePdf::where("sort_id", $sort_id);
		//$course->sort_id = $sort_id + 1;
		$course->update(array("sort_id"=>$sort_id + 1));
		$course1 = CoursePdf::findOrFail($id);
		$course1->sort_id = $sort_id;
		$course1->save();

	    return redirect()->back();
	}

	public function sortDownPdf($id,$sort_id)
	{
		$sort_id = $sort_id + 1;
		$course = CoursePdf::where("sort_id", $sort_id);
		//$course->sort_id = $sort_id - 1;
		$course->update(array("sort_id"=>$sort_id - 1));
		$course1 = CoursePdf::findOrFail($id);
		$course1->sort_id = $sort_id;
		$course1->save();

	    return redirect()->back();
	}

}