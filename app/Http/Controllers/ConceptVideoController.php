<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Conceptvideo;
use App\Models\VideoTemp;
use AWS;


class ConceptVideoController extends Controller
{
	public function index()
	{
		$data = Conceptvideo::where('deleted',0)->orderBy('sort_id', 'ASC');
		$totalData = $data->count();
		$data = $data->paginate(50);

		return view('admin.conceptvideos.index', compact('data','totalData'));
	}
	
	public function create()
	{
		return view('admin.conceptvideos.create');
	}

	public function store(Request $request)
	{
		$s3 = AWS::createClient('s3');
		
		$validator = Validator::make($request->all(), [
			'name' => 'required',
			'image' => 'required',
			'video' => 'required|mimes:mp4',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		}else { 
			//echo '<pre />'; print_r($request->all()); die;
			$imagefilename = $videofilename = $video_1filename = $video_2filename = $video_3filename = NULL;
			$file = $request->file('image');
			$video = $request->file('video');

			$destinationPath = public_path() . '/upload/conceptvideos/';
			if($file){
				$imageOriginalName = $file->getClientOriginalName();
				$imagefilename = time() . $imageOriginalName;
				$file->move($destinationPath, $imagefilename);
			}
			if($video){
				$originalFile = $video->getClientOriginalName();
				//$videofilename = strtotime(date('Y-m-d H:isa')).$originalFile;
				$videofilename = time() . "_org.mp4";
				$video->move($destinationPath, $videofilename);
			}
			
			$conceptvideo = new Conceptvideo();
			$conceptvideo->name = $request->get('name');
			$conceptvideo->video_thumb = $imagefilename;
			$conceptvideo->video = $videofilename;
			$conceptvideo->paid = ($request->get('paid')) ? $request->get('paid') : 0;
			$conceptvideo->status = 1;
			$conceptvideo->save();
			$conceptvideoId = $conceptvideo->id;
			if($conceptvideoId){
				$concept = Conceptvideo::findOrFail($conceptvideoId);
				$concept->sort_id = $conceptvideoId;
				$concept->save();
			}

			/*Upload on S3 Start*/
			$concept = Conceptvideo::where("id",$conceptvideoId)->first();

			if(!empty($concept->video)){
				$newvideo = "concept_".$concept->id."_".time()."_org.mp4";
				$videopath = $destinationPath . $concept->video;
				$s3->putObject(array(
				    'Bucket'     => env('AWS_BUCKET'),
				    'Key'        => $newvideo,
				    'SourceFile' => $videopath,
				));
				$s3->putObject(array(
				    'Bucket'     => env('AWS_BUCKET'),
				    'Key'        => "mobile_app_data/".$newvideo,
				    'SourceFile' => $videopath,
				));
				if(!empty($video) && file_exists( $videopath )) {
					unlink( $videopath );
				}
				$update = Conceptvideo::where("id",$concept->id)->update(["video" => $newvideo, "uploads3" => 1]);
			}
			/*Upload on S3 End*/

			\Session::flash('msg', 'Concept Video Added Successfully.');
			return back();
		}
	}

	public function show(Conceptvideo $conceptvideos)
	{
		//
	}

	public function edit(Conceptvideo $conceptvideos, $id)
	{
		$data = Conceptvideo::findOrFail($id);

		return view('admin.conceptvideos.edit',compact('data'));
	}

	public function update(Request $request, Conceptvideo $conceptvideos, $id)
	{
		$s3 = AWS::createClient('s3');
		
		$validator = Validator::make($request->all(), [
			'name' => 'required',
			//'image' => 'required',
			'video' => 'mimes:mp4',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			//echo '<pre />'; print_r($request->all()); die;
			$conceptvideo = Conceptvideo::findOrFail($id);

			$file = $request->file('image');
			$video = $request->file('video');

			$destinationPath = public_path() . '/upload/conceptvideos/';
			if($file){
				$imageOriginalName = $file->getClientOriginalName();
				$imagefilename = rand(111,999).strtotime(date('Y-m-d-H:isa')).$imageOriginalName;
				$file->move($destinationPath, $imagefilename);
			}else{
				$imagefilename = $conceptvideo->video_thumb;  
			}
			if($video){
				$videoOriginalName = $video->getClientOriginalName();
				//$videofilename = rand(111,999).strtotime(date('Y-m-d-H:isa')).$videoOriginalName;
				$videofilename = time() . "_org.mp4";
				$video->move($destinationPath, $videofilename);
			    $uploads3 = 0;
			}else{
				$videofilename = $conceptvideo->video;  
			    $uploads3 = $conceptvideo->uploads3;
			}

			if($video){
				if(!empty($conceptvideo->video) && file_exists( public_path().'/upload/conceptvideos/'.$conceptvideo->video )) {
					unlink( public_path().'/upload/conceptvideos/'.$conceptvideo->video );
				}
				if(!empty($conceptvideo->video)){
					$videoName = $conceptvideo->video;
					$s3->deleteObject(array(
				        'Bucket' => env('AWS_BUCKET'),
				        'Key'    => $videoName
				    ));
					$s3->deleteObject(array(
				        'Bucket' => env('AWS_BUCKET'),
				        'Key'    => "mobile_app_data/".$videoName
				    ));
				}
				$conceptvideo->processtatus = 0;
				$conceptvideo->starttime = NULL;
				$conceptvideo->endtime = NULL;
			}
			$conceptvideo->name = $request->get('name');
			$conceptvideo->video_thumb = $imagefilename;
			$conceptvideo->video = $videofilename;
			$conceptvideo->paid = ($request->get('paid')) ? $request->get('paid') : 0;
			$conceptvideo->uploads3 = $uploads3;
			$conceptvideo->save();
			$conceptvideoId = $conceptvideo->id;

			/*Upload on S3 Start*/
			$concept = Conceptvideo::where("id",$conceptvideoId)->first();
			if($video){
				if(!empty($concept->video) && $concept->uploads3==0){
					$newvideo = "concept_".$concept->id."_".time()."_org.mp4";
					$videopath = $destinationPath . $concept->video;
					$s3->putObject(array(
					    'Bucket'     => env('AWS_BUCKET'),
					    'Key'        => $newvideo,
					    'SourceFile' => $videopath,
					));
					$s3->putObject(array(
					    'Bucket'     => env('AWS_BUCKET'),
					    'Key'        => "mobile_app_data/".$newvideo,
					    'SourceFile' => $videopath,
					));
					if(!empty($video) && file_exists( $videopath )) {
						unlink( $videopath );
					}
					$update = Conceptvideo::where("id",$concept->id)->update(["video" => $newvideo, "uploads3" => 1]);
				}
			}
			/*Upload on S3 End*/

			\Session::flash('msg', 'Concept Video Updated Successfully.');
			return redirect('/admin/conceptvideos');
		}

	}

	public function delete($id)
	{
		$conceptvideo = Conceptvideo::findOrFail($id);
		$conceptvideo->deleted=1;
		$conceptvideo->update();

		return redirect('/admin/conceptvideos');
	}

	public function updateStatus($id,$status)
	{
		$conceptvideo = Conceptvideo::findOrFail($id);
		$conceptvideo->status=$status;
		$conceptvideo->update();

		return redirect('/admin/conceptvideos');
	}

	public function sortUp($id,$sort_id)
	{
		$sort_id = $sort_id - 1;
		$conceptvideo = Conceptvideo::where("sort_id", $sort_id);
		//$conceptvideo->sort_id = $sort_id + 1;
		$conceptvideo->update(array("sort_id"=>$sort_id + 1));
		$conceptvideo1 = Conceptvideo::findOrFail($id);
		$conceptvideo1->sort_id = $sort_id;
		$conceptvideo1->save();

	    return redirect('/admin/conceptvideos');
	}

	public function sortDown($id,$sort_id)
	{
		$sort_id = $sort_id + 1;
		$conceptvideo = Conceptvideo::where("sort_id", $sort_id);
		//$conceptvideo->sort_id = $sort_id - 1;
		$conceptvideo->update(array("sort_id"=>$sort_id - 1));
		$conceptvideo1 = Conceptvideo::findOrFail($id);
		$conceptvideo1->sort_id = $sort_id;
		$conceptvideo1->save();

	    return redirect('/admin/conceptvideos');
	}

	public function convertVideo()
	{
		$data = VideoTemp::where('conceptvideoId','!=',0)->orderBy('id', 'ASC')->get();
		
		return view('admin.conceptvideos.convertVideo', compact('data'));
	}

	public function approveVideo($id)
	{
		$videoTemp = VideoTemp::where('id',$id)->first();
		$conceptvideoId = $videoTemp->conceptvideoId;
		$low_status = $videoTemp->low_status;
		$med_status = $videoTemp->med_status;
		$high_status = $videoTemp->high_status;
		$conceptvideo = Conceptvideo::findOrFail($conceptvideoId);
		if ($low_status == 1) {
			$conceptvideo->video_1 = $videoTemp->low_video;
		}
		if ($med_status == 1) {
			$conceptvideo->video_2 = $videoTemp->med_video;
		}
		if ($high_status == 1) {
			$conceptvideo->video_3 = $videoTemp->high_video;
		}
		$conceptvideo->update();
		
		$delete = VideoTemp::where('id',$id)->delete();

		\Session::flash('msg', 'Converted Video Approved Successfully.');
	    return redirect('/admin/conceptvideos/convertVideo');
	}

	public function imgremove($id)
	{
		$conceptvideo = Conceptvideo::findOrFail($id);
		if(file_exists( public_path().'/upload/conceptvideos/'.$conceptvideo->video_thumb )) {
			unlink( public_path().'/upload/conceptvideos/'.$conceptvideo->video_thumb );
		}
		$conceptvideo->video_thumb = NULL;
		$conceptvideo->update();

		\Session::flash('msg', 'Conceptvideo Image Removed Successfully.');
	    return redirect()->back();
	}

	public function vidremove($id, $video_type)
	{
		$s3 = AWS::createClient('s3');
		
		$conceptvideo = Conceptvideo::findOrFail($id);
		if($video_type=='video'){
			if(!empty($conceptvideo->video) && file_exists( public_path().'/upload/conceptvideos/'.$conceptvideo->video )) {
				unlink( public_path().'/upload/conceptvideos/'.$conceptvideo->video );
			}
			if(!empty($conceptvideo->video)){
				$videoName = $conceptvideo->video;
				$s3->deleteObject(array(
			        'Bucket' => env('AWS_BUCKET'),
			        'Key'    => $videoName
			    ));
				$s3->deleteObject(array(
			        'Bucket' => env('AWS_BUCKET'),
			        'Key'    => "mobile_app_data/".$videoName
			    ));
			}
			$conceptvideo->video = NULL;
			$conceptvideo->uploads3 = 0;
			$conceptvideo->processtatus = 0;
			$conceptvideo->starttime = NULL;
			$conceptvideo->endtime = NULL;
		}
		$conceptvideo->update();

		\Session::flash('msg', 'Concept Video Removed Successfully.');
	    return redirect()->back();
	}

}
