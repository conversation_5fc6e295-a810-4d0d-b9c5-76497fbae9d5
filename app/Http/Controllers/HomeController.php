<?php

namespace App\Http\Controllers;

use App\Http\Requests;
use Illuminate\Http\Request;
use phpDocumentor\Reflection\Types\Null_;
use App\Models\Chapter;
use App\Models\Conceptvideo;
use App\Models\Contactus;
use App\Models\CouponCode;
use App\CourseRequest;
use App\Models\Courses;
use App\Models\Enquiry;
use App\Models\Lession;
use App\Models\LiveClass;
use App\Models\Popularvideo;
use App\Models\Portfolio;
use App\Models\QuestionAsk;
use App\Models\Quiz;
use App\Models\RatingUser;
use App\Models\Team;
use App\Models\Testimonial;
use App\Models\User;
use App\Models\UserSubscription;
use Auth;




class HomeController extends Controller
{
	/**
	 * Create a new controller instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		$this->middleware('auth');
	}



	/**
	 * Show the application dashboard.
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function index(){
	  $courses_pending = CourseRequest::whereNull('builderId')->latest()->get();
	  $courses_active = CourseRequest::whereNotNull('builderId')->latest()->get();
	  $totalTeachers = User::where("role_id",2)->where("status",1)->where("deleted",0)->count();
	  $totalStudents = User::where("role_id",6)->where("status",1)->where("deleted",0)->count();
	  //$totalUserSubscriptions = UserSubscription::where("end_date",">",date('Y-m-d'))->count();
	  $totalUserSubscriptions = UserSubscription::where("paymentStatus", 1)->count();
	  $totalCourses = Courses::where("status",1)->where("deleted",0)->count();
	  $totalLessions = Lession::where("status",1)->where("deleted",0)->count();
	  $totalTopics = Chapter::where("status",1)->where("deleted",0)->count();
	  $totalConceptvideos = Conceptvideo::where("status",1)->where("deleted",0)->count();
	  $totalLiveClasses = LiveClass::where("status",1)->where("deleted",0)->count();
	  $totalPopularvideos = Popularvideo::where("status",1)->where("deleted",0)->count();
	  $totalQuizes = Quiz::where("status",1)->where("deleted",0)->count();
	  $totalQuestionAsks = QuestionAsk::where("status",1)->where("deleted",0)->count();
	  $totalCouponCodes = CouponCode::where("status",1)->where("deleted",0)->count();
	  $totalContactus = Contactus::count();
	  $totalEnquiries = Enquiry::count();
	  $totalUserRatings = RatingUser::count();
	  $totalTeamMembers = Team::where("status",1)->where("deleted",0)->count();
	  $totalTestimonials = Testimonial::where("status",1)->where("deleted",0)->count();
	  $totalPortfolios = Portfolio::where("status",1)->where("deleted",0)->count();
	  $totalExpiredSubscriptions = UserSubscription::where("end_date", "<", date('Y-m-d'))->where("paymentStatus", 1)->count();
	  $user = Auth::user();
	  $timezone = $user->timezone_offset;
	  if($timezone==null){
		  date_default_timezone_set('Asia/Kolkata');
	}else{
	  date_default_timezone_set($timezone);
	}
	//	if($user->role_id == 2){
			 // if($user->request_status==0)
    //             {
    //                 return redirect('/logout')->with('success','Your Account Not Approved by admin');
    //             }
    //             else if($user->request_status==2)
    //             {
    //                 return redirect('/logout')->with('errmsg','Your Account Rejeted by admin');
    //             }
                
			//dd($user);
			$userId = !empty($user) ? $user->id : 0;
			$allowedCourses = $user->allow_courses;
			$allowedCourses = explode(",", trim($allowedCourses));
			$totalLiveClasses = LiveClass::where("added_by",$userId)->where("status",1)->where("deleted",0)->count();
			$totalQuestionAsks = QuestionAsk::whereIn("course_id", $allowedCourses)->where("course_id", "!=", 0)->where("status",1)->where("deleted",0)->count();
//		}

//dd(Auth::check('banners'));
//	dd($allowedCourses);


		return view('home',compact('courses_pending','courses_active','totalTeachers','totalStudents','totalUserSubscriptions','totalCourses','totalLessions','totalTopics','totalConceptvideos','totalLiveClasses','totalPopularvideos','totalQuizes','totalQuestionAsks','totalCouponCodes','totalContactus','totalEnquiries','totalUserRatings','totalTeamMembers','totalTestimonials','totalPortfolios','totalExpiredSubscriptions'));
	}
}
