<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\PinMessage;
use App\Models\ChatGroup;
use App\Models\StudentClass;

class PinMessageController extends Controller
{
	public function index(Request $request)
	{
		$class_id = ($request->class_id) ? $request->class_id : '';
		$msg_type = ($request->msg_type) ? $request->msg_type : '';
		$status   = isset($request->status) ? ($request->status=='active') ? 1 : 2 : '';
		$classes = StudentClass::orderBy("id", "ASC")->get();
		$data = PinMessage::orderBy("id", "DESC")
				 ->Where(function($query) use ($class_id) {
					if (isset($class_id) && !empty($class_id)) {
						$query->whereRaw("find_in_set($class_id,class_ids)");
					}
				 })
				 ->Where(function($query) use ($msg_type) {
					if (isset($msg_type) && !empty($msg_type)) {
						$query->where("msg_type", $msg_type);
					}
				 })
				 ->Where(function($query) use ($status) {
					if (isset($status) && !empty($status)) {
						$query->where("status", $status);
					}
				 })
				 ->paginate(50);

		return view('admin.pin_message.index', compact('classes','data'));
	}

	public function create()
	{
		$classes = StudentClass::orderBy("id", "ASC")->get();
		return view('admin.pin_message.create',compact('classes'));
	}

	public function store(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'message' => 'required',
			'msg_type' => 'required',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$pin_message = new PinMessage();
			$pin_message->msg_type = $request->input('msg_type');
			$pin_message->group_id = $request->input('group_id');
			$pin_message->class_ids = !empty($request->class_ids) ? implode(",", $request->class_ids) : NULL;
			$pin_message->message = $request->input('message');
			$pin_message->status = 2;
			$pin_message->save();
			
			\Session::flash('msg', 'Pin Message Added Successfully.');
			return back();
		}
	}

	public function show(PinMessage $pin_message)
	{
		//
	}

	public function edit($id)
	{
		$pin_message = PinMessage::find($id);
		$moduleDataArr = [];
		if ($pin_message->msg_type==203) {
			$getModuleData = ChatGroup::where('status',1)->orderBy('sort_id','ASC')->get();
			foreach ($getModuleData as $key => $value) {
				$moduleData['id'] = $value['id'];
				$moduleData['name'] = $value['title'];
				array_push($moduleDataArr, $moduleData);
			}
		}
		$classes = StudentClass::orderBy("id", "ASC")->get();

		return view('admin.pin_message.edit',compact('pin_message','moduleDataArr','classes'));
	}

	public function update(Request $request, $id)
	{
		$validator = Validator::make($request->all(), [
			'message' => 'required',
			'msg_type' => 'required',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$pin_message = PinMessage::findOrFail($id);
			$pin_message->msg_type = $request->input('msg_type');
			$pin_message->group_id = $request->input('group_id');
			$pin_message->class_ids = !empty($request->class_ids) ? implode(",", $request->class_ids) : NULL;
			$pin_message->message = $request->input('message');
			$pin_message->save();

			\Session::flash('msg', 'Pin Message updated Successfully.');
			return redirect('/admin/pin_messages');
		}
	}

	public function delete($id)
	{
		$data = PinMessage::findOrFail($id);
		$data->delete();
		
		\Session::flash('msg', 'Pin Message deleted Successfully.');
	    return redirect('/admin/pin_messages');
	}

	public function updateStatus($id,$status)
	{
		$data = PinMessage::findOrFail($id);
		if($status==1){
			$msg_type = $data->msg_type;
		}
		$data->status=$status;
		$data->update();

	    return redirect()->back();
	}

	public function getPinMessageModuleList(Request $request)
	{
		$msg_type = $request->input('msg_type');
		?>
		<option value="0">--Select--</option>
		<?php
		if ($msg_type==203) {
			$getModuleData = ChatGroup::where('status',1)->orderBy('sort_id','ASC')->get();
			foreach ($getModuleData as $key => $value) {
				?>
				<option value="<?php echo $value['id']; ?>"><?php echo $value['title']; ?></option>
				<?php
			}
		} else {
		}
	}

}
