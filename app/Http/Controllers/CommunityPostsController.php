<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use App\Http\Helper as Helper;
use App\Models\CommunityCategory;
use App\Models\CommunityPost;
use App\Models\CommunityPostBanner;
use App\Models\Notification;
use App\Models\PostEntry;
use App\Models\PostEntryComment;
use App\Models\PostEntryLike;
use AWS;

class CommunityPostsController extends Controller
{
	protected $helper;

	public function __construct(Helper $helper)
	{
		$this->helper = $helper;
	}

	public function index(Request $request)
	{
		$categories = CommunityCategory::orderBy('title','ASC')->get();

		$post_type = ($request->post_type) ? $request->post_type : '';
		$cat_id = ($request->cat_id) ? $request->cat_id : '';

		$data = CommunityPost::OrderBy('sort_id','ASC')
				 ->Where(function($query) use ($post_type) {
					if (isset($post_type) && !empty($post_type)) {
						$query->where("post_type", $post_type);
					}
				 })
				 ->Where(function($query) use ($cat_id) {
					if (isset($cat_id) && !empty($cat_id)) {
						$query->where("cat_id", $cat_id);
					}
				 })
				 ->where('deleted',0);
		$totalData = $data->count();
		$data = $data->paginate(50);
		
		return view('admin.community_post.index', compact('categories','data','totalData'));
	}

	public function create()
	{
		$categories = CommunityCategory::OrderBy('title','ASC')->get();
		return view('admin.community_post.create', compact('categories'));
	}

	public function store(Request $request)
	{
		// echo '<pre />'; print_r($request->all());die;
		$validator = Validator::make($request->all(), [
			'title' => 'required',
			'description' => 'required',
			'image_file' => 'required',
			'video_file' => 'mimes:mp4',
            'end_date'   => 'required|date|date_format:Y-m-d|after:today',
            'resultdate' => 'required|date|date_format:Y-m-d|after:today',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$imageUrl = $videoUrl = NULL;
			$image_file = $request->file('image_file');
			$video_file = $request->file('video_file');
			
			if($image_file){
				$destinationPath = public_path().'/upload/community_posts/';
				$imageOriginalFile = $image_file->getClientOriginalName();
				$imageFilename = "community_post_0_".time().$imageOriginalFile;
				$image_file->move($destinationPath, $imageFilename);
				//S3 Upload
				$s3 = AWS::createClient('s3');
				$s3->putObject(array(
				    'Bucket'     => env('AWS_BUCKET'),
				    'Key'        => "assignment_data/".$imageFilename,
				    'SourceFile' => $destinationPath.$imageFilename,
				));
				$imageUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $imageFilename;
				if(!empty($image_file) && file_exists( $destinationPath.$imageFilename )) {
					unlink( $destinationPath.$imageFilename );
				}
			}
			if($video_file){
				$destinationPath = public_path().'/upload/community_posts/';
				$videoOriginalFile = $video_file->getClientOriginalName();
				//$videoFilename=rand(111,999).strtotime(date('Y-m-d-H:isa')).$videoOriginalFile;
				$videoFilename = "community_post_0_".time()."_org.mp4";
				$video_file->move($destinationPath, $videoFilename);
				//S3 Upload
				$s3 = AWS::createClient('s3');
				$s3->putObject(array(
				    'Bucket'     => env('AWS_BUCKET'),
				    'Key'        => "assignment_data/".$videoFilename,
				    'SourceFile' => $destinationPath.$videoFilename,
				));
				$videoUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $videoFilename;
				if(!empty($video_file) && file_exists( $destinationPath.$videoFilename )) {
					unlink( $destinationPath.$videoFilename );
				}
			}
			
			$community_post = new CommunityPost();
			$community_post->post_type = $request->get('post_type');
			$community_post->cat_id = $request->get('cat_id');
			$community_post->title = $request->get('title');
			$community_post->image = $imageUrl;
			$community_post->video = $videoUrl;
			$community_post->description = $request->get('description');
			$community_post->end_date = date('Y-m-d', strtotime($request->get('end_date')));
			$community_post->resultdate = date('Y-m-d', strtotime($request->get('resultdate')));
			$community_post->save();
			$community_postId=$community_post->id;
			if($community_postId){
				$cpost = CommunityPost::findOrFail($community_postId);
				$cpost->sort_id = $community_postId;
				$cpost->update();
			}
			
			\Session::flash('msg', 'Community Post Added Successfully.');
			return back();
		}
	}

	public function edit($id)
	{
		$community_post = CommunityPost::find($id);
		$categories = CommunityCategory::OrderBy('title','ASC')->get();
		return view('admin.community_post.edit',compact('community_post','categories'));
	}

	public function update(Request $request, $id)
	{
		//dd($request->all());
		$validator = Validator::make($request->all(), [
			'title' => 'required',
			'description' => 'required',
			'video_file' => 'mimes:mp4',
            //'end_date'   => 'required|date|date_format:Y-m-d|after:today',
            //'resultdate' => 'required|date|date_format:Y-m-d|after:today',
		]);

		if ($validator->fails()) {
			return back()
				->withErrors($validator)
				->withInput();
		}else {
			$community_post = CommunityPost::findOrFail($id);
			$image_file = $request->file('image_file');
			$video_file = $request->file('video_file');

			if($image_file){
				$destinationPath = public_path().'/upload/community_posts/';
				$imageOriginalFile = $image_file->getClientOriginalName();
				$imageFilename = "community_post_".$id."_".time().$imageOriginalFile;
				$image_file->move($destinationPath, $imageFilename);
				//S3 Upload
				$s3 = AWS::createClient('s3');
				$s3->putObject(array(
				    'Bucket'     => env('AWS_BUCKET'),
				    'Key'        => "assignment_data/".$imageFilename,
				    'SourceFile' => $destinationPath.$imageFilename,
				));
				$imageUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $imageFilename;
				if(!empty($image_file) && file_exists( $destinationPath.$imageFilename )) {
					unlink( $destinationPath.$imageFilename );
				}
				if(!empty($community_post->image) && file_exists( $destinationPath.$community_post->image )) {
					unlink( $destinationPath.$community_post->image );
				}
				if(!empty($community_post->image)){
					$uploadedImageUrl = $community_post->image;
					$uploadedImageArr = explode("https://s3.ap-south-1.amazonaws.com/video.brainywoodindia.com/assignment_data/", $uploadedImageUrl);
					$imageName = $uploadedImageArr[1];
					$result = $s3->deleteObject(array(
				        'Bucket' => env('AWS_BUCKET'),
				        'Key'    => "assignment_data/".$imageName
				    ));
				}
			}else {
				$imageUrl = $community_post->image;
			}
			if($video_file){
				$destinationPath = public_path().'/upload/community_posts/';
				$videoOriginalFile = $video_file->getClientOriginalName();
				//$videoFilename = rand(111,999).strtotime(date('Y-m-d-H:isa')).$videoOriginalFile;
				$videoFilename = "community_post_".$id."_".time()."_org.mp4";
				$video_file->move($destinationPath, $videoFilename);
				//S3 Upload
				$s3 = AWS::createClient('s3');
				$s3->putObject(array(
				    'Bucket'     => env('AWS_BUCKET'),
				    'Key'        => "assignment_data/".$videoFilename,
				    'SourceFile' => $destinationPath.$videoFilename,
				));
				$videoUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $videoFilename;
				if(!empty($video_file) && file_exists( $destinationPath.$videoFilename )) {
					unlink( $destinationPath.$videoFilename );
				}
				if(!empty($community_post->video) && file_exists( $destinationPath.$community_post->video )) {
					unlink( $destinationPath.$community_post->video );
				}
				if(!empty($community_post->video)){
					$uploadedVideoUrl = $community_post->video;
					$uploadedVideoArr = explode("https://s3.ap-south-1.amazonaws.com/video.brainywoodindia.com/assignment_data/", $uploadedVideoUrl);
					$videoName = $uploadedVideoArr[1];
					$result = $s3->deleteObject(array(
				        'Bucket' => env('AWS_BUCKET'),
				        'Key'    => "assignment_data/".$videoName
				    ));
				}
			}else {
				$videoUrl = $community_post->video;
			}
			
			if($video_file){
				if(!empty($community_post->video) && file_exists( public_path().'/upload/community_posts/'.$community_post->video )) {
					unlink( public_path().'/upload/community_posts/'.$community_post->video );
				}
			}
			$community_post->post_type = $request->get('post_type');
			$community_post->cat_id = $request->get('cat_id');
			$community_post->title = $request->get('title');
			$community_post->image = $imageUrl;
			$community_post->video = $videoUrl;
			$community_post->description = $request->get('description');
			$community_post->end_date = date('Y-m-d', strtotime($request->get('end_date')));
			$community_post->resultdate = date('Y-m-d', strtotime($request->get('resultdate')));
			$community_post->save();

			\Session::flash('msg', 'Community Post updated Successfully.');
			return redirect('/admin/community_posts');
		}
	}

	public function delete($id)
	{
		$record = CommunityPost::findOrFail($id);
		$record->deleted=1;
		$record->update();

	   return redirect('/admin/community_posts');
	}

	public function updateStatus($id,$status)
	{
		$record = CommunityPost::findOrFail($id);
		$record->status=$status;
		$record->update();

		\Session::flash('msg', 'Status updated Successfully.');
		return redirect()->back();
	}

	public function sortUp($id,$sort_id)
	{
		$sort_id = $sort_id - 1;
		$record = CommunityPost::where("sort_id", $sort_id);
		$record->update(array("sort_id"=>$sort_id + 1));
		$record1 = CommunityPost::findOrFail($id);
		$record1->sort_id = $sort_id;
		$record1->save();

		\Session::flash('msg', 'Community Post Order Changed Successfully.');
	    return redirect()->back();
	}

	public function sortDown($id,$sort_id)
	{
		$sort_id = $sort_id + 1;
		$record = CommunityPost::where("sort_id", $sort_id);
		$record->update(array("sort_id"=>$sort_id - 1));
		$record1 = CommunityPost::findOrFail($id);
		$record1->sort_id = $sort_id;
		$record1->save();

		\Session::flash('msg', 'Community Post Order Changed Successfully.');
	    return redirect()->back();
	}

	public function imageremove($id)
	{
		$community_post = CommunityPost::findOrFail($id);
		if(file_exists( public_path().'/upload/community_posts/'.$community_post->image )) {
			unlink( public_path().'/upload/community_posts/'.$community_post->image );
		}
		//S3 Connection
		$s3 = AWS::createClient('s3');
		if(!empty($community_post->image)){
			$uploadedImageUrl = $community_post->image;
			$uploadedImageArr = explode("https://s3.ap-south-1.amazonaws.com/video.brainywoodindia.com/assignment_data/", $uploadedImageUrl);
			$imageName = $uploadedImageArr[1];
			$result = $s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => "assignment_data/".$imageName
		    ));
		}
		$community_post->image = NULL;
		$community_post->update();

		\Session::flash('msg', 'Community Post Image Removed Successfully.');
		return redirect()->back();
	}

	public function videoremove($id)
	{
		$community_post = CommunityPost::findOrFail($id);
		if(!empty($community_post->video) && file_exists( public_path().'/upload/community_posts/'.$community_post->video )) {
			unlink( public_path().'/upload/community_posts/'.$community_post->video );
		}
		//S3 Connection
		$s3 = AWS::createClient('s3');
		if(!empty($community_post->video)){
			$uploadedVideoUrl = $community_post->video;
			$uploadedVideoArr = explode("https://s3.ap-south-1.amazonaws.com/video.brainywoodindia.com/assignment_data/", $uploadedVideoUrl);
			$videoName = $uploadedVideoArr[1];
			$result = $s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => "assignment_data/".$videoName
		    ));
		}
		$community_post->video = NULL;
		$community_post->update();

		\Session::flash('msg', 'Community Post Video Removed Successfully.');
		return redirect()->back();
	}

	public function resultAnnounce(Request $request)
	{
		CommunityPost::where('result_announce', 1)->update(['result_announce' => 0]);
		$id = $request->communityPostId;
		$communityPost = CommunityPost::findOrFail($id);
		$communityPost->result_announce = 1;
		$communityPost->update();
		$message = '<div class="alert alert-info"><a class="close" data-dismiss="alert">×</a><strong>Community Post Result Announce Updated Successfully.</strong></div>';
	    return $message;
	}

	public function categoryList(Request $request)
	{
		$data = CommunityCategory::orderBy('id','DESC')->get();
		
		return view('admin.community_post.categorylist', compact('data'));
	}
	public function storeCategory(Request $request)
	{
		$category_id = ($request->category_id) ? $request->category_id : 0;
		
		$validator = Validator::make($request->all(), [
			'title' => 'required|unique:community_categories,title,'.$category_id.',id',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			if ($category_id == 0) {
				$cpost_category = new CommunityCategory();
				$cpost_category->title = $request->get('title');
				$cpost_category->save();
				
				\Session::flash('msg', 'Community Post Category Added Successfully.');
				return back();
			} else {
				$cpost_category = CommunityCategory::findOrFail($category_id);
				$cpost_category->title = $request->get('title');
				$cpost_category->save();
				
				\Session::flash('msg', 'Community Post Category Updated Successfully.');
				return back();
			}
		}
	}
	public function categoryUpdateStatus($id,$status)
	{
		$record = CommunityCategory::findOrFail($id);
		$record->status=$status;
		$record->update();

		\Session::flash('msg', 'Community Category Status updated Successfully.');
		return redirect()->back();
	}


	public function banners(Request $request)
	{
		$communityPosts = CommunityPost::orderBy('sort_id','ASC')->get();

		$community_post_id = ($request->community_post_id) ? $request->community_post_id : '';

		$data = CommunityPostBanner::OrderBy('sort_id','ASC')
				 ->Where(function($query) use ($community_post_id) {
					if (isset($community_post_id) && !empty($community_post_id)) {
						$query->where("community_post_id", $community_post_id);
					}
				 });
		$totalData = $data->count();
		$data = $data->paginate(50);
		
		return view('admin.community_post.banners', compact('communityPosts','data','totalData'));
	}
	public function banner_create()
	{
		$communityPosts = CommunityPost::orderBy('sort_id','ASC')->get();
		return view('admin.community_post.banner_create', compact('communityPosts'));
	}
	public function banner_edit($id)
	{
		$communityPosts = CommunityPost::orderBy('sort_id','ASC')->get();
		$record_data = CommunityPostBanner::find($id);
		return view('admin.community_post.banner_create',compact('communityPosts','record_data'));
	}
	public function banner_update(Request $request, $banner_id)
	{
		$validator = Validator::make($request->all(), [
			'community_post_id' => 'required',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			if ($banner_id > 0) {
				$banner = CommunityPostBanner::find($banner_id);
			} else {
				$banner = new CommunityPostBanner();
				$banner_exist = CommunityPostBanner::where('community_post_id',$request->community_post_id)->first();
				if (!empty($banner_exist)) {
					$banner = CommunityPostBanner::find($banner_exist->id);
				}
			}

			$image_file = $request->file('image_file');
			$destinationPath = public_path().'/upload/community_posts/';
			if($image_file){
				$imageOriginalFile = $image_file->getClientOriginalName();
				$imageFilename = "community_post_banner_".time().$imageOriginalFile;
				$image_file->move($destinationPath, $imageFilename);
				//S3 Upload
				$s3 = AWS::createClient('s3');
				$s3->putObject(array(
				    'Bucket'     => env('AWS_BUCKET'),
				    'Key'        => "assignment_data/".$imageFilename,
				    'SourceFile' => $destinationPath.$imageFilename,
				));
				$imageUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $imageFilename;
				if(!empty($image_file) && file_exists( $destinationPath.$imageFilename )) {
					unlink( $destinationPath.$imageFilename );
				}
			} else {
				$imageUrl = !empty($banner) ? $banner->image : NULL;
			}

			$banner->community_post_id = $request->get('community_post_id');
			$banner->image = $imageUrl;
			$banner->save();
			$bannerId=$banner->id;
			if($bannerId){
				$cpost = CommunityPostBanner::findOrFail($bannerId);
				$cpost->sort_id = $bannerId;
				$cpost->update();
			}
			
			\Session::flash('msg', 'Community Post Banner Added Successfully.');
			return redirect()->route('admin.community_posts.banners');
		}
	}
	public function banner_updateStatus($id,$status)
	{
		$record = CommunityPostBanner::findOrFail($id);
		$record->status=$status;
		$record->update();

		\Session::flash('msg', 'Status updated Successfully.');
		return redirect()->back();
	}
	public function banner_sortUp($id,$sort_id)
	{
		$sort_id = $sort_id - 1;
		$record = CommunityPostBanner::where("sort_id", $sort_id);
		$record->update(array("sort_id"=>$sort_id + 1));
		$record1 = CommunityPostBanner::findOrFail($id);
		$record1->sort_id = $sort_id;
		$record1->save();

		\Session::flash('msg', 'Community Post Banner Order Changed Successfully.');
	    return redirect()->back();
	}
	public function banner_sortDown($id,$sort_id)
	{
		$sort_id = $sort_id + 1;
		$record = CommunityPostBanner::where("sort_id", $sort_id);
		$record->update(array("sort_id"=>$sort_id - 1));
		$record1 = CommunityPostBanner::findOrFail($id);
		$record1->sort_id = $sort_id;
		$record1->save();

		\Session::flash('msg', 'Community Post Banner Order Changed Successfully.');
	    return redirect()->back();
	}
	public function banner_image_remove($id)
	{
		$record = CommunityPostBanner::findOrFail($id);
		if(file_exists( public_path().'/upload/community_posts/'.$record->image )) {
			unlink( public_path().'/upload/community_posts/'.$record->image );
		}
		//S3 Connection
		$s3 = AWS::createClient('s3');
		if(!empty($record->image)){
			$uploadedImageUrl = $record->image;
			$uploadedImageArr = explode("https://s3.ap-south-1.amazonaws.com/video.brainywoodindia.com/assignment_data/", $uploadedImageUrl);
			$imageName = $uploadedImageArr[1];
			$result = $s3->deleteObject(array(
		        'Bucket' => env('AWS_BUCKET'),
		        'Key'    => "assignment_data/".$imageName
		    ));
		}
		$record->image = NULL;
		$record->update();

		\Session::flash('msg', 'Community Post Banner Image Removed Successfully.');
		return redirect()->back();
	}

	public function show(CommunityPost $community_post)
	{
		$post_entries = CommunityPost::find($community_post->id)->post_entries_data()->where('deleted',0)->OrderBy('sort_id','ASC')->paginate(10);
		return view('admin.community_post.view',compact('community_post','post_entries'));
	}

	public function user_post_show($id)
	{
		$post_entry = PostEntry::find($id);
		$post_likes = PostEntry::find($id)->post_likes_data()->OrderBy('id','ASC')->get();
		$post_comments = PostEntry::find($id)->post_comments_data()->OrderBy('id','ASC')->get();
		
		return view('admin.community_post.user_post_view',compact('post_entry','post_likes','post_comments'));
	}

	public function user_post_updateStatus($id,$status)
	{
		$record = PostEntry::findOrFail($id);

		if ($status==1 && $record->community_post_id==0) {
			$postLike = PostEntryLike::where('post_entry_id',$id)->where('user_id',$record->user_id)->first();
			if (empty($postLike)) {
				$newData = PostEntryLike::insert([
                    'post_entry_id'	=> $id,
                    'user_id'		=> $record->user_id,
                ]);
			}
		}

		if ($status==1) {
			if($record->user_id>0){
				$msg = "Congratulations, your post has been approved successfully, check it now.";
				$notify = new Notification();
				$notify->user_id = $record->user_id;
				$notify->message = $msg;
				$notify->save();
			
				$token = isset($record->user_data->deviceToken) ? $record->user_data->deviceToken : '';
				if ($token!='') {
					$title = 'BrainyWood';
					$click_action = 'CommunityPost';
					$module_id = $record->community_post_id;
				//	$this->helper->notificationsend($token, $title, $msg, $click_action, $module_id); //ak
				}
			}
		}

		$record->status=$status;
		$record->update();

		\Session::flash('msg', 'Status updated Successfully.');
		return redirect()->back();
	}

	public function user_post_order_change(Request $request)
	{
		$post_id = ($request->post_id > 0) ? $request->post_id : 0;
		$sort_id = ($request->sort_id > 0) ? $request->sort_id : 0;
		$record1 = PostEntry::findOrFail($post_id);
		$record1->sort_id = $sort_id;
		$record1->save();

		echo 'Done';

		/*\Session::flash('msg', 'User Post Order Changed Successfully.');
	    return redirect()->back();*/
	}

	public function comment_update(Request $request)
	{
		dd($request->all());
	}

	public function comment_delete($id)
	{
		$record = PostEntryComment::findOrFail($id);
		//$record->deleted=1;
		$record->delete();

		\Session::flash('msg', 'Comment deleted Successfully.');
		return redirect()->back();
	}

	public function usersValuablePosts(Request $request)
	{
		$user_id = ($request->user_id) ? $request->user_id : '';
		
		$data = PostEntry::OrderBy('id','DESC')
				 ->Where(function($query) use ($user_id) {
					if (isset($user_id) && !empty($user_id)) {
						$query->where("user_id", $user_id);
					}
				 })
				 ->where('community_post_id',0)->where('deleted',0);
		$totalData = $data->count();
		$data = $data->paginate(50);
		
		return view('admin.community_post.users_posts', compact('data','totalData'));
	}

	public function updatetopPost(Request $request)
	{
		$id = $request->postEntryId;
		$data = PostEntry::findOrFail($id);
		if($data->topPost==0){
			$data->topPost=1;
		}else{
			$data->topPost=0;
		}
		$data->update();
		$message = '<div class="alert alert-info"><a class="close" data-dismiss="alert">×</a><strong>Top Post Status Updated Successfully.</strong></div>';
	    return $message;
	}

}
