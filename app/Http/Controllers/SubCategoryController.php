<?php

namespace App\Http\Controllers;


use Illuminate\Http\Request;
use Validator;
use App\Models\Category;
use App\Models\SubCategory;
class SubCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $Category = Category::where('deleted',0)->orderBy('sort_id', 'ASC');
        $data = SubCategory::where('deleted',0)->orderBy('sort_id', 'ASC');
        $totalData = $data->count();
        $data = $data->paginate(50);
        return view('admin.subcategory.index',compact('totalData','data','Category'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $category = Category::Select("id","name")->where(['deleted'=>0,'status'=>1])->get();
        return view('admin.subcategory.create',compact('category'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|unique:sub_categories',
            'category_id' => 'required',
            'image' => 'required',
            ]);
    
            if ($validator->fails()) {
                return back()->withErrors($validator)->withInput();
            } else {
    
            $file = $request->file('image');
    
            $destinationPath = public_path().'/subcategory/';
         
            if($file){
                $originalFile = $file->getClientOriginalName();
                $filename = strtotime(date('Y-m-d-H:isa')).$originalFile;
                $file->move($destinationPath, $filename);
            }
    
                $SubCategory = new SubCategory();
                $SubCategory->name = $request->get('name');
                $SubCategory->category_id = $request->get('category_id');
                $SubCategory->image = $filename;
                $SubCategory->save();
                $subcategoryId=$SubCategory->id;
                if($SubCategory){
                    $SubCategory = SubCategory::findOrFail($subcategoryId);
                    $SubCategory->sort_id = $subcategoryId;
                    $SubCategory->update();
                }
                \Session::flash('msg', 'Sub Category Added Successfully.');
                return back();
            }
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\SubCategory  $subCategory
     * @return \Illuminate\Http\Response
     */
    public function show(SubCategory $subCategory)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\SubCategory  $subCategory
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $subcategory = SubCategory::findOrFail($id);
        $category = Category::Select("id","name")->where(['deleted'=>0,'status'=>1])->get();
        return view('admin.subcategory.edit',compact('category','subcategory'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\SubCategory  $subCategory
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'category_id' => 'required',
            ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        } else {
            $SubCategory = SubCategory::findOrFail($id);
            $file = $request->file('image');
          
            $destinationPath = public_path().'/subcategory/';
            if($file){
                $originalFile = $file->getClientOriginalName();
                $filename = time() . $originalFile;
                $file->move($destinationPath, $filename);
            }else{
               $filename=$SubCategory->image;  
            }
            $SubCategory->name = $request->get('name');
            $SubCategory->category_id = $request->get('category_id');
            $SubCategory->image = $filename;
            $SubCategory->save();
            \Session::flash('msg', 'Sub Category Updated Successfully.');
            return redirect('/admin/subcategory');
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\SubCategory  $subCategory
     * @return \Illuminate\Http\Response
     */
    public function destroy(SubCategory $subCategory)
    {
        //
    }

    public function imgremove($id)
    {
        $category = SubCategory::findOrFail($id);
        if(file_exists( public_path().'/subcategory/'.$category->image )) {
            unlink( public_path().'/subcategory/'.$category->image );
        }
        $category->image = NULL;
        $category->update();

        \Session::flash('msg', 'SubCategory Image Removed Successfully.');
        return redirect()->back();
    }
    public function delete($id){
        $category = SubCategory::findOrFail($id);
      
        $category->deleted = 1;
        $category->update();

        $data = SubCategory::findOrFail($id);
        $data->deleted=1;
        $data->update();
        \Session::flash('msg', 'SubCategory Deleted Successfully.');
        return redirect()->back();
    }
    public function sortUp($id,$sort_id)
	{
		$sort_id = $sort_id - 1;
		$SubCategory = SubCategory::where("sort_id", $sort_id);
		//$course->sort_id = $sort_id + 1;
		$SubCategory->update(array("sort_id"=>$sort_id + 1));
		$SubCategory = SubCategory::findOrFail($id);
		$SubCategory->sort_id = $sort_id;
		$SubCategory->save();
	    return redirect()->back();
	}

	public function sortDown($id,$sort_id)
	{
		$sort_id = $sort_id + 1;
		$SubCategory = SubCategory::where("sort_id", $sort_id);
		$SubCategory->update(array("sort_id"=>$sort_id - 1));
		$SubCategory = SubCategory::findOrFail($id);
		$SubCategory->sort_id = $sort_id;
		$SubCategory->save();
	    return redirect()->back();
	}
    
    public function getCategoryBySubCateogry(Request $request){
        if($request->all())
        {
           $SubCategory = SubCategory::where("category_id",$request->category_id)->get();
            if(!empty($SubCategory))
            {
                $output = '<option value="">Select Sub Category</option>';
                foreach ($SubCategory as $key => $value) {
                    $output .= '<option value="'.$value->id.'">'.$value->name.'</option>';
                }
                echo $output;
            }
        }
    }
   public function updateStatus($id,$status)
    {
        $data = SubCategory::findOrFail($id);
        $data->status=$status;
        $data->update();
        return redirect()->back();
    }


}
