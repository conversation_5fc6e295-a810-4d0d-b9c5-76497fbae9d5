<?php
namespace App\Http\Controllers\Front;

use App\Http\Controllers\Controller;
use App\Http\Helper as Helper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Mail;
use App\Mail\SendMail;
use App\Models\AboutUs;
use App\Models\Assignment;
use App\Models\AssignmentSubmission;
use App\Models\Blog;
use App\Models\Chapter;
use App\Models\ChatGroup;
use App\Models\ChatGroupUser;
use App\Models\ChatMessage;
use App\Models\City;
use App\Models\CommunityCategory;
use App\Models\CommunityPost;
use App\Models\CommunityPostBanner;
use App\Models\Conceptvideo;
use App\Models\Contactus;
use App\Models\ContinueStudy;
use App\Models\CouponCode;
use App\Models\Coursefeature;
use App\Models\Coursefeq;
use App\Models\Courses;
use App\Models\Enquiry;
use App\Models\Franchise;
use App\Models\Lession;
use App\Models\LiveClass;
use App\Models\LiveclassJoin;
use App\Models\LiveclassNotify;
use App\Models\LiveClassTimeTable;
use App\Models\LiveClassTimeTableDoc;
use App\Models\Notification;
use App\Models\NotificationSend;
use App\Models\Page;
use App\Models\PinMessage;
use App\Models\Popularvideo;
use App\Models\Portfolio;
use App\Models\PostEntry;
use App\Models\PostEntryComment;
use App\Models\PostEntryLike;
use App\Models\Product;
use App\Models\QuestionAnswer;
use App\Models\QuestionAnswerLike;
use App\Models\QuestionAsk;
use App\Models\Quiz;
use App\Models\Quizoption;
use App\Models\Quizquestions;
use App\Models\RatingMessage;
use App\Models\RatingType;
use App\Models\RatingUser;
use App\Models\ReferPoint;
use App\Models\State;
use App\Models\StudentClass;
use App\Models\StudentClassSubject;
use App\Models\StudentExam;
use App\Models\StudentExamAnswer;
use App\Models\Subject;
use App\Models\Subscription;
use App\Models\Team;
use App\Models\TeamDepartment;
use App\Models\Testimonial;
use App\Models\TestimonialRelate;
use App\Models\User;
use App\Models\UserPoint;
use App\Models\UserTemp;
use App\Models\UserSubscription;
use App\Models\VideoTemp;
use App\Models\LoginSession;
use App\Models\UserTracking;
use AWS;
use Image;
use DB;
use PDF;
use Carbon\Carbon;
include public_path().'/razorpay-php/Razorpay.php';
use Razorpay\Api\Api;
use Log;


class FrontController extends Controller
{
	protected $helper;

	public function __construct(Helper $helper)
	{
		$this->helper = $helper;
	}

	public function index(Request $request)
	{
		$pagename = "Home";
		$user = Auth::user();
		if($user==null)
		{
			return redirect('/login');
		}
		$userId = !empty($user) ? $user->id : 0;
		$class_id = !empty($user) ? $user->class_id : '';
		$classId = ($request->class_id) ? $request->class_id : $class_id;
		$userSubscription = $this->helper->userSubscription($userId);
		$today	= date('Y-m-d');

		$studentClasses = StudentClass::where("status",1)->where("deleted",0)->orderBy("id", "ASC")->get();
		$conceptvideos = Conceptvideo::where("status", 1)->where("deleted", 0)->orderBy("sort_id", "ASC")->limit(6)->get();
		$relates = TestimonialRelate::orderBy("id", "ASC")->get();
		if ($userId > 0 && $user->role_id == 3) {
			$courses = Courses::where("isHome", 502)->whereRaw("find_in_set($classId,class_ids)")->where("status",1)->where("deleted",0)->orderBy("sort_id", "ASC")->get();
		} else {
			$courses = Courses::where("isHome", 502)->where("status",1)->where("deleted",0)->orderBy("sort_id", "ASC")->get();
		}
		$couponCode = CouponCode::where("coupon", "Joy10")->where("status", 1)->where("deleted", 0)->first();
		$couponCodeLastDate=date('M d, Y H:i:s');
		if($couponCode){
			$couponCodeLastDate = date('M d, Y H:i:s', strtotime($couponCode->end_date));
			if(strtotime($couponCode->end_date) <= strtotime($today)){
				$date = strtotime($today);
				$date = strtotime("+7 days", $date);
				$end_date = date('Y-m-d', $date);
				$update = CouponCode::where("id", $couponCode->id)->update(["end_date"=>$end_date]);
			}
		}
		if ($userId > 0 && $user->role_id == 3) {
			if($request->session()->has('openModal')){
				$request->session()->forget('openModal');
			}
			$this->helper->trackingApi($userId, 'home', '', 0, '', 'website');
		}
		return view('front.home', compact('pagename','userSubscription','studentClasses','conceptvideos','relates','courses','couponCodeLastDate'));
	}
	public function saveLead(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'name' => 'required|regex:/^[\pL\s\-]+$/u',
			'phone' => 'required|numeric',
			'email' => 'required|email',
			'g-recaptcha-response' => 'required|captcha',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$name 		= $request->name;
			$phone 		= $request->phone;
			$email 		= $request->email;
			$st_class 	= $request->st_class;
			$city 		= $request->city;
			if (!empty($name) && !empty($phone) && !empty($email)) {
				$data = array(
					'name'  		=> $name,
					'phone'  		=> $phone,
					'email'  		=> $email,
					'st_class'  	=> $st_class,
					'city'  		=> $city,
					'created_at'    => date('Y-m-d H:i:s'),
				);
				$insertId = Enquiry::insertGetId($data);
				
				\Session::flash('success', 'Your Information Submitted Successfully.');
				return back();
			} else {
				\Session::flash('error', 'Wrong Paramenter Passed!');
				return back();
			}
		}
	}

	public function aboutUs()
	{
		$pagename = "About Us";
		$aboutus = AboutUs::where("id", 1)->first();
		$relates = TestimonialRelate::orderBy("id", "ASC")->get();
		$firstPortfolios = Portfolio::where("select_row", 1)->where("status", 1)->where("deleted", 0)->orderBy("id","DESC")->get();
		$secondPortfolios = Portfolio::where("select_row", 2)->where("status", 1)->where("deleted", 0)->orderBy("id","DESC")->get();
		$user = Auth::user();
		$userId = !empty($user) ? $user->id : 0;
		if ($userId > 0 && $user->role_id == 3) {
			$this->helper->trackingApi($userId, 'aboutus', '', 0, '', 'website');
		}

		return view('front.about_us', compact('pagename','aboutus','relates','firstPortfolios','secondPortfolios'));
	}

	public function ourTeam()
	{
		$pagename = "Our Team";
		$departments = TeamDepartment::orderBy("id", "ASC")->get();

		return view('front.our_team', compact('pagename','departments'));
	}

	public function services()
	{
		$pagename = "Services";
		$testimonials = Testimonial::where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->limit(4)->get();

		return view('front.services', compact('pagename','testimonials'));
	}
	
	public function loginForm(Request $request)
	{
		$pagename = "Login";
		$user = Auth::user();
		$userId = !empty($user) ? $user->id : 0;
		if ($userId > 0 && $user->role_id == 3) {
			return redirect()->route('home');
		}

		return view('front.login_form', compact('pagename'));
	}
	public function login(Request $request)
	{
		//dd($request->all());
		/*$validator = Validator::make($request->all(), [
			//'email' => 'required',
			'password' => 'required|min:6',
			'g-recaptcha-response' => 'required|captcha',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {*/
			$country_code	= !empty($_REQUEST['country_code']) ? trim($_REQUEST['country_code'],"+") : '';
			$country_flag	= !empty($_REQUEST['country_flag']) ? strtoupper($_REQUEST['country_flag']) : '';
			$email			= !empty($_REQUEST['email']) ? trim($_REQUEST['email']) : '';
			$phone			= !empty($_REQUEST['phone']) ? trim($_REQUEST['phone']) : '';
			$password		= !empty($_REQUEST['password']) ? trim($_REQUEST['password']) : '';
			$remember_me 	= $request->has('remember_me') ? true : false;
			//if (!empty($password)) {
				if (is_numeric($phone) && !empty($country_code)) {
					if ($country_code == '91') {
						$validator = Validator::make($request->all(), [
							'phone' => 'required|digits:10',
							'password' => 'required|min:6',
						]);
						if ($validator->fails()) {
							return back()->withErrors($validator)->withInput();
						}
					}
					$checkUser = User::where("country_code", $country_code)->where("phone", $phone)->first();
					//dd($checkUser);
					if(!empty($checkUser)){
						if(!empty($checkUser) && $checkUser->status != 1){
							$otpnumber = rand(1111, 9999);
							$update = User::where("id", $checkUser->id)->update(["otp_match" => $otpnumber]);
							$request->session()->put('phone', $checkUser->phone);
						
						/*
						//ak
							$this->helper->sms($checkUser->phone, $otpnumber);
							$this->helper->sendEmail($checkUser->email, 'BrainyWood: Verify OTP', $data = array('userName' => $checkUser->name, 'message' => '<p>Thank you for connecting at BrainyWood,</p><p>You have got successfully your OTP: ' . $otpnumber . '</p>'));
						*/

							\Session::flash('error', 'Please verify your account first!');
							return back();
						}
						if(!empty($checkUser) && $checkUser->deleted == 1){
							\Session::flash('error', 'Please contact to our Team first to activate your account!');
							return back();
						}
						if (Auth::attempt(['phone' => $phone, 'password' => $password], $remember_me)) {
							$user = Auth::user();
							if($user->role_id != 3){
								\Session::flash('error', 'You are not allow to login here!');
								return back();
							}
							if ($request->session()->has('phone')){
								$request->session()->forget('phone');
							}
							if ($request->session()->has('forgotPassPhone')){
								$request->session()->forget('forgotPassPhone');
							}
							$user->generateToken();
							$api_token = Str::random(60);
							$userss = User::find($user->id);
							$userss->api_token = $api_token;
							if ($userss->country_code=='') {
								$userss->country_code = $country_code;
							}
							if ($userss->country_flag=='') {
								$userss->country_flag = $country_flag;
							}
							$userss->save();
							//dd($userss->id);
							$loginSession = new LoginSession();
							$loginSession->user_id = $userss->id;
							$loginSession->login_time = date('Y-m-d H:i:s');
							$loginSession->save();
							
							$request->session()->put('loginToken', $api_token);

							return redirect()->route('ourCourses')->with('success','Login successfully.');
						}else{
							\Session::flash('error', 'Please enter correct mobile no or password!');
							return redirect()->route('loginForm');
						}
					}else{
						\Session::flash('error', 'Please enter correct mobile no or country code!');
						return redirect()->route('loginForm');
					}
				} else {
					$validator = Validator::make($request->all(), [
						'email' => 'required|email',
						'password' => 'required|min:6',
					]);
					if ($validator->fails()) {
						return back()->withErrors($validator)->withInput();
					} else {
						$checkUser = User::where("email", $email)->first();
						if(!empty($checkUser) && $checkUser->status != 1){
							$otpnumber = rand(1111, 9999);
							$update = User::where("id", $checkUser->id)->update(["otp_match" => $otpnumber]);
							$request->session()->put('phone', $checkUser->phone);
							//$this->helper->sms($checkUser->phone, $otpnumber);
							//$this->helper->sendEmail($checkUser->email, 'BrainyWood: Verify OTP', $data = array('userName' => $checkUser->name, 'message' => '<p>Thank you for connecting at BrainyWood,</p><p>You have got successfully your OTP: ' . $otpnumber . '</p>'));

							\Session::flash('error', 'Please verify your account first!');
							return back();
						}
						if(!empty($checkUser) && $checkUser->deleted == 1){
							\Session::flash('error', 'Please contact to our Team first to activate your account!');
							return back();
						}
						if(Auth::attempt(['email' => $email, 'password' => $password], $remember_me)){
							$user = Auth::user();
							if($user->role_id != 3){
								\Session::flash('error', 'You are not allow to login here!');
								return back();
							}
							if ($request->session()->has('phone')){
								$request->session()->forget('phone');
							}
							if ($request->session()->has('forgotPassPhone')){
								$request->session()->forget('forgotPassPhone');
							}
							$user->generateToken();
							$api_token = Str::random(60);
							$userss = User::find($user->id);
							$userss->api_token = $api_token;
							//$userss->deviceToken = $deviceToken;
							$userss->save();
							
							$loginSession = new LoginSession();
							$loginSession->user_id = $userss->id;
							$loginSession->login_time = date('Y-m-d H:i:s');
							$loginSession->save();
							
							$request->session()->put('loginToken', $api_token);
							
							\Session::flash('success', 'Login successfully.');
							return redirect()->route('ourCourses');
						}else{
							\Session::flash('error', 'Please enter correct email or password!');
							return redirect()->route('loginForm');
						}
					}
				}
			/*}else{
				\Session::flash('error', 'Please enter correct mobile no or email!');
				return back();
			}
		}*/
	}
	public function logout(Request $request)
	{
		$user = Auth::user();
		$userId = !empty($user) ? $user->id : 0;
		if ($userId > 0 && $user->role_id == 3) {
			$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
		}
		$loginSession = LoginSession::where("user_id",$userId)->orderBy('id', 'DESC')->first();
		if (!empty($loginSession)) {
			LoginSession::where("id",$loginSession->id)->update(['logout_time' => date('Y-m-d H:i:s')]);
		}
		Auth::logout();
		\Session::flush();
		return redirect()->route('home');
	}

	public function signUp(Request $request)
	{
		$pagename = "Sign Up";
		$user = Auth::user();
		$userId = !empty($user) ? $user->id : 0;
		if ($userId > 0 && $user->role_id == 3) {
			return redirect()->route('home');
		}
		if($request->session()->has('phone')){
			$sessionEmail = $request->session()->get('email');
			$sessionCcode = $request->session()->get('ccode');
			$sessionCflag = $request->session()->get('cflag');
			$sessionPhone = $request->session()->get('phone');
		}else{
			$sessionEmail = $sessionCcode = $sessionCflag = $sessionPhone = '';
		}

		return view('front.register', compact('pagename','sessionEmail','sessionCcode','sessionCflag','sessionPhone'));
	}

	public function register(Request $request)
	{
		//dd($request->all());
		$email 			= $request->input('email');
		$country_code 	= trim($request->input('country_code'),"+");
		$country_flag 	= strtoupper($request->input('country_flag'));
		$phone 			= $request->input('phone');
		$validator = Validator::make($request->all(), [
			'email' => 'required|email|unique:users,email',
			'country_code' => 'required',
			'phone' => 'required|numeric|unique:users,phone',
			//'g-recaptcha-response' => 'required|captcha',
		]);

		if ($validator->fails()) {
			$msg = trim($validator->messages()->first());
			if ($msg=='The email has already been taken.') {
				$usercheck = User::where("email", $email)->first();
				if (!empty($usercheck)) {
					if ($usercheck->status==1) {
						$msg = 'Email id already exists. Please login directly.';
						if ($usercheck->name=='' || $usercheck->class_id==0) {
							$userId = $usercheck->id;
							$request->session()->put('userId', $userId);
							return redirect()->route('signUpForm');
						}
						\Session::flash('success', $msg);
						return redirect()->route('loginForm');
					} else {
						$msg = 'Email id already exists. Please login directly.';
						return back()->withErrors($msg)->withInput();
					}
				}
			} elseif ($msg=='The phone has already been taken.') {
				$phonecheck = User::where("country_code", $country_code)->where("phone", $phone)->first();
				if (!empty($phonecheck)) {
					if ($phonecheck->status==1) {
						$msg = 'Phone number already exists. Please login directly.';
						if ($phonecheck->name=='' || $phonecheck->class_id==0) {
							$userId = $usercheck->id;
							$request->session()->put('userId', $userId);
							return redirect()->route('signUpForm');
						}
						\Session::flash('success', $msg);
						return redirect()->route('loginForm');
					} else {
						$msg = 'Phone number already exists. Please login directly.';
						return back()->withErrors($msg)->withInput();
					}
				}
			} else {
				return back()->withErrors($validator)->withInput();
			}
			return back()->withErrors($validator)->withInput();
		} else {
			$msg = '';
			if (!empty($email) && !empty($country_code)  && !empty($phone)) {
				if ($country_code == '91') {
					$validator = Validator::make($request->all(), [
						'phone' => 'required|digits:10',
					]);
					if ($validator->fails()) {
						$msg = 'Phone number should be 10 digits only.';
						return back()->withErrors($msg)->withInput();
					}
				}
				//echo $country_code. '----'.$country_flag; die;
				$usercheck = User::where("email", $email)->first();
				if (!empty($usercheck)) {
					if ($usercheck->status==1) {
						if ($usercheck->name=='' || $usercheck->class_id==0) {
							$userId = $usercheck->id;
							$request->session()->put('userId', $userId);
							\Session::flash('error', 'Email id already exists. Please login directly.');
							return redirect()->back();
						}
					} else {
						\Session::flash('error', 'Email id already exists. Please login directly.');
						return redirect()->back();
					}
				} else {
					$phonecheck = User::where("country_code", $country_code)->where("phone", $phone)->first();
					if (!empty($phonecheck)) {
						if ($phonecheck->status==1) {
							if ($phonecheck->name=='' || $phonecheck->class_id==0) {
								$userId = $usercheck->id;
								$request->session()->put('userId', $userId);
								\Session::flash('error', 'Phone number already exists. Please login directly.');
								return redirect()->back();
							}
						} else {
							\Session::flash('error', 'Phone number already exists. Please login directly.');
							return redirect()->back();
						}
					} else {
						$otp = rand(1111, 9999);
						$request['otp'] = $otp;
						$request['country_code'] = $country_code;
						$request['country_flag'] = $country_flag;
						$userTemp = UserTemp::where("email", $email)->where("country_code", $country_code)->where("phone", $phone)->first();
						if (empty($userTemp)) {
							$userTemp = UserTemp::where("phone", $phone)->first();
							if (empty($userTemp)) {
								$userTemp = UserTemp::where("email", $email)->first();
								if (empty($userTemp)) {
									$userTemp = new UserTemp();
								}
							}
						}
						$userTemp->fill($request->all());
						$userTemp->save();
						$userId = $userTemp->id;
						if($userId){
							$request->session()->put('email', $email);
							$request->session()->put('ccode', $country_code);
							$request->session()->put('cflag', $country_flag);
							$request->session()->put('phone', $phone);
						}
						$msg = 'OTP sent on your mobile OR email, Please check and verify your account first.';
						$this->helper->addNotification($userId,$msg);
						/*
						//ak
						$this->helper->sms($phone, $otp);
						$this->helper->sendEmail($email, 'BrainyWood: Verify your account', $data = array('userName' => 'User', 'message' => '<p>Thank you for connecting at BrainyWood,</p><p>You have got successfully your OTP: ' . $otp . '</p>'));
						*/
						\Session::flash('success', $msg);
						return redirect()->back();
					}
				}
			} else {
				\Session::flash('error', 'Wrong Paramenter Passed!');
				return redirect()->back();
			}
		}
	}

	public function verifyAccount($token)
	{
		$user = User::where("remember_token", $token)->first();
		if (empty($user)) {
			\Session::flash('error', 'This token has been expired, please try again!');
			return redirect()->route('home');
		} else {
			$user = User::find($user->id);
			$user->status = 1;
			$user->save();
			\Session::flash('success', 'Welcome to the BrainyWood, Account successfully verified, Now you can access your account, Please login now.');
			return redirect()->route('home');
		}
	}

	public function verifyAccountByOtp(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'otp' => 'required|digits:4',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$country_code = trim($request->country_code,"+");
			$country_flag = strtoupper($request->country_flag);
			$phone = $request->phone;
			$otp = $request->otp;
			$userTemp = UserTemp::where("phone", $phone)->where("otp", $otp)->first();
			if (!empty($userTemp)) {
				$user = User::where("email", $userTemp->email)->where("phone", $userTemp->phone)->first();
				if (empty($user)) {
					$data = array(
						'role_id'		=> 3,
						'email' 		=> $userTemp->email,
						'country_code' 	=> $userTemp->country_code,
						'country_flag' 	=> $userTemp->country_flag,
						'phone' 		=> $userTemp->phone,
						'otp_match' 	=> $userTemp->otp,
						'remember_token'=> Str::random(60),
						'api_token' 	=> Str::random(60),
						'status'    	=> 1,
						'created_at' 	=> date('Y-m-d H:i:s'),
					);
					$userId = User::insertGetId($data);
				} else {
					$userId = $user->id;
					$user = User::find($user->id);
					$user->status = 1;
					$user->save();
				}

				$request->session()->forget('phone');
				$request->session()->put('userId', $userId);
				//$request->session()->put('openModal', 'saveFirstProfile');
				
				//\Session::flash('success', 'Welcome to the BrainyWood, Account successfully verified, Please complete your first profile.');
				return redirect()->route('signUpForm');
			} else {
				$user = User::where("phone", $phone)->where("otp_match", $otp)->first();
				if (!empty($user)) {
					$userId = $user->id;
					$user = User::find($user->id);
					$user->status = 1;
					$user->save();

					$request->session()->forget('phone');
					$request->session()->put('openModal', 'login');
					
					\Session::flash('success', 'Welcome to the BrainyWood, Account successfully verified, Now you can access your account, Please login now.');
					return redirect()->route('home');
				} else {
					\Session::flash('error', 'Invalid OTP');
					return redirect()->back();
				}
			}
		}
	}
	public function resendOtp(Request $request)
	{
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
		}else{
			$userId = $user->id;
		}
		$phone = $request->phone;
		$otpnumber = rand(1111, 9999);
		if (!empty($phone)) {
			$user = User::where("phone", $phone)->first();
			if (!empty($user)) {
				$userId = $user->id;
				$update = User::where("id", $userId)->update(["otp_match" => $otpnumber]);
				if ($update) {
					if ($request->session()->has('new_email')){
						$request->session()->forget('phone');
					} elseif ($request->session()->has('new_phone')){
						$request->session()->forget('phone');
					} else {
						$request->session()->put('phone', $phone);
					}

					$msg = 'Verification Otp Send, Please Check.';
					/*//ak
					$this->helper->sms($phone, $otpnumber);
					$this->helper->sendEmail($user->email, 'BrainyWood: Verify OTP', $data = array('userName' => $user->name, 'message' => '<p>Thank you for connecting at BrainyWood,</p><p>You have got successfully your OTP: ' . $otpnumber . '</p>'));
					*/

					\Session::flash('success', 'Verification Otp Send, Please Check your Mobile or Email id.');
					return redirect()->back();
				}
			} else {
				if($userId==0){
					$userTemp = UserTemp::where("phone", $phone)->first();
					$userId = $userTemp->id;
					$update = UserTemp::where("id", $userId)->update(["otp" => $otpnumber]);
					if ($update) {
						if ($request->session()->has('new_email')){
							$request->session()->forget('phone');
						} elseif ($request->session()->has('new_phone')){
							$request->session()->forget('phone');
						} else {
							$request->session()->put('phone', $phone);
						}

						$msg = 'Verification Otp Send, Please Check.';
						/*
						//ak
						$this->helper->sms($phone, $otpnumber);
						$this->helper->sendEmail($userTemp->email, 'BrainyWood: Verify OTP', $data = array('userName' => 'User', 'message' => '<p>Thank you for connecting at BrainyWood,</p><p>You have got successfully your OTP: ' . $otpnumber . '</p>'));
						*/

						\Session::flash('success', 'Verification Otp Send, Please Check your Mobile or Email id.');
						return redirect()->back();
					}
				}
			}
		} else {
			\Session::flash('error', 'Wrong Paramenter Passed!');
			return redirect()->back();
		}
	}
	public function closeVerification(Request $request)
	{
		if ($request->session()->has('phone')){
			$request->session()->forget('phone');
		}
		if ($request->session()->has('forgotPassPhone')){
			$request->session()->forget('forgotPassPhone');
		}
		if ($request->session()->has('new_phone')){
			$request->session()->forget('new_phone');
		}
		if ($request->session()->has('new_email')){
			$request->session()->forget('new_email');
		}
		if ($request->session()->has('new_email_phone')){
			$request->session()->forget('new_email_phone');
		}
		//return "Done";
		return redirect()->back();
	}

	public function signUpForm(Request $request)
	{
		$pagename = "Sign Up";
		$user = Auth::user();
		$userId = !empty($user) ? $user->id : 0;
		if ($userId > 0 && $user->role_id == 3) {
			return redirect()->route('home');
		}
		if($request->session()->has('userId')){
			$sessionUserId = $request->session()->get('userId');
		}else{
			return redirect()->back();
		}
		$studentClasses = StudentClass::where('status',1)->where('deleted',0)->orderBy('id', 'ASC')->get();

		return view('front.register_form', compact('pagename','sessionUserId','studentClasses'));
	}
	public function saveFirstProfile(Request $request)
	{
		if ($request->session()->has('userId')) {
			$userId = $request->session()->get('userId');
		} else {
			\Session::flash('error', 'User Not Found!');
			return redirect()->back();
		}
		$validator = Validator::make($request->all(), [
			'name' => 'required|regex:/^[\pL\s\-]+$/u',
			'class_id' => 'required',
			'password' => 'required|min:6',
			'confirm_password' => 'required|same:password|min:6',
			//'g-recaptcha-response' => 'required|captcha',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$name 		      = $request->input('name');
			$class_id	 	  = $request->input('class_id');
			$password 		  = $request->input('password');
			$confirm_password = $request->input('confirm_password');
			$refer_code		  = $request->input('refer_code');
			$msg = '';
			if (!empty($name)  && !empty($class_id) && !empty($password)) {
				if ($password != $confirm_password) {
					\Session::flash('error', 'Password and Confirm password not matched!');
					return redirect()->back();
				}
				$user = User::where("id", $userId)->first();
				if (!empty($user)) {
					$studentClass = StudentClass::where("id", $class_id)->first();
					$class_name = !empty($studentClass) ? $studentClass->class_name : 'NA';
					$email 		= $user->email;
					$phone 		= $user->phone;
					$gender 	= $user->gender;
					$city 		= !empty($user->city) ? $user->city : 'NA';
					$cities = City::where("city", $city)->first();
					$state_id = !empty($cities) ? $cities->state_id : 0;
					$states = State::where("id", $state_id)->first();
					$state_name = !empty($states) ? $states->state : 'Rajasthan';
					$franchiseUserId = 0;
					if (!empty($refer_code)) {
						$usedReferCode = User::where("refer_code", $refer_code)->count();
						$franchise = Franchise::where("refer_code", $refer_code)->first();
						if (!empty($franchise)) {
							if (strtotime($franchise->ref_code_valid) >= strtotime(date('Y-m-d'))) {
								if ($franchise->no_of_used > $usedReferCode) {
									$franchiseUserId = $franchise->user_id;
									if ($gender=='Female'){
										$user_gender = 202;
									} elseif ($gender=='Non Binary'){
										$user_gender = 203;
									} else {
										$user_gender = 201;
									}
									$ch = curl_init();
									$url = config('constant.FMSLEADAPIURL');
									curl_setopt($ch, CURLOPT_URL,$url);
									curl_setopt($ch, CURLOPT_POST, true);
									curl_setopt($ch, CURLOPT_POSTFIELDS, "user_id=$franchiseUserId&full_name=$name&email_address=$email&mobile_number=$phone&class_name=$class_name&user_gender=$user_gender&city_name=$city&state_name=$state_name");
									curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
									$output = curl_exec ($ch);

									curl_close ($ch);

									$response = json_decode($output);
									//echo '<pre />'; print_r($response); die; // Show output
									if ($response->Status==false) {
										$refer_code = NULL;
										\Session::flash('error', $response->Message);
										return redirect()->back();
									}
								} else {
									$refer_code = NULL;
									$msg = "Referral code no of use limit expired. Can not be used it!";
									\Session::flash('error', $msg);
									return redirect()->back();
								}
							} else {
								$refer_code = NULL;
								$msg = "Referral code has been expired. Can not be used it!";
								\Session::flash('error', $msg);
								return redirect()->back();
							}
						} else {
							$refer_code = NULL;
							\Session::flash('error', 'Referral code not exists. Please try again!');
							return redirect()->back();
						}
					}
					$otp = rand(1111, 9999);
					$remember_token = Str::random(80);
					$api_token = Str::random(60);
					$data = array(
						'name' 		=> $name,
						'class_id'  => $class_id,
						'password'	=> bcrypt($password),
						'userpass'	=> $password,
						'gender' 	=> $gender,
						'city' 		=> $city,
						'state' 	=> $state_name,
						'franchise_user_id' => $franchiseUserId,
						'refer_code' => $refer_code,
						'otp_match' => $otp,
						'remember_token' => $remember_token,
						'api_token' => $api_token,
						'updated_at' => date('Y-m-d H:i:s'),
					);
					$update = User::where("id", $userId)->update($data);
					
					$request->session()->forget('openModal');

					//Add 7 days free trial to new user
					$today	  = date('Y-m-d');
					$userSubscription = UserSubscription::where("user_id", $userId)->orderBy('id', 'DESC')->first();
					if (empty($userSubscription)) {
						$franchise = Franchise::where("refer_code", $refer_code)->where("refer_code", "!=", "")->first();
						if (!empty($franchise)) {
							$date = strtotime($today);
							if ($franchise->refer_code == 'IVR4LDY5') {
								$date = strtotime("+3 days", $date);
							} else {
								$date = strtotime("+7 days", $date);
							}
							$end_date = date('Y-m-d', $date);
							$data1 = array(
								'user_id'			=> $userId,
								'subscription_id'	=> 1,
								'start_date'		=> $today,
								'end_date'			=> $end_date,
								'mode'				=> 'FREE',
								'paymentStatus'		=> 1,
								'created_at'		=> date('Y-m-d H:i:s'),
							);
							$inserId = UserSubscription::insertGetId($data1);
						}

						//send coupon code to franchise student
						$checkCoupon = CouponCode::where('user_id', $userId)->where('condition_1', 1)->first();
						if (empty($checkCoupon)) {
							$franchise = Franchise::where("refer_code", $refer_code)->where("refer_code", "!=", "")->first();
							if (!empty($franchise)) {
								$coupon = $this->helper->getcouponcode();
								$date = strtotime($today);
								$date = strtotime("+1 month", $date);
								$coupon_end_date = date('Y-m-d', $date);
								$couponcode = new CouponCode();
								$couponcode->coupon = $coupon;
								$couponcode->discount = 25;
								$couponcode->end_date = $coupon_end_date;
								$couponcode->description = 'Franchise user auto generated coupon code.';
								$couponcode->condition_1 = 1;
								$couponcode->user_id = $userId;
								$couponcode->subscription_id = 0;
								$couponcode->no_of_users = 0;
								$couponcode->save();
								$couponcodeId = $couponcode->id;
								
/*
//ak
								$this->helper->sendEmail($user->email, 'BrainyWood: Coupon Code Discount', $data = array('userName' => $user->name, 'message' => '<p>Thank you for connecting at BrainyWood,</p><p>You have got successfully your Coupon Code: ' . $coupon . '</p><p>This Coupon code discount valid till: ' . $coupon_end_date . '</p>'));
*/
							}
						}
					}

					//Refer point on New Signup
					$this->helper->addUserReferPoints($userId,1);

					$remember_me 	= $request->has('remember_me') ? true : false;
					if (Auth::attempt(['phone' => $phone, 'password' => $password], $remember_me)) {
						$user = Auth::user();
						$request->session()->put('loginToken', $api_token);

						return redirect()->route('ourCourses')->with('success','Welcome to the BrainyWood, Now you can access your account.');
					}

					\Session::flash('success', 'User Registration Completed Successfully, Please login now.');
					return redirect()->back();
				} else {
					\Session::flash('error', 'User Not Found!');
					return redirect()->back();
				}
			} else {
				\Session::flash('error', 'Wrong Paramenter Passed!');
				return redirect()->back();
			}
		}
	}

	public function forgotPassword(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'email' => 'required',
		]);

		if ($validator->fails()) {
			return back()
				->withErrors($validator)
				->withInput();
		} else {
			$email = $request->input('email');
			if (is_numeric($email)) {
				$usercheck = User::where("phone", $email)->first();
			} else {
				$usercheck = User::where("email", $email)->first();
			}
			if (!empty($usercheck)) {
				$userId = $usercheck->id;
				$name = $usercheck->name;
				$email = $usercheck->email;
				$phone = $usercheck->phone;
				$remember_token = Str::random(60);
				$otpnumber = rand(1111, 9999);
				$user = User::find($userId);
				$user->remember_token = $remember_token;
				$user->otp_match = $otpnumber;
				$user->save();
				
				$request->session()->put('forgotPassPhone', $phone);

/*
//ak
				$this->helper->sms($phone, $otpnumber);
				$this->helper->sendEmail($email, 'BrainyWood: Forgot Password', $data = array('userName' => $name, 'message' => '<p>You have been forgotton your password, don\'t worry, Please reset your password <a href=\"' . route('passwordToken', $remember_token) . '\">click here</a></p><br>OR<br><p>You have got successfully your OTP: ' . $otpnumber . ' to reset your password</p>'));
*/
				
				\Session::flash('success', 'Please check your mobile or email id to reset your password.');
			} else {
				\Session::flash('error', 'Mobile or Email id not found!');
			}
		}
		return redirect()->back();

	}
	public function forgotPasswordByOtp(Request $request)
	{
		$phone = $request->phone;
		$otp = $request->otp;
		$user = User::where("phone", $phone)->where("otp_match", $otp)->first();
		if (empty($user)) {
			\Session::flash('error', 'Invalid OTP');
			return redirect()->route('home');
		} else {
			if ($user->remember_token!='') {
				$remember_token = $user->remember_token;
			} else {
				$remember_token = Str::random(60);
				$user = User::find($user->id);
				$user->remember_token = $remember_token;
				$user->save();
			}

			$request->session()->forget('forgotPassPhone');
			
			\Session::flash('success', 'You can reset your password now.');
			return redirect()->route('passwordToken', $remember_token);
		}
	}
	public function forgotResendOtp(Request $request)
	{
		$phone = $request->phone;
		if (!empty($phone)) {
			$checkUser = User::where("phone", $phone)->first();
			if ($checkUser) {
				$userId = $checkUser->id;
				$otpnumber = rand(1111, 9999);
				$phone = $checkUser->phone;
				$update = User::where("id", $userId)->update(["otp_match" => $otpnumber]);
				if ($update) {
					$request->session()->put('forgotPassPhone', $phone);

/*
//ak
					$this->helper->sms($phone, $otpnumber);
					$this->helper->sendEmail($checkUser->email, 'BrainyWood: Forgot Password Verify OTP', $data = array('userName' => $checkUser->name, 'message' => '<p>Thank you for connecting at BrainyWood,</p><p>You have got successfully your OTP: ' . $otpnumber . '</p>'));
*/

					\Session::flash('success', 'Verification Otp Send, Please Check your Mobile or Email id.');
					return redirect()->back();
				} else {
					\Session::flash('error', 'Somthing Went Wrong!');
					return redirect()->back();
				}
			}
		}
	}

	public function passwordToken($token)
	{
		$pagename = "Reset Password";
		$user = User::where("remember_token", $token)->first();
		if (empty($user)) {
			\Session::flash('error', 'This token has been expired, please try again!');
			return redirect()->route('home');
		}
		return view('front.reset_password', compact('pagename','user'));
	}
	public function resetPassword(Request $request)
	{
		$userId = $request->userId;
		$newPassword = $request->get('new_pass');
		$confirmPassword = $request->get('con_pass');
		if ($newPassword != $confirmPassword) {
			\Session::flash('error', 'Password and Confirm password not matched!');
			return back();
		}
		$users = User::findOrFail($userId);
		$users->password = bcrypt($newPassword);
		$users->userpass = $newPassword;
		$users->save();
		
		\Session::flash('success', 'Password reset successfully.');
		return redirect()->route('home');
	}

	public function ourCourses(Request $request)
	{
		$pagename = "Our Courses";
		$stClasses = StudentClass::where('status',1)->where('deleted',0)->orderBy('id', 'ASC')->get();
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
		}else{
			$userId = $user->id;
			if($request->session()->has('openModal')){
				$request->session()->forget('openModal');
			}
		}

		$userSubscription = $this->helper->userSubscription($userId);
		
		$update = ($userId>0) ? User::where("id", $userId)->update(['updated_at'=>date('Y-m-d H:i:s')]) : '';
		
		$class_id = !empty($user) ? $user->class_id : 1;
		$classId = ($request->class_id) ? $request->class_id : $class_id;
		$search   = $request->search;
		$classSubject = StudentClassSubject::where("class_id", $classId)->first();
		$academicCourseData = $skillCourseData = array();
		if (!empty($classSubject)) {
			$subject_ids = !empty($classSubject->subject_ids) ? explode(",", $classSubject->subject_ids) : [];
			$acad = $skil = 0;
			foreach ($subject_ids as $subject) {
				$subjectdata = Subject::where("id",$subject)->first();
				$academicCourses = Courses::where("class_type", 301)->whereRaw("find_in_set($classId,class_ids)")->where("subject_id", $subject)->where("status", 1)->where('deleted', 0);
				if (!empty($search)) {
					$academicCourses = $academicCourses->where("name", 'like', "%" . $search . "%");
				}
				$academicCourses = $academicCourses->orderBy('sort_id', 'ASC')->get();
				$academicCourseArr = array();
				if (!empty($academicCourses)) {
					foreach ($academicCourses as $key => $academicCourse) {
						array_push($academicCourseArr, $academicCourse);
					}
					if (!empty($academicCourseArr)) {
						$academicCourseData[$acad]['subject_title'] = $subjectdata->title;
						$academicCourseData[$acad++]['academic_courses'] = $academicCourseArr;
					}
				}
				$skillCourses = Courses::where("class_type", 302)->whereRaw("find_in_set($classId,class_ids)")->where("subject_id", $subject)->where("status", 1)->where('deleted', 0);
				if (!empty($search)) {
					$skillCourses = $skillCourses->where("name", 'like', "%" . $search . "%");
				}
				$skillCourses = $skillCourses->orderBy('sort_id', 'ASC')->get();
				$skillCourseArr = array();
				if (!empty($skillCourses)) {
					foreach ($skillCourses as $key => $skillCourse) {
						array_push($skillCourseArr, $skillCourse);
					}
					if (!empty($skillCourseArr)) {
						$skillCourseData[$skil]['subject_title'] = $subjectdata->title;
						$skillCourseData[$skil++]['skill_courses'] = $skillCourseArr;
					}
				}
			}
		}

		if($request->session()->has('quizTime')){
			$request->session()->forget('quizTime');
			$request->session()->forget('quizId');
		}

		if ($userId > 0 && $user->role_id == 3) {
			$this->helper->trackingApi($userId, 'course', '', 0, '', 'website');
		}

		return view('front.our_courses', compact('pagename','stClasses','userSubscription','classId','search','academicCourseData','skillCourseData'));
	}

	public function courseDetails(Request $request, $id)
	{
		$pagename = "Course Details";
		$course = Courses::where("id", $id)->where("status", 1)->where("deleted", 0)->first();
		if (empty($course)) {
			\Session::flash('error', 'Course not available.');
			return redirect()->route('ourCourses');
		}
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			\Session::flash('error', 'Please login first to proceed.');
			return redirect()->route('ourCourses');
		}else{
			$userId = $user->id;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		if ($userId > 0 && $user->role_id == 3) {
			$this->helper->trackingApi($userId, 'course', '', $id, '', 'website');
		}
		$userSubscription = $this->helper->userSubscription($userId);
		
		if($course->isFree==1){
			if($userSubscription==0){
				\Session::flash('error', 'Please subscribe a plan first to proceed.');
				return redirect()->route('ourCourses');
			}
		}
		$getQuiz = Quiz::where("courseId", $id)->where("islession", 1)->where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->first();
		$examId = 0;
		$checkStudentExam = StudentExam::where("user_id", $userId)->where("course_id", $id)->where("lession_id", 0)->where("topic_id", 0)->orderBy("id", "DESC")->first();
		if (!empty($checkStudentExam)) {
			$examId = $checkStudentExam->id;
			if ($checkStudentExam->start_time != '00:00:00') {
				$certificate_status = $checkStudentExam->is_complete;
			} else {
				$certificate_status = 0;
			}
		} else {
			$certificate_status = 0;
		}
		$courseFeatures = Coursefeature::where("courseId", $id)->orderBy("id", "ASC")->get();
		$courseFaqs = Coursefeq::where("courseId", $id)->orderBy("id", "ASC")->get();
		$lessions = Lession::where("courseId", $id)->where("status", 1)->where("deleted", 0)->orderBy("sort_id", "ASC")->get();
		//$ratingMessages = RatingMessage::groupBy("message")->get();
		$ratingMessages = RatingMessage::get();
		$assignments = Assignment::where("course_id", $id)->where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->get();
		if($request->session()->has('quizTime')){
			$request->session()->forget('quizTime');
			$request->session()->forget('quizId');
		}

		return view('front.course_details', compact('pagename','course','userSubscription','getQuiz','examId','certificate_status','courseFeatures','courseFaqs','lessions','ratingMessages','assignments'));
	}

	public function lessionDetails(Request $request, $id)
	{
		$pagename = "Lesson Details";
		$lession = Lession::where("id", $id)->where("status", 1)->where("deleted", 0)->first();
		if (empty($lession)) {
			return redirect()->route('ourCourses');
		}
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			\Session::flash('error', 'Please login first to proceed.');
			return redirect()->route('ourCourses');
		}else{
			$userId = $user->id;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		if ($userId > 0 && $user->role_id == 3) {
			$this->helper->trackingApi($userId, 'lesson', '', $id, '', 'website');
		}
		$userSubscription = $this->helper->userSubscription($userId);
		
		if($lession->isFree==1){
			if($userSubscription==0){
				\Session::flash('error', 'Please subscribe a plan first to proceed.');
				return redirect()->route('ourCourses');
			}
		}
		$getQuiz = Quiz::where("courseId", $lession->courseId)->where("lessionId", $id)->where("islession", 0)->where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->first();
		$examId = 0;
		$checkStudentExam = StudentExam::where("user_id", $userId)->where("course_id", $lession->courseId)->where("lession_id", $id)->where("topic_id", 0)->orderBy("id","DESC")->first();
		if (!empty($checkStudentExam)) {
			$examId = $checkStudentExam->id;
			$is_complete = $checkStudentExam->is_complete;
		} else {
			$is_complete = 0;
		}
		$lessions = Lession::where("courseId", $lession->courseId)->where("status", 1)->where("deleted", 0)->orderBy("sort_id", "ASC")->get();
		$topics = Chapter::where("courseId", $lession->courseId)->where("lessionId", $id)->where("status", 1)->where("deleted", 0)->orderBy("sort_id", "ASC")->get();
		//$ratingMessages = RatingMessage::groupBy("message")->get();
		$ratingMessages = RatingMessage::get();
		if($request->session()->has('quizTime')){
			$request->session()->forget('quizTime');
			$request->session()->forget('quizId');
		}

		return view('front.lession_details', compact('id','pagename','lession','userSubscription','getQuiz','examId','is_complete','lessions','topics','ratingMessages'));
	}

	public function topicDetails(Request $request, $id)
	{
		$pagename = "Topic Details";
		$topic = Chapter::where("id", $id)->where("status", 1)->where("deleted", 0)->first();
		if (empty($topic)) {
			return redirect()->route('ourCourses');
		}
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			\Session::flash('error', 'Please login first to proceed.');
			return redirect()->route('ourCourses');
		}else{
			$userId = $user->id;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		if ($userId > 0 && $user->role_id == 3) {
			$this->helper->trackingApi($userId, 'topic', '', $id, '', 'website');
		}
		$userSubscription = $this->helper->userSubscription($userId);
		
		if($topic->isFree==1){
			if($userSubscription==0){
				\Session::flash('error', 'Please subscribe a plan first to proceed.');
				return redirect()->route('ourCourses');
			}
		}
		$getQuiz = Quiz::where("courseId", $topic->courseId)->where("lessionId", $topic->lessionId)->where("topicId", $topic->id)->where("islession", 2)->where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->first();
		$examId = 0;
		$checkStudentExam = StudentExam::where("user_id", $userId)->where("course_id", $topic->courseId)->where("lession_id", $topic->lessionId)->where("topic_id", $id)->orderBy("id","DESC")->first();
		if (!empty($checkStudentExam)) {
			$examId = $checkStudentExam->id;
			$is_complete = $checkStudentExam->is_complete;
		} else {
			$is_complete = 0;
		}
		$topics = Chapter::where("courseId", $topic->courseId)->where("lessionId", $topic->lessionId)->where("status", 1)->where("deleted", 0)->orderBy("sort_id", "ASC")->get();
		//$ratingMessages = RatingMessage::groupBy("message")->get();
		$ratingMessages = RatingMessage::get();
		if($request->session()->has('quizTime')){
			$request->session()->forget('quizTime');
			$request->session()->forget('quizId');
		}

		return view('front.topic_details', compact('id','pagename','topic','userSubscription','getQuiz','examId','is_complete','topics','ratingMessages'));
	}

	public function rateByUser(Request $request)
	{
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			\Session::flash('error', 'You are not allow to access here!');
			return redirect()->route('home');
		}
		$userId = $user->id;
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}

		$validator = Validator::make($request->all(), [
			'message' => 'required',
		]);

		if ($validator->fails()) {
			return back()
				->withErrors($validator)
				->withInput();
		} else {
			$courseId 	= $request->courseId;
			$lessionId  = $request->lessionId;
			$topicId 	= $request->topicId;
			$ratingType = $request->ratingType;
			$ratingMessage = implode(',', $request->ratingMessage);
			$message 	= $request->message;
			$msg = '';
			$data = array(
				'userId' 		=> $userId,
				'courseId' 		=> $courseId,
				'lessionId' 	=> $lessionId,
				'topicId' 		=> $topicId,
				'ratingType' 	=> $ratingType,
				'ratingMessage' => $ratingMessage,
				'message' 		=> $message,
				'status' 		=> 0,
				'created_at' 	=> date('Y-m-d H:i:s'),
			);
			$insertId = RatingUser::insertGetId($data);
			\Session::flash('success', 'Rating Submitted Successfully.');
			return redirect()->back();
		}
	}

	public function courseAssignments(Request $request, $id)
	{
		$id = base64_decode($id);
		$pagename = "Course Assignments";
		$course = Courses::where("id", $id)->where("status", 1)->where("deleted", 0)->first();
		if (empty($course)) {
			\Session::flash('error', 'Course not available.');
			return redirect()->route('ourCourses');
		}
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			\Session::flash('error', 'Please login first to proceed.');
			return redirect()->route('ourCourses');
		}else{
			$userId = $user->id;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		if ($userId > 0 && $user->role_id == 3) {
			$this->helper->trackingApi($userId, 'course', '', $id, '', 'website');
		}
		$userSubscription = $this->helper->userSubscription($userId);
		
		if($course->isFree==1){
			if($userSubscription==0){
				\Session::flash('error', 'Please subscribe a plan first to proceed.');
				return redirect()->route('ourCourses');
			}
		}
		$assignments = Assignment::where("course_id", $id)->where("status", 1)->where("deleted", 0)->orderBy("sort_id", "ASC")->get();

		return view('front.course_assignments', compact('pagename','course','userSubscription','assignments'));
	}

	public function assignmentDetails(Request $request, $id)
	{
		$id = base64_decode($id);
		$pagename = "Assignment Details";
		$assignment = Assignment::where("id", $id)->where("status", 1)->where("deleted", 0)->first();
		if (empty($assignment)) {
			return redirect()->route('ourCourses');
		}
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			\Session::flash('error', 'Please login first to proceed.');
			return redirect()->route('ourCourses');
		}else{
			$userId = $user->id;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		if ($userId > 0 && $user->role_id == 3) {
			$this->helper->trackingApi($userId, 'assignment', '', $id, '', 'website');
		}
		$userSubscription = $this->helper->userSubscription($userId);
		
		if($userSubscription==0){
			\Session::flash('error', 'Please subscribe a plan first to proceed.');
			return redirect()->route('ourCourses');
		}
		$userAssignmentSubmitted = AssignmentSubmission::where("assignment_id", $assignment->id)->where("user_id", $userId)->where("user_status", 1)->where("deleted", 0)->orderBy("id","DESC")->first();
		$submit_status = !empty($userAssignmentSubmitted) ? 1 : 0;

		return view('front.assignment_details', compact('id','pagename','assignment','userSubscription','userAssignmentSubmitted','submit_status'));
	}

	public function assignmentUpload(Request $request, $id)
	{
		$id = base64_decode($id);
		$pagename = "Assignment Upload";
		$assignment = Assignment::where("id", $id)->where("status", 1)->where("deleted", 0)->first();
		if (empty($assignment)) {
			return redirect()->route('ourCourses');
		}
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			\Session::flash('error', 'Please login first to proceed.');
			return redirect()->route('ourCourses');
		}else{
			$userId = $user->id;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$userSubscription = $this->helper->userSubscription($userId);
		
		if($userSubscription==0){
			\Session::flash('error', 'Please subscribe a plan first to proceed.');
			return redirect()->route('ourCourses');
		}

		return view('front.assignment_upload', compact('id','pagename','assignment','userSubscription'));
	}
	public function assignmentSubmit(Request $request, $id)
	{
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			\Session::flash('error', 'Please login first to proceed.');
			return redirect()->route('ourCourses');
		}else{
			$userId = $user->id;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		//dd($request->all());
		$assignmentId = $id;
		$userContent = $request->user_content;
		if (!empty($userId)  && !empty($assignmentId) || !empty($userContent) || !empty($request->file('user_images_file')) || !empty($request->file('user_videos_file')) || !empty($request->file('user_other_file')) ) {
			$image_files = $request->file('user_images_file');
			$video_files = $request->file('user_videos_file');
			$other_files = $request->file('user_other_file');
			$imagesData = [];
			if($request->hasFile('user_images_file')){
				foreach($image_files as $image_file){
					if($image_file){
						$destinationPath = public_path().'/upload/assignments/';
						$imageOriginalFile = $image_file->getClientOriginalName();
						$imageFilename = "user_".$assignmentId."_".time().$imageOriginalFile;
						$image_file->move($destinationPath, $imageFilename);
						/*$url = 'upload/assignments/' . $imageFilename;
						$thumb_img = Image::make($url)->resize(200, 200);
						$thumb_img->save('upload/assignments/thumb/'.$imageFilename,80);*/
						//S3 Upload
						$s3 = AWS::createClient('s3');
						$s3->putObject(array(
							'Bucket'     => env('AWS_BUCKET'),
							'Key'        => "assignment_data/".$imageFilename,
							'SourceFile' => $destinationPath.$imageFilename,
						));
						$imagesData[] = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $imageFilename;
						if(!empty($image_file) && file_exists( $destinationPath.$imageFilename )) {
							unlink( $destinationPath.$imageFilename );
						}
					}
				}
			}
			$videosData = [];
			if($request->hasFile('user_videos_file')){
				foreach($video_files as $video_file){
					if($video_file){
						$destinationPath = public_path().'/upload/assignments/';
						$videoOriginalFile = $video_file->getClientOriginalName();
						//$videoFilename = rand(111,999).strtotime(date('Y-m-d-H:isa')).$videoOriginalFile;
						$videoFilename = "user_".$assignmentId."_".time()."_org.mp4";
						$video_file->move($destinationPath, $videoFilename);
						//S3 Upload
						$s3 = AWS::createClient('s3');
						$s3->putObject(array(
							'Bucket'     => env('AWS_BUCKET'),
							'Key'        => "assignment_data/".$videoFilename,
							'SourceFile' => $destinationPath.$videoFilename,
						));
						$videosData[] = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $videoFilename;
						if(!empty($video_file) && file_exists( $destinationPath.$videoFilename )) {
							unlink( $destinationPath.$videoFilename );
						}
					}
				}
			}
			$otherfilesData = [];
			if($request->hasFile('user_other_file')){
				foreach($other_files as $other_file){
					if($other_file){
						$destinationPath = public_path().'/upload/assignments/';
						$otherOriginalFile = $other_file->getClientOriginalName();
						$otherFilename = "user_".$assignmentId."_".time().$otherOriginalFile;
						$other_file->move($destinationPath, $otherFilename);
						//S3 Upload
						$s3 = AWS::createClient('s3');
						$s3->putObject(array(
							'Bucket'     => env('AWS_BUCKET'),
							'Key'        => "assignment_data/".$otherFilename,
							'SourceFile' => $destinationPath.$otherFilename,
						));
						$otherfilesData[] = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $otherFilename;
						if(!empty($other_file) && file_exists( $destinationPath.$otherFilename )) {
							unlink( $destinationPath.$otherFilename );
						}
					}
				}
			}
			//Assign Teacher who already assigned for Re-Assignment.
			$getUserPreAssignmnt = AssignmentSubmission::where("assignment_id", $assignmentId)->where("user_id", $userId)->where("assigned_to", ">", 0)->where("deleted", 0)->orderBy("id", "DESC")->first();
			$assigned_to = !empty($getUserPreAssignmnt) ? $getUserPreAssignmnt->assigned_to : 0;
			$data = array(
				'assignment_id'		=> $assignmentId,
				'user_id'			=> $userId,
				'user_images'		=> !empty($imagesData) ? implode(",", $imagesData) : NULL,
				'user_videos'		=> !empty($videosData) ? implode(",", $videosData) : NULL,
				'user_other_files' 	=> !empty($otherfilesData) ? implode(",", $otherfilesData) : NULL,
				'user_content'		=> $userContent,
				'user_status'		=> 1,
				'assigned_to'		=> $assigned_to,
				'teacher_approved'  => 2,
				'created_at'		=> date('Y-m-d H:i:s'),
			);
			$assignSubId = AssignmentSubmission::insertGetId($data);
			//$update  = AssignmentSubmission::where("id", $assignSubId)->update([ 'assigned_to' => $assigned_to ]);
			//Refer point on Assignment Submission
			$this->helper->addUserReferPoints($userId,43);
			$totalAssignmentSubmission = AssignmentSubmission::where("user_id", $userId)->count();
			if ($totalAssignmentSubmission == 10) {
				$this->helper->addUserReferPoints($userId,44);
			} elseif ($totalAssignmentSubmission == 20) {
				$this->helper->addUserReferPoints($userId,45);
			} elseif ($totalAssignmentSubmission == 30) {
				$this->helper->addUserReferPoints($userId,46);
			} elseif ($totalAssignmentSubmission == 40) {
				$this->helper->addUserReferPoints($userId,47);
			} elseif ($totalAssignmentSubmission == 50) {
				$this->helper->addUserReferPoints($userId,48);
			}
			\Session::flash('success', 'Your Assignment uploaded successfully, Brain Science Experts will check and revert.');
			//return back();
			return redirect()->route('userAssignments');
		}else{
			\Session::flash('error', 'Wrong Paramenter Passed!');
			return back();
		}
	}

	public function submittedAssignments(Request $request)
	{
		$pagename = "My Assignments";
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			\Session::flash('error', 'Please login first to proceed.');
			return redirect()->route('ourCourses');
		}else{
			$userId = $user->id;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		if ($userId > 0 && $user->role_id == 3) {
			$this->helper->trackingApi($userId, 'assignment', '', 0, '', 'website');
		}
		$userAssignments = AssignmentSubmission::with("assignment","user")->where("user_id", $userId)->where("user_status", 1)->where("deleted", 0)->groupBy("assignment_id")->orderBy("id","DESC")->get();

		return view('front.user_assignments', compact('pagename','userAssignments'));
	}

	public function assignmentReview(Request $request, $id, $userAssignmentId)
	{
		$id = base64_decode($id);
		$userAssignmentId = base64_decode($userAssignmentId);
		$pagename = "Assignment Review";
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			\Session::flash('error', 'Please login first to proceed.');
			return redirect()->route('ourCourses');
		}else{
			$userId = $user->id;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		if ($userId > 0 && $user->role_id == 3) {
			$this->helper->trackingApi($userId, 'user_assignment', 'teacher reviewed', $id, '', 'website');
		}
		$assignment = Assignment::where("id", $id)->where("status", 1)->where("deleted", 0)->first();
		$userAssignmentSubmitted = AssignmentSubmission::where("assignment_id", $id)->where("user_id", $userId)->where("user_status", 1)->where("deleted", 0)->get();
		$userAssignment = AssignmentSubmission::with("assignment","user")->where("id", $userAssignmentId)->where("user_status", 1)->where("deleted", 0)->first();
		if (empty($userAssignment)) {
			return redirect()->route('userAssignments');
		}

		return view('front.user_assignment_review', compact('id','pagename','assignment','userAssignmentSubmitted','userAssignment'));
	}

	public function getQuiz(Request $request, $id)
	{
		$pagename = "Quiz";
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			\Session::flash('error', 'You are not allow to access here!');
			return redirect()->route('home');
		}
		$userId = $user->id;
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$examId = 0;
		$quiz = Quiz::where("id", $id)->where("status", 1)->where("deleted", 0)->first();
		if (!empty($quiz)) {
			$examQuestions = Quizquestions::where("quizId", $quiz->id)->get();
			$courseId = $quiz->courseId;
			$lessionId = $quiz->lessionId;
			$topicId = $quiz->topicId;
			$checkStudentExam = StudentExam::where("user_id", $userId)->where("course_id", $courseId)->where("lession_id", $lessionId)->where("topic_id", $topicId)->where("is_complete", 0)->first();
			if (!empty($checkStudentExam)) {
				$examId = $checkStudentExam->id;
				$data = array(
					'start_time' => date('H:i:s'),
					'end_time'   => date('H:i:s'),
					'updated_at' => date('Y-m-d H:i:s'),
				);
				$update = StudentExam::where("id", $examId)->update($data);
				//$duration = $quiz->duration;
				$time = explode(':', $quiz->duration);
				$durationInMinutes = ($time[0]*60) + ($time[1]) + ($time[2]/60);
				$takenMinutes = 0;
				if ($checkStudentExam->total_time != ''){
					$takenTime = explode(':', $checkStudentExam->total_time);
					$takenMinutes = ($takenTime[0]*60) + ($takenTime[1]) + ($takenTime[2]/60);
					$durationInMinutes = round($durationInMinutes - $takenMinutes);
				}
				//echo $durationInMinutes;
				//"2021-06-19 16:40:00";
				$dateTime = date("Y-m-d H:i:s");
				$dateTime = strtotime($dateTime);
				$dateTime = strtotime("+".$durationInMinutes." minute", $dateTime);
				$duration = date('M d, Y H:i:s', $dateTime);
				if($request->session()->has('quizId') && $request->session()->get('quizId')==$id){
					if($request->session()->has('quizTime')){
						$quizTime = $request->session()->get('quizTime');
						//echo $quizTime; //die;
					}else{
						$quizTime = $request->session()->put('quizTime', $duration);
						return redirect()->route('getQuiz',$id);
					}
				}else{
					$quizId = $request->session()->put('quizId', $id);
					$quizTime = $request->session()->put('quizTime', $duration);
					return redirect()->route('getQuiz',$id);
				}
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'quiz', 'quiz re-start', $id, '', 'website');
				}
			} else {
				$data = array(
					'user_id'     => $userId,
					'course_id'   => $courseId,
					'lession_id'  => $lessionId,
					'topic_id' 	  => $topicId,
					'start_time'  => date('H:i:s'),
					'is_complete' => 0,
					'created_at'  => date('Y-m-d H:i:s'),
				);
				$examId = StudentExam::insertGetId($data);
				//$duration = $quiz->duration;
				$time = explode(':', $quiz->duration);
				$durationInMinutes = ($time[0]*60) + ($time[1]) + ($time[2]/60);
				$date = date("Y-m-d H:i:s"); //"2021-06-19 16:40:00";
				$date = strtotime($date);
				$date = strtotime("+".$durationInMinutes." minute", $date);
				$duration = date('M d, Y H:i:s', $date); //"June 19, 2021 16:40:00";

				//Refer point on Attempt Quiz
				$userPoint = UserPoint::where("user_id", $userId)->where("module_id", $id)->where("refer_point_id", 49)->first();
				if (empty($userPoint)) {
					$this->helper->addUserReferPoints($userId,49,$id);
				}
				if($request->session()->has('quizId') && $request->session()->get('quizId')==$id){
					if($request->session()->has('quizTime')){
						$quizTime = $request->session()->get('quizTime');
					}else{
						$quizTime = $request->session()->put('quizTime', $duration);
						return redirect()->route('getQuiz',$id);
					}
				}else{
					$quizId = $request->session()->put('quizId', $id);
					$quizTime = $request->session()->put('quizTime', $duration);
					return redirect()->route('getQuiz',$id);
				}
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'quiz', 'quiz start', $id, '', 'website');
				}
			}
		} else {
			\Session::flash('error', 'Quiz not available for now!');
			return redirect()->back();
		}

		//echo $quizTime; die;
		return view('front.quiz', compact('pagename','quiz','quizTime','examQuestions','examId'));
	}
	public function pauseQuiz(Request $request)
	{
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			\Session::flash('error', 'You are not allow to access here!');
			return redirect()->route('home');
		}
		$userId = $user->id;
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$examId = $request->examId;
		$studentExam = StudentExam::where("id", $examId)->where("user_id", $userId)->where("is_complete", 0)->first();
		if (!empty($studentExam)) {
			$start_time = $studentExam->start_time;
			$end_time = date('H:i:s');
			$start = Carbon::parse($start_time);
			$end = Carbon::parse($end_time);
			/*$hours = $end->diffInHours($start);
			$minutes = $end->diffInMinutes($start);*/
			$seconds = $end->diffInSeconds($start);
			$total_time = sprintf('%02d:%02d:%02d', ($seconds/3600),($seconds/60%60), $seconds%60);
			$before_total_time = $studentExam->total_time;
			if (!empty($before_total_time)) {
				$newDateTime = Carbon::parse($before_total_time)->addSeconds($seconds);
				$total_time = date('H:i:s', strtotime($newDateTime));
			}
			//echo $total_time; die;
			$data = array(
				'end_time'   => $end_time,
				'total_time' => $total_time,
				'updated_at' => date('Y-m-d H:i:s'),
			);
			$update = StudentExam::where("id", $examId)->update($data);

			if($request->session()->has('quizTime')){
				$request->session()->forget('quizTime');
				//$request->session()->forget('quizId');
			}
			$message = "Exam paused successfully.";
			echo $message;
		}
	}
	public function nextQuiz(Request $request)
	{
		$examId = $request->examId;
		$quesId = $request->quesId;
		$answer = $request->answer;
		if (!empty($answer)) {
			$attemp = 1;
		} else {
			$attemp = 0;
		}
		//echo 'EXID '.$examId.' QID '.$quesId.' ANSID '.$answer; die;
		$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $quesId)->first();
		if (!empty($studentAns)) {
			$studentAnsId = $studentAns->id;
			$data = array(
				'answer'     => $answer,
				'attemp'     => $attemp,
				'updated_at' => date('Y-m-d H:i:s'),
			);
			$update = StudentExamAnswer::where("id", $studentAnsId)->update($data);
			$message = "Answer updated successfully.";
		} else {
			$data = array(
				'exam_id'    => $examId,
				'ques_id'    => $quesId,
				'answer'     => $answer,
				'attemp'     => $attemp,
				'created_at' => date('Y-m-d H:i:s'),
			);
			$studentAnsId = StudentExamAnswer::insertGetId($data);
			$message = "Answer inserted successfully.";
		}
		echo $message;
	}
	public function endQuiz(Request $request)
	{
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id == 3 || !empty($user)){
			$userId = $user->id;
			$examId = $request->examId;
			$quesId = $request->quesId;
			$answer = $request->answer;
			if (!empty($answer)) {
				$attemp = 1;
			} else {
				$attemp = 0;
			}
			//echo 'EXID '.$examId.' QID '.$quesId.' ANSID '.$answer; die;
			$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $quesId)->first();
			if (!empty($studentAns)) {
				$studentAnsId = $studentAns->id;
				$data = array(
					'answer'     => $answer,
					'attemp'     => $attemp,
					'updated_at' => date('Y-m-d H:i:s'),
				);
				$update = StudentExamAnswer::where("id", $studentAnsId)->update($data);
				$message = "Answer updated successfully.";
			} else {
				$data = array(
					'exam_id'    => $examId,
					'ques_id'    => $quesId,
					'answer'     => $answer,
					'attemp'     => $attemp,
					'created_at' => date('Y-m-d H:i:s'),
				);
				$studentAnsId = StudentExamAnswer::insertGetId($data);
				$message = "Answer inserted successfully.";
			}
			$studentExam = StudentExam::where("id", $examId)->where("user_id", $userId)->where("is_complete", 0)->first();
			if (!empty($studentExam)) {
				$start_time = $studentExam->start_time;
				$end_time = date('H:i:s');
				$start = Carbon::parse($start_time);
				$end = Carbon::parse($end_time);
				/*$hours = $end->diffInHours($start);
				$minutes = $end->diffInMinutes($start);*/
				$seconds = $end->diffInSeconds($start);
				$total_time = sprintf('%02d:%02d:%02d', ($seconds/3600),($seconds/60%60), $seconds%60);
				$before_total_time = $studentExam->total_time;
				if (!empty($before_total_time)) {
					$newDateTime = Carbon::parse($before_total_time)->addSeconds($seconds);
					$total_time = date('H:i:s', strtotime($newDateTime));
				}
				//echo $total_time; die;
				$data1 = array(
					'end_time'   => $end_time,
					'total_time' => $total_time,
					'is_complete' => 1,
					'updated_at' => date('Y-m-d H:i:s'),
				);
				$update = StudentExam::where("id", $examId)->update($data1);
				$message = "Exam ended successfully.";
			}
			$getQuiz = $this->helper->getQuizDetails($examId, $userId);
			$quizId = !empty($getQuiz) ? $getQuiz->id : 0;
			if ($userId > 0 && $user->role_id == 3) {
				$this->helper->trackingApi($userId, 'quiz', 'quiz end', $quizId, '', 'website');
			}
		}
		echo $message;
	}
	public function autoEndQuiz(Request $request)
	{
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id == 3 || !empty($user)){
			$userId = $user->id;
			$examId = $request->examId;
			//echo 'EXID '.$examId; die;
			$studentExam = StudentExam::where("id", $examId)->where("user_id", $userId)->where("is_complete", 0)->first();
			if (!empty($studentExam)) {
				$start_time = $studentExam->start_time;
				$end_time = date('H:i:s');
				$start = Carbon::parse($start_time);
				$end = Carbon::parse($end_time);
				/*$hours = $end->diffInHours($start);
				$minutes = $end->diffInMinutes($start);*/
				$seconds = $end->diffInSeconds($start);
				$total_time = sprintf('%02d:%02d:%02d', ($seconds/3600),($seconds/60%60), $seconds%60);
				$before_total_time = $studentExam->total_time;
				if (!empty($before_total_time)) {
					$newDateTime = Carbon::parse($before_total_time)->addSeconds($seconds);
					$total_time = date('H:i:s', strtotime($newDateTime));
				}
				//echo $total_time; die;
				$data1 = array(
					'end_time'   => $end_time,
					'total_time' => $total_time,
					'is_complete' => 1,
					'updated_at' => date('Y-m-d H:i:s'),
				);
				$update = StudentExam::where("id", $examId)->update($data1);
				$message = "Exam ended successfully.";
			}
			$getQuiz = $this->helper->getQuizDetails($examId, $userId);
			$quizId = !empty($getQuiz) ? $getQuiz->id : 0;
			if ($userId > 0 && $user->role_id == 3) {
				$this->helper->trackingApi($userId, 'quiz', 'quiz end', $quizId, '', 'website');
			}
		}
		echo $message;
	}

	public function quizList(Request $request, $quizId,$examId)
	{
		$pagename = "Quiz List";
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			\Session::flash('error', 'You are not allow to access here!');
			return redirect()->route('home');
		}
		$userId = $user->id;
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$studentExam = StudentExam::where("id", $examId)->where("user_id", $userId)->where("is_complete", 0)->first();
		$examQuestions = Quizquestions::where("quizId", $quizId)->get();
		$total_questions = count($examQuestions);
		$total_attemped = $total_unattemped = 0;
		if (!empty($examQuestions)) {
			$total_attemped = StudentExamAnswer::where("exam_id", $examId)->count();
			$total_unattemped = $total_questions - $total_attemped;
		} else {
			\Session::flash('error', 'Quiz Not Found!');
			return redirect()->route('home');
		}

		return view('front.quiz_list', compact('pagename','quizId','examId','studentExam','examQuestions','total_questions','total_attemped','total_unattemped'));
	}

	public function quizResult(Request $request, $quizId, $examId)
	{
		$pagename = "Quiz Result";
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			\Session::flash('error', 'You are not allow to access here!');
			return redirect()->route('home');
		}
		$userId = $user->id;
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$quiz = Quiz::where("id", $quizId)->where("status", 1)->where("deleted", 0)->first();
		if (!empty($quiz)) {
			$islession = $quiz->islession;
			$passing_percent = $quiz->passing_percent;
			$coursename = !empty($quiz->courses->name) ? $quiz->courses->name : '';
			$quiz['examId'] = $examId;
			$examQuestions = Quizquestions::where("quizId", $quiz->id)->get();
			$total_questions = count($examQuestions);
			$quiz['total_questions'] = $total_questions;
			$studentExam = StudentExam::where("id", $examId)->where("user_id", $userId)->first();
			$quiz['time_efficiency'] = ($studentExam->total_time) ? $studentExam->total_time : '';
			$certificate = $studentExam->certificate;
			$attemped_date = date('d M, Y', strtotime($studentExam->created_at));
			$quiz['attemped_date'] = $attemped_date;
			$total_marking = $score = $right = $wrong = $total_solved = $not_solved = 0;
			foreach ($examQuestions as $key => $val) {
				if (!empty($val->image)) {
					$strUrl = $val->image;
					$arrParsedUrl = parse_url($strUrl);
					if (!empty($arrParsedUrl['scheme']))
					{
						// Contains http:// schema
						if ($arrParsedUrl['scheme'] === "http")
						{
							$questionImage = $val->image;
						}
						// Contains https:// schema
						else if ($arrParsedUrl['scheme'] === "https")
						{
							$questionImage = $val->image;
						}
					}
					// Don't contains http:// or https://
					else
					{
						$questionImage = asset('upload/quizquestions') . "/" . $val->image;
					}
				}
				$quizdata[$key]['id'] = $val->id;
				$quizdata[$key]['questions'] = $val->questions;
				$quizdata[$key]['image'] = !empty($val->image) ? $questionImage : '';
				$quizdata[$key]['marking'] = $val->marking;
				$total_marking += $val->marking;

				$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $val->id)->first();
				if (!empty($studentAns)) {
					$quizAnswers = Quizoption::where("questionId", $val->id)->orderBy("id", "ASC")->get();
					$answer = '';
					foreach ($quizAnswers as $key1 => $value) {
						if ($key1 == ($val->currect_option - 1)){
							$answer = $value->id;
						}
					}
					//if ($val->currect_option == $studentAns->answer) {
					if ($answer == $studentAns->answer) {
						$right++;
						$score += $val->marking;
					} else {
						$wrong++;
					}
				}
			}
			$total_solved = $right + $wrong;
			$not_solved = $total_questions - $total_solved;
			$quiz['total_attemped'] = $total_solved;
			$quiz['right_answer'] = $right;
			$quiz['wrong_answer'] = $wrong + $not_solved;
			$quiz['total_score'] = $score.' out of '.$total_marking;
			//$percentage = number_format((($right * 100) / $total_questions), 2);
			$percentage = number_format((($score * 100) / $total_marking), 2);
			$quiz['percentage'] = $percentage;
			if ($percentage >= $passing_percent) {
				$download_status = 1;
				$quiz['remark_status'] = 'Passed';
			} else {
				$download_status = 0;
				$quiz['remark_status'] = 'Failed';
			}
			if ($islession==1){
				$download_status = $download_status;
			} else {
				$download_status = 0;
			}
			$quiz['download_status'] = $download_status;
			if ($download_status==1) {
				if ($certificate !='') {
					$quiz['certificate_url'] = asset('upload/generatedPDF') . "/" . $certificate;
				} else {
					$user = User::where("id", $userId)->first();
					$username = $user->name;
					$newCertificate = $this->generatePDF($examId, $username, $coursename, $attemped_date);
					$quiz['certificate_url'] = asset('upload/generatedPDF') . "/" . $newCertificate;
				}
			} else {
				$quiz['certificate_url'] = '';
			}
			
			if ($download_status == 1) {
				$msg = 'Hi, '.$user->name.' Wonderful score on the recent quiz. Keep learning';
				$userPoint = UserPoint::where("user_id", $userId)->where("module_id", $quizId)->where("refer_point_id", 50)->first();
				if (empty($userPoint)) {
					//Refer point on Passing the Quiz
					$this->helper->addUserReferPoints($userId,50,$quizId);
				}
				$userPoint = UserPoint::where("user_id", $userId)->where("module_id", $quizId)->where("refer_point_id", 51)->first();
				if (empty($userPoint)) {
					if ($percentage >= 80) {
						//Refer point on Get above 80% score in Quiz
						$this->helper->addUserReferPoints($userId,51,$quizId);
					}
				}
				$totalCertificates = StudentExam::where("user_id", $userId)->where("is_complete", 1)->whereNotNull("certificate")->count();
				if ($totalCertificates == 4) {
					//Refer point on 4 Certificate Generated
					$this->helper->addUserReferPoints($userId,52);
				} elseif ($totalCertificates == 5) {
					//Refer point on 5 Certificate Generated
					$this->helper->addUserReferPoints($userId,53);
				} elseif ($totalCertificates == 10) {
					//Refer point on 10 Certificate Generated
					$this->helper->addUserReferPoints($userId,54);
				}
			} else {
				$msg = 'Sorry, Minimum '.$passing_percent.'% was needed to get the certificate. Please re-attempt the test again. All the best.';
			}
			$this->helper->addNotification($userId,$msg);

			$courseId = $quiz->courseId;
			$lessionId = $quiz->lessionId;
			$topicId = $quiz->topicId;
			$getStudentExams = StudentExam::where("user_id", $userId)->where("course_id", $courseId)->where("lession_id", $lessionId)->where("topic_id", $topicId)->where("is_complete", 1)->get();

			if($request->session()->has('quizTime')){
				$request->session()->forget('quizId');
				$request->session()->forget('quizTime');
			}

			if ($userId > 0 && $user->role_id == 3) {
				$this->helper->trackingApi($userId, 'quiz', 'result', $quizId, '', 'website');
			}

		} else {
			\Session::flash('error', 'Quiz Not Found!');
			return redirect()->route('home');
		}

		return view('front.quiz_result', compact('pagename','quiz','getStudentExams'));
	}

	public function quizResultViewQuestion(Request $request, $quizId,$examId)
	{
		$pagename = "Quiz Result View Question";
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			\Session::flash('error', 'You are not allow to access here!');
			return redirect()->route('home');
		}
		$userId = $user->id;
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$quiz = Quiz::where("id", $quizId)->where("status", 1)->where("deleted", 0)->first();
		$quizdata = array();
		if (!empty($quiz)) {
			$passing_percent = $quiz->passing_percent;
			$quiz['examId'] = $examId;
			$examQuestions = Quizquestions::where("quizId", $quiz->id)->get();
			$total_questions = count($examQuestions);
			$quiz['total_questions'] = $total_questions;
			$studentExam = StudentExam::where("id", $examId)->where("user_id", $userId)->first();
			$quiz['time_efficiency'] = !empty($studentExam) ? $studentExam->total_time : '';
			$total_marking = $score = $right = $wrong = $total_solved = $not_solved = 0;
			foreach ($examQuestions as $key => $val) {
				if (!empty($val->image)) {
					$strUrl = $val->image;
					$arrParsedUrl = parse_url($strUrl);
					if (!empty($arrParsedUrl['scheme']))
					{
						// Contains http:// schema
						if ($arrParsedUrl['scheme'] === "http")
						{
							$questionImage = $val->image;
						}
						// Contains https:// schema
						else if ($arrParsedUrl['scheme'] === "https")
						{
							$questionImage = $val->image;
						}
					}
					// Don't contains http:// or https://
					else
					{
						$questionImage = asset('upload/quizquestions') . "/" . $val->image;
					}
				}
				$quizdata[$key]['id'] = $val->id;
				$quizdata[$key]['questions'] = $val->questions;
				$quizdata[$key]['image'] = !empty($val->image) ? $questionImage : '';
				$quizdata[$key]['marking'] = $val->marking;
				$quizdata[$key]['solution'] = $val->solution;
				$total_marking += $val->marking;
				$queoptions = Quizoption::where("questionId", $val->id)->get();
				$optiondata = array();
				foreach ($queoptions as $key1 => $value) {
					$optiondata[$key1]['id'] = $value->id;
					$optiondata[$key1]['option'] = $value->quizoption;
				}
				$quizdata[$key]['answers'] = $optiondata;
				$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $val->id)->first();
				if (!empty($studentAns)) {
					$quizStAnswer = Quizoption::where("id", $studentAns->answer)->first();
				}
				//$quizdata[$key]['given_answer'] = isset($quizStAnswer->quizoption) ? $quizStAnswer->quizoption : 'NA';
				$given_answer = isset($studentAns->answer) ? $studentAns->answer : 'NA';
				$quizdata[$key]['given_answer'] = $given_answer;
				$quizAnswers = Quizoption::where("questionId", $val->id)->orderBy("id", "ASC")->get();
				$answer = '';
				foreach ($quizAnswers as $key1 => $value) {
					if ($key1 == ($val->currect_option - 1)){
						$answer = $value->id;
					}
				}
				if ($answer == $given_answer) {
					$right++;
					$score += $val->marking;
				} else {
					$wrong++;
				}
				$quizAnswer = Quizoption::where("id", $answer)->first();
				//$quizdata[$key]['correct_answer'] = isset($quizAnswer->quizoption) ? $quizAnswer->quizoption : 'NA';
				$quizdata[$key]['correct_answer'] = isset($answer) ? $answer : 'NA';
			}
			$total_solved = $right + $wrong;
			$not_solved = $total_questions - $total_solved;
			$quiz['total_attemped'] = $total_solved;
			$quiz['right_answer'] = $right;
			$quiz['wrong_answer'] = $wrong + $not_solved;
			//$percentage = number_format((($right * 100) / $total_questions), 2);
			$percentage = number_format((($score * 100) / $total_marking), 2);
			$quiz['percentage'] = $percentage;
			if ($percentage >= $passing_percent) {
				$download_status = 1;
			} else {
				$download_status = 0;
			}
			$quiz['download_status'] = $download_status;
			$quiz['certificate_url'] = asset('lessions/978material.pdf');

			if ($userId > 0 && $user->role_id == 3) {
				$this->helper->trackingApi($userId, 'quiz', 'result', $quizId, '', 'website');
			}
		} else {
			\Session::flash('error', 'Quiz Not Found!');
			return redirect()->route('home');
		}

		return view('front.quiz_result_view_question', compact('pagename','quiz','quizdata'));
	}

	public function liveClasses(Request $request)
	{
		$pagename = "Live Classes";
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			\Session::flash('error', 'You are not allow to access here!');
			return redirect()->route('home');
		}
		$userId = $user->id;
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$userSubscription = $this->helper->userSubscription($userId);
		$from = date('Y-m-d').' 00:00:00';
		$to = date('Y-m-d').' 23:59:59';
		$now = date('Y-m-d H:i:s');
		$pastClasses = LiveClass::where("status", 1)->where("deleted", 0)->where("class_time", "<", $now)->orderBy("class_time", "DESC")->get();
		$liveClasses = LiveClass::where("status", 1)->where("deleted", 0)->whereBetween("class_time", [$from, $to])->orderBy("class_time", "ASC")->get();
		$upcomingClasses = LiveClass::where("status", 1)->where("deleted", 0)->where("class_time", ">=", $now)->orderBy("class_time", "ASC")->get();

		if ($userId > 0 && $user->role_id == 3) {
			$this->helper->trackingApi($userId, 'live_class', '', 0, '', 'website');
		}

		return view('front.live_classes', compact('pagename','userSubscription','pastClasses','liveClasses','upcomingClasses'));
	}

	public function liveClassesNow(Request $request)
	{
		$pagename = "Live Classes";
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			\Session::flash('error', 'You are not allow to access here!');
			return redirect()->route('home');
		}
		$userId = $user->id;
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$userSubscription = $this->helper->userSubscription($userId);
		
		$classes = StudentClass::where("status", 1)->where("deleted", 0)->orderBy("id", "ASC")->get();
		$class_id = !empty($user) ? $user->class_id : '';
		$classId = ($request->class_id) ? $request->class_id : $class_id;
		$search = $request->search;
		$from = date('Y-m-d H:i:s', strtotime("-45 minutes")); //date('Y-m-d').' 00:00:00';
		$to = date('Y-m-d').' 23:59:59';

		$timeTableDoc = LiveClassTimeTableDoc::where("class_id", $classId)->orderBy("id", "DESC")->first();
		$downloadTimeTable = !empty($timeTableDoc) ? asset('upload/liveclass_timetables/'.$timeTableDoc->doc_file) : '';
		$pinMessage = PinMessage::where("msg_type", 204)->whereRaw("find_in_set($classId,class_ids)")->where("status", 1)->orderBy('id', 'DESC')->first();
		$pinMessageLiveClass = !empty($pinMessage) ? $pinMessage->message : '';
		$classSubject = StudentClassSubject::where("class_id", $classId)->first();
		$academicClassData = $skillClassData = array();
		if (!empty($classSubject)) {
			$subject_data = $subject_ids = [];
			if (!empty($search)) {
				$subject_data = Subject::select("id")->where("title", 'like', "%" . $search . "%")->get();
				foreach ($subject_data as $subjects) {
					$subject_ids[] = $subjects->id;
				}
				if (empty($subject_ids)) {
					$subject_data = [];
					$subject_ids = !empty($classSubject->subject_ids) ? explode(",", $classSubject->subject_ids) : [];
				}
			} else {
				$subject_ids = !empty($classSubject->subject_ids) ? explode(",", $classSubject->subject_ids) : [];
			}
			$acad = $skil = 0;
			foreach ($subject_ids as $subject) {
				$subjectdata = Subject::where("id",$subject)->first();
				$academicLiveClasses = LiveClass::with('user','subject_data')->where("class_type", 301)->whereRaw("find_in_set($classId,class_ids)")->where("subject_id", $subject)->whereBetween('class_time', [$from, $to])->where("status", 1)->where("deleted", 0);
				if (empty($subject_data)) {
					if (!empty($search)) {
						$academicLiveClasses = $academicLiveClasses->where("title", 'like', "%" . $search . "%");
					}
				}
				$academicLiveClasses = $academicLiveClasses->orderBy('class_time', 'ASC')->get();
				$academicClassNow = array();
				if (!empty($academicLiveClasses)) {
					foreach ($academicLiveClasses as $key => $val) {
						$academicClass['id']			= $val['id'];
						$academicClass['added_by']		= ($val['added_by']>0) ? $val->user->name : 'NA';
						$academicClass['title'] 		= $val['title'];
						$academicClass['subject']		= ($val['subject_id']>0) ? $val->subject_data->title : 'NA';
						$academicClass['image'] 		= asset('upload/liveclasses') . "/" . $val['image'];
						$academicClass['meeting_id']	= $val['meeting_id'];
						$academicClass['pass_code']		= $val['pass_code'];
						$academicClass['master_class']	= $val['master_class'];
						$academicClass['class_time']	= $val['class_time'];
						$academicClass['end_time']		= $val['end_time'];
						$academicClass['isFree']		= $val['isFree'];
						$check_interest = LiveclassNotify::where("user_id", $userId)->where("class_id", $val['id'])->first();
						$my_interest = !empty($check_interest) ? 1 : 0;
						$academicClass['my_interest'] 	= $my_interest;
						$total_interest = LiveclassNotify::where("class_id", $val['id'])->count();
						$academicClass['total_interest'] = $total_interest;
						array_push($academicClassNow, $academicClass);
					}
					if (!empty($academicClassNow)) {
						$academicClassData[$acad]['subject_title'] = $subjectdata->title;
						$academicClassData[$acad++]['academic_classes'] = $academicClassNow;
					}
				}
				$skillLiveClasses = LiveClass::with('user','subject_data')->where("class_type", 302)->whereRaw("find_in_set($classId,class_ids)")->where("subject_id", $subject)->whereBetween('class_time', [$from, $to])->where("status", 1)->where("deleted", 0);
				if (empty($subject_data)) {
					if (!empty($search)) {
						$skillLiveClasses = $skillLiveClasses->where("title", 'like', "%" . $search . "%")->orWhere('subject_title', 'LIKE', "%".$search."%");
					}
				}
				$skillLiveClasses = $skillLiveClasses->orderBy('class_time', 'ASC')->limit(10)->get();
				$skillClassNow = array();
				if (!empty($skillLiveClasses)) {
					foreach ($skillLiveClasses as $key => $val) {
						$skillClass['id'] 			 = $val['id'];
						$skillClass['added_by'] 	 = ($val['added_by']>0) ? $val->user->name : 'NA';
						$skillClass['title'] 		 = $val['title'];
						$skillClass['subject'] 	 	 = ($val['subject_id']>0) ? $val->subject_data->title : 'NA';
						$skillClass['image'] 		 = asset('upload/liveclasses') . "/" . $val['image'];
						$skillClass['meeting_id'] 	 = $val['meeting_id'];
						$skillClass['pass_code'] 	 = $val['pass_code'];
						$skillClass['master_class']	 = $val['master_class'];
						$skillClass['class_time'] 	 = $val['class_time'];
						$skillClass['end_time'] 	 = $val['end_time'];
						$skillClass['isFree'] 		 = $val['isFree'];
						$check_interest = LiveclassNotify::where("user_id", $userId)->where("class_id", $val['id'])->first();
						$my_interest = !empty($check_interest) ? 1 : 0;
						$skillClass['my_interest']  = $my_interest;
						$total_interest = LiveclassNotify::where("class_id", $val['id'])->count();
						$skillClass['total_interest'] = $total_interest;
						array_push($skillClassNow, $skillClass);
					}
					if (!empty($skillClassNow)) {
						$skillClassData[$skil]['subject_title'] = $subjectdata->title;
						$skillClassData[$skil++]['skill_classes'] = $skillClassNow;
					}
				}
			}
		}
		//dd($academicClassData);

		if ($userId > 0 && $user->role_id == 3) {
			$this->helper->trackingApi($userId, 'live_class', 'Live Now Live Class', 0, '', 'website');
		}

		return view('front.live_classes_now', compact('pagename','userSubscription','classes','classId','search','downloadTimeTable','pinMessageLiveClass','academicClassData','skillClassData'));
	}

	public function liveClassesUpcoming(Request $request)
	{
		$pagename = "Live Classes";
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			\Session::flash('error', 'You are not allow to access here!');
			return redirect()->route('home');
		}
		$userId = $user->id;
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$userSubscription = $this->helper->userSubscription($userId);
		
		$classes = StudentClass::where("status", 1)->where("deleted", 0)->orderBy("id", "ASC")->get();
		$class_id = !empty($user) ? $user->class_id : '';
		$classId = ($request->class_id) ? $request->class_id : $class_id;
		$search = $request->search;
		$todayFrom = date('Y-m-d H:i:s'); //date('Y-m-d').' 00:00:00';
		$todayTo = date('Y-m-d').' 23:59:59'; //date('Y-m-d H:i:s');
		$tomorrowFrom = date('Y-m-d', strtotime("+1 day")).' 00:00:00';
		$tomorrowTo = date('Y-m-d', strtotime("+1 day")).' 23:59:59';
		$dATomorrowFrom = date('Y-m-d', strtotime("+2 day")).' 00:00:00';
		$dATomorrowTo = date('Y-m-d', strtotime("+2 day")).' 23:59:59';

		$timeTableDoc = LiveClassTimeTableDoc::where("class_id", $classId)->orderBy("id", "DESC")->first();
		$downloadTimeTable = !empty($timeTableDoc) ? asset('upload/liveclass_timetables/'.$timeTableDoc->doc_file) : '';
		$pinMessage = PinMessage::where("msg_type", 205)->whereRaw("find_in_set($classId,class_ids)")->where("status", 1)->orderBy('id', 'DESC')->first();
		$pinMessageLiveClass = !empty($pinMessage) ? $pinMessage->message : '';
		$classSubject = StudentClassSubject::where("class_id", $classId)->first();
		$todayAcademicData = $todaySkillData = $tomorrowAcademicData = $tomorrowSkillData = $dATomorrowAcademicData = $dATomorrowSkillData = array();
		if (!empty($classSubject)) {
			$subject_data = $subject_ids = [];
			if (!empty($search)) {
				$subject_data = Subject::select("id")->where("title", 'like', "%" . $search . "%")->get();
				foreach ($subject_data as $subjects) {
					$subject_ids[] = $subjects->id;
				}
				if (empty($subject_ids)) {
					$subject_data = [];
					$subject_ids = !empty($classSubject->subject_ids) ? explode(",", $classSubject->subject_ids) : [];
				}
			} else {
				$subject_ids = !empty($classSubject->subject_ids) ? explode(",", $classSubject->subject_ids) : [];
			}
			$todayAca = $todaySki = $tomorrowAca = $tomorrowSki = $dATomorrowAca = $dATomorrowSki = 0;
			foreach ($subject_ids as $subject) {
				$subjectdata = Subject::where("id",$subject)->first();
				//start today classes
				$todayAcademicClasses = LiveClass::with('user','subject_data')->where("class_type", 301)->whereRaw("find_in_set($classId,class_ids)")->where("subject_id", $subject)->whereBetween('class_time', [$todayFrom, $todayTo])->where("status", 1)->where("deleted", 0);
				if (empty($subject_data)) {
					if (!empty($search)) {
						$todayAcademicClasses = $todayAcademicClasses->where("title", 'like', "%" . $search . "%")->orWhere('subject_title', 'LIKE', "%".$search."%");
					}
				}
				$todayAcademicClasses = $todayAcademicClasses->orderBy('class_time', 'ASC')->get();
				$todayAcademicClassArr = array();
				if (!empty($todayAcademicClasses)) {
					foreach ($todayAcademicClasses as $key => $val) {
						$todayAcademic['id']			= $val['id'];
						$todayAcademic['added_by']		= ($val['added_by']>0) ? $val->user->name : 'NA';
						$todayAcademic['title'] 		= $val['title'];
						$todayAcademic['subject']		= ($val['subject_id']>0) ? $val->subject_data->title : 'NA';
						$todayAcademic['image'] 		= asset('upload/liveclasses') . "/" . $val['image'];
						$todayAcademic['meeting_id']	= $val['meeting_id'];
						$todayAcademic['pass_code']	= $val['pass_code'];
						$todayAcademic['master_class']	= $val['master_class'];
						$todayAcademic['class_time']	= $val['class_time'];
						$todayAcademic['end_time']	= $val['end_time'];
						$todayAcademic['isFree']		= $val['isFree'];
						$check_interest = LiveclassNotify::where("user_id", $userId)->where("class_id", $val['id'])->first();
						$todayAcademic['my_interest'] 	= !empty($check_interest) ? 1 : 0;
						$total_interest = LiveclassNotify::where("class_id", $val['id'])->count();
						$todayAcademic['total_interest'] = $total_interest;
						array_push($todayAcademicClassArr, $todayAcademic);
					}
					if (!empty($todayAcademicClassArr)) {
						$todayAcademicData[$todayAca]['subject_title'] = $subjectdata->title;
						$todayAcademicData[$todayAca++]['today_academic_classes'] = $todayAcademicClassArr;
					}
				}
				$todaySkillClasses = LiveClass::with('user','subject_data')->where("class_type", 302)->whereRaw("find_in_set($classId,class_ids)")->where("subject_id", $subject)->whereBetween('class_time', [$todayFrom, $todayTo])->where("status", 1)->where("deleted", 0);
				if (empty($subject_data)) {
					if (!empty($search)) {
						$todaySkillClasses = $todaySkillClasses->where("title", 'like', "%" . $search . "%")->orWhere('subject_title', 'LIKE', "%".$search."%");
					}
				}
				$todaySkillClasses = $todaySkillClasses->orderBy('class_time', 'ASC')->limit(10)->get();
				$todaySkillClassArr = array();
				if (!empty($todaySkillClasses)) {
					foreach ($todaySkillClasses as $key => $val) {
						$todaySkillClass['id'] 			 = $val['id'];
						$todaySkillClass['added_by'] 	 = ($val['added_by']>0) ? $val->user->name : 'NA';
						$todaySkillClass['title'] 		 = $val['title'];
						$todaySkillClass['subject'] 	 = ($val['subject_id']>0) ? $val->subject_data->title : 'NA';
						$todaySkillClass['image'] 		 = asset('upload/liveclasses') . "/" . $val['image'];
						$todaySkillClass['meeting_id'] 	 = $val['meeting_id'];
						$todaySkillClass['pass_code'] 	 = $val['pass_code'];
						$todaySkillClass['master_class'] = $val['master_class'];
						$todaySkillClass['class_time'] 	 = $val['class_time'];
						$todaySkillClass['end_time']	= $val['end_time'];
						$todaySkillClass['isFree'] 		 = $val['isFree'];
						$check_interest = LiveclassNotify::where("user_id", $userId)->where("class_id", $val['id'])->first();
						$my_interest = !empty($check_interest) ? 1 : 0;
						$todaySkillClass['my_interest']  = $my_interest;
						$total_interest = LiveclassNotify::where("class_id", $val['id'])->count();
						$todaySkillClass['total_interest'] = $total_interest;
						array_push($todaySkillClassArr, $todaySkillClass);
					}
					if (!empty($todaySkillClassArr)) {
						$todaySkillData[$todaySki]['subject_title'] = $subjectdata->title;
						$todaySkillData[$todaySki++]['today_skill_classes'] = $todaySkillClassArr;
					}
				}
				//end today classes
				//start tomorrow classes
				$tomorrowAcademicClasses = LiveClass::with('user','subject_data')->where("class_type", 301)->whereRaw("find_in_set($classId,class_ids)")->where("subject_id", $subject)->whereBetween('class_time', [$tomorrowFrom, $tomorrowTo])->where("status", 1)->where("deleted", 0);
				if (empty($subject_data)) {
					if (!empty($search)) {
						$tomorrowAcademicClasses = $tomorrowAcademicClasses->where("title", 'like', "%" . $search . "%")->orWhere('subject_title', 'LIKE', "%".$search."%");
					}
				}
				$tomorrowAcademicClasses = $tomorrowAcademicClasses->orderBy('class_time', 'ASC')->get();
				$tomorrowAcademicClassArr = array();
				if (!empty($tomorrowAcademicClasses)) {
					foreach ($tomorrowAcademicClasses as $key => $val) {
						$tomorrowAcademic['id']			= $val['id'];
						$tomorrowAcademic['added_by']		= ($val['added_by']>0) ? $val->user->name : 'NA';
						$tomorrowAcademic['title'] 		= $val['title'];
						$tomorrowAcademic['subject']		= ($val['subject_id']>0) ? $val->subject_data->title : 'NA';
						$tomorrowAcademic['image'] 		= asset('upload/liveclasses') . "/" . $val['image'];
						$tomorrowAcademic['meeting_id']	= $val['meeting_id'];
						$tomorrowAcademic['pass_code']	= $val['pass_code'];
						$tomorrowAcademic['master_class']	= $val['master_class'];
						$tomorrowAcademic['class_time']	= $val['class_time'];
						$tomorrowAcademic['end_time']	= $val['end_time'];
						$tomorrowAcademic['isFree']		= $val['isFree'];
						$check_interest = LiveclassNotify::where("user_id", $userId)->where("class_id", $val['id'])->first();
						$tomorrowAcademic['my_interest'] 	= !empty($check_interest) ? 1 : 0;
						$total_interest = LiveclassNotify::where("class_id", $val['id'])->count();
						$tomorrowAcademic['total_interest'] = $total_interest;
						array_push($tomorrowAcademicClassArr, $tomorrowAcademic);
					}
					if (!empty($tomorrowAcademicClassArr)) {
						$tomorrowAcademicData[$tomorrowAca]['subject_title'] = $subjectdata->title;
						$tomorrowAcademicData[$tomorrowAca++]['tomorrow_academic_classes'] = $tomorrowAcademicClassArr;
					}
				}
				$tomorrowSkillClasses = LiveClass::with('user','subject_data')->where("class_type", 302)->whereRaw("find_in_set($classId,class_ids)")->where("subject_id", $subject)->whereBetween('class_time', [$tomorrowFrom, $tomorrowTo])->where("status", 1)->where("deleted", 0);
				if (empty($subject_data)) {
					if (!empty($search)) {
						$tomorrowSkillClasses = $tomorrowSkillClasses->where("title", 'like', "%" . $search . "%")->orWhere('subject_title', 'LIKE', "%".$search."%");
					}
				}
				$tomorrowSkillClasses = $tomorrowSkillClasses->orderBy('class_time', 'ASC')->limit(10)->get();
				$tomorrowSkillClassArr = array();
				if (!empty($tomorrowSkillClasses)) {
					foreach ($tomorrowSkillClasses as $key => $val) {
						$tomorrowSkillClass['id'] 			 = $val['id'];
						$tomorrowSkillClass['added_by'] 	 = ($val['added_by']>0) ? $val->user->name : 'NA';
						$tomorrowSkillClass['title'] 		 = $val['title'];
						$tomorrowSkillClass['subject'] 	 = ($val['subject_id']>0) ? $val->subject_data->title : 'NA';
						$tomorrowSkillClass['image'] 		 = asset('upload/liveclasses') . "/" . $val['image'];
						$tomorrowSkillClass['meeting_id'] 	 = $val['meeting_id'];
						$tomorrowSkillClass['pass_code'] 	 = $val['pass_code'];
						$tomorrowSkillClass['master_class'] = $val['master_class'];
						$tomorrowSkillClass['class_time'] 	 = $val['class_time'];
						$tomorrowSkillClass['end_time'] 	 = $val['end_time'];
						$tomorrowSkillClass['isFree'] 		 = $val['isFree'];
						$check_interest = LiveclassNotify::where("user_id", $userId)->where("class_id", $val['id'])->first();
						$my_interest = !empty($check_interest) ? 1 : 0;
						$tomorrowSkillClass['my_interest']  = $my_interest;
						$total_interest = LiveclassNotify::where("class_id", $val['id'])->count();
						$tomorrowSkillClass['total_interest'] = $total_interest;
						array_push($tomorrowSkillClassArr, $tomorrowSkillClass);
					}
					if (!empty($tomorrowSkillClassArr)) {
						$tomorrowSkillData[$tomorrowSki]['subject_title'] = $subjectdata->title;
						$tomorrowSkillData[$tomorrowSki++]['tomorrow_skill_classes'] = $tomorrowSkillClassArr;
					}
				}
				//end tomorrow classes
				//start day after tomorrow classes
				$dATomorrowAcademicClasses = LiveClass::with('user','subject_data')->where("class_type", 301)->whereRaw("find_in_set($classId,class_ids)")->where("subject_id", $subject)->whereBetween('class_time', [$dATomorrowFrom, $dATomorrowTo])->where("status", 1)->where("deleted", 0);
				if (empty($subject_data)) {
					if (!empty($search)) {
						$dATomorrowAcademicClasses = $dATomorrowAcademicClasses->where("title", 'like', "%" . $search . "%")->orWhere('subject_title', 'LIKE', "%".$search."%");
					}
				}
				$dATomorrowAcademicClasses = $dATomorrowAcademicClasses->orderBy('class_time', 'ASC')->get();
				$dATomorrowAcademicClassArr = array();
				if (!empty($dATomorrowAcademicClasses)) {
					foreach ($dATomorrowAcademicClasses as $key => $val) {
						$dATomorrowAcademic['id']			= $val['id'];
						$dATomorrowAcademic['added_by']		= ($val['added_by']>0) ? $val->user->name : 'NA';
						$dATomorrowAcademic['title'] 		= $val['title'];
						$dATomorrowAcademic['subject']		= ($val['subject_id']>0) ? $val->subject_data->title : 'NA';
						$dATomorrowAcademic['image'] 		= asset('upload/liveclasses') . "/" . $val['image'];
						$dATomorrowAcademic['meeting_id']	= $val['meeting_id'];
						$dATomorrowAcademic['pass_code']	= $val['pass_code'];
						$dATomorrowAcademic['master_class']	= $val['master_class'];
						$dATomorrowAcademic['class_time']	= date('d/m/Y, h:i A', strtotime($val['class_time']));
						$dATomorrowAcademic['isFree']		= $val['isFree'];
						$check_interest = LiveclassNotify::where("user_id", $userId)->where("class_id", $val['id'])->first();
						$dATomorrowAcademic['my_interest'] 	= !empty($check_interest) ? 1 : 0;
						$total_interest = LiveclassNotify::where("class_id", $val['id'])->count();
						$dATomorrowAcademic['total_interest'] = $total_interest;
						array_push($dATomorrowAcademicClassArr, $dATomorrowAcademic);
					}
					if (!empty($dATomorrowAcademicClassArr)) {
						$dATomorrowAcademicData[$dATomorrowAca]['subject_title'] = $subjectdata->title;
						$dATomorrowAcademicData[$dATomorrowAca++]['dATomorrow_academic_classes'] = $dATomorrowAcademicClassArr;
					}
				}
				$dATomorrowSkillClasses = LiveClass::with('user','subject_data')->where("class_type", 302)->whereRaw("find_in_set($classId,class_ids)")->where("subject_id", $subject)->whereBetween('class_time', [$dATomorrowFrom, $dATomorrowTo])->where("status", 1)->where("deleted", 0);
				if (empty($subject_data)) {
					if (!empty($search)) {
						$dATomorrowSkillClasses = $dATomorrowSkillClasses->where("title", 'like', "%" . $search . "%")->orWhere('subject_title', 'LIKE', "%".$search."%");
					}
				}
				$dATomorrowSkillClasses = $dATomorrowSkillClasses->orderBy('class_time', 'ASC')->limit(10)->get();
				$dATomorrowSkillClassArr = array();
				if (!empty($dATomorrowSkillClasses)) {
					foreach ($dATomorrowSkillClasses as $key => $val) {
						$dATomorrowSkillClass['id'] 		 = $val['id'];
						$dATomorrowSkillClass['added_by'] 	 = ($val['added_by']>0) ? $val->user->name : 'NA';
						$dATomorrowSkillClass['title'] 		 = $val['title'];
						$dATomorrowSkillClass['subject'] 	 = ($val['subject_id']>0) ? $val->subject_data->title : 'NA';
						$dATomorrowSkillClass['image'] 		 = asset('upload/liveclasses') . "/" . $val['image'];
						$dATomorrowSkillClass['meeting_id']  = $val['meeting_id'];
						$dATomorrowSkillClass['pass_code'] 	 = $val['pass_code'];
						$dATomorrowSkillClass['master_class'] = $val['master_class'];
						$dATomorrowSkillClass['class_time']   = date('d/m/Y, h:i A', strtotime($val['class_time']));
						$dATomorrowSkillClass['isFree'] 	  = $val['isFree'];
						$check_interest = LiveclassNotify::where("user_id", $userId)->where("class_id", $val['id'])->first();
						$my_interest = !empty($check_interest) ? 1 : 0;
						$dATomorrowSkillClass['my_interest']  = $my_interest;
						$total_interest = LiveclassNotify::where("class_id", $val['id'])->count();
						$dATomorrowSkillClass['total_interest'] = $total_interest;
						array_push($dATomorrowSkillClassArr, $dATomorrowSkillClass);
					}
					if (!empty($dATomorrowSkillClassArr)) {
						$dATomorrowSkillData[$dATomorrowSki]['subject_title'] = $subjectdata->title;
						$dATomorrowSkillData[$dATomorrowSki++]['dATomorrow_skill_classes'] = $dATomorrowSkillClassArr;
					}
				}
				//end day after tomorrow classes
			}
		}
		//dd($academicClassData);

		if ($userId > 0 && $user->role_id == 3) {
			$this->helper->trackingApi($userId, 'live_class', 'Upcoming Live Class', 0, '', 'website');
		}

		return view('front.live_classes_upcoming', compact('pagename','userSubscription','classes','classId','search','downloadTimeTable','pinMessageLiveClass','todayAcademicData','todaySkillData','tomorrowAcademicData','tomorrowSkillData','dATomorrowAcademicData','dATomorrowSkillData'));
	}

	public function liveClassesPast(Request $request)
	{
		$pagename = "Live Classes";
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			\Session::flash('error', 'You are not allow to access here!');
			return redirect()->route('home');
		}
		$userId = $user->id;
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$userSubscription = $this->helper->userSubscription($userId);
		
		$classes = StudentClass::where("status", 1)->where("deleted", 0)->orderBy("id", "ASC")->get();
		$class_id = !empty($user) ? $user->class_id : '';
		$classId = ($request->class_id) ? $request->class_id : $class_id;
		$search = $request->search;
		$subject_id = $request->subject_id;
		//$search_date = ($request->search_date) ? date('Y-m-d',strtotime($request->search_date)) : '';
		$search_date = ($request->search_date) ? $request->search_date : '';
		$now = date('Y-m-d H:i:s');

		$timeTableDoc = LiveClassTimeTableDoc::where("class_id", $classId)->orderBy("id", "DESC")->first();
		$downloadTimeTable = !empty($timeTableDoc) ? asset('upload/liveclass_timetables/'.$timeTableDoc->doc_file) : '';
		$pinMessage = PinMessage::where("msg_type", 206)->whereRaw("find_in_set($classId,class_ids)")->where("status", 1)->orderBy('id', 'DESC')->first();
		$pinMessageLiveClass = !empty($pinMessage) ? $pinMessage->message : '';
		if (!empty($classId)) {
			$classSubject = StudentClassSubject::where("class_id", $classId)->first();
			$subject_ids = !empty($classSubject['subject_ids']) ? explode(",", $classSubject['subject_ids']) : [];
			$subjects = Subject::whereIn("id",$subject_ids)->orderBy("title", "ASC")->get();
		} else {
			$subjects = Subject::orderBy("title", "ASC")->get();
		}
		$academicLiveClasses = LiveClass::with('user','subject_data')->where("class_type", 301)->whereRaw("find_in_set($classId,class_ids)")->where("status", 1)->where("deleted", 0);
		if (!empty($search)) {
			$subject_data = Subject::select("id")->where("title", 'like', "%" . $search . "%")->first();
			$srch_subject_id = !empty($subject_data) ? $subject_data->id : '';
			if (!empty($srch_subject_id)) {
				$academicLiveClasses = $academicLiveClasses->where('subject_id', $srch_subject_id);
			} else {
				$academicLiveClasses = $academicLiveClasses->where("title", 'like', "%" . $search . "%");
			}
		}
		if (!empty($subject_id)) {
			$academicLiveClasses = $academicLiveClasses->whereHas('subject_data', function($query) use ($subject_id) {
						if (isset($subject_id) && !empty($subject_id)) {
							$query->where('subject_id', $subject_id);
						}
					});
		}
		if (!empty($search_date)) {
			$academicLiveClasses = $academicLiveClasses->whereDate('class_time', $search_date);
		/*} else {
			$academicLiveClasses = $academicLiveClasses->where('end_time', '<', $now);*/
		}
		$academicLiveClasses = $academicLiveClasses->orderBy('class_time', 'ASC')->get();
		$academicClassData = array();
		if (!empty($academicLiveClasses)) {
			foreach ($academicLiveClasses as $key => $val) {
				$academicClass['id']			= $val['id'];
				$academicClass['class_date']	= date('d F, Y', strtotime($val['class_time']));
				$academicClass['class_time']	= date('h:i A', strtotime($val['class_time'])).' to '.date('h:i A', strtotime($val['end_time']));
				$academicClass['subject']		= ($val['subject_id']>0) ? $val->subject_data->title : 'NA';
				$academicClass['title']			= $val['title'];
				$academicClass['video']			= !empty($val['video']) ? $val['video'] : '';
				$academicClass['notes']			= !empty($val['pdf']) ? asset('upload/liveclasses') . "/" . $val['pdf'] : '';
				$academicClass['isFree']		= $val['isFree'];
				$academicClass['uploads3']		= $val['uploads3'];
				$academicClass['image']			= $val['image'];
				array_push($academicClassData, $academicClass);
			}
		}
		$skillLiveClasses = LiveClass::with('user','subject_data')->where("class_type", 302)->whereRaw("find_in_set($classId,class_ids)")->where("status", 1)->where("deleted", 0);
		if (!empty($search)) {
			$subject_data = Subject::select("id")->where("title", 'like', "%" . $search . "%")->first();
			$srch_subject_id = !empty($subject_data) ? $subject_data->id : '';
			if (!empty($srch_subject_id)) {
				$skillLiveClasses = $skillLiveClasses->where('subject_id', $srch_subject_id);
			} else {
				$skillLiveClasses = $skillLiveClasses->where("title", 'like', "%" . $search . "%");
			}
		}
		if (!empty($subject_id)) {
			$skillLiveClasses = $skillLiveClasses->whereHas('subject_data', function($query) use ($subject_id) {
						if (isset($subject_id) && !empty($subject_id)) {
							$query->where('subject_id', $subject_id);
						}
					});
		}
		if (!empty($search_date)) {
			$skillLiveClasses = $skillLiveClasses->whereDate('class_time', $search_date);
		/*} else {
			$skillLiveClasses = $skillLiveClasses->where('end_time', '<', $now);*/
		}
		$skillLiveClasses = $skillLiveClasses->orderBy('class_time', 'ASC')->get();
		$skillClassData = array();
		if (!empty($skillLiveClasses)) {
			foreach ($skillLiveClasses as $key => $val) {
				$skillClass['id']				= $val['id'];
				$skillClass['class_date']		= date('d F, Y', strtotime($val['class_time']));
				$skillClass['class_time']		= date('h:i A', strtotime($val['class_time'])).' to '.date('h:i A', strtotime($val['end_time']));
				$skillClass['subject']			= ($val['subject_id']>0) ? $val->subject_data->title : 'NA';
				$skillClass['title']			= $val['title'];
				$skillClass['video']			= !empty($val['video']) ? $val['video'] : '';
				$skillClass['notes']			= !empty($val['pdf']) ? asset('upload/liveclasses') . "/" . $val['pdf'] : '';
				$skillClass['isFree']			= $val['isFree'];
				$skillClass['uploads3']			= $val['uploads3'];
				$skillClass['image']			= $val['image'];
				array_push($skillClassData, $skillClass);
			}
		}
		//dd($academicClassData);

		if ($userId > 0 && $user->role_id == 3) {
			$this->helper->trackingApi($userId, 'live_class', 'Past Live Class', 0, '', 'website');
		}

		return view('front.live_classes_past', compact('pagename','userSubscription','classes','classId','search','downloadTimeTable','pinMessageLiveClass','subjects','academicClassData','skillClassData'));
	}

	public function liveClassTimeTable(Request $request)
	{
		$pagename = "Live Class Time Table";
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			\Session::flash('error', 'You are not allow to access here!');
			return redirect()->route('home');
		}
		$userId = $user->id;
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$userSubscription = $this->helper->userSubscription($userId);
		
		$classes = StudentClass::where("status", 1)->where("deleted", 0)->orderBy("id", "ASC")->get();
		$class_id = !empty($user) ? $user->class_id : '';
		$classId = ($request->class_id) ? $request->class_id : $class_id;
		$search = $request->search;
		$subject_id = $request->subject_id;
		//$search_date = ($request->search_date) ? date('Y-m-d',strtotime($request->search_date)) : '';
		$search_date = ($request->search_date) ? $request->search_date : '';
		$from = date('Y-m-d').' 00:00:00';
		$userClass = StudentClass::where("id", $classId)->first();
		$className = !empty($userClass) ? $userClass->class_name : '';
		if (!empty($classId)) {
			$classSubject = StudentClassSubject::where("class_id", $classId)->first();
			$subject_ids = !empty($classSubject['subject_ids']) ? explode(",", $classSubject['subject_ids']) : [];
			$subjects = Subject::whereIn("id",$subject_ids)->orderBy("title", "ASC")->get();
		} else {
			$subjects = Subject::orderBy("title", "ASC")->get();
		}
		//$LiveClassTimeTables = LiveClassTimeTable::with('class_data','subject_data')->where("class_id", $classId)->where('start_time', '>', $from)->where("status", 1)->orderBy('start_time', 'ASC')->get();
		$LiveClassTimeTables = LiveClassTimeTable::with('class_data','subject_data')->where("class_id", $classId)->where("status", 1);
		if (!empty($search)) {
			$subject_data = Subject::select("id")->where("title", 'like', "%" . $search . "%")->first();
			$srch_subject_id = !empty($subject_data) ? $subject_data->id : '';
			if (!empty($srch_subject_id)) {
				$LiveClassTimeTables = $LiveClassTimeTables->where('subject_id', $srch_subject_id);
			} else {
				$LiveClassTimeTables = $LiveClassTimeTables->where("title", 'like', "%" . $search . "%");
			}
		}
		if (!empty($subject_id)) {
			$LiveClassTimeTables = $LiveClassTimeTables->whereHas('subject_data', function($query) use ($subject_id) {
						if (isset($subject_id) && !empty($subject_id)) {
							$query->where('subject_id', $subject_id);
						}
					});
		}
		if (!empty($search_date)) {
			$LiveClassTimeTables = $LiveClassTimeTables->whereDate('start_time', $search_date);
		/*} else {
			$LiveClassTimeTables = $LiveClassTimeTables->where('start_time', '>', $from);*/
		}
		$LiveClassTimeTables = $LiveClassTimeTables->orderBy('start_time', 'ASC')->get();
		$timeTableData = array();
		if (!empty($LiveClassTimeTables)) {
			foreach ($LiveClassTimeTables as $key => $val) {
				$timeTable['id']		= $val['id'];
				$timeTable['date']		= date('d F, Y', strtotime($val['start_time']));
				$timeTable['time']		= date('h:i A', strtotime($val['start_time'])).' to '.date('h:i A', strtotime($val['end_time']));
				$timeTable['title'] 	= $val['title'];
				$timeTable['subject']	= ($val['subject_id']>0) ? $val->subject_data->title : 'NA';
				array_push($timeTableData, $timeTable);
			}
		}
		//dd($timeTableData);

		if ($userId > 0 && $user->role_id == 3) {
			$this->helper->trackingApi($userId, 'live_class', 'Live Class Time Table', 0, '', 'website');
		}

		return view('front.live_classes_timetable', compact('pagename','userSubscription','search','className','subjects','timeTableData'));
	}

	public function notify_me(Request $request)
	{
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			/*$message = 'You are not allow to access here!';
			return $message;*/
			\Session::flash('error', 'You are not allow to access here!');
			return redirect()->route('home');
		}
		$userId   = $user->id;
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				Auth::logout();
				/*$message = 'You have already logged-in another device!';
				return $message;*/
				\Session::flash('error', 'You have already logged-in another device!');
				return redirect()->route('home');
			}
		}
		$classId = $request->classId;
		if (!empty($userId) && !empty($classId)) {
			$check_interest = LiveclassNotify::where("user_id", $userId)->where("class_id", $classId)->first();
			if (empty($check_interest)) {
				$data = array(
					'user_id'  		=> $userId,
					'class_id'  	=> $classId,
					'status'  		=> 0,
					'created_at'    => date('Y-m-d H:i:s'),
				);
				$insertId = LiveclassNotify::insertGetId($data);
				//Refer point on Live Class Notify
				$totalLiveclassNotify = LiveclassNotify::where("user_id", $userId)->count();
				if ($totalLiveclassNotify == 5) {
					$this->helper->addUserReferPoints($userId,13);
				} elseif ($totalLiveclassNotify == 10) {
					$this->helper->addUserReferPoints($userId,14);
				} elseif ($totalLiveclassNotify == 20) {
					$this->helper->addUserReferPoints($userId,15);
				} elseif ($totalLiveclassNotify == 50) {
					$this->helper->addUserReferPoints($userId,16);
				}
				//Send Notification
				$liveClass = LiveClass::with('subject_data')->where("id", $classId)->where("status", 1)->where("deleted", 0)->first();
				if ($liveClass) {
					$subject_data = ($liveClass->subject_id>0) ? $liveClass->title.' '.$liveClass->subject_data->title : $liveClass->title;
/*
//ak
					$this->helper->smsWithTemplate($user->phone, 'LiveClassSMS', $user->name, $subject_data, $liveClass->class_time);
					$this->helper->sendEmail($user->email, 'BrainyWood: Live Class', $data = array('userName' => $user->name, 'message' => '<p>Your live class of '.$subject_data.' will start at '.$liveClass->class_time.', Please join timely.</p><p>Team Brainywood</p>'));
*/
				}
			} else {
				$data = array(
					'status'  		=> 0,
					'updated_at'    => date('Y-m-d H:i:s'),
				);
				$insertId = LiveclassNotify::where("id", $check_interest->id)->update($data);
			}
			//$message = "Your Class Request Added Successfully.";
			/*$message = "Done";
			return $message;*/
			\Session::flash('success', 'Your Class Request Added Successfully.');
			return redirect()->back();
		} else {
			/*$message = "Wrong Paramenter Passed!";
			return $message;*/
			\Session::flash('error', 'Wrong Paramenter Passed!');
			return redirect()->route('home');
		}
	}

	public function join_now(Request $request)
	{
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			\Session::flash('error', 'You are not allow to access here!');
			return redirect()->route('home');
		}
		$userId   = $user->id;
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				Auth::logout();
				\Session::flash('error', 'You have already logged-in another device!');
				return redirect()->route('home');
			}
		}
		$liveclassId = $request->liveclassId;
		if (!empty($userId) && !empty($liveclassId)) {
			$liveClass = LiveClass::where("id", $liveclassId)->first();
			$meeting_id = !empty($liveClass) ? $liveClass->meeting_id : '';
			$pass_code = !empty($liveClass) ? $liveClass->pass_code : '';
			$liveclassJoin = LiveclassJoin::where('user_id', $userId)->where('liveclass_id', $liveclassId)->first();
			if (empty($liveclassJoin)) {
				$data = array(
					'user_id'  			=> $userId,
					'liveclass_id'  	=> $liveclassId,
					'created_at'        => date('Y-m-d H:i:s'),
					'updated_at'        => date('Y-m-d H:i:s'),
				);
				$liveclassJoinId = LiveclassJoin::insertGetId($data);
				//Refer point on Live Class Joining
				$this->helper->addUserReferPoints($userId,8);
				$totalLiveclassJoined = LiveclassJoin::where('user_id', $userId)->count();
				if ($totalLiveclassJoined == 5) {
					$this->helper->addUserReferPoints($userId,9);
				} elseif ($totalLiveclassJoined == 10) {
					$this->helper->addUserReferPoints($userId,10);
				} elseif ($totalLiveclassJoined == 20) {
					$this->helper->addUserReferPoints($userId,11);
				} elseif ($totalLiveclassJoined == 50) {
					$this->helper->addUserReferPoints($userId,12);
				}
			} else {
				$data1 = array(
					'user_id'  			=> $userId,
					'liveclass_id'  	=> $liveclassId,
					'updated_at'        => date('Y-m-d H:i:s'),
				);
				$liveclassJoinId = $liveclassJoin->id;
				LiveclassJoin::where('id', $liveclassJoinId)->update($data1);
			}
			//\Session::flash('success', 'User live class joined successfully.');
			return redirect()->away(url('/public/zoom/CDN?meeting_number='.$meeting_id.'&meeting_pwd='.$pass_code));
		} else {
			\Session::flash('error', 'Wrong Paramenter Passed!');
			return redirect()->back();
		}
	}

	public function questionAnswer(Request $request)
	{
		$pagename = "Question & Answers";
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			\Session::flash('error', 'You are not allow to access here!');
			return redirect()->route('home');
		}
		$userId = $user->id;
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$userSubscription = $this->helper->userSubscription($userId);
		
		$classes = StudentClass::where("status", 1)->where("deleted", 0)->orderBy("id", "ASC")->get();
		$class_id = !empty($user) ? $user->class_id : '';
		$classId = ($request->class_id) ? $request->class_id : $class_id;
		$questioncount = QuestionAsk::where("user_id", $userId)->count();
		$courses = Courses::whereRaw("find_in_set($classId,class_ids)")->where("status", 1)->where("deleted", 0)->orderBy("sort_id", "ASC")->get();
		$course_ids_arr = array();
		foreach ($courses as $key => $value) {
			$course_ids_arr[]  = $value['id'];
		}
		$dt = Carbon::now();
		$from = $dt->subMonth();
		$to = date('Y-m-d H:i:s');
		//$latestQuesAsks = QuestionAsk::where("status", 1)->whereBetween('created_at', [$from, $to])->orderBy("id", "DESC")->get();
		$latestQuesAsks = QuestionAsk::with('user','course','lession','topic')->whereIn("course_id",$course_ids_arr)->where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->get();
		$myQuestions = QuestionAsk::with('user','course','lession','topic')->whereIn("course_id",$course_ids_arr)->where("user_id", $userId)->where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->get();
		$suggestedQues = QuestionAsk::with('user','course','lession','topic')->whereIn("course_id",$course_ids_arr)->where("status", 1)->where("deleted", 0)->orderByRaw("RAND()")->limit(3)->get();

		if ($userId > 0 && $user->role_id == 3) {
			$this->helper->trackingApi($userId, 'q&a', '', 0, '', 'website');
		}

		return view('front.question_answer', compact('pagename','userSubscription','classes','classId','questioncount','courses','latestQuesAsks','myQuestions','suggestedQues'));
	}

	public function getLessionsBycourse(Request $request)
	{
		$courseId = $request->courseId;
		$lessions = Lession::where("courseId", $courseId)->where("status", 1)->where("deleted", 0)->orderBy("sort_id", "ASC")->get();
		$output = '<option>Select Lesson</option>';
		foreach ($lessions as $key => $value) {
			$output .= '<option value="'.$value->id.'">'.$value->name.'</option>';
		}
		echo $output;
	}

	public function getTopicsByLession(Request $request)
	{
		$courseId = $request->courseId;
		$lessionId = $request->lessionId;
		$chapters = Chapter::where("courseId", $courseId)->where("lessionId", $lessionId)->where("status", 1)->where("deleted", 0)->orderBy("sort_id", "ASC")->get();
		$output = '<option>Select Topic</option>';
		foreach ($chapters as $key => $value) {
			$output .= '<option value="'.$value->id.'">'.$value->name.'</option>';
		}
		echo $output;
	}

	public function getQuestionsBylession(Request $request)
	{
		$courseId = $request->courseId;
		$lessionId = !empty($request->lessionId) ? $request->lessionId : 0;
		$topicId = !empty($request->topicId) ? $request->topicId : 0;
		$search = $request->search;
		$questions = QuestionAsk::where("course_id", $courseId)->where("lession_id", $lessionId)->where("topic_id", $topicId)->where("status", 1)->where("deleted", 0);
		if (!empty($questions)) {
			$questions = $questions->where("question", 'like', "%" . $search . "%");
		}
		$questions = $questions->limit(3)->get();
		$output = '';
		foreach ($questions as $key => $value) {
			$quesUrl = route('questionAnswerView',$value->id);
			$output .= '<li><a href="'.$quesUrl.'" target="_blank">'.$value->question.'</a></li>';
		}
		echo $output;
	}

	public function saveQuestion(Request $request)
	{
		$user = Auth::user();
		$userId = $user->id;
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			\Session::flash('error', 'You are not allow to access here!');
			return redirect()->route('home');
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$userSubscription = $this->helper->userSubscription($userId);
		
		$questioncount = QuestionAsk::where("user_id", $userId)->count();
		if ($userSubscription==0) {
			if ($questioncount > 3) {
				\Session::flash('error', 'You are not allow to ask more question, Please buy a subscription plan first!');
				return back();
			}
		}

		$validator = Validator::make($request->all(), [
			'courseId' => 'required',
			'lessionId' => 'required',
			'question' => 'required',
		]);

		if ($validator->fails()) {
			return back()
				->withErrors($validator)
				->withInput();
		} else {
			$imagesData = [];
			$files = $request->file('image');
			if ($request->hasFile('image')) {
				foreach ($files as $file) {
					if($file){
						$destinationPath = public_path().'/upload/questionask/';
						$originalFile = $file->getClientOriginalName();
						$newImage = rand(111,999).strtotime(date('Y-m-d-H:isa')).$originalFile;
						$file->move($destinationPath, $newImage);
						//$imagesData[] = $imagess;
						$url = 'upload/questionask/' . $newImage;
						$thumb_img = Image::make($url)->resize(200, 200);
						$thumb_img->save('upload/questionask/thumb/'.$newImage,80);
						$imagesData[] =  $thumb_img->basename;
					}
				}
			}
			$courseId 	= $request->input('courseId');
			$lessionId 	= $request->input('lessionId');
			$topicId 	= ($request->input('topicId')) ? $request->input('topicId') : 0;
			$question 	= $request->input('question');

			$questionAsk = new QuestionAsk;
			$questionAsk->user_id 	= $userId;
			$questionAsk->course_id = $courseId;
			$questionAsk->lession_id = $lessionId;
			$questionAsk->topic_id 	= $topicId;
			$questionAsk->question 	= $question;
			$questionAsk->image 	= implode(",", $imagesData);
			$questionAsk->save();
			//Refer point on Asking Question
			$this->helper->addUserReferPoints($userId,67);
			if ($questioncount == 10) {
				$this->helper->addUserReferPoints($userId,69);
			} elseif ($questioncount == 20) {
				$this->helper->addUserReferPoints($userId,70);
			} elseif ($questioncount == 30) {
				$this->helper->addUserReferPoints($userId,71);
			}

			\Session::flash('success', 'Thank you, Your Question added successfully, After approval by Team it will be appear here.');
			return back();
		}
	}
	public function answerAQuestion(Request $request)
	{
		$user = Auth::user();
		$userId = $user->id;
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			\Session::flash('error', 'You are not allow to access here!');
			return redirect()->route('home');
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}

		$validator = Validator::make($request->all(), [
			'quesId' => 'required',
			//'answer' => 'required',
		]);

		if ($validator->fails()) {
			return back()
				->withErrors($validator)
				->withInput();
		} else {
			$quesId = $request->quesId;
			$answer = $request->answer;
			$msg = '';
			if (!empty($userId) && !empty($quesId) && !empty($answer) || !empty($request->file('image'))) {
				$questioncheck = QuestionAsk::where("id", $quesId)->first();
				if (!empty($questioncheck)) {
					$imagesData = [];
					$files = $request->file('image');
					if ($request->hasFile('image')) {
						foreach ($files as $file) {
							if($file){
								$destinationPath = public_path().'/upload/questionask/';
								$originalFile = $file->getClientOriginalName();
								$newImage = rand(111,999).strtotime(date('Y-m-d-H:isa')).$originalFile;
								$file->move($destinationPath, $newImage);
								//$imagesData[] = $newImage;
								$url = 'upload/questionask/' . $newImage;
								$thumb_img = Image::make($url)->resize(200, 200);
								$thumb_img->save('upload/questionask/thumb/'.$newImage,80);
								$imagesData[] =  $thumb_img->basename;
							}
						}
					}
					$data = array(
						'user_id' 	 => $userId,
						'ques_id'    => $quesId,
						'answer'     => $answer,
						'image'      => implode(",", $imagesData),
						'status'     => 1,
						'created_at' => date('Y-m-d H:i:s'),
					);
					$insertId = QuestionAnswer::insertGetId($data);

					$ques = QuestionAsk::where("id", $quesId)->first();
					$msg = $user->name.', Answered a question '.$ques->question.' in Q&A, check it now.';
					$click_action = 'QA';
					$module_id = $quesId;
					$this->helper->addNotification($ques->user_id,$msg, $click_action, $module_id);
					//Refer point on Giving Answer
					$this->helper->addUserReferPoints($userId,76);

					\Session::flash('success', 'Your Answer Submitted Successfully.');
					return redirect()->back();
				} else {
					\Session::flash('error', 'Question not Found!');
					return redirect()->back();
				}
			} else {
				\Session::flash('error', 'Wrong Paramenter Passed!');
				return redirect()->back();
			}
		}
	}

	public function questionAnswerView(Request $request, $id)
	{
		$pagename = "Question & Answers";
		$userId = 0;
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id == 3 || !empty($user)){
			$userId = $user->id;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$userSubscription = $this->helper->userSubscription($userId);
		
		$questioncount = QuestionAsk::where("user_id", $userId)->count();
		$askedQuestion = QuestionAsk::where("id", $id)->where("status", 1)->where("deleted", 0)->first();
		if(empty($askedQuestion)){
			\Session::flash('error', 'Question not found!');
			return redirect()->route('questionAnswer');
		}
		$courseLessionTopicName = '';
		if($askedQuestion->topic_id > 0){
			$courseLessionTopicName = $askedQuestion->course->name.' / '.$askedQuestion->lession->name.' / '.$askedQuestion->topic->name;
		}elseif($askedQuestion->lession_id > 0){
			$courseLessionTopicName = $askedQuestion->course->name.' / '.$askedQuestion->lession->name;
		}else{
			if($askedQuestion->course_id > 0){
				$courseLessionTopicName = $askedQuestion->course->name;
			}
		}
		$answers = QuestionAnswer::where("ques_id", $id)->where("status", 1)->get();

		if ($userId > 0 && $user->role_id == 3) {
			$this->helper->trackingApi($userId, 'q&a', 'view Answers', $id, '', 'website');
		}

		return view('front.question_answer_view', compact('pagename','userSubscription','questioncount','askedQuestion','courseLessionTopicName','answers'));
	}

	public function answerLike(Request $request)
	{
		$user = Auth::user();
		$userId = $user->id;
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			\Session::flash('error', 'Please login first to like it!');
			return redirect()->back();
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$answerId = $request->answerId;
		if (!empty($userId) && !empty($answerId)) {
			$getAnswer = QuestionAnswer::where("id", $answerId)->first();
			if (!empty($getAnswer)) {
				$getAnswerLike = QuestionAnswerLike::where("user_id", $userId)->where("answer_id", $answerId)->first();
				$userLiked = !empty($getAnswerLike) ? $getAnswerLike->ans_like : 0;
				$userDisliked = !empty($getAnswerLike) ? $getAnswerLike->ans_unlike : 0;
				if ($userLiked==0) {
					$preLike = $getAnswer->ans_like;
					$preDislike = $getAnswer->ans_unlike;
					if ($userDisliked==1) {
						$preDislike = $preDislike - 1;
					}
					$like = $preLike + 1;
					$data = array(
						'ans_like' 	 => $like,
						'ans_unlike' => $preDislike,
						'updated_at' => date('Y-m-d H:i:s'),
					);
					$update = QuestionAnswer::where("id", $answerId)->update($data);
					if (!empty($getAnswerLike)) {
						$data1 = array(
							'ans_like' 	 => 1,
							'ans_unlike' => 0,
							'updated_at' => date('Y-m-d H:i:s'),
						);
						$update = QuestionAnswerLike::where("id", $getAnswerLike->id)->update($data1);
					} else {
						$data1 = array(
							'user_id' 	 => $userId,
							'answer_id'  => $answerId,
							'ans_like' 	 => 1,
							'ans_unlike' => 0,
							'created_at' => date('Y-m-d H:i:s'),
						);
						$insert = QuestionAnswerLike::insertGetId($data1);
						//Refer point on Giving Likes
						$totalQuestionLikes = QuestionAnswerLike::where("user_id", $userId)->where("ans_like", 1)->count();
						if ($totalQuestionLikes == 5) {
							$this->helper->addUserReferPoints($userId,72);
						} elseif ($totalQuestionLikes == 10) {
							$this->helper->addUserReferPoints($userId,73);
						} elseif ($totalQuestionLikes == 15) {
							$this->helper->addUserReferPoints($userId,74);
						} elseif ($totalQuestionLikes == 20) {
							$this->helper->addUserReferPoints($userId,75);
						}
					}
					\Session::flash('success', 'Your Like Submitted Successfully.');
				} else {
					\Session::flash('success', 'You have already liked this answer.');
				}
			}
		}
		return back();
	}

	public function answerUnLike(Request $request)
	{
		$user = Auth::user();
		$userId = $user->id;
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			\Session::flash('error', 'Please login first to dislike it!');
			return redirect()->back();
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$answerId = $request->answerId;
		if (!empty($userId) && !empty($answerId)) {
			$getAnswer = QuestionAnswer::where("id", $answerId)->first();
			if (!empty($getAnswer)) {
				$getAnswerLike = QuestionAnswerLike::where("user_id", $userId)->where("answer_id", $answerId)->first();
				$userLiked = !empty($getAnswerLike) ? $getAnswerLike->ans_like : 0;
				$userDisliked = !empty($getAnswerLike) ? $getAnswerLike->ans_unlike : 0;
				if ($userDisliked==0) {
					$preLike = $getAnswer->ans_like;
					if ($userLiked==1) {
						$preLike = $preLike - 1;
					}
					$preUnLike = $getAnswer->ans_unlike;
					$unlike = $preUnLike + 1;
					$data = array(
						'ans_like' 	 => $preLike,
						'ans_unlike' => $unlike,
						'updated_at' => date('Y-m-d H:i:s'),
					);
					$update = QuestionAnswer::where("id", $answerId)->update($data);
					if (!empty($getAnswerLike)) {
						$data1 = array(
							'ans_like' 	 => 0,
							'ans_unlike' => 1,
							'updated_at' => date('Y-m-d H:i:s'),
						);
						$update = QuestionAnswerLike::where("id",$getAnswerLike->id)->update($data1);
					} else {
						$data1 = array(
							'user_id' 	 => $userId,
							'answer_id'  => $answerId,
							'ans_like' 	 => 0,
							'ans_unlike' => 1,
							'created_at' => date('Y-m-d H:i:s'),
						);
						$insert = QuestionAnswerLike::insertGetId($data1);
					}
					\Session::flash('success', 'Your Dislike Submitted Successfully.');
				} else {
					\Session::flash('success', 'You have already disliked this answer.');
				}
			}
		}
		return back();
	}

	public function myProfile(Request $request)
	{
		$pagename = "My Profile";
		$user = Auth::user();
		$userId = $user->id;
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			\Session::flash('error', 'You are not allow to access here!');
			return redirect()->route('home');
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}

		return view('front.my_profile', compact('pagename','user'));
	}

	public function myAccount(Request $request)
	{
		$pagename = "My Account";
		$user = Auth::user();
		$userId = $user->id;
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			\Session::flash('error', 'You are not allow to access here!');
			return redirect()->route('home');
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$subscriptions = UserSubscription::where("user_id", $userId)->where("paymentStatus", 1)->orderBy("id", "DESC")->get();
		$studentClasses = StudentClass::where("status",1)->where("deleted",0)->orderBy("id", "ASC")->get();
		$states = State::orderBy("state", "ASC")->get();
		$userState = $user->state;
		$state = State::where("state", $userState)->first();
		if(!empty($state)){
			$stateId = $state->id;
			$cities = City::where("state_id", $stateId)->orderBy("city", "ASC")->get();
		}else{
			$cities = City::orderBy("city", "ASC")->get();
		}

		if ($userId > 0 && $user->role_id == 3) {
			$this->helper->trackingApi($userId, 'user', 'user profile', $userId, '', 'website');
		}

		return view('front.my_account', compact('pagename','user','subscriptions','studentClasses','states','cities'));
	}

	public function uploadProfile(Request $request)
	{
		$user = Auth::user();
		$userId = $user->id;
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			\Session::flash('error', 'You are not allow to access here!');
			return redirect()->route('home');
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}

		$validator = Validator::make($request->all(), [
			'image' => 'required',
		]);

		if ($validator->fails()) {
			return back()
				->withErrors($validator)
				->withInput();
		} else {
			$imagess = '';
			if (isset($_FILES['image']['name'])) {
				$profileimagename = $_FILES['image']['name'];
				$tmpimage1 = $_FILES['image']['tmp_name'];
				$newprofileImage = rand(00000, 99999) . date('d') . $profileimagename;
				$location = "upload/profile/";
				move_uploaded_file($tmpimage1, $location . $newprofileImage);
				$url = "upload/profile/" . $newprofileImage;
				$img = Image::make($url)->resize(200, 200);
				$imagess =  $img->basename;
			}
			//echo $imagess; die;
			$users = User::findOrFail($userId);
			$users->image = $imagess;
			$users->save();
			
			\Session::flash('success', 'Profile Image Uploaded Successfully.');
			return back();
		}
	}
	public function updateAccount(Request $request)
	{
		$user = Auth::user();
		$userId = $user->id;
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			\Session::flash('error', 'You are not allow to access here!');
			return redirect()->route('home');
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$users = User::findOrFail($userId);
		$users->name = $request->get('name');
		$users->phone = $request->get('phone');
		$users->email = $request->get('email');
		$users->save();
		
		\Session::flash('success', 'Account updated successfully.');
		return back();
	}
	public function mobileUpdate(Request $request)
	{
		$user = Auth::user();
		$userId = $user->id;
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$message = 'You are not allow to access here!';
			return $message;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				$message = 'You have already logged-in another device!';
				Auth::logout();
				return $message;
			}
		}

		$validator = Validator::make($request->all(), [
			'phone' => 'required|numeric',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$country_code 	= trim($request->country_code,"+");
			$country_flag 	= strtoupper($request->country_flag);
			$phone 			= $request->phone;
			if (!empty($country_code) && !empty($phone)) {
				if ($country_code == '91') {
					$validator = Validator::make($request->all(), [
						'phone' => 'required|digits:10',
					]);
					if ($validator->fails()) {
						return 'Phone number should be 10 digits only.';
					}
				}
				$checkPhone = User::where("id", "!=", $userId)->where("country_code", $country_code)->where("phone", $phone)->first();
				if (empty($checkPhone)) {
					$request->session()->put('country_code', $country_code);
					$request->session()->put('country_flag', $country_flag);
					$request->session()->put('new_phone', $phone);
					$otp = rand(1111, 9999);
					$updateData = User::where("id", $userId)->update([
						"otp_match" 	=> $otp,
						"updated_at" 	=> date('Y-m-d H:i:s')
					]);
/*
//ak					$this->helper->sms($phone, $otp);
					$this->helper->sendEmail($user->email, 'BrainyWood: Verify OTP', $data = array('userName' => $user->name, 'message' => '<p>Thank you for connecting at BrainyWood,</p><p>You have got successfully your OTP: ' . $otp . '</p>'));
*/
					$message = 'Done';
					return $message;
				} else {
					$message = 'Phone number already exists, please enter another number!';
					return $message;
				}
			}
		}
	}
	public function mobileOtpSend(Request $request)
	{
		$user = Auth::user();
		$userId = $user->id;
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$message = 'You are not allow to access here!';
			return $message;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				$message = 'You have already logged-in another device!';
				Auth::logout();
				return $message;
			}
		}

		$validator = Validator::make($request->all(), [
			'new_phone' => 'required|numeric',
			'otp' => 'required|numeric',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$country_code = ($request->session()->has('country_code')) ? $request->session()->get('country_code') : '91';
			$country_flag = ($request->session()->has('country_flag')) ? $request->session()->get('country_flag') : 'IN';
			$phone 	= $request->new_phone;
			$otp 	= $request->otp;
			$checkUser = User::where("id", $userId)->where("otp_match", $otp)->first();
			if (!empty($checkUser)) {
				if (!empty($country_code) && !empty($phone)) {
					if ($country_code == '91') {
						$validator = Validator::make($request->all(), [
							'phone' => 'required|digits:10',
						]);
						if ($validator->fails()) {
							return 'Phone number should be 10 digits only.';
						}
					}
					$checkPhone = User::where("id", "!=", $userId)->where("country_code", $country_code)->where("phone", $phone)->first();
					if (empty($checkPhone)) {
						$updateData = User::where("id", $userId)->update([
							'country_code' 	=> $country_code,
							'country_flag' 	=> $country_flag,
							'phone' 		=> $phone,
							'updated_at' 	=> date('Y-m-d H:i:s')
						]);
						$request->session()->forget('country_code');
						$request->session()->forget('country_flag');
						$request->session()->forget('new_phone');
						$message = 'Done';
						return $message;
					} else {
						$message = 'Mobile number already exists, can not update for now.';
						return $message;
					}
				}
			} else {
				$message = 'OTP not matched, please resend a OTP to update your mobile number';
				return $message;
			}
		}
	}
	public function emailUpdate(Request $request)
	{
		$user = Auth::user();
		$userId = $user->id;
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$message = 'You are not allow to access here!';
			return $message;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				$message = 'You have already logged-in another device!';
				Auth::logout();
				return $message;
			}
		}

		$validator = Validator::make($request->all(), [
			'email' => 'required|email',
		]);
		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$email 	= $request->email;
			if (!empty($email)) {
				$checkEmail = User::where("id", "!=", $userId)->where("email", $email)->first();
				if (empty($checkEmail)) {
					$request->session()->put('new_email', $email);
					$request->session()->put('new_email_phone', $user->phone);
					$otp = rand(1111, 9999);
					$updateData = User::where("id", $userId)->update([
						"otp_match" 	=> $otp,
						"updated_at" 	=> date('Y-m-d H:i:s')
					]);/*
					//ak
										$this->helper->sms($user->phone, $otp);
					//sendEmail
					$this->helper->sendEmail($user->email, 'BrainyWood: Verify OTP', $data = array('userName' => $user->name, 'message' => '<p>Thank you for connecting at BrainyWood,</p><p>You have got successfully your OTP: ' . $otp . '</p>'));
					*/	
					$message = 'Done';
					return $message;
				}
			}
		}
	}
	public function emailOtpSend(Request $request)
	{
		$user = Auth::user();
		$userId = $user->id;
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$message = 'You are not allow to access here!';
			return $message;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				$message = 'You have already logged-in another device!';
				Auth::logout();
				return $message;
			}
		}

		$validator = Validator::make($request->all(), [
			'new_email' => 'required|email',
			'otp' => 'required|numeric',
		]);
		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$email 	= $request->new_email;
			$otp 	= $request->otp;
			$checkUser = User::where("id", $userId)->where("otp_match", $otp)->first();
			if (!empty($checkUser)) {
				if (!empty($email)) {
					$checkEmail = User::where("id", "!=", $userId)->where("email", $email)->first();
					if (empty($checkEmail)) {
						$updateData = User::where("id", $userId)->update([
							"email" 		=> $email,
							"updated_at" 	=> date('Y-m-d H:i:s')
						]);
						$request->session()->forget('new_email');
						$request->session()->forget('new_email_phone');
						$message = 'Done';
						return $message;
					} else {
						$message = 'Email id already exists, can not update for now.';
						return $message;
					}
				}
			} else {
				$message = 'OTP not matched, please resend a OTP to update your email id';
				return $message;
			}
		}
	}
	public function changePassword(Request $request)
	{
		$user = Auth::user();
		$userId = $user->id;
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			\Session::flash('error', 'You are not allow to access here!');
			return redirect()->route('home');
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}

		$validator = Validator::make($request->all(), [
			'current_password' => 'required',
			'new_password' => 'required|min:6',
			'confirm_password' => 'required|same:new_password|min:6',
		]);

		if ($validator->fails()) {
			return back()
				->withErrors($validator)
				->withInput();
		} else {
			$currentPassword = $request->get('current_password');
			$newPassword = $request->get('new_password');
			$confirmPassword = $request->get('confirm_password');
			if (Hash::check($currentPassword, $user->password)) {
				if ($newPassword != $confirmPassword) {
					\Session::flash('error', 'Password and Confirm password not matched!');
					return back();
				}
				$users = User::findOrFail($userId);
				$users->password = bcrypt($newPassword);
				$users->userpass = $newPassword;
				$users->save();
			} else {
				\Session::flash('error', 'Current Password not matched!');
				return back();
			}
			
			\Session::flash('success', 'Password updated successfully.');
			return back();
		}
	}
	public function updateInfo(Request $request)
	{
		//	dd($request->all());
		$user = Auth::user();
		$userId = $user->id;
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			\Session::flash('error', 'You are not allow to access here!');
			return redirect()->route('home');
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}

		$validator = Validator::make($request->all(), [
			'name' => 'required|regex:/^[\pL\s\-]+$/u',
			'city' => 'required|regex:/^[\pL\s\-]+$/u',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$country_code = $user->country_code;
			if ($country_code == '91') {
				$validator = Validator::make($request->all(), [
					'postal_code' => 'required|digits:6',
					'parents_phone' => 'required|digits:10',
				]);
				if ($validator->fails()) {
					return back()->withErrors($validator)->withInput();
				}
			}
			$users = User::findOrFail($userId);
			$users->name = $request->get('name');
			$users->dob = date('Y-m-d H:i:s',strtotime($request->get('dob')));
			$users->gender = $request->get('gender');
			$users->class_id = $request->get('class_id');
			$users->school_college = $request->get('school_college');
			$users->state = $request->get('state');
			$users->city = $request->get('city');
			$users->postal_code = $request->get('postal_code');
			$users->parents_phone = $request->get('parents_phone');
			$users->save();
		}
		
		\Session::flash('success', 'Profile info updated successfully.');
		return back();
	}

	public function getCitiesByState(Request $request)
	{
		$state = $request->state;
		$stateData = State::where("state", $state)->first();
		$stateId = $stateData->id;
		$cities = City::where("state_id", $stateId)->orderBy("city", "ASC")->get();
		$output = '<option>Select City</option>';
		foreach ($cities as $key => $value) {
			$output .= '<option value="'.$value->city.'">'.$value->city.'</option>';
		}
		echo $output;
	}

	public function pricing()
	{
		$pagename = "Pricing";
		$user = Auth::user();
		$userId = !empty($user) ? $user->id : 0;
		$checkSubscriptionTaken = UserSubscription::where("user_id", $userId)->where("mode", "!=", "FREE")->where("paymentStatus", 1)->orderBy("id", "DESC")->first();
		$userSubscription = !empty($checkSubscriptionTaken) ? 1 : 0;
		//$subscriptions = Subscription::where("plan_type", $userSubscription)->where("status", 1)->where("deleted", 0)->orderBy("month", "ASC")->get();
		$subscriptions = Subscription::where("plan_type", 0)->where("status", 1)->where("deleted", 0)->orderBy("month", "ASC")->get();

		if ($userId > 0 && $user->role_id == 3) {
			$this->helper->trackingApi($userId, 'subscription', '', 0, '', 'website');
		}
		
		return view('front.pricing', compact('pagename','subscriptions'));
	}
	public function planDetails(Request $request, $id)
	{
		$pagename = "Subscription Plan Details";
		$user = Auth::user();
		$userId = !empty($user) ? $user->id : 0;
		$subscription = Subscription::where("id", $id)->where("status", 1)->where("deleted", 0)->first();
		if(empty($subscription)){
			\Session::flash('error', 'Subscription plan not available.');
			return redirect()->route('pricing');
		}
		$today	  = date('Y-m-d');
		$subscriptionTaken = UserSubscription::where("user_id", $userId)->where("end_date", ">=", $today)->where("paymentStatus", 1)->orderBy("id", "DESC")->first();
		$userSubscription = !empty($subscriptionTaken) ? 1 : 0;
		$subscriptionMode = !empty($subscriptionTaken) ? $subscriptionTaken->mode : '';

		$amount = $discount = $discountAmt = $payableAmt = 0;
		if ($request->session()->has('planId') && $request->session()->get('planId')==$id) {
			if ($request->session()->has('appliedCode')) {
				$couponCode = $request->session()->get('appliedCode');
			}
		} else {
			if ($userId > 0) {
				$franchise = Franchise::where("refer_code", $user->refer_code)->first();
				if (!empty($franchise)) {
					$checkCoupon = CouponCode::where("user_id", $userId)->where("condition_1", 1)->where("end_date", ">=", $today)->where("status", 1)->where("deleted", 0)->first();
					if (!empty($checkCoupon)) {
						$couponCode = $checkCoupon->coupon;
						if ($checkCoupon->discount > 0) {
							$discount = $checkCoupon->discount;
						}
						$checkUserSubscription = UserSubscription::where("user_id", $userId)->where("coupon_code", $couponCode)->where("paymentStatus", 1)->orderBy("id", "DESC")->first();
						if (empty($checkUserSubscription)) {
							if (!empty($subscription) && $subscription->price > 1) {
								$amount = $subscription->price;
								if ($discount > 0) {
									$checkRefferedUser = !empty($user->refer_code) ? 1 : 0;
									if ($checkRefferedUser == 1) {
										if ($subscription->id == 1) {
											$discount = 20;
										} elseif ($subscription->id == 2) {
											$discount = 30;
										} elseif ($subscription->id == 3) {
											$discount = 29.41;
										}
									}
									$discountAmt = ($amount * $discount) / 100;
								}
								if ($discountAmt > 0) {
									if ($amount > $discountAmt) {
										$payableAmt = $amount - $discountAmt;
									} else {
										$payableAmt = 0;
									}
								}
								if ($payableAmt > 0) {
									$request->session()->put('planId', $id);
									$request->session()->put('appliedCode', $couponCode);
									$request->session()->put('discountAmt', $discountAmt);
									$request->session()->put('discount', $discount);
									$request->session()->put('payableAmt', $payableAmt);
								}
							}
						}
					}
				}
			}
		}

		if ($userId > 0 && $user->role_id == 3) {
			$this->helper->trackingApi($userId, 'subscription', '', $id, '', 'website');
		}
		//echo $payableAmt; die;

		return view('front.plan_details', compact('pagename','subscription','userSubscription','subscriptionMode'));
	}

	public function apply_coupon_code(Request $request)
	{
		if($request->session()->has('payableAmt') && $request->couponCode!=''){
			$request->session()->forget('payableAmt');
			$request->session()->forget('discountAmt');
			$request->session()->forget('discount');
		}
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			\Session::flash('error', 'Please login first to apply coupon code!');
			return redirect()->back();
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				$message = '<span style="color:red;">You have already logged-in another device!</span>';
				return $message;
			}
		}
		$userId   = $request->userId;
		$subscriptionId = $request->subscriptionId;
		$couponCode		= $request->couponCode;
		$discountAmt = $discount = $payableAmt = $coupon_no_of_users = $coupon_user_id = $coupon_subscription_id = $appliedCoupon = 0;
		if (!empty($userId) && !empty($subscriptionId) && !empty($couponCode)) {
			$today	  = date('Y-m-d');
			/*$subscription = UserSubscription::where("user_id", $userId)->where("end_date", ">=", $today)->where("paymentStatus", 1)->orderBy("id", "DESC")->first();
			$userSubscription = !empty($subscription) ? 1 : 0;
			$subscriptionMode = !empty($subscription) ? $subscription->mode : '';
			if ($userSubscription==1 && $subscriptionMode!='FREE') {
				$message = '<span style="color:red;">You have already subscribed a plan, after expired you can take new subscription!</span>';
				return $message;
			}*/
			$getCoupon = CouponCode::where("coupon", $couponCode)->where("end_date", ">=", $today)->where("status", 1)->where("deleted", 0)->first();
			if (!empty($getCoupon) && $getCoupon->discount > 0) {
				$discount = $getCoupon->discount;
			} else {
				$message = '<span style="color:red;">This Coupon Code not found!</span>';
				return $message;
			}
			$checkSubscription = UserSubscription::where("user_id", $userId)->where("coupon_code", $couponCode)->where("paymentStatus", 1)->first();
			if (!empty($checkSubscription)) {
				$message = '<span style="color:red;">This Coupon Code already in used!</span>';
				return $message;
			} else {
				if ($getCoupon->condition_1 == 1){
					if ($getCoupon->user_id > 0){
						$coupon_user_id = $getCoupon->user_id;
						if ($userId != $coupon_user_id) {
							$message = '<span style="color:red;">This Coupon Code not made for you!</span>';
							return $message;
						}
					}
				} elseif ($getCoupon->condition_1 == 2){
					if ($getCoupon->no_of_users > 0){
						$coupon_no_of_users = $getCoupon->no_of_users;
					}
				} elseif ($getCoupon->condition_1 == 3){
					$appliedCoupon = 1;
					$franchise_user_id = $getCoupon->franchise_user_id;
					if ($getCoupon->no_of_users > 0){
						$coupon_no_of_users = $getCoupon->no_of_users;
					}
					$countUserSubscription = UserSubscription::where("user_id", $userId)->where("mode", "!=", "FREE")->where("paymentStatus", 1)->count();
					$discount = ($countUserSubscription > 1) ? $getCoupon->renewal_discount : $discount;
					if ($user->franchise_user_id != $franchise_user_id) {
						$message = "This Coupon Code not matched with your franchise!";
						return $message;
					}
				} else {
					if ($getCoupon->no_of_users > 0){
						$coupon_no_of_users = $getCoupon->no_of_users;
					}
				}
				if ($getCoupon->subscription_id > 0){
					$coupon_subscription_id = $getCoupon->subscription_id;
					if ($subscriptionId != $coupon_subscription_id) {
						$message = '<span style="color:red;">This Coupon Code not made for this subscription plan!</span>';
						return $message;
					}
				}
				if ($coupon_no_of_users > 0) {
					$totalUsedCouponCode = UserSubscription::where("coupon_code", $couponCode)->where("paymentStatus", 1)->count();
					if ($totalUsedCouponCode >= $coupon_no_of_users) {
						$message = '<span style="color:red;">Invalid Coupon Code!</span>';
						return $message;
					}
				}
			}
			$getSubscription = Subscription::where("id", $subscriptionId)->where("status", 1)->where("deleted", 0)->first();
			if (!empty($getSubscription) && $getSubscription->price > 1) {
				$amount = $getSubscription->price;
				if ($discount > 0) {
					$checkRefferedUser = !empty($user->refer_code) ? 1 : 0;
					if ($checkRefferedUser == 1 && $appliedCoupon == 0) {
						if ($subscriptionId == 1) {
							$discount = 20;
						} elseif ($subscriptionId == 2) {
							$discount = 30;
						} elseif ($subscriptionId == 3) {
							$discount = 29.41;
						}
					}
					$discountAmt = ($amount * $discount) / 100;
				}
				if ($discountAmt > 0) {
					if ($amount > $discountAmt) {
						$payableAmt = $amount - $discountAmt;
					} else {
						$payableAmt = 0;
					}
				}
				if ($payableAmt > 0) {
					$request->session()->put('planId', $subscriptionId);
					$request->session()->put('appliedCode', $couponCode);
					$request->session()->put('discountAmt', $discountAmt);
					$request->session()->put('discount', $discount);
					$request->session()->put('payableAmt', $payableAmt);

					
					$payment_gateway = 1;
					$message = "Done";
				} else {
					$month = !empty($getSubscription->month) ? $getSubscription->month : 0;
					$today = date('Y-m-d');
					$countUserSubscription = UserSubscription::where("user_id", $userId)->where("mode", "!=", "FREE")->where("paymentStatus", 1)->count();
					$commissionType = ($countUserSubscription > 1) ? 'Renewal Plan' : 'First Plan';
					$getUserSubscription = UserSubscription::where("user_id", $userId)->where("end_date", ">=", $today)->where("paymentStatus", 1)->orderBy("id", "DESC")->first();
					if (!empty($getUserSubscription)) {
						$subscriptionEndDate = $getUserSubscription->end_date;
						$endDate = strtotime($subscriptionEndDate);
						$endDate = strtotime("+1 day", $endDate);
						$start_date = date('Y-m-d', $endDate);
						$startDate = strtotime($start_date);
						$startDate = strtotime("+".$month." months", $startDate);
						$end_date = date('Y-m-d', $startDate);
					} else {
						$start_date = date('Y-m-d');
						$end_date = date('Y-m-d', strtotime('+'.$month.' months'));
					}
					/*$month = !empty($getSubscription->month) ? $getSubscription->month : 0;
					$today = date('Y-m-d');
					$end_date = date('Y-m-d', strtotime('+'.$month.' months'));*/
					$data1 = array(
						'user_id'			=> $userId,
						'subscription_id'	=> $subscriptionId,
						'start_date'		=> $start_date,
						'end_date'			=> $end_date,
						'amount'			=> $payableAmt,
						'coupon_code'		=> $couponCode,
						'mode'				=> 'Coupon',
						'commission_type'	=> $commissionType,
						'paymentStatus'		=> 1,
						'created_at'		=> date('Y-m-d H:i:s'),
					);
					$orderId = UserSubscription::insertGetId($data1);
					$user = User::where("id", $userId)->first();
					$msg = 'Subscription plan activated';
					$this->helper->addNotification($userId,$msg);
/*
//ak
					$this->helper->smsWithTemplate($user->phone, 'Mambership', $user->name, $getSubscription->name);
					$this->helper->sendEmail($user->email, 'BrainyWood: Subscription payment initiated', $data = array('userName' => $user->name, 'message' => '<p>Thank you for payment initiated at BrainyWood,</p><p>You have subscribed a plan successfully with Coupon Code: ' . $couponCode . '</p>'));
*/


					$payment_gateway = 0;
					$message = "Activated";
				}

				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'subscription', 'apply Coupon', $subscriptionId, '', 'website');
				}
				return $message;
			} else {
				$message = '<span style="color:red;">Subscription not found!</span>';
				return $message;
			}
		} else {
			$message = '<span style="color:red;">Wrong Paramenter Passed!</span>';
			return $message;
		}
	}

	public function razorpay()
	{        
		return view('front.razorpay');
	}

	public function payment(Request $request, $id)
	{
		$planID = $id;
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			\Session::flash('error', 'You are not allow to access here!');
			return redirect()->route('home');
		}else{
			$userId = $user->id;
		}

		$input = $request->all();
		//echo '<pre />'; print_r($input);
		$api = new Api(env('RAZOR_KEY'), env('RAZOR_SECRET'));
		$payment = $api->payment->fetch($input['razorpay_payment_id']);

		if(count($input)  && !empty($input['razorpay_payment_id'])) 
		{
			try 
			{
				$response = $api->payment->fetch($input['razorpay_payment_id'])->capture(array('amount'=>$payment['amount']));
				//echo '<pre />'; print_r($response); die; //success@razorpay
				$orderId = isset($response['order_id']) ? $response['order_id'] : NULL;
				$paymentStatus = isset($response['status']) ? $response['status'] : 'failed';
				//if ($paymentStatus!='failed') {
					//$txn_id = isset($response['id']) ? $response['id'] : $input['razorpay_payment_id'];
					$txn_id = $input['razorpay_payment_id'];
					$amount = isset($response['amount']) ? $response['amount'] : '';
					if ($txn_id != '' && $amount != '') {
						$subPrice = $amount / 100;
						$couponCode = NULL;
						$paymentMode = 'Online';
						if($request->session()->has('payableAmt')){
							$couponCode = $request->session()->get('appliedCode');
							if($request->session()->get('payableAmt')==$subPrice){
								$paymentMode = 'Coupon & Online';
							}
						}
						$getSubscription = Subscription::where("id", $planID)->where("status", 1)->where("deleted", 0)->first();
						//echo '<pre />'; print_r($getSubscription); die;
						$subscriptionId = $getSubscription->id;
						$month = !empty($getSubscription->month) ? $getSubscription->month : 0;
						$today = date('Y-m-d');
						$countUserSubscription = UserSubscription::where("user_id", $userId)->where("mode", "!=", "FREE")->where("paymentStatus", 1)->count();
						$commissionType = ($countUserSubscription > 1) ? 'Renewal Plan' : 'First Plan';
						$getUserSubscription = UserSubscription::where("user_id", $userId)->where("end_date", ">=", $today)->where("paymentStatus", 1)->orderBy("id", "DESC")->first();
						if (!empty($getUserSubscription)) {
							$subscriptionEndDate = $getUserSubscription->end_date;
							$endDate = strtotime($subscriptionEndDate);
							$endDate = strtotime("+1 day", $endDate);
							$start_date = date('Y-m-d', $endDate);
							$startDate = strtotime($start_date);
							$startDate = strtotime("+".$month." months", $startDate);
							$end_date = date('Y-m-d', $startDate);
						} else {
							$start_date = date('Y-m-d');
							$end_date = date('Y-m-d', strtotime('+'.$month.' months'));
						}
						/*$month = !empty($getSubscription->month) ? $getSubscription->month : 0;
						$today = date('Y-m-d');
						$end_date = date('Y-m-d', strtotime('+'.$month.' months'));*/
						$user = User::where("id", $userId)->first();
						$data1 = array(
							'user_id'			=> $userId,
							'subscription_id'	=> $subscriptionId,
							'start_date'		=> $start_date,
							'end_date'			=> $end_date,
							'txn_id'			=> $txn_id,
							'amount'			=> $subPrice,
							'coupon_code'		=> $couponCode,
							'mode'				=> $paymentMode,
							'commission_type'	=> $commissionType,
							'order_id'			=> $orderId,
							'paymentStatus'		=> ($paymentStatus!='failed') ? 1 : 0,
							'created_at'		=> date('Y-m-d H:i:s'),
						);
						$inserId = UserSubscription::insertGetId($data1);
						if ($paymentStatus!='failed') {
							$msg = 'Subscription payment initiated';
							$this->helper->addNotification($userId,$msg);
							
							/*
							//ak
							$this->helper->smsWithTemplate($user->phone, 'Mambership', $user->name, $getSubscription->name);
							$this->helper->sendEmail($user->email, 'BrainyWood: Subscription payment initiated', $data = array('userName' => $user->name, 'message' => '<p>Thank you for payment initiated at BrainyWood,</p><p>You have subscribed a plan successfully with Payment id: ' . $txn_id . '</p>'));
							*/
							//Refer point on Purchase Membership
							if ($month == 1) {
								$this->helper->addUserReferPoints($userId,60);
							} elseif ($month == 6) {
								$this->helper->addUserReferPoints($userId,61);
							} elseif ($month == 12) {
								$this->helper->addUserReferPoints($userId,62);
							}


							//Remove applied coupon session values
							if($request->session()->has('payableAmt')){
								$request->session()->forget('planId');
								$request->session()->forget('appliedCode');
								$request->session()->forget('discount');
								$request->session()->forget('payableAmt');
							}

							if ($userId > 0 && $user->role_id == 3) {
								$this->helper->trackingApi($userId, 'subscription', 'payment initiated', $subscriptionId, '', 'website');
							}
							\Session::flash('success', 'Payment successful, your subscription available now.');
						} else {
							\Session::flash('error', 'Payment not initiated!');
							return redirect()->back();
						}
					}
				//}
			} 
			catch (\Exception $e) 
			{
				return  $e->getMessage();
				\Session::flash('error',$e->getMessage());
				return redirect()->back();
			}            
		}
		
		return redirect()->route('pricing');
	}

	public function order_status_check()
	{
		$date = Carbon::now()->subDays(5);
		$userSubscription = UserSubscription::where("paymentStatus", 0)->where("created_at", ">=", $date)->whereNotNull("order_id")->get();
		$api = new Api(env('RAZOR_KEY'), env('RAZOR_SECRET'));
	
		foreach($userSubscription as $val )
		{
			$orderId=$val->order_id;
			$response=$api->order->fetch($orderId)->payments();
			$orderStatus="failed";
			/*echo "<pre>";
			print_r($response);*/
			foreach($response->items as $val1)
			{
				if($val1->status=="captured")
				{
					$orderStatus="success"; 
				}
			}
			if($orderStatus=="success")
			{
				$getSubscription = Subscription::where("id", $val->subscription_id)->first();
				//echo '<pre />'; print_r($getSubscription); die;
				$month = !empty($getSubscription->month) ? $getSubscription->month : 0;
				$today = date('Y-m-d');
				$end_date = date('Y-m-d', strtotime('+'.$month.' months'));
				$userSub=UserSubscription::find($val->id);
				$userSub->start_date=$today;
				$userSub->end_date=$end_date;
				$userSub->paymentStatus=1;
				$userSub->cronUpdate=2;
				$userSub->save();
			}
		}
	}

	public function payment_capture(Request $request)
	{
		//Log::info($request);
		//$this->helper->sendEmail('<EMAIL>', 'BrainyWood: Webhook Testing', $data = array('userName' => 'Raj', 'message' => '<p>Thank you for payment initiated at BrainyWood,</p><p>You have subscribed a plan successfully with Webhook Testing'.$request->all().'</p>'));
		$response = array (
					  'entity' => 'event',
					  'account_id' => 'acc_GWJhT6MmcVEaZ2',
					  'event' => 'payment.failed',
					  'contains' => 
					  array (
						0 => 'payment',
					  ),
					  'payload' => 
					  array (
						'payment' => 
						array (
						  'entity' => 
						  array (
							'id' => 'pay_JJA5ccejFCl5N4',
							'entity' => 'payment',
							'amount' => 299925,
							'currency' => 'INR',
							'status' => 'failed',
							'order_id' => 'order_JJA5M7qrtsA927',
							'invoice_id' => NULL,
							'international' => false,
							'method' => 'wallet',
							'amount_refunded' => 0,
							'refund_status' => NULL,
							'captured' => false,
							'description' => NULL,
							'card_id' => NULL,
							'bank' => NULL,
							'wallet' => 'phonepe',
							'vpa' => NULL,
							'email' => '<EMAIL>',
							'contact' => '+************',
							'notes' => 
							array (
							),
							'fee' => NULL,
							'tax' => NULL,
							'error_code' => 'BAD_REQUEST_ERROR',
							'error_description' => 'Your payment has been cancelled. Try again or complete the payment later.',
							'error_source' => 'customer',
							'error_step' => 'payment_authentication',
							'error_reason' => 'payment_cancelled',
							'acquirer_data' => 
							array (
							  'transaction_id' => NULL,
							),
							'created_at' => **********,
						  ),
						),
					  ),
					  'created_at' => **********,
		);
		//dd($response['payload']['payment']['entity']);
		if ($request['payload']['payment']['entity']['status']=='captured') {
			$payment_id = $request['payload']['payment']['entity']['id'];
			$order_id = $request['payload']['payment']['entity']['order_id'];
			$order = UserSubscription::where('order_id',$order_id)->where('paymentStatus',0)->first();
			if(!empty($order)){
				$getSubscription = Subscription::where("id", $order->subscription_id)->first();
				$month = !empty($getSubscription->month) ? $getSubscription->month : 0;
				$today = date('Y-m-d');
				$end_date = date('Y-m-d', strtotime('+'.$month.' months'));
				$orderUpdate = UserSubscription::where('id',$order->id)->update(['start_date' => $today, 'end_date'	=> $end_date, 'txn_id' => $payment_id, 'paymentStatus' => 1]);
				$userId = $order->user_id;
				$user = User::where("id", $userId)->first();
				$msg = 'Subscription payment initiated';
				$this->helper->addNotification($userId,$msg);
				
				/*
				//ak
				$this->helper->smsWithTemplate($user->phone, 'Mambership', $user->name, $getSubscription->name);
				$this->helper->sendEmail($user->email, 'BrainyWood: Subscription payment initiated', $data = array('userName' => $user->name, 'message' => '<p>Thank you for payment initiated at BrainyWood,</p><p>You have subscribed a plan successfully with Payment id: ' . $payment_id . '</p>'));
				*/
			}
		}
	}

	public function contactUs()
	{
		$pagename = "Contact Us";

		return view('front.contact_us', compact('pagename'));
	}
	public function postContact(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'name' => 'required|regex:/^[\pL\s\-]+$/u',
			'phone' => 'required|numeric',
			'email' => 'required|email',
			'message' => 'required',
			'g-recaptcha-response' => 'required|captcha',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$user = Auth::user();
			$userId 	= !empty($user) ? $user->id : 0;
			$name 		= !empty($request->name) ? $request->name : $user->name;
			$phone 		= !empty($request->phone) ? $request->phone : $user->phone;
			$email 		= !empty($request->email) ? $request->email : $user->email;
			$message 	= $request->message;
			if (!empty($email) && !empty($message)) {
				$data = array(
					'user_id'  		=> $userId,
					'name'  		=> $name,
					'phone'  		=> $phone,
					'email'  		=> $email,
					'message'  		=> $message,
					'created_at'    => date('Y-m-d H:i:s'),
				);
				$insertId = Contactus::insertGetId($data);
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'contactus', '', $insertId, '', 'website');
				}
				
				\Session::flash('success', 'Your Message Added Successfully.');
				return back();
			} else {
				\Session::flash('error', 'Wrong Paramenter Passed!');
				return back();
			}
		}
	}

	public function discussionGroups(Request $request)
	{
		$pagename = "Discussion Groups";
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			\Session::flash('error', 'Please login first to proceed.');
			return redirect()->route('home');
		}else{
			$userId = $user->id;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$userSubscription = $this->helper->userSubscription($userId);
		
		$class_id = !empty($user) ? $user->class_id : '';
		$classId = ($request->class_id) ? $request->class_id : $class_id;
		$search   = $request->search;
		$pinMessage = PinMessage::where("msg_type", 202)->where("status", 1)->orderBy('id', 'DESC')->first();
		$pinMessageChatGroup = !empty($pinMessage) ? $pinMessage->message : '';
		$acapopulargroups = ChatGroup::where('class_type', 301)->whereRaw("find_in_set($classId,class_ids)")->whereRaw("find_in_set(402,type)")->where("status", 1);
		if (!empty($search)) {
			$acapopulargroups = $acapopulargroups->where("title", "like", "%" . $search . "%");
		}
		$acapopulargroups = $acapopulargroups->orderBy('sort_id', 'ASC')->get();
		$acaothergroups = ChatGroup::where('class_type', 301)->whereRaw("find_in_set($classId,class_ids)")->whereRaw("find_in_set(401,type)")->where("status", 1);
		if (!empty($search)) {
			$acaothergroups = $acaothergroups->where("title", "like", "%" . $search . "%");
		}
		$acaothergroups = $acaothergroups->orderBy('sort_id', 'ASC')->get();

		$sklpopulargroups = ChatGroup::where('class_type', 302)->whereRaw("find_in_set($classId,class_ids)")->whereRaw("find_in_set(402,type)")->where("status", 1);
		if (!empty($search)) {
			$sklpopulargroups = $sklpopulargroups->where("title", "like", "%" . $search . "%");
		}
		$sklpopulargroups = $sklpopulargroups->orderBy('sort_id', 'ASC')->get();
		$sklothergroups = ChatGroup::where('class_type', 302)->whereRaw("find_in_set($classId,class_ids)")->whereRaw("find_in_set(401,type)")->where("status", 1);
		if (!empty($search)) {
			$sklothergroups = $sklothergroups->where("title", "like", "%" . $search . "%");
		}
		$sklothergroups = $sklothergroups->orderBy('sort_id', 'ASC')->get();

		$userJoinedGroups = ChatGroupUser::select("group_id")->where("user_id", $userId)->get();
		$usergroups = array();
		if (!empty($userJoinedGroups) && !empty($userJoinedGroups->toArray())) {
			foreach ($userJoinedGroups as $value) {
				$userJoinedGroupIds[] = $value['group_id'];
			}
			$usergroups  = ChatGroup::whereIn("id", $userJoinedGroupIds)->where("status", 1);
			if (!empty($usergroups)) {
				$usergroups = $usergroups->where("title", "like", "%" . $search . "%");
			}
			$usergroups = $usergroups->orderBy("sort_id", "ASC")->get();
		}

		if ($userId > 0 && $user->role_id == 3) {
			//$this->helper->trackingApi($userId, 'chat', '', 0, '', 'website');
		}

		return view('front.discussion_groups', compact('pagename','userId','userSubscription','search','pinMessageChatGroup','acapopulargroups','acaothergroups','sklpopulargroups','sklothergroups','usergroups'));
	}

	public function joinGroup(Request $request)
	{
		$pagename = "Join Group";
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			\Session::flash('error', 'Please login first to proceed.');
			return redirect()->route('home');
		}else{
			$userId = $user->id;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$userSubscription = $this->helper->userSubscription($userId);
		
		$validator = Validator::make($request->all(), [
			'group_id' => 'required|numeric',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$groupId = $request->group_id;
			$userGroup = ChatGroupUser::where("group_id", $groupId)->where("user_id", $userId)->first();
			if (!empty($userGroup)) {
				if ($userGroup->block_status == 1) {
					$message = "You have been blocked due to Violating user chat policies. Contact Support.";
				} else {
					$message = "You have been already joined this group.";
				}
				\Session::flash('error', $message);
				return redirect()->back();
			} else {
				$data = array(
					'group_id'		=> $groupId,
					'user_id'		=> $userId,
					'created_at'	=> date('Y-m-d H:i:s'),
				);
				$joinGroupId = ChatGroupUser::insertGetId($data);
				//Refer point on Chat Group Joining
				$totalGroupsAdded = ChatGroupUser::where("user_id", $userId)->where("block_status", 0)->count();
				if ($totalGroupsAdded == 3) {
					$this->helper->addUserReferPoints($userId,63);
				} elseif ($totalGroupsAdded == 5) {
					$this->helper->addUserReferPoints($userId,64);
				} elseif ($totalGroupsAdded == 10) {
					$this->helper->addUserReferPoints($userId,65);
				} elseif ($totalGroupsAdded == 15) {
					$this->helper->addUserReferPoints($userId,66);
				}
				\Session::flash('success', 'Welcome! You have been joined the group successfully.');
				return redirect()->back();
			}
		}
	}

	public function groupDiscussion(Request $request, $groupId)
	{
		$pagename = "Student Group Discussion";
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			\Session::flash('error', 'Please login first to proceed.');
			return redirect()->route('home');
		}else{
			$userId = $user->id;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$userSubscription = $this->helper->userSubscription($userId);
		
		$class_id = !empty($user) ? $user->class_id : '';
		$classId = ($request->class_id) ? $request->class_id : $class_id;
		$page = $request->has('page') ? $request->get('page') : 1;
		$limit = $request->has('limit') ? $request->get('limit') : 10;
		$group = ChatGroup::findOrFail($groupId);
		$userGroup = array();
		$messagedata_arr = array();
		if (!empty($group)) {
			$userGroup = ChatGroupUser::where("group_id", $groupId)->where("user_id", $userId)->first();
			if (empty($userGroup)) {
				\Session::flash('error', 'Please join the group first to proceed.');
				return redirect()->route('discussionGroups');
			}
			$pinMessageIn = PinMessage::whereRaw("find_in_set($classId,class_ids)")->where("msg_type", 203)->where("group_id", $groupId)->where("status", 1)->orderBy('id', 'DESC')->first();
			$pinMessageChatInbox = !empty($pinMessageIn) ? $pinMessageIn->message : '';
			//$chat_messages_data = ChatMessage::where("group_id", $groupId)->orderBy("id", "DESC")->paginate($limit);
			$chat_messages_data = ChatMessage::where("group_id", $groupId)->orderBy("id", "DESC")->get();
			foreach ($chat_messages_data as $key => $message) {
				if ($message->parent_id > 0) {
					if ($message->user_id == $userId) {
						$type = 3;
					} else {
						$type = 4;
					}
					if ($message->user_data->role_id == 3) {
					} elseif ($message->user_data->role_id == 2) {
						$type = 7;
					} else {
						$type = 8;
					}
				} else {
					if ($message->user_id == $userId) {
						$type = 1;
					} else {
						$type = 2;
					}
					if ($message->user_data->role_id == 3) {
					} elseif ($message->user_data->role_id == 2) {
						$type = 5;
					} else {
						$type = 6;
					}
				}
				$messagedata['id'] 			= $message->id;
				$messagedata['parent_id'] 	= $message->parent_id;
				//$messagedata['user_id'] 	= $message->user_id;
				$messagedata['user_name'] 	= $message->user_data->name;
				$messagedata['user_image'] 	= ($message->user_data->image) ? asset('upload/profile/' . $message->user_data->image) : '';
				$messagedata['image'] 		= !empty($message->image) ? $message->image : '';
				$messagedata['video'] 		= !empty($message->video) ? $message->video : '';
				$messagedata['message'] 	= $this->helper->checkBadWords($message->message);
				$messagedata['status'] 		= $message->status;
				$messagedata['created_at'] 	= $message->created_at->diffForHumans();
				$messagedata['type'] 		= $type;
				$messagedata['oldmsg_user_name'] 	= ($message->parent_id > 0) ? $message->oldmsg_data->user_data->name : '';
				$messagedata['oldmsg_user_image'] 	= ($message->parent_id > 0) ? ($message->oldmsg_data->user_data->image) ? asset('upload/profile/' . $message->oldmsg_data->user_data->image) : '' : '';
				$messagedata['oldmsg_image'] 		= ($message->parent_id > 0) ? !empty($message->oldmsg_data->image) ? $message->oldmsg_data->image : '' : '';
				$messagedata['oldmsg_video'] 		= ($message->parent_id > 0) ? !empty($message->oldmsg_data->video) ? $message->oldmsg_data->video : '' : '';
				$messagedata['oldmsg_message'] 		= ($message->parent_id > 0) ? $this->helper->checkBadWords($message->oldmsg_data->message) : '';
				$messagedata['oldmsg_created_at'] 	= ($message->parent_id > 0) ? $message->oldmsg_data->created_at->diffForHumans() : '';
				array_push($messagedata_arr, $messagedata);
			}
			$messagedata_arr = array_reverse($messagedata_arr);
			//dd($messagedata_arr);
			/*$data = '';
			if ($request->ajax()) {
				foreach ($messagedata_arr as $user) {
					$data.='<li>'.'Name:'.' <strong>'.$user['user_name'].'</strong><br> MSGID: '.$user['id'].'</li>';
				}
				return $data;
			}*/
		}
		if ($userId > 0 && $user->role_id == 3) {
			//$this->helper->trackingApi($userId, 'chat', '', $groupId, '', 'website');
		}

		return view('front.discussion_group_chat', compact('pagename','userId','userSubscription','group','userGroup','pinMessageChatInbox','messagedata_arr'));
	}

	public function chatSendMessage(Request $request, $groupId)
	{
		$s3 = AWS::createClient('s3');
		$msg = '';
		//dd($request->all());
		$userId = Auth::user()->id;
		$validator = Validator::make($request->all(), [
			//'message' => 'required',
			//'image' => 'required',
			'file' => 'mimes:jpg,jpeg,png,gif,webp,mp4',
		]);

		if ($validator->fails()) {
			$msg = $validator->messages()->first();
			//return back()->withErrors($validator)->withInput();
		}
		$userSubscription = $this->helper->userSubscription($userId);
		if ($userSubscription == 0) {
			$chatMessageCount = ChatMessage::where("group_id", $groupId)->where("user_id", $userId)->count();
			if ($chatMessageCount >= config('constant.FREE_CHAT_MSG')) {
				$msg = "subscribe";
			}
		}
		$getUserStatus = ChatGroupUser::where("group_id", $groupId)->where("user_id", $userId)->first();
		if (empty($getUserStatus)) {
			$msg = "joingroup";
		} else {
			if ($getUserStatus->block_status == 1) {
				$msg = "blocked";
			}
		}
		if ($msg != '') {
			echo $msg;
		} else {
			$image = $video = '';
			$filename = $request->file('file');
			if (!empty($filename)) {
				$extension = $request->file('file')->extension();
				//$extension = pathinfo($filename, PATHINFO_EXTENSION);
				$supported_image = array('gif', 'jpg', 'jpeg', 'png', 'webp');
				if (in_array($extension, $supported_image)) {
					$image = $request->file('file');
				}
				$supported_video = array('mp4');
				if (in_array($extension, $supported_video)) {
					$video = $request->file('file');
				}
			}

			try
			{
				$input = $request->all();
				$input['group_id'] = $groupId;
				$input['user_id']  = $userId;
				$imageUrl = $videoUrl = NULL;
				$destinationPath = public_path().'/upload/chat_message_images/';
				if($image){
					$originalFile = $image->getClientOriginalName();
					$imageFilename = "chat_".time().$originalFile;
					$image->move($destinationPath, $imageFilename);
					//S3 Upload
					$s3->putObject(array(
						'Bucket'     => env('AWS_BUCKET'),
						'Key'        => "assignment_data/".$imageFilename,
						'SourceFile' => $destinationPath.$imageFilename,
					));
					$imageUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $imageFilename;
					if(!empty($image) && file_exists( $destinationPath.$imageFilename )) {
						unlink( $destinationPath.$imageFilename );
					}
					$input['image'] = $imageUrl;
				}
				if($video){
					$originalFile = $video->getClientOriginalName();
					$videoFilename = "chat_".time().".mp4";
					$video->move($destinationPath, $videoFilename);
					//S3 Upload
					$s3->putObject(array(
						'Bucket'     => env('AWS_BUCKET'),
						'Key'        => "assignment_data/".$videoFilename,
						'SourceFile' => $destinationPath.$videoFilename,
					));
					$videoUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $videoFilename;
					if(!empty($video_file) && file_exists( $destinationPath.$videoFilename )) {
						unlink( $destinationPath.$videoFilename );
					}
					$input['video'] = $videoUrl;
				}
				
				$input['message'] = ($request->message) ? $request->message : NULL;

				\DB::beginTransaction();
					$chatMsg = new ChatMessage();
					$chatMsg->fill($input)->save();
					$insertId = $chatMsg->id;
					$chatMessage = ChatMessage::with('user_data','group_data')->where("id", $insertId)->first();
					if (!empty($chatMessage)) {
						$userName = $chatMessage->user_data->name;
						$groupName = $chatMessage->group_data->title;
						$getGroupUsers = ChatGroupUser::select("user_id")->where("group_id", $groupId)->where("block_status", 0)->where("mute_status", 0)->get();
						if (!empty($getGroupUsers) && !empty($getGroupUsers->toArray())) {
							foreach ($getGroupUsers as $value) {
								//$groupUserIds[] = $value['user_id'];
								$user = User::select("deviceToken")->where("id", "!=", $userId)->where("id", $value['user_id'])->first();
								$token = isset($user->deviceToken) ? $user->deviceToken : '';
								if ($token!='') {
									$title = 'BrainyWood';
									$message = $userName . ' sent a message in ' . $groupName . ' ' . $chatMessage->message;
									$click_action = 'Chat';
									$module_id = $groupId;
									$this->helper->notificationsend($token, $title, $message, $click_action, $module_id);
								}
							}
						}
					}
				\DB::commit();
				echo "Done";
				/*\Session::flash('success', 'Chat Message Sent Successfully.');
				return redirect()->back();*/
			}
			catch (\Throwable $e)
			{
				\DB::rollback();
				echo $e->getMessage().' on line '.$e->getLine();
				/*\Session::flash('error', $e->getMessage().' on line '.$e->getLine());
				return back();*/
			}
		}
	}

	public function chatMuteNotification(Request $request, $groupId)
	{
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			\Session::flash('error', 'Please login first to proceed.');
			return redirect()->route('home');
		}else{
			$userId = $user->id;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$userSubscription = $this->helper->userSubscription($userId);
		
		$chatGroupUser = ChatGroupUser::where("group_id", $groupId)->where("user_id", $userId)->first();
		if (!empty($chatGroupUser)) {
			if ($chatGroupUser->mute_status == 0) {
				$mute_status = 1;
			} else {
				$mute_status = 0;
			}
			$chatGroupUser->update(["mute_status" => $mute_status]);
			//$this->helper->trackingApi($userId, 'chat', '', 0, '', $deviceType);
			\Session::flash('success', 'Chat group mute notification updated successfully.');
			return redirect()->back();
		} else {
			\Session::flash('error', 'Chat group mute notification not updated!');
			return redirect()->back();
		}
	}

	public function chatGroupLeave(Request $request, $groupId)
	{
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			\Session::flash('error', 'Please login first to proceed.');
			return redirect()->route('home');
		}else{
			$userId = $user->id;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$userSubscription = $this->helper->userSubscription($userId);
		
		$chatGroupUser = ChatGroupUser::where("group_id", $groupId)->where("user_id", $userId)->first();
		if (!empty($chatGroupUser)) {
			$chatGroupUser->delete();
			//$this->helper->trackingApi($userId, 'chat', '', 0, '', $deviceType);
			$message = "Chat group left successfully.";
			\Session::flash('success', 'Chat group left successfully.');
			return redirect()->route('discussionGroups');
		} else {
			\Session::flash('error', 'Chat group not found!');
			return redirect()->back();
		}
	}

	public function referralSystem(Request $request)
	{
		$pagename = "Referral System";
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			\Session::flash('error', 'Please login first to proceed.');
			return redirect()->route('home');
		}else{
			$userId = $user->id;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$userSubscription = $this->helper->userSubscription($userId);
		
		$myBalance = $user->user_points_credit->sum('points') - $user->user_points_debit->sum('points');
		$points_arr = ($request->points) ? explode("to", $request->points) : [];
		$startPoint = !empty($points_arr) ? $points_arr[0] : '';
		$endPoint   = !empty($points_arr) ? $points_arr[1] : '';
		$referPoints = ReferPoint::orderBy('sort_id', 'ASC');
		if (is_numeric($endPoint) && $endPoint!='above') {
			$referPoints = $referPoints->where('points', '>=', $startPoint)->where('points', '<=', $endPoint);
		} elseif ($endPoint=='above') {
			$referPoints = $referPoints->where('points', '>=', $startPoint);
		}
		$referPoints = $referPoints->where('status',1)->get();
		
		return view('front.referral_system', compact('pagename','userSubscription','myBalance','referPoints'));
	}

	public function pointsHistory(Request $request)
	{
		$pagename = "My Points History";
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			\Session::flash('error', 'Please login first to proceed.');
			return redirect()->route('home');
		}else{
			$userId = $user->id;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$userSubscription = $this->helper->userSubscription($userId);
		
		$acheived = $user->user_points_credit->sum('points');
		$redeemed = $user->user_points_debit->sum('points');
		$acheived_history = UserPoint::where('user_id', $userId)->where('transaction_type', 502)->orderBy('id','DESC')->get();
		$redeemed_history = UserPoint::where('user_id', $userId)->where('transaction_type', 501)->orderBy('id','DESC')->get();
		
		return view('front.points_history', compact('pagename','userSubscription','acheived','redeemed','acheived_history','redeemed_history'));
	}

	public function leadershipBoard(Request $request)
	{
		$pagename = "Leadership Board";
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			\Session::flash('error', 'Please login first to proceed.');
			return redirect()->route('home');
		}else{
			$userId = $user->id;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$classId = !empty($user) ? $user->class_id : '';
		$userSubscription = $this->helper->userSubscription($userId);
		
		$myBalance = $user->user_points_credit->sum('points') - $user->user_points_debit->sum('points');
		$classRank = $appRank = 0;
		$pinMessage = '';
		$class_ranking = UserPoint::select('users.id', 'users.name', 'users.image', 'users.city', DB::raw('SUM(points) as sum_points'))
					   ->join('users', 'users.id', '=', 'user_points.user_id')
					   ->where('user_points.transaction_type', 502)
					   ->where('users.class_id', $classId)
					   ->groupBy('user_id')
					   ->orderBy('sum_points', 'DESC')
					   ->orderBy('name', 'ASC')
					   //->take(15)
					   ->get();
		$classrankdata = [];
		$class_rank = 1;
		if (!empty($class_ranking)) {
			foreach ($class_ranking as $ckey => $classUser) {
				if ($userId == $classUser->id) {
					$classRank = $class_rank;
				}
				if ((int)$classUser->sum_points > 0) {
					$classrank['user_id']		= $classUser->id;
					$classrank['user_name']		= $classUser->name;
					$classrank['user_image']	= !empty($classUser->image) ? asset('upload/profile/'.$classUser->image) : '';
					$classrank['user_city']		= $classUser->city;
					$classrank['user_points']	= (int)$classUser->sum_points;
					$classrank['user_rank']		= $class_rank++;
					if ($class_rank <= 16) {
						array_push($classrankdata, $classrank);
					}
				}
				if ($userId != $classUser->id && $classRank == 0) {
					$classRank = count($class_ranking) + 1;
				}
			}
		}
		$app_ranking = UserPoint::select('users.id', 'users.name', 'users.image', 'users.city', 'users.class_id', DB::raw('SUM(points) as sum_points'))
					   ->join('users', 'users.id', '=', 'user_points.user_id')
					   ->where('user_points.transaction_type', 502)
					   ->groupBy('user_id')
					   ->orderBy('sum_points', 'DESC')
					   ->orderBy('name', 'ASC')
					   //->take(15)
					   ->get();
		$apprankdata = [];
		$app_rank = 1;
		if (!empty($app_ranking)) {
			foreach ($app_ranking as $ckey => $appUser) {
				if ($userId == $appUser->id) {
					$appRank = $app_rank;
				}
				if ((int)$appUser->sum_points > 0) {
					$userClass = StudentClass::where("id", $appUser->class_id)->first();
					$userClassName = !empty($userClass) ? $userClass->class_name : '';
					$apprank['user_id']		= $appUser->id;
					$apprank['user_name']	= $appUser->name;
					$apprank['user_class']	= $userClassName;
					$apprank['user_image']	= !empty($appUser->image) ? asset('upload/profile/'.$appUser->image) : '';
					$apprank['user_city']	= $appUser->city;
					$apprank['user_points']	= (int)$appUser->sum_points;
					$apprank['user_rank']	= $app_rank++;
					if ($app_rank <= 16) {
						array_push($apprankdata, $apprank);
					}
				}
				if ($userId != $appUser->id && $appRank == 0) {
					$appRank = count($app_ranking) + 1;
				}
			}
		}
		
		return view('front.leadership_board', compact('pagename','userSubscription','myBalance','classRank','appRank','pinMessage','classrankdata','apprankdata'));
	}

	public function purchaseGallery(Request $request)
	{
		$pagename = "Purchase Gallery";
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			\Session::flash('error', 'Please login first to proceed.');
			return redirect()->route('home');
		}else{
			$userId = $user->id;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$userSubscription = $this->helper->userSubscription($userId);
		
		$myBalance = $user->user_points_credit->sum('points') - $user->user_points_debit->sum('points');
		$products = Product::where('status', 1)->orderBy('sort_id', 'ASC')->get();
		
		return view('front.purchase_gallery', compact('pagename','userSubscription','myBalance','products'));
	}

	public function redeemPoint(Request $request)
	{
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			\Session::flash('error', 'Please login first to proceed.');
			return redirect()->route('home');
		}else{
			$userId = $user->id;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$userSubscription = $this->helper->userSubscription($userId);

		$productId = $request->productId;
		if ($userSubscription == 1) {
			$product = Product::where('id', $productId)->where('status', 1)->first();
			if (!empty($product)) {
				$myBalance = $user->user_points_credit->sum('points') - $user->user_points_debit->sum('points');
				if ($myBalance >= $product->points) {
					$data = array(
						'user_id'  			=> $userId,
						'refer_point_id'  	=> 0,
						'points'  			=> !empty($product) ? $product->points : 0,
						'description'  		=> !empty($product) ? $product->title : NULL,
						'transaction_type'  => 501,
						'order_status'		=> 601,
						'created_at'        => date('Y-m-d H:i:s'),
						'updated_at'        => date('Y-m-d H:i:s'),
					);
					$insertId = UserPoint::insertGetId($data);
					$message = "Congratulations. Your order has been placed succeesfully and the team will reach you out for delievery";
					\Session::flash('success', $message);
					return redirect()->route('purchaseGallery');
				} else {
					$message = "You have insufficient points to redeem this Product!";
					\Session::flash('error', $message);
					return redirect()->route('purchaseGallery');
				}
			} else {
				$message = "Product not Available!";
				\Session::flash('error', $message);
				return redirect()->route('purchaseGallery');
			}
		} else {
			$message = "Please Subscribe a Plan first to Proceed Continue!";
			\Session::flash('error', $message);
			return redirect()->route('purchaseGallery');
		}
	}

	public function earnedPointCloseModal(Request $request, $pointId)
	{
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			\Session::flash('error', 'Please login first to proceed.');
			return redirect()->route('home');
		}else{
			$userId = $user->id;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$userSubscription = $this->helper->userSubscription($userId);

		$userPoint = UserPoint::where('id', $pointId)->where('user_id', $userId)->first();
		if (!empty($userPoint)) {
			$data = array(
				'view_status'	=> 1,
				'updated_at'	=> date('Y-m-d H:i:s'),
			);
			$update = UserPoint::where('id', $pointId)->update($data);
		}
		return redirect()->back();
	}

	public function getCommunityPosts(Request $request)
	{
		$pagename = "Community Posts";
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			/*\Session::flash('error', 'Please login first to proceed.');
			return redirect()->route('ourCourses');*/
		}else{
			$userId = $user->id;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		if ($userId > 0 && $user->role_id == 3) {
			$this->helper->trackingApi($userId, 'community_post', '', 0, '', 'website');
		}
		$userSubscription = $this->helper->userSubscription($userId);
		/*if($userSubscription==0){
			\Session::flash('error', 'Please subscribe a plan first to proceed.');
			return redirect()->route('ourCourses');
		}*/
		$cPostCategories = CommunityCategory::where("status", 1)->orderBy("title","ASC")->get();
		/*$resultCommPost = CommunityPost::where("result_announce", 1)->where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->first();
		$resultCommPostId = !empty($resultCommPost) ? $resultCommPost->id : '';*/
		$resultBanners = CommunityPostBanner::where('status',1)->orderBy('sort_id','ASC')->get();
		/*$topPosts = PostEntry::selectRaw('post_entries.*, count(post_entries_likes.id) as likes_count')
				->leftJoin('post_entries_likes', function ($join) {
					$join->on('post_entries_likes.post_entry_id', '=', 'post_entries.id');
				})
				->where("status", 1)->where("deleted", 0)->groupBy('post_entries.id')->orderBy('likes_count', 'desc')
				//->take(10)->get();
				->paginate(10);*/
		$topPosts = PostEntry::where("topPost", 1)->where("status", 1)->where("deleted", 0)->orderBy("updated_at", "DESC")->paginate(10);


		return view('front.community_posts', compact('pagename','userId','userSubscription','cPostCategories','resultBanners','topPosts'));
	}
	public function communityPostDetails(Request $request, $id)
	{
		$id = base64_decode($id);
		$pagename = "Community Post Details";
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			/*\Session::flash('error', 'Please login first to proceed.');
			return redirect()->route('ourCourses');*/
		}else{
			$userId = $user->id;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		if ($userId > 0 && $user->role_id == 3) {
			$this->helper->trackingApi($userId, 'community_post', '', $id, '', 'website');
		}
		$userSubscription = $this->helper->userSubscription($userId);
		/*if($userSubscription==0){
			\Session::flash('error', 'Please subscribe a plan first to proceed.');
			return redirect()->route('ourCourses');
		}*/
		$communityPost = CommunityPost::where("id", $id)->where("status", 1)->where("deleted", 0)->first();
		$postEntries = $communityPost->post_entries_data()->where("status", 1)->where("deleted", 0)->orderBy("sort_id", "ASC")->get();
		$leaderboardPosts = $communityPost->post_entries_data()->where("status", 1)->where("deleted", 0)->orderBy("sort_id", "ASC")->take(5)->get();
		
		return view('front.community_post_details', compact('pagename','userId','userSubscription','communityPost','postEntries','leaderboardPosts'));
	}
	public function postUpload(Request $request, $id)
	{
		$id = base64_decode($id);
		$pagename = "Upload Your Entry";
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			\Session::flash('error', 'Please login first to proceed.');
			return redirect()->route('communityPostDetails', base64_encode($id));
		}else{
			$userId = $user->id;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$userSubscription = $this->helper->userSubscription($userId);
		if($userSubscription==0){
			\Session::flash('error', 'Please subscribe a plan first to proceed.');
			//return redirect()->route('communityPostDetails', base64_encode($id));
			return redirect()->route('pricing');
		}

		return view('front.post_upload', compact('id','pagename','userSubscription'));
	}
	public function userPostSubmit(Request $request, $id)
	{
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			\Session::flash('error', 'Please login first to proceed.');
			return redirect()->route('ourCourses');
		}else{
			$userId = $user->id;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		$userSubscription = $this->helper->userSubscription($userId);
		if($userSubscription==0){
			\Session::flash('error', 'Please subscribe a plan first to proceed.');
			return redirect()->route('pricing');
		}
		$communityPostId = $id;
		$title = $request->title;
		$content = $request->content;
		$msg = '';
		if (!empty($userId) && !empty($communityPostId) || !empty($content) || !empty($request->file('image_file')) || !empty($request->file('video_file')) ) {
			$image_file = $request->file('image_file');
			$video_file = $request->file('video_file');
			if($request->hasFile('image_file')){
				if($image_file){
					$destinationPath = public_path().'/upload/community_posts/';
					$imageOriginalFile = $image_file->getClientOriginalName();
					$imageFilename = "user_post_".$communityPostId."_".time().$imageOriginalFile;
					$image_file->move($destinationPath, $imageFilename);
					/*$url = 'upload/community_posts/' . $imageFilename;
					$thumb_img = Image::make($url)->resize(200, 200);
					$thumb_img->save('upload/community_posts/thumb/'.$imageFilename,80);*/
					//S3 Upload
					$s3 = AWS::createClient('s3');
					$s3->putObject(array(
						'Bucket'     => env('AWS_BUCKET'),
						'Key'        => "assignment_data/".$imageFilename,
						'SourceFile' => $destinationPath.$imageFilename,
					));
					$imageUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $imageFilename;
					if(!empty($image_file) && file_exists( $destinationPath.$imageFilename )) {
						unlink( $destinationPath.$imageFilename );
					}
				}
			}
			if($request->hasFile('video_file')){
				if($video_file){
					$destinationPath = public_path().'/upload/community_posts/';
					$videoOriginalFile = $video_file->getClientOriginalName();
					//$videoFilename = rand(111,999).strtotime(date('Y-m-d-H:isa')).$videoOriginalFile;
					$videoFilename = "user_post_".$communityPostId."_".time()."_org.mp4";
					$video_file->move($destinationPath, $videoFilename);
					//S3 Upload
					$s3 = AWS::createClient('s3');
					$s3->putObject(array(
						'Bucket'     => env('AWS_BUCKET'),
						'Key'        => "assignment_data/".$videoFilename,
						'SourceFile' => $destinationPath.$videoFilename,
					));
					$videoUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/assignment_data/'. $videoFilename;
					if(!empty($video_file) && file_exists( $destinationPath.$videoFilename )) {
						unlink( $destinationPath.$videoFilename );
					}
				}
			}
			$data = array(
				'community_post_id' => $communityPostId,
				'user_id' 			=> $userId,
				'title' 			=> !empty($title) ? $title : NULL,
				'image' 			=> !empty($imageUrl) ? $imageUrl : NULL,
				'video' 			=> !empty($videoUrl) ? $videoUrl : NULL,
				'description' 		=> $content,
				'status' 			=> 0,
				'created_at' 		=> date('Y-m-d H:i:s'),
			);
			$insertId = PostEntry::insertGetId($data);
			$userPoint = UserPoint::where("user_id", $userId)->where("module_id", $communityPostId)->where("refer_point_id", 78)->first();
			if (empty($userPoint)) {
				$this->helper->addUserReferPoints($userId,78,$communityPostId);
			}
			\Session::flash('success', 'Your post uploaded successfully, Team will approve it.');
			//return redirect()->back();
			//return redirect()->route('communityPostDetails', base64_encode($communityPostId));
			return redirect()->route('getCommunityPosts');
		} else {
			\Session::flash('error', 'Wrong Paramenter Passed!');
			return redirect()->back();
		}
	}
	public function like_post(Request $request)
	{
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			echo "Please login first to proceed.";
		}else{
			$userId = $user->id;
		}
		$userPostId = $request->postId;
		$userPostLikes = PostEntryLike::where("post_entry_id", $userPostId)->where("user_id", $userId)->first();
		if (!empty($userPostLikes)) {
			$delete = $userPostLikes->delete();
			echo "Unlike";
		} else {
			$data = array(
				'post_entry_id' => $userPostId,
				'user_id' 		=> $userId,
				'created_at' 	=> date('Y-m-d H:i:s'),
			);
			$insertId = PostEntryLike::insertGetId($data);
			echo "Done";
		}
	}
	public function userPostComments(Request $request, $id)
	{
		$id = base64_decode($id);
		$pagename = "Post Details With Comments";
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			/*\Session::flash('error', 'Please login first to proceed.');
			return redirect()->route('ourCourses');*/
		}else{
			$userId = $user->id;
		}
		if ($request->session()->has('loginToken')){
			if(!empty($user) && $user->api_token != $request->session()->get('loginToken')){
				if ($userId > 0 && $user->role_id == 3) {
					$this->helper->trackingApi($userId, 'logout', '', 0, '', 'website', 1);
				}
				\Session::flash('error', 'You have already logged-in another device!');
				Auth::logout();
				return redirect()->route('home');
			}
		}
		if ($userId > 0 && $user->role_id == 3) {
			$this->helper->trackingApi($userId, 'community_post_user', 'users post', $id, '', 'website');
		}
		$userSubscription = $this->helper->userSubscription($userId);
		/*if($userSubscription==0){
			\Session::flash('error', 'Please subscribe a plan first to proceed.');
			return redirect()->route('ourCourses');
		}*/
		$postEntry = PostEntry::where("id", $id)->where("status", 1)->where("deleted", 0)->first();
		$postComments = $postEntry->post_comments_data()->orderBy("id", "ASC")->get();
		
		return view('front.post_details_comments', compact('pagename','userId','userSubscription','postEntry','postComments'));
	}
	public function savePostComment(Request $request)
	{
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
			echo "Please login first to proceed.";
		}else{
			$userId = $user->id;
		}
		$userPostId = $request->postId;
		$comment = $request->comment;
		$data = array(
			'post_entry_id' => $userPostId,
			'user_id' 		=> $userId,
			'comment' 		=> $comment,
			'created_at' 	=> date('Y-m-d H:i:s'),
		);
		$insertId = PostEntryComment::insertGetId($data);
		echo "Done";
	}

	public function franchiseplans()
	{
		$pagename = "Franchise Plans";
		$relates = TestimonialRelate::whereIn("id",[8,2,4])->orderBy("id", "DESC")->get();
		$testimonials = Testimonial::where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->limit(15)->get();

		return view('front.franchiseplans', compact('pagename','relates','testimonials'));
	}
	public function saveFranchise(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'name' => 'required|regex:/^[\pL\s\-]+$/u',
			'phoneno' => 'required|numeric',
			'email' => 'required|email',
		]);

		if ($validator->fails()) {
			return back()
				->withErrors($validator)
				->withInput();
		} else {
			$name 		= $request->name;
			$email		= $request->email;
			$phone 		= $request->phoneno;
			$profession	= $request->profession;
			if (!empty($email) && !empty($phone)) {
				
				/*
				//ak
				$this->helper->sendEmail($email, 'BrainyWood: Request for Franchise', $data = array('userName' => $name, 'message' => '<p>Thank you for connecting at BrainyWood,</p><p>Your Request Sent Successfully, Team will contact you as soon as possible.</p>'));
				$this->helper->sendEmail('<EMAIL>', 'BrainyWood: Request for Franchise', $data = array('userName' => 'Team', 'message' => '<p>Request for Franchise details as below: </p><p>Name: ' . $name . '</p><p>Email: ' . $email . '</p><p>Phone: ' . $phone . '</p><p>Profession: ' . $profession . '</p>'));
				*/
				
				\Session::flash('success', 'Your Request Sent Successfully, Team will contact you as soon as.');
				return back();
			} else {
				\Session::flash('error', 'Wrong Paramenter Passed!');
				return back();
			}
		}
	}

	public function franchiseOne()
	{
		$pagename = "Franchise Landing Page";
		$relates = TestimonialRelate::whereIn("id",[8,2,4])->orderBy("id", "DESC")->get();
		$testimonials = Testimonial::where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->limit(15)->get();

		return view('front.franchiseone', compact('pagename','relates','testimonials'));
	}
	public function franchiseOneLead(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'name' => 'required|regex:/^[\pL\s\-]+$/u',
			'phoneno' => 'required|numeric',
			'email' => 'required|email',
			'g-recaptcha-response' => 'required|captcha',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$name 		= $request->name;
			$email		= $request->email;
			$phone 		= $request->phoneno;
			$profession	= $request->profession;
			if (!empty($email) && !empty($phone)) {
				
	/*
	//ak
				$this->helper->sendEmail($email, 'BrainyWood: Request for Franchise', $data = array('userName' => $name, 'message' => '<p>Thank you for connecting at BrainyWood,</p><p>Your Request Sent Successfully, Team will contact you as soon as possible.</p><p>STEP 1: Watch This Video How BrainyWood can help you grow your Income while helping students learn effectively</p><p>https://www.youtube.com/embed/0M1Mr2Jn2CI</p><p>https://brainywoodindia.com/franchise-thankyou</p><p>STEP 2: Schedule a call as per your availability</p><p>https://brainywoodindia.com/franchise-thankyou</p>'));
				$this->helper->sendEmail('<EMAIL>', 'BrainyWood: Request for Franchise', $data = array('userName' => 'Team', 'message' => '<p>Request for Franchise details as below: </p><p>Name: ' . $name . '</p><p>Email: ' . $email . '</p><p>Phone: ' . $phone . '</p><p>Profession: ' . $profession . '</p>'));
	*/
				
				//\Session::flash('success', 'Your Request Sent Successfully, Team will contact you as soon as.');
				//return back();
				return redirect()->route('franchiseLeadThank');
			} else {
				\Session::flash('error', 'Wrong Paramenter Passed!');
				return back();
			}
		}
	}

	public function franchiseTwo($user_code=NULL)
	{
		$pagename = "Franchise Landing Page";
		$relates = TestimonialRelate::whereIn("id",[8,2,4])->orderBy("id", "DESC")->get();
		$testimonials = Testimonial::where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->limit(15)->get();
		$classes = StudentClass::where("status", 1)->where("deleted", 0)->orderBy("id", "ASC")->get();
		if($user_code!=NULL){
			$refer_code = base64_decode($user_code);
			$franchise = Franchise::where("refer_code", $refer_code)->first();
			if(!empty($franchise)){
				$franchise_user_id = $franchise->user_id;
			}else{
				$franchise_user_id = 1;
			}
		}else{
			$franchise_user_id = 1;
		}

		return view('front.franchisetwo', compact('pagename','relates','testimonials','classes','franchise_user_id'));
	}
	public function franchiseTwoLead(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'name' => 'required|regex:/^[\pL\s\-]+$/u',
			'phoneno' => 'required|numeric',
			'email' => 'required|email',
			'city' => 'required',
			'class_name' => 'required',
			'user_gender' => 'required',
			'g-recaptcha-response' => 'required|captcha',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			//dd($request->all());
			$franchiseUserId = $request->franchise_user_id;
			$name 			= $request->name;
			$city 			= $request->city;
			$email			= $request->email;
			$phone 			= $request->phoneno;
			$class_name		= $request->class_name;
			$user_gender	= $request->user_gender;
			$cities = City::where("city", $city)->first();
			$state_id = $cities->state_id;
			$states = State::where("id", $state_id)->first();
			$state_name = $states->state;
			if (!empty($email) && !empty($phone)) {
				$leadFrom = '';
				if ($franchiseUserId!=1) {
					$franchise = Franchise::where("user_id", $franchiseUserId)->first();
					$franchiseId = $franchise->user_id;
					$franchiseName = $franchise->user_name;
					$franchiseCode = $franchise->user_code;
					$leadFrom = '<p>franchiseId: '.$franchiseId.' franchiseName: '.$franchiseName.' franchiseCode: '.$franchiseCode.'</p><br>';

					$ch = curl_init();
					$url = config('constant.FMSLEADAPIURL');
					curl_setopt($ch, CURLOPT_URL,$url);
					curl_setopt($ch, CURLOPT_POST, true);
					curl_setopt($ch, CURLOPT_POSTFIELDS, "user_id=$franchiseUserId&full_name=$name&email_address=$email&mobile_number=$phone&class_name=$class_name&user_gender=$user_gender&city_name=$city&state_name=$state_name");
					curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
					$output = curl_exec ($ch);

					curl_close ($ch);

					$response = json_decode($output);

					//echo '<pre />'; print_r($response); die; // Show output
				}
				if (isset($response->Status) && $response->Status==true) {
				
/*
//ak
					$this->helper->sendEmail($email, 'BrainyWood: Request for Franchise', $data = array('userName' => $name, 'message' => '<p>Thank you for connecting at BrainyWood,</p><p>Your Request Sent Successfully, Team will contact you as soon as possible.</p>'));
					//'<EMAIL>'
					$this->helper->sendEmail('<EMAIL>', 'BrainyWood: Request for Franchise', $data = array('userName' => 'Team', 'message' => $leadFrom . '<p>Request for Franchise details as below: </p><p>Name: ' . $name . '</p><p>Email: ' . $email . '</p><p>Phone: ' . $phone . '</p><p>City: ' . $city . '</p><p>Class: ' . $class_name . '</p>'));
*/
					
					//\Session::flash('success', 'Your Request Sent Successfully, Team will contact you as soon as.');
					//return back();
					return redirect()->route('franchiseLeadThank');
				} else {
					\Session::flash('error', 'Your Request not Sent, Please try again!');
					return back();
				}
			} else {
				\Session::flash('error', 'Wrong Paramenter Passed!');
				return back();
			}
		}
	}

	public function franchiseThree()
	{
		$pagename = "Franchise Landing Page";
		$relates = TestimonialRelate::whereIn("id",[8,2,4])->orderBy("id", "DESC")->get();
		$testimonials = Testimonial::where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->limit(15)->get();

		return view('front.franchisethree', compact('pagename','relates','testimonials'));
	}
	public function franchiseThreeLead(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'name' => 'required|regex:/^[\pL\s\-]+$/u',
			'phoneno' => 'required|numeric',
			'email' => 'required|email',
			'g-recaptcha-response' => 'required|captcha',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$name 		= $request->name;
			$email		= $request->email;
			$phone 		= $request->phoneno;
			$profession	= $request->profession;
			if (!empty($email) && !empty($phone)) {
				
				/*
				//ak
				$this->helper->sendEmail($email, 'BrainyWood: Request for Franchise', $data = array('userName' => $name, 'message' => '<p>Thank you for connecting at BrainyWood,</p><p>Your Request Sent Successfully, Team will contact you as soon as possible.</p><p>STEP 1: Watch This Video How BrainyWood can help you grow your Income while helping students learn effectively</p><p>https://www.youtube.com/embed/0M1Mr2Jn2CI</p><p>https://brainywoodindia.com/franchise-thankyou</p><p>STEP 2: Schedule a call as per your availability</p><p>https://brainywoodindia.com/franchise-thankyou</p>'));
				$this->helper->sendEmail('<EMAIL>', 'BrainyWood: Request for Franchise', $data = array('userName' => 'Team', 'message' => '<p>Request for Franchise details as below: </p><p>Name: ' . $name . '</p><p>Email: ' . $email . '</p><p>Phone: ' . $phone . '</p><p>Profession: ' . $profession . '</p>'));
				*/
				
				//\Session::flash('success', 'Your Request Sent Successfully, Team will contact you as soon as.');
				//return back();
				return redirect()->route('franchiseLeadThank');
			} else {
				\Session::flash('error', 'Wrong Paramenter Passed!');
				return back();
			}
		}
	}

	public function franchiseFour()
	{
		$pagename = "Franchise Landing Page";
		$relates = TestimonialRelate::whereIn("id",[8,2,4])->orderBy("id", "DESC")->get();

		return view('front.franchisefour', compact('pagename','relates'));
	}

	public function franchiseFive($user_code=NULL)
	{
		$pagename = "Franchise B2C Landing Page";
		$relates = DB::table('testimonial_relates')->whereIn('id',[8,2,4])->orderBy('id', 'DESC')->get();
		$testimonials = Testimonial::where("status", 1)->where('deleted', 0)->orderBy('id', 'DESC')->limit(15)->get();
		$cities = DB::table('cities')->orderBy('city', 'ASC')->get();
		$classes = StudentClass::where('status', 1)->where('deleted', 0)->orderBy('id', 'ASC')->get();
		$referral_code = '';
		if($user_code!=NULL){
			//echo $user_code = base64_encode($user_code); die;
			$refer_code = base64_decode($user_code);
			$franchise = Franchise::where("refer_code", $refer_code)->first();
			if(!empty($franchise)){
				//$franchise_user_id = $franchise->user_id;
				$referral_code = $franchise->refer_code;
			}
		}
		if ($referral_code == '') {
			\Session::flash('error', 'Referral code not found!');
			redirect()->route('home');
		}

		return view('front.franchiseb2c', compact('pagename','relates','testimonials','cities','classes','referral_code'));
	}

	public function franchiseLeadThank()
	{
		$pagename = "Franchise Thank You";
		$relates = TestimonialRelate::whereIn("id",[8,2,4])->orderBy("id", "DESC")->get();

		return view('front.franchise_lead_thank', compact('pagename','relates'));
	}

	public function blogs(Request $request)
	{
		$pagename = "Blog";
		$search   = $request->search;
		$blogs  = Blog::where("status", 1)->where("deleted", 0);
		if (!empty($blogs)) {
			$blogs = $blogs->where("title", "like", "%" . $search . "%");
		}
		$blogs = $blogs->orderBy("id", "DESC")->get();

		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
		}else{
			$userId = $user->id;
		}
		$userSubscription = $this->helper->userSubscription($userId);
		
		return view('front.blogs', compact('pagename','search','blogs','userSubscription'));
	}

	public function blogDetails($slug_url)
	{
		$pagename = "Blog Details";
		$blog = Blog::where("slug_url", $slug_url)->where("status", 1)->where("deleted", 0)->first();
		if (empty($blog)) {
			\Session::flash('error', 'Blog not available.');
			return redirect()->route('blogs');
		}
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
		}else{
			$userId = $user->id;
		}
		$userSubscription = $this->helper->userSubscription($userId);
		
		return view('front.blog_details', compact('pagename','blog','userSubscription'));
	}

	public function staticPage($slug_url)
	{
		$pagename = "Page";
		$page = Page::where("slug_url", $slug_url)->where("status", 1)->where("deleted", 0)->first();
		if (empty($page)) {
			return redirect()->route('home');
		}
		
		return view('front.staticpage', compact('pagename','page'));
	}

	public static function get_excerpt($content, $length = 30, $more = '...' ) {
		$excerpt = strip_tags( trim( $content ) );
		$words = str_word_count( $excerpt, 2 );
		if ( count( $words ) > $length ) {
			$words = array_slice( $words, 0, $length, true );
			end( $words );
			$position = key( $words ) + strlen( current( $words ) );
			$excerpt = substr( $excerpt, 0, $position ) . $more;
		}
		return $excerpt;
	}

	/*public function generatePDF()
	{
		$username = 'Rajendra Kataria';
		$coursename = 'Brain Science Level 1';
		$date = '02 March, 2022';
		$data = ['username' => $username, 'coursename' => $coursename, 'date' => $date];
		$pdf = PDF::loadView('front.myPDF', $data)->setPaper('A4', 'landscape');
		$pdf->getDomPDF()->setHttpContext(
			stream_context_create([
				'ssl' => [
					'allow_self_signed'=> TRUE,
					'verify_peer' => FALSE,
					'verify_peer_name' => FALSE,
				]
			])
		);

		return $pdf->stream();
  
		//return $pdf->download('certificate.pdf');
		$destinationPath = public_path().'/upload/generatedPDF/';
		//return $pdf->save($destinationPath.'my_stored_file.pdf')->stream('download.pdf');
	}*/

	public function generatePDF($examId, $username, $coursename, $date)
	{
		$date = date('d/m/Y',strtotime($date));
		$data = ['username' => $username, 'coursename' => $coursename, 'date' => $date];
		$pdf = PDF::loadView('front.myPDF', $data)->setPaper('A4', 'landscape');
		$pdf->getDomPDF()->setHttpContext(
			stream_context_create([
				'ssl' => [
					'allow_self_signed'=> TRUE,
					'verify_peer' => FALSE,
					'verify_peer_name' => FALSE,
				]
			])
		);

		//return $pdf->stream();
  
		//return $pdf->download('certificate.pdf');
		$destinationPath = public_path().'/upload/generatedPDF/';
		$filename = $examId.'_'.time().'.pdf';
		if($pdf->save($destinationPath.$filename)->stream('download.pdf')){
			$update = StudentExam::where("id", $examId)->update(array("certificate" => $filename));
			return $filename;
		} else {
			return 0;
		}
	}

	public function removeMsgSession(Request $request)
	{
		if ($request->msg=='ok') {
			if(\Session::has('success')){
				\Session::forget('success');
			}
		}
		echo 1;
	}

	public function webClosed(Request $request)
	{
		$user = Auth::user();
		if(isset($user->role_id) && $user->role_id != 3 || empty($user)){
			$userId = 0;
		}else{
			$userId = $user->id;
		}
		if ($request->msg=='ok') {
			if ($userId > 0) {
				$deviceType = 'website';
				$trackData = UserTracking::where('user_id',$userId)->where('device_type',$deviceType)->whereNull('out_time')->orderBy('id','DESC')->first();
				if (!empty($trackData)) {
					$data = array(
						'out_time' 		=> date('Y-m-d H:i:s'),
						'updated_at'	=> date('Y-m-d H:i:s'),
					);
					$update = UserTracking::where('id',$trackData->id)->update($data);
				}
				$message = "Tracking Time Closed Successfully.";
				echo 1;
			}
		}
	}

	public function pi()
	{
		echo phpinfo();
	}
	//cron job
	public function sendLiveClassSecondNotification()
	{
		error_reporting(E_ALL);
		ini_set('max_execution_time', 0);

		/*Notification Send 45 minutes Before Start Live Class*/
		$from = date("Y-m-d H:i:s");
		//$to = date("Y-m-d H:i:s", strtotime("+1 hours"));
		$to = date("Y-m-d H:i:s", strtotime("+45 minutes"));
		$liveClasses = LiveClass::whereBetween("class_time",[$from, $to])->where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->get();
		foreach($liveClasses as $liveClass){
			$liveClassNotify = LiveclassNotify::where("class_id",$liveClass->id)->where("status",0)->get();
			foreach($liveClassNotify as $classNotify){
				$userId = $classNotify->user_id;
				$user = User::where("id", $userId)->first();
				$token = isset($user->deviceToken) ? $user->deviceToken : '';
				$liveCls = LiveClass::with('subject_data')->where("id",$classNotify->class_id)->where("status", 1)->where("deleted", 0)->first();
				$subject_data = ($liveCls->subject_id>0) ? $liveCls->title.' '.$liveCls->subject_data->title : $liveCls->title;
				$class_time = $liveCls->class_time;
				$to_time = strtotime($class_time);
				$from_time = strtotime($from);
				echo $minutes = round(abs($to_time - $from_time) / 60). " minute"; //die;
				if(strtotime($liveCls->class_time) > strtotime($from)){
					if ($token!='') {
						$title = 'BrainyWood';
						$message = 'Please check your Live class.';
						$click_action = 'Liveclass';
						$module_id = $liveCls->id;
					//ak	$this->helper->notificationsend($token, $title, $message, $click_action, $module_id);
					}
					
					//ak $this->helper->smsWithTemplate($user->phone, 'live_class_starting', $user->name, $subject_data, $class_time, $minutes);
					//ak $this->helper->sendEmail($user->email, 'BrainyWood: Live Class Notification', $data = array('userName' => $user->name, 'message' => '<p>Your live class of '.$subject_data.' at '.$class_time.' will start in '.$minutes.'. Please join timely.</p><p>Team Brainywood</p>'));
				}
			}
		}
	}
	public function sendLiveClassThirdNotification()
	{
		error_reporting(E_ALL);
		ini_set('max_execution_time', 0);

		/*Notification Send 15 minutes Before Start Live Class*/
		$from = date("Y-m-d H:i:s");
		//$to = date("Y-m-d H:i:s", strtotime("+1 hours"));
		$to = date("Y-m-d H:i:s", strtotime("+15 minutes"));
		$liveClasses = LiveClass::whereBetween("class_time",[$from, $to])->where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->get();
		foreach($liveClasses as $liveClass){
			$liveClassNotify = LiveclassNotify::where("class_id",$liveClass->id)->where("status",0)->get();
			foreach($liveClassNotify as $classNotify){
				$userId = $classNotify->user_id;
				$user = User::where("id", $userId)->first();
				$token = isset($user->deviceToken) ? $user->deviceToken : '';
				$liveCls = LiveClass::with('subject_data')->where("id",$classNotify->class_id)->where("status", 1)->where("deleted", 0)->first();
				$subject_data = ($liveCls->subject_id>0) ? $liveCls->title.' '.$liveCls->subject_data->title : $liveCls->title;
				$class_time = $liveCls->class_time;
				$to_time = strtotime($class_time);
				$from_time = strtotime($from);
				echo $minutes = round(abs($to_time - $from_time) / 60). " minute"; //die;
				if(strtotime($liveCls->class_time) > strtotime($from)){
					if ($token!='') {
						$title = 'BrainyWood';
						$message = 'Please check your Live class.';
						$click_action = 'Liveclass';
						$module_id = $liveCls->id;
					//ak	$this->helper->notificationsend($token, $title, $message, $click_action, $module_id);
					}
				//ak	$this->helper->smsWithTemplate($user->phone, 'live_class_starting', $user->name, $subject_data, $class_time, $minutes);
					//ak $this->helper->sendEmail($user->email, 'BrainyWood: Live Class Notification', $data = array('userName' => $user->name, 'message' => '<p>Your live class of '.$subject_data.' at '.$class_time.' will start in '.$minutes.'. Please join timely.</p><p>Team Brainywood</p>'));
				}
			}
		}
	}
	public function sendLiveClassFourthNotification()
	{
		error_reporting(E_ALL);
		ini_set('max_execution_time', 0);

		/*Notification Send Same Time Start Live Class*/
		$from = date("Y-m-d H:i:s");
		//echo $to = date("Y-m-d H:i:s", strtotime("+15 minutes")); die;
		$liveClasses = LiveClass::where("class_time", $from)->where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->get();
		foreach($liveClasses as $liveClass){
			$liveClassNotify = LiveclassNotify::where("class_id",$liveClass->id)->where("status",0)->get();
			foreach($liveClassNotify as $classNotify){
				$userId = $classNotify->user_id;
				$user = User::where("id", $userId)->first();
				$token = isset($user->deviceToken) ? $user->deviceToken : '';
				$liveCls = LiveClass::with('subject_data')->where("id",$classNotify->class_id)->where("status", 1)->where("deleted", 0)->first();
				$subject_data = ($liveCls->subject_id>0) ? $liveCls->title.' '.$liveCls->subject_data->title : $liveCls->title;
				$class_time = $liveCls->class_time;
				$to_time = strtotime($class_time);
				$from_time = strtotime($from);
				echo $minutes = round(abs($to_time - $from_time) / 60). " minute"; //die;
				//if(strtotime($liveCls->class_time) > strtotime($from)){
					if ($token!='') {
						$title = 'BrainyWood';
						$message = 'Please check your Live class.';
						$click_action = 'Liveclass';
						$module_id = $liveCls->id;
					//ak	$this->helper->notificationsend($token, $title, $message, $click_action, $module_id);
					}
				//ak	$this->helper->smsWithTemplate($user->phone, 'LiveClassStarted', $user->name, $subject_data);
				//ak	$this->helper->sendEmail($user->email, 'BrainyWood: Live Class Started Notification', $data = array('userName' => $user->name, 'message' => '<p>Your live class of '.$subject_data.' has been started. Please join the class.</p><p>Team Brainywood</p>'));
				//}
			}
		}

		//Inactive LiveClass after end
		$now = date('Y-m-d H:i:s');
		$pastClasses = LiveClass::where("status", 1)->where("deleted", 0)->where("end_time", "<", $now)->whereDate("end_time", "=", date('Y-m-d'))->orderBy("end_time", "DESC")->get();
		foreach($pastClasses as $pastCls){
			LiveClass::where("id", $pastCls->id)->update(["status" => 2]);
		}
	}
	public function convertVideoOneByOne()
	{
		error_reporting(E_ALL);
		ini_set('max_execution_time', 0);

		/*Live Class Last Notification Send*/
		$from = date("Y-m-d H:i:s", strtotime("-10 minutes"));
		$to = date("Y-m-d H:i:s");
		$liveClasses = LiveClass::whereBetween("class_time",[$from, $to])->where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->get();
		foreach($liveClasses as $liveClass){
			$liveClassNotify = LiveclassNotify::where("class_id",$liveClass->id)->where("status",0)->get();
			foreach($liveClassNotify as $classNotify){
				$userId = $classNotify->user_id;
				$user = User::where("id", $userId)->first();
				$token = isset($user->deviceToken) ? $user->deviceToken : '';
				$liveCls = LiveClass::with('subject_data')->where("id",$classNotify->class_id)->where("status", 1)->where("deleted", 0)->first();
				$subject_data = ($liveCls->subject_id>0) ? $liveCls->title.' '.$liveCls->subject_data->title : $liveCls->title;
				if(strtotime($liveCls->class_time) < strtotime($from)){
					if ($token!='') {
						$title = 'BrainyWood';
						$message = 'Please check your Live class.';
						$click_action = 'Liveclass';
						$module_id = $liveCls->id;
					//ak	$this->helper->notificationsend($token, $title, $message, $click_action, $module_id);
					}
				//ak	$this->helper->smsWithTemplate($user->phone, 'LiveClassStarted', $user->name, $subject_data);
				//ak	$this->helper->sendEmail($user->email, 'BrainyWood: Live Class Started Notification', $data = array('userName' => $user->name, 'message' => '<p>Your live class of '.$subject_data.' has been started. Please join the class.</p><p>Team Brainywood</p>'));
					$liveClsNotify = LiveclassNotify::findOrFail($classNotify->id);
					$liveClsNotify->status=1;
					$liveClsNotify->update();
				}
			}
		}


		/*Video Convert Start*/

		// $localurl = "D:/xampp/htdocs/fmpag/ffmpeg/bin/ffmpeg.exe";
		$localurl = "/usr/bin/ffmpeg";

		//Topic Videos
		//$topic = Chapter::where("fullvideo","!=","")->where("status", 1)->where("deleted", 0)->orWhere("video_1","")->orWhere("video_2","")->orWhere("video_3","")->orderBy("id", "DESC")->first();
		$topics = DB::select("SELECT * FROM `lession_chapters` WHERE (video_1 is null or video_2 is null or video_3 is null) and fullvideo !='' and `status` = 1 and `deleted` = 0 ORDER BY `id` desc limit 1");
		//echo '<pre />'; print_r($topic); die;
		if (!empty($topics)) {
			foreach ($topics as $topic) {
				$topic1 = Chapter::where("processtatus",1)->orderBy("id", "DESC")->first();
				$starttime = @$topic1->starttime;
				if (!empty($starttime)) {
					//echo $videolist1[0]['starttime']."=".date("Y-m-d H:i:s");
					$hourdiff = round((strtotime(date("Y-m-d H:i:s")) - strtotime($starttime)) / 3600, 1);
					echo $hourdiff;
					if ($hourdiff > 2) {
						//@mail("<EMAIL>", "BrainyWood Topic Video Stuck", "video Id" . $topic1->id);
						$update = Chapter::where("id",$topic1->id)->update(array('endtime' => date("Y-m-d H:i:s"), 'processtatus' => 3));
					}

					echo "underprocess";
					exit;
				}

				$foldername = public_path().'/lessions/';
				
				$orgurl = $foldername . $topic->fullvideo;
				
				if (empty($topic->video_1)) {
					//echo "vbmxbglxdjb"; die;
					$name1 = $topic->id . "_240.mp4";
					$name2 = $topic->id . '_topic_' . time() . "_240.mp4";

					//$name1="1585034850720.mp4"; 
					//$videoname = "topic/" . $foldername . "/" . "video/" . $name1;
					$videourl = "temp/" .  $name1;
					$videoname = $foldername . $name2;
					$update = Chapter::where("id",$topic->id)->update(array('video_1' => 'NA', 'starttime' => date("Y-m-d H:i:s"), 'processtatus' => 1));

					$command1 = $localurl . " -i " . $orgurl . "  -aspect 4:3 -s 320x240 " . $videoname;

					$process = exec($command1, $result);
					echo $command1;
					
					// $upload = $this->s3->digitalocean($videoname, $videourl);
					//$upload = $this->s3->s3upload($videoname, $videourl);

					$videoTemp = new VideoTemp();
					$videoTemp->courseId = $topic->courseId;
					$videoTemp->lessionId = $topic->lessionId;
					$videoTemp->topicId = $topic->id;
					$videoTemp->low_video = $name2;
					$videoTemp->low_status = 1;
					$videoTemp->save();
					//$videoTempId=$videoTemp->id;
					$update = Chapter::where("id",$topic->id)->update(array('endtime' => date("Y-m-d H:i:s"), 'processtatus' => 2));
				} elseif (empty($topic->video_2)) {
					//echo 'here '.$topic->video_1; die;
					$name1 = $topic->id . "_480.mp4";
					$name2 = $topic->id . '_topic_' . time() . "_480.mp4";

					$videourl = "temp/" .  $name1;
					$videoname = $foldername . $name2;
					$update = Chapter::where("id",$topic->id)->update(array('video_2' => 'NA', 'starttime' => date("Y-m-d H:i:s"), 'processtatus' => 1));

					$command2 = $localurl . " -i " . $orgurl . "  -aspect 4:3 -s 480x360 " . $videoname;

					$process = exec($command2, $result);
					echo $command2;
					
					// $upload = $this->s3->digitalocean($videoname, $videourl);
					//$upload = $this->s3->s3upload($videoname, $videourl);

					$videoTemp = new VideoTemp();
					$videoTemp->courseId = $topic->courseId;
					$videoTemp->lessionId = $topic->lessionId;
					$videoTemp->topicId = $topic->id;
					$videoTemp->med_video = $name2;
					$videoTemp->med_status = 1;
					$videoTemp->save();
					//$videoTempId=$videoTemp->id;
					$update = Chapter::where("id",$topic->id)->update(array('endtime' => date("Y-m-d H:i:s"), 'processtatus' => 2));
				} else {
					if (empty($topic->video_3)) {
						$name1 = $topic->id . "_720.mp4";
						$name2 = $topic->id . '_topic_' . time() . "_720.mp4";

						$videourl = "temp/" .  $name1;
						$videoname = $foldername . $name2;
						$update = Chapter::where("id",$topic->id)->update(array('video_3' => 'NA', 'starttime' => date("Y-m-d H:i:s"), 'processtatus' => 1));

						$command3 = $localurl . " -i " . $orgurl . "  -aspect 16:9 -s 960x540 " . $videoname;

						$process = exec($command3, $result);
						echo $command3;
						
						// $upload = $this->s3->digitalocean($videoname, $videourl);
						//$upload = $this->s3->s3upload($videoname, $videourl);

						$videoTemp = new VideoTemp();
						$videoTemp->courseId = $topic->courseId;
						$videoTemp->lessionId = $topic->lessionId;
						$videoTemp->topicId = $topic->id;
						$videoTemp->high_video = $name2;
						$videoTemp->high_status = 1;
						$videoTemp->save();
						//$videoTempId=$videoTemp->id;
						$update = Chapter::where("id",$topic->id)->update(array('endtime' => date("Y-m-d H:i:s"), 'processtatus' => 2));

						//unlink($videourl);
						//print_r($result);
					}
				}
			}
		} else {
			//Lession Videos
			//$lession = Lession::where("fullvideo","!=","")->where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->orWhere("video_1","")->orWhere("video_2","")->orWhere("video_3","")->first();
			$lessions = DB::select("SELECT * FROM `lessions` WHERE (video_1 is null or video_2 is null or video_3 is null) and fullvideo !='' and `status` = 1 and `deleted` = 0 ORDER BY `id` desc limit 1");
			if (!empty($lessions)) {
				foreach ($lessions as $lession) {
					$lession1 = Lession::where("processtatus",1)->orderBy("id", "DESC")->first();
					$starttime = @$lession1->starttime;
					if (!empty($starttime)) {
						//echo $videolist1[0]['starttime']."=".date("Y-m-d H:i:s");
						$hourdiff = round((strtotime(date("Y-m-d H:i:s")) - strtotime($starttime)) / 3600, 1);
						echo $hourdiff;
						if ($hourdiff > 2) {
							//@mail("<EMAIL>", "BrainyWood Lession Video Stuck", "video Id" . $lession1->id);
							$update = Lession::where("id",$lession1->id)->update(array('endtime' => date("Y-m-d H:i:s"), 'processtatus' => 3));
						}

						echo "underprocess";
						exit;
					}

					$foldername = public_path().'/lessions/';
					
					$orgurl = $foldername . $lession->fullvideo;
					
					if (empty($lession->video_1)) {
						//echo "vbmxbglxdjb"; die;
						$name1 = $lession->id . "_240.mp4";
						$name2 = $lession->id . '_lession_' . time() . "_240.mp4";

						$videourl = "temp/" .  $name1;
						$videoname = $foldername . $name2;
						$update = Lession::where("id",$lession->id)->update(array('video_1' => 'NA', 'starttime' => date("Y-m-d H:i:s"), 'processtatus' => 1));

						$command1 = $localurl . " -i " . $orgurl . "  -aspect 4:3 -s 320x240 " . $videoname;

						$process = exec($command1, $result);
						echo $command1;
						
						// $upload = $this->s3->digitalocean($videoname, $videourl);
						//$upload = $this->s3->s3upload($videoname, $videourl);

						$videoTemp = new VideoTemp();
						$videoTemp->courseId = $lession->courseId;
						$videoTemp->lessionId = $lession->id;
						$videoTemp->topicId = 0;
						$videoTemp->low_video = $name2;
						$videoTemp->low_status = 1;
						$videoTemp->save();
						//$videoTempId=$videoTemp->id;
						$update = Lession::where("id",$lession->id)->update(array('endtime' => date("Y-m-d H:i:s"), 'processtatus' => 2));
					} elseif (empty($lession->video_2)) {
						//echo 'here '.$lession->video_1; die;
						$name1 = $lession->id . "_480.mp4";
						$name2 = $lession->id . '_lession_' . time() . "_480.mp4";

						$videourl = "temp/" .  $name1;
						$videoname = $foldername . $name2;
						$update = Lession::where("id",$lession->id)->update(array('video_2' => 'NA', 'starttime' => date("Y-m-d H:i:s"), 'processtatus' => 1));

						$command2 = $localurl . " -i " . $orgurl . "  -aspect 4:3 -s 480x360 " . $videoname;

						$process = exec($command2, $result);
						echo $command2;
						
						// $upload = $this->s3->digitalocean($videoname, $videourl);
						//$upload = $this->s3->s3upload($videoname, $videourl);

						$videoTemp = new VideoTemp();
						$videoTemp->courseId = $lession->courseId;
						$videoTemp->lessionId = $lession->id;
						$videoTemp->topicId = 0;
						$videoTemp->med_video = $name2;
						$videoTemp->med_status = 1;
						$videoTemp->save();
						//$videoTempId=$videoTemp->id;
						$update = Lession::where("id",$lession->id)->update(array('endtime' => date("Y-m-d H:i:s"), 'processtatus' => 2));
					} else {
						$name1 = $lession->id . "_720.mp4";
						$name2 = $lession->id . '_lession_' . time() . "_720.mp4";

						$videourl = "temp/" .  $name1;
						$videoname = $foldername . $name2;
						$update = Lession::where("id",$lession->id)->update(array('video_3' => 'NA', 'starttime' => date("Y-m-d H:i:s"), 'processtatus' => 1));

						$command3 = $localurl . " -i " . $orgurl . "  -aspect 16:9 -s 960x540 " . $videoname;

						$process = exec($command3, $result);
						echo $command3;
						
						// $upload = $this->s3->digitalocean($videoname, $videourl);
						//$upload = $this->s3->s3upload($videoname, $videourl);

						$videoTemp = new VideoTemp();
						$videoTemp->courseId = $lession->courseId;
						$videoTemp->lessionId = $lession->id;
						$videoTemp->topicId = 0;
						$videoTemp->high_video = $name2;
						$videoTemp->high_status = 1;
						$videoTemp->save();
						//$videoTempId=$videoTemp->id;
						$update = Lession::where("id",$lession->id)->update(array('endtime' => date("Y-m-d H:i:s"), 'processtatus' => 2));

						//unlink($videourl);
						//print_r($result);
					}
				}
			} else {
				//Course Videos
				//$course = Courses::where("video","!=","")->where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->orWhere("video_1","")->orWhere("video_2","")->orWhere("video_3","")->first();
				$courses = DB::select("SELECT * FROM `courses` WHERE (video_1 is null or video_2 is null or video_3 is null) and video !='' and `status` = 1 and `deleted` = 0 ORDER BY `id` desc limit 1");
				if (!empty($courses)) {
					foreach ($courses as $course) {
						$course1 = Courses::where("processtatus",1)->orderBy("id", "DESC")->first();
						$starttime = @$course1->starttime;
						if (!empty($starttime)) {
							//echo $videolist1[0]['starttime']."=".date("Y-m-d H:i:s");
							$hourdiff = round((strtotime(date("Y-m-d H:i:s")) - strtotime($starttime)) / 3600, 1);
							echo $hourdiff;
							if ($hourdiff > 2) {
								//@mail("<EMAIL>", "BrainyWood Course Video Stuck", "video Id" . $course1->id);
								$update = Courses::where("id",$course1->id)->update(array('endtime' => date("Y-m-d H:i:s"), 'processtatus' => 3));
							}

							echo "underprocess";
							exit;
						}

						$foldername = public_path().'/course/';
						
						$orgurl = $foldername . $course->video;
						
						if (empty($course->video_1)) {
							//echo "vbmxbglxdjb"; die;
							$name1 = $course->id . "_240.mp4";
							$name2 = $course->id . '_course_' . time() . "_240.mp4";

							$videourl = "temp/" .  $name1;
							$videoname = $foldername . $name2;
							$update = Courses::where("id",$course->id)->update(array('video_1' => 'NA', 'starttime' => date("Y-m-d H:i:s"), 'processtatus' => 1));

							$command1 = $localurl . " -i " . $orgurl . "  -aspect 4:3 -s 320x240 " . $videoname;

							$process = exec($command1, $result);
							echo $command1;
							
							// $upload = $this->s3->digitalocean($videoname, $videourl);
							//$upload = $this->s3->s3upload($videoname, $videourl);

							$videoTemp = new VideoTemp();
							$videoTemp->courseId = $course->id;
							$videoTemp->lessionId = 0;
							$videoTemp->topicId = 0;
							$videoTemp->low_video = $name2;
							$videoTemp->low_status = 1;
							$videoTemp->save();
							//$videoTempId=$videoTemp->id;
							$update = Courses::where("id",$course->id)->update(array('endtime' => date("Y-m-d H:i:s"), 'processtatus' => 2));
						} elseif (empty($course->video_2)) {
							//echo 'here '.$course->video_1; die;
							$name1 = $course->id . "_480.mp4";
							$name2 = $course->id . '_course_' . time() . "_480.mp4";

							$videourl = "temp/" .  $name1;
							$videoname = $foldername . $name2;
							$update = Courses::where("id",$course->id)->update(array('video_2' => 'NA', 'starttime' => date("Y-m-d H:i:s"), 'processtatus' => 1));

							$command2 = $localurl . " -i " . $orgurl . "  -aspect 4:3 -s 480x360 " . $videoname;

							$process = exec($command2, $result);
							echo $command2;
							
							// $upload = $this->s3->digitalocean($videoname, $videourl);
							//$upload = $this->s3->s3upload($videoname, $videourl);

							$videoTemp = new VideoTemp();
							$videoTemp->courseId = $course->id;
							$videoTemp->lessionId = 0;
							$videoTemp->topicId = 0;
							$videoTemp->med_video = $name2;
							$videoTemp->med_status = 1;
							$videoTemp->save();
							//$videoTempId=$videoTemp->id;
							$update = Courses::where("id",$course->id)->update(array('endtime' => date("Y-m-d H:i:s"), 'processtatus' => 2));
						} else {
							$name1 = $course->id . "_720.mp4";
							$name2 = $course->id . '_course_' . time() . "_720.mp4";

							$videourl = "temp/" .  $name1;
							$videoname = $foldername . $name2;
							$update = Courses::where("id",$course->id)->update(array('video_3' => 'NA', 'starttime' => date("Y-m-d H:i:s"), 'processtatus' => 1));

							$command3 = $localurl . " -i " . $orgurl . "  -aspect 16:9 -s 960x540 " . $videoname;

							$process = exec($command3, $result);
							echo $command3;
							
							// $upload = $this->s3->digitalocean($videoname, $videourl);
							//$upload = $this->s3->s3upload($videoname, $videourl);

							$videoTemp = new VideoTemp();
							$videoTemp->courseId = $course->id;
							$videoTemp->lessionId = 0;
							$videoTemp->topicId = 0;
							$videoTemp->high_video = $name2;
							$videoTemp->high_status = 1;
							$videoTemp->save();
							//$videoTempId=$videoTemp->id;
							$update = Courses::where("id",$course->id)->update(array('endtime' => date("Y-m-d H:i:s"), 'processtatus' => 2));

							//unlink($videourl);
							//print_r($result);
						}
					}
				} else {
					//Popular Videos
					//$popularVideo = Popularvideo::where("video","!=","")->where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->orWhere("video_1","")->orWhere("video_2","")->orWhere("video_3","")->first();
					$popularVideos = DB::select("SELECT * FROM `popular_videos` WHERE (video_1 is null or video_2 is null or video_3 is null) and video !='' and `status` = 1 and `deleted` = 0 ORDER BY `id` desc limit 1");
					if (!empty($popularVideos)) {
						foreach ($popularVideos as $popularVideo) {
							$popularVideo1 = Popularvideo::where("processtatus",1)->orderBy("id", "DESC")->first();
							$starttime = @$popularVideo1->starttime;
							if (!empty($starttime)) {
								//echo $videolist1[0]['starttime']."=".date("Y-m-d H:i:s");
								$hourdiff = round((strtotime(date("Y-m-d H:i:s")) - strtotime($starttime)) / 3600, 1);
								echo $hourdiff;
								if ($hourdiff > 2) {
									//@mail("<EMAIL>", "BrainyWood Popular Video Stuck", "video Id" . $popularVideo1->id);
									$update = Popularvideo::where("id",$popularVideo1->id)->update(array('endtime' => date("Y-m-d H:i:s"), 'processtatus' => 3));
								}

								echo "underprocess";
								exit;
							}

							$foldername = public_path().'/upload/popularvideos/';
							
							$orgurl = $foldername . $popularVideo->video;
							
							if (empty($popularVideo->video_1)) {
								//echo "vbmxbglxdjb"; die;
								$name1 = $popularVideo->id . "_240.mp4";
								$name2 = $popularVideo->id . '_popular_' . time() . "_240.mp4";

								$videourl = "temp/" .  $name1;
								$videoname = $foldername . $name2;
								$update = Popularvideo::where("id",$popularVideo->id)->update(array('video_1' => 'NA', 'starttime' => date("Y-m-d H:i:s"), 'processtatus' => 1));

								$command1 = $localurl . " -i " . $orgurl . "  -aspect 4:3 -s 320x240 " . $videoname;

								$process = exec($command1, $result);
								echo $command1;
								
								// $upload = $this->s3->digitalocean($videoname, $videourl);
								//$upload = $this->s3->s3upload($videoname, $videourl);

								$videoTemp = new VideoTemp();
								$videoTemp->popularvideoId = $popularVideo->id;
								$videoTemp->low_video = $name2;
								$videoTemp->low_status = 1;
								$videoTemp->save();
								//$videoTempId=$videoTemp->id;
								$update = Popularvideo::where("id",$popularVideo->id)->update(array('endtime' => date("Y-m-d H:i:s"), 'processtatus' => 2));
							} elseif (empty($popularVideo->video_2)) {
								//echo 'here '.$popularVideo->video_1; die;
								$name1 = $popularVideo->id . "_480.mp4";
								$name2 = $popularVideo->id . '_popular_' . time() . "_480.mp4";

								$videourl = "temp/" .  $name1;
								$videoname = $foldername . $name2;
								$update = Popularvideo::where("id",$popularVideo->id)->update(array('video_2' => 'NA', 'starttime' => date("Y-m-d H:i:s"), 'processtatus' => 1));

								$command2 = $localurl . " -i " . $orgurl . "  -aspect 4:3 -s 480x360 " . $videoname;

								$process = exec($command2, $result);
								echo $command2;
								
								// $upload = $this->s3->digitalocean($videoname, $videourl);
								//$upload = $this->s3->s3upload($videoname, $videourl);

								$videoTemp = new VideoTemp();
								$videoTemp->popularvideoId = $popularVideo->id;
								$videoTemp->med_video = $name2;
								$videoTemp->med_status = 1;
								$videoTemp->save();
								//$videoTempId=$videoTemp->id;
								$update = Popularvideo::where("id",$popularVideo->id)->update(array('endtime' => date("Y-m-d H:i:s"), 'processtatus' => 2));
							} else {
								$name1 = $popularVideo->id . "_720.mp4";
								$name2 = $popularVideo->id . '_popular_' . time() . "_720.mp4";

								$videourl = "temp/" .  $name1;
								$videoname = $foldername . $name2;
								$update = Popularvideo::where("id",$popularVideo->id)->update(array('video_3' => 'NA', 'starttime' => date("Y-m-d H:i:s"), 'processtatus' => 1));

								$command3 = $localurl . " -i " . $orgurl . "  -aspect 16:9 -s 960x540 " . $videoname;

								$process = exec($command3, $result);
								echo $command3;
								
								// $upload = $this->s3->digitalocean($videoname, $videourl);
								//$upload = $this->s3->s3upload($videoname, $videourl);

								$videoTemp = new VideoTemp();
								$videoTemp->popularvideoId = $popularVideo->id;
								$videoTemp->high_video = $name2;
								$videoTemp->high_status = 1;
								$videoTemp->save();
								//$videoTempId=$videoTemp->id;
								$update = Popularvideo::where("id",$popularVideo->id)->update(array('endtime' => date("Y-m-d H:i:s"), 'processtatus' => 2));

								//unlink($videourl);
								//print_r($result);
							}
						}
					} else {
						//LiveClass Videos
						//$liveClass = LiveClass::where("video","!=","")->where("status", 1)->where("deleted", 0)->orderBy("id", "DESC")->orWhere("video_1","")->orWhere("video_2","")->orWhere("video_3","")->first();
						$liveClasses = DB::select("SELECT * FROM `live_classes` WHERE (video_1 is null or video_2 is null or video_3 is null) and video !='' and `status` = 1 and `deleted` = 0 ORDER BY `id` desc limit 1");
						if (!empty($liveClasses)) {
							foreach ($liveClasses as $liveClass) {
								$liveClass1 = LiveClass::where("processtatus",1)->orderBy("id", "DESC")->first();
								$starttime = @$liveClass1->starttime;
								if (!empty($starttime)) {
									//echo $videolist1[0]['starttime']."=".date("Y-m-d H:i:s");
									$hourdiff = round((strtotime(date("Y-m-d H:i:s")) - strtotime($starttime)) / 3600, 1);
									echo $hourdiff;
									if ($hourdiff > 2) {
										//@mail("<EMAIL>", "BrainyWood Popular Video Stuck", "video Id" . $liveClass1->id);
										$update = LiveClass::where("id",$liveClass1->id)->update(array('endtime' => date("Y-m-d H:i:s"), 'processtatus' => 3));
									}

									echo "underprocess";
									exit;
								}

								$foldername = public_path().'/upload/liveclasses/';
								
								$orgurl = $foldername . $liveClass->video;
								
								if (empty($liveClass->video_1)) {
									//echo "vbmxbglxdjb"; die;
									$name1 = $liveClass->id . "_240.mp4";
									$name2 = $liveClass->id . '_live_' . time() . "_240.mp4";

									$videourl = "temp/" .  $name1;
									$videoname = $foldername . $name2;
									$update = LiveClass::where("id",$liveClass->id)->update(array('video_1' => 'NA', 'starttime' => date("Y-m-d H:i:s"), 'processtatus' => 1));

									$command1 = $localurl . " -i " . $orgurl . "  -aspect 4:3 -s 320x240 " . $videoname;

									$process = exec($command1, $result);
									echo $command1;
									
									// $upload = $this->s3->digitalocean($videoname, $videourl);
									//$upload = $this->s3->s3upload($videoname, $videourl);

									$videoTemp = new VideoTemp();
									$videoTemp->liveclassId = $liveClass->id;
									$videoTemp->low_video = $name2;
									$videoTemp->low_status = 1;
									$videoTemp->save();
									//$videoTempId=$videoTemp->id;
									$update = LiveClass::where("id",$liveClass->id)->update(array('endtime' => date("Y-m-d H:i:s"), 'processtatus' => 2));
								} elseif (empty($liveClass->video_2)) {
									//echo 'here '.$liveClass->video_1; die;
									$name1 = $liveClass->id . "_480.mp4";
									$name2 = $liveClass->id . '_live_' . time() . "_480.mp4";

									$videourl = "temp/" .  $name1;
									$videoname = $foldername . $name2;
									$update = LiveClass::where("id",$liveClass->id)->update(array('video_2' => 'NA', 'starttime' => date("Y-m-d H:i:s"), 'processtatus' => 1));

									$command2 = $localurl . " -i " . $orgurl . "  -aspect 4:3 -s 480x360 " . $videoname;

									$process = exec($command2, $result);
									echo $command2;
									
									// $upload = $this->s3->digitalocean($videoname, $videourl);
									//$upload = $this->s3->s3upload($videoname, $videourl);

									$videoTemp = new VideoTemp();
									$videoTemp->liveclassId = $liveClass->id;
									$videoTemp->med_video = $name2;
									$videoTemp->med_status = 1;
									$videoTemp->save();
									//$videoTempId=$videoTemp->id;
									$update = LiveClass::where("id",$liveClass->id)->update(array('endtime' => date("Y-m-d H:i:s"), 'processtatus' => 2));
								} else {
									$name1 = $liveClass->id . "_720.mp4";
									$name2 = $liveClass->id . '_live_' . time() . "_720.mp4";

									$videourl = "temp/" .  $name1;
									$videoname = $foldername . $name2;
									$update = LiveClass::where("id",$liveClass->id)->update(array('video_3' => 'NA', 'starttime' => date("Y-m-d H:i:s"), 'processtatus' => 1));

									$command3 = $localurl . " -i " . $orgurl . "  -aspect 16:9 -s 960x540 " . $videoname;

									$process = exec($command3, $result);
									echo $command3;
									
									// $upload = $this->s3->digitalocean($videoname, $videourl);
									//$upload = $this->s3->s3upload($videoname, $videourl);

									$videoTemp = new VideoTemp();
									$videoTemp->liveclassId = $liveClass->id;
									$videoTemp->high_video = $name2;
									$videoTemp->high_status = 1;
									$videoTemp->save();
									//$videoTempId=$videoTemp->id;
									$update = LiveClass::where("id",$liveClass->id)->update(array('endtime' => date("Y-m-d H:i:s"), 'processtatus' => 2));

									//unlink($videourl);
									//print_r($result);
								}
							}
						}
					}
				}
			}
		}
	}
	public function membershipExpiry()
	{
		error_reporting(E_ALL);
		ini_set('max_execution_time', 0);

		/*Notification Send Before 2 days Membership Expired*/
		echo $from = date("Y-m-d");
		echo $to = date("Y-m-d", strtotime("+2 days")); //die;
		$userSubscriptions = UserSubscription::whereBetween("end_date",[$from, $to])->where("paymentStatus", 1)->orderBy("id", "DESC")->get();
		foreach($userSubscriptions as $userPlan){
			$userId = $userPlan->user_id;
			$user = User::where("id", $userId)->first();
			$token = isset($user->deviceToken) ? $user->deviceToken : '';
			$subscription = Subscription::where("id", $userPlan->subscription_id)->first();
			if(strtotime($userPlan->end_date) > strtotime($from)){
				if ($token!='') {
					$title = 'BrainyWood';
					$message = 'Please purchase our new subscription plans to avoid interruption into the services.';
				//ak	$this->helper->notificationsend($token, $title, $message);
				}
			//ak	$this->helper->smsWithTemplate($user->phone, 'MembershipExpiry', $user->name, $subscription->name, $userPlan->end_date);
			//ak	$this->helper->sendEmail($user->email, 'BrainyWood: Live Class Started Notification', $data = array('userName' => $user->name, 'message' => '<p>Your brainywood subscription plan of '.$subscription->name.' will be expired on '.$userPlan->end_date.', Please purchase our new subscription plans to avoid interruption into the services.</p>'));
			}
		}
	}

	public function autoLogout()
	{
		error_reporting(E_ALL);
		ini_set('max_execution_time', 0);

		$from = date("Y-m-d")." 23:59:59";
		$date = strtotime($from);
		$date = strtotime("-1 day", $date);
		$date = strtotime("-4 minutes", $date);
		echo $last_time = date('Y-m-d H:i:s', $date);
		//echo $from = date("Y-m-d H:i:s", strtotime("-120 minutes"));
		$users = User::where("role_id",3)->where("status",1)->where("deleted",0)->whereNotNull("api_token")->whereNotNull("updated_at")->orderBy('id', 'DESC')->get();
		if (!empty($users)) {
			foreach ($users as $user) {
				$apiToken = isset($user->api_token) ? $user->api_token : '';
				$updatedAt = isset($user->updated_at) ? $user->updated_at : '';
				if ($apiToken!='') {
					if (strtotime($updatedAt) < strtotime($last_time)) {
						echo $user->id.' Last Active '.$user->updated_at.' Name '.$user->name.'<br>';
						/*User::where("id",$user->id)->update(['api_token' => NULL, 'isLogin' => 2]);
						$loginSession = LoginSession::where("user_id",$user->id)->orderBy('id', 'DESC')->first();
						if (!empty($loginSession)) {
							LoginSession::where("id",$loginSession->id)->update(['logout_time' => date('Y-m-d H:i:s')]);
						}*/
						$trackData = UserTracking::where('user_id',$user->id)->whereNull('out_time')->orderBy('id','DESC')->first();
						if (!empty($trackData)) {
							$data = array(
								'out_time' 		=> date('Y-m-d H:i:s'),
								'updated_at'	=> date('Y-m-d H:i:s'),
							);
							$update = UserTracking::where('id',$trackData->id)->update($data);
						}
					}
				}
			}
		}
	}

	public function notificationCronJob()
	{
		error_reporting(E_ALL);
		ini_set('max_execution_time', 0);

		$userIds = [];
		$notification = Notification::where("user_id",0)->where("status",1)->orderBy('id', 'DESC')->first();
		if (!empty($notification)) {
			//echo "working..";
			$sentNotifications = NotificationSend::where("notification_id",$notification->id)->get();
			if (!empty($sentNotifications)) {
				foreach ($sentNotifications as $sent) {
					$userIds[] = $sent->user_id;
				}
				$users = User::whereNotIn("id",$userIds)->where("role_id",3)->where("status",1)->where("deleted",0)->whereNotNull("deviceToken")->orderBy('id', 'DESC')->get();
				if (!empty($users)) {
					foreach ($users as $user) {
						if ($notification->status==1) {
							$allNotif = new NotificationSend();
							$allNotif->notification_id = $notification->id;
							$allNotif->user_id = $user->id;
							$allNotif->save();
							$allNotifId = $allNotif->id;

							$token = isset($user->deviceToken) ? $user->deviceToken : '';
							if ($token!='') {
								$title = 'BrainyWood';
								$this->helper->notificationsend($token, $title, $notification->message);
							}
						}
					}
				} else {
					$notification = Notification::where("id",$notification->id)->update(["status",0]);
				}
			}
		}
	}

	//Out side registeration API
	public function outerregister(Request $request)
	{
		$phone = $request->phone;
		$phonecheck = User::where("phone", $phone)->first();
		if (!empty($phonecheck)) {
			if ($phonecheck->status!=1) {
				$validator = Validator::make($request->all(), [
					'name' => 'required|regex:/^[\pL\s\-]+$/u',
					'city' => 'required',
					'password' => 'required|min:6',
					'confirm_password' => 'required|same:password|min:6',
				]);

				if ($validator->fails()) {
					//return back()->withErrors($validator)->withInput();
					$msg = $validator->messages()->first();
					return redirect()->route('home')->withErrors($msg)->withInput();
				} else {
					$name 		      = $request->input('name');
					$gender 		  = $request->input('gender');
					$password 		  = $request->input('password');
					$confirm_password = $request->input('confirm_password');
					$refer_code		  = $request->input('refer_code');
					$class_name 	  = $request->input('class_name');
					$userClass = StudentClass::where("class_name", $class_name)->first();
					$class_id = !empty($userClass) ? $userClass->id : NULL;
					$city 		 	  = $request->input('city');
					$cities = City::where("city", $city)->first();
					$state_id = $cities->state_id;
					$states = State::where("id", $state_id)->first();
					$state_name = $states->state;
					$otpnumber = rand(1111, 9999);
					$data = array(
						'name' 		=> $name,
						'password'	=> bcrypt($password),
						'userpass'	=> $password,
						'gender' 	=> $gender,
						'class_id'  => $class_id,
						'city' 		=> $city,
						'state' 	=> $state_name,
						'refer_code' => $refer_code,
						'otp_match' => $otpnumber,
						'api_token' => Str::random(60),
						'status'    => 1,
						'role_id'   => 3,
						'created_at' => date('Y-m-d H:i:s'),
					);
					$update = User::where("id", $phonecheck->id)->update($data);
					if($update){
						$request->session()->put('phone', $phone);
					}
				//ak	$this->helper->sms($phone, $otpnumber);
				//ak	$this->helper->sendEmail($phonecheck->email, 'BrainyWood: Verify OTP', $data = array('userName' => $phonecheck->name, 'message' => '<p>Thank you for connecting at BrainyWood,</p><p>You have got successfully your OTP: ' . $otpnumber . '</p>'));
				}
			}
			\Session::flash('success', 'User Registration Completed Successfully.');
			return redirect()->route('home');
		} else {
			$validator = Validator::make($request->all(), [
				'name' => 'required|regex:/^[\pL\s\-]+$/u',
				'city' => 'required',
				'email' => 'required|email|unique:users,email',
				'phone' => 'required|numeric|unique:users,phone',
				'password' => 'required|min:6',
				'confirm_password' => 'required|same:password|min:6',
			]);

			if ($validator->fails()) {
				$msg = $validator->messages()->first();
				if($msg=='The email has already been taken.'){
					$msg = 'Email id already exists. Please login directly.';
					return redirect()->route('home')->withErrors($msg)->withInput();
				}
				if($msg=='The phone has already been taken.'){
					$msg = 'Phone number already exists. Please login directly.';
					return redirect()->route('home')->withErrors($msg)->withInput();
				}
				//return redirect()->route('home')->withErrors($validator)->withInput();
				$msg = $validator->messages()->first();
				return redirect()->route('home')->withErrors($msg)->withInput();
			} else {
				$name 		      = $request->input('name');
				$email 			  = $request->input('email');
				$phone 			  = $request->input('phone');
				$gender 		  = $request->input('gender');
				$password 		  = $request->input('password');
				$confirm_password = $request->input('confirm_password');
				$refer_code		  = $request->input('refer_code');
				$class_name 	  = $request->input('class_name');
				$userClass = StudentClass::where("class_name", $class_name)->first();
				$class_id = !empty($userClass) ? $userClass->id : NULL;
				$city 		 	  = $request->input('city');
				$cities = City::where("city", $city)->first();
				$state_id = $cities->state_id;
				$states = State::where("id", $state_id)->first();
				$state_name = $states->state;
				$msg = '';
				if (!empty($name)  && !empty($email) && !empty($phone) && !empty($password)) {
					if ($password != $confirm_password) {
						\Session::flash('error', 'Password and Confirm password not matched!');
						return redirect()->route('home');
					}
					$usercheck = User::where("email", $email)->first();
					if (!empty($usercheck)) {
						\Session::flash('error', 'Email id already exists. Please login directly.');
						return redirect()->route('home');
					} else {
						$phonecheck = User::where("phone", $phone)->first();
						if (!empty($phonecheck)) {
							\Session::flash('error', 'Phone number already exists. Please login directly.');
							return redirect()->route('home');
						} else {
							$franchiseUserId = 0;
							if (!empty($refer_code)) {
								$franchise = Franchise::where("refer_code", $refer_code)->first();
								if (!empty($franchise)) {
									$franchiseUserId = $franchise->user_id;
									if ($gender=='Female'){
										$user_gender = 202;
									} elseif ($gender=='Non Binary'){
										$user_gender = 203;
									} else {
										$user_gender = 201;
									}
									$ch = curl_init();
									$url = config('constant.FMSLEADAPIURL');
									curl_setopt($ch, CURLOPT_URL,$url);
									curl_setopt($ch, CURLOPT_POST, true);
									curl_setopt($ch, CURLOPT_POSTFIELDS, "user_id=$franchiseUserId&full_name=$name&email_address=$email&mobile_number=$phone&class_name=$class_name&user_gender=$user_gender&city_name=$city&state_name=$state_name");
									curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
									$output = curl_exec ($ch);

									curl_close ($ch);

									$response = json_decode($output);
									//echo '<pre />'; print_r($response); die; // Show output
									if ($response->Status==false) {
										\Session::flash('error', $response->Message);
										return redirect()->route('home');
									}
								} else{
									\Session::flash('error', 'Refferal code not exists. Please try again!');
									return redirect()->route('home');
								}
							}

							$otp = rand(1111, 9999);
							$remember_token = Str::random(80);
							$data = array(
								'name' 		=> $name,
								'phone' 	=> $phone,
								'email' 	=> $email,
								'password'	=> bcrypt($password),
								'userpass'	=> $password,
								'gender' 	=> $gender,
								'class_id'  => $class_id,
								'city' 		=> $city,
								'state' 	=> $state_name,
								'franchise_user_id' => $franchiseUserId,
								'refer_code' => $refer_code,
								'otp_match' => $otp,
								'remember_token' => $remember_token,
								'api_token' => Str::random(60),
								'status'    => 1,
								'role_id'   => 3,
								'created_at' => date('Y-m-d H:i:s'),
							);
							$userId = User::insertGetId($data);
							if($userId){
								$request->session()->put('phone', $phone);
							}
							$msg = 'User Registration Completed Successfully.';
							$this->helper->addNotification($userId,$msg);
						//ak	$this->helper->sms($phone, $otp);
						//ak	$this->helper->sendEmail($email, 'BrainyWood: Verify your account', $data = array('userName' => $name, 'message' => '<p>Thank you for connecting at BrainyWood,</p><p>You have got successfully your OTP: ' . $otp . '</p>'));

							//Add 7 days free trial to new user
							$today	  = date('Y-m-d');
							$userSubscription = UserSubscription::where("user_id", $userId)->orderBy('id', 'DESC')->first();
							if (empty($userSubscription)) {
								$franchise = Franchise::where("refer_code", $refer_code)->where("refer_code", "!=", "")->first();
								if (!empty($franchise)) {
									$date = strtotime($today);
									if ($franchise->refer_code == 'IVR4LDY5') {
										$date = strtotime("+3 days", $date);
									} else {
										$date = strtotime("+7 days", $date);
									}
									$end_date = date('Y-m-d', $date);
									$data1 = array(
										'user_id'			=> $userId,
										'subscription_id'	=> 1,
										'start_date'		=> $today,
										'end_date'			=> $end_date,
										'mode'				=> 'FREE',
										'paymentStatus'		=> 1,
										'created_at'		=> date('Y-m-d H:i:s'),
									);
									$inserId = UserSubscription::insertGetId($data1);
								}

								//send coupon code to franchise student
								$checkCoupon = CouponCode::where('user_id', $userId)->where('condition_1', 1)->first();
								if (empty($checkCoupon)) {
									$franchise = Franchise::where("refer_code", $refer_code)->where("refer_code", "!=", "")->first();
									if (!empty($franchise)) {
										$coupon = $this->helper->getcouponcode();
										$date = strtotime($today);
										$date = strtotime("+1 month", $date);
										$coupon_end_date = date('Y-m-d', $date);
										$couponcode = new CouponCode();
										$couponcode->coupon = $coupon;
										$couponcode->discount = 25;
										$couponcode->end_date = $coupon_end_date;
										$couponcode->description = 'Franchise user auto generated coupon code.';
										$couponcode->condition_1 = 1;
										$couponcode->user_id = $userId;
										$couponcode->subscription_id = 0;
										$couponcode->no_of_users = 0;
										$couponcode->save();
										$couponcodeId = $couponcode->id;
										
										//ak $this->helper->sendEmail($email, 'BrainyWood: Coupon Code Discount', $data = array('userName' => $name, 'message' => '<p>Thank you for connecting at BrainyWood,</p><p>You have got successfully your Coupon Code: ' . $coupon . '</p><p>This Coupon code discount valid till: ' . $coupon_end_date . '</p>'));
									}
								}
							}

							\Session::flash('success', 'User Registration Completed Successfully.');
							return redirect()->route('home');
						}
					}
				} else {
					\Session::flash('error', 'Wrong Paramenter Passed!');
					return redirect()->route('home');
				}
			}
		}
	}
	


}
