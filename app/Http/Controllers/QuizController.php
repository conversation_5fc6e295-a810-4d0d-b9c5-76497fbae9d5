<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Chapter;
use App\Models\Courses;
use App\Models\Lession;
use App\Models\Quiz;
use App\Models\Quizoption;
use App\Models\Quizquestions;
use App\Models\UploadMedia;
use App\Models\StudentClass;
use AWS;


class QuizController extends Controller
{
	public function index(Request $request)
	{
		$classes = StudentClass::orderBy("id", "ASC")->get();
		$courses  = Courses::where("deleted", 0)->orderBy("sort_id", "ASC")->get();
		$lessions = Lession::where("deleted", 0)->orderBy("sort_id", "ASC")->get();
		$topics   = Chapter::where("deleted", 0)->orderBy("sort_id", "ASC")->get();

		$class_type = ($request->class_type) ? $request->class_type : '';
		$class_id 	= ($request->class_id) ? $request->class_id : '';
		$courseId = ($request->course) ? $request->course : '';
		$lessonId = ($request->lession) ? $request->lession : '';
		$topicId  = ($request->topic) ? $request->topic : '';
		$from     = ($request->from) ? $request->from : '';
		$to       = ($request->to) ? $request->to.' 23:59:59' : date('Y-m-d H:i:s');

		$data = Quiz::With("courses","lession")->orderBy("id", "DESC")
			 ->whereHas("courses", function($query) use ($class_type) {
				if (isset($class_type) && !empty($class_type)) {
					$query->where("class_type", $class_type);
				}
			 })
			 ->whereHas("courses", function($query) use ($class_id) {
				if (isset($class_id) && !empty($class_id)) {
					$query->whereRaw("find_in_set($class_id,class_ids)");
				}
			 })
			 ->Where(function($query) use ($courseId) {
				if (isset($courseId) && !empty($courseId)) {
					$query->where("courseId", $courseId);
				}
			 })
			 ->Where(function($query) use ($lessonId) {
				if (isset($lessonId) && !empty($lessonId)) {
					$query->where("lessionId", $lessonId);
				}
			 })
			 ->Where(function($query) use ($topicId) {
				if (isset($topicId) && !empty($topicId)) {
					$query->where("topicId", $topicId);
				}
			 })
			 ->Where(function($query) use ($from, $to) {
				if (isset($from) && !empty($from)) {
					$query->whereBetween("created_at", [$from, $to]);
				}
			 })
			 ->where("deleted", 0);
		$totalResult = $data->count();
		$data = $data->paginate(50);
		
		/*echo "<pre>";
		print_r($data);die;*/
		return view('admin.quiz.index', compact('classes','courses','lessions','topics','data','totalResult'));
	}

	public function create()
	{
		$courses = Courses::Where('deleted',0)->orderBy('sort_id','ASC')->get();
		return view('admin.quiz.create',compact('courses'));
	}

	public function store(Request $request)
	{
		//echo '<pre />'; print_r($request->all()); die;
		$validator = Validator::make($request->all(), [
			'name' => 'required',
			'type' => 'required',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		}else {
			$name = $request->get('name');
			$type = $request->get('type');
			$courseId = $request->get('courseId');
			$lessionId = $request->get('lessionId');
			$topicId = $request->get('topicId');
			if($lessionId=='--Select--' || $lessionId==null)
			{
				$lessionId=0;
				$topicId=0;
			}
			if($topicId=='--Select--' || $topicId==null)
			{
				$topicId=0;
			}
			if($type==0){
				$checkQuiz = Quiz::where('lessionId',$lessionId)->where('islession',0)->where('deleted',0)->first();
				if(!empty($checkQuiz)){
					\Session::flash('error', 'Quiz already exists for this lession!');
					return back();
				}
			}elseif($type==2){
				$checkQuiz = Quiz::where('topicId',$topicId)->where('islession',2)->where('deleted',0)->first();
				if(!empty($checkQuiz)){
					\Session::flash('error', 'Quiz already exists for this topic!');
					return back();
				}
			}else{
				$checkQuiz = Quiz::where('courseId',$courseId)->where('islession',1)->where('deleted',0)->first();
				if(!empty($checkQuiz)){
					\Session::flash('error', 'Quiz already exists for this course!');
					return back();
				}
			}
			if($request->get('hours')==00){
				if($request->get('minutes')==00){
					\Session::flash('error', 'Quiz minimum time should be required!');
					return back();
				}
			}
			$duration = $request->get('hours').':'.$request->get('minutes').':00';
			$guideline = $request->get('guideline');
			$passing_percent = $request->get('passing_percent');
			$question = $request->get('question');
			$option1 = $request->get('option');
			$Currectoption = $request->get('Currectoption');
			$marks = $request->get('marks');
			$solution = $request->get('solution');
			$file = $request->file('image');
			$optionfile = $request->file('option');

			$QuizId=0;
			$Quiz = new Quiz();
			$Quiz->name = $name;
			$Quiz->islession = $type;
			$Quiz->courseId = $courseId;
			$Quiz->lessionId = $lessionId;
			$Quiz->topicId = $topicId;
			$Quiz->duration = $duration;
			$Quiz->guideline = $guideline;
			$Quiz->passing_percent = $passing_percent;
			$Quiz->status = 0;
			$Quiz->save();
			$QuizId = $Quiz->id;
			
			if (!empty($question) && count($question) > 0) {
				for ($i=0; $i <count($question); $i++) {
					if($marks[$i]){
						//image exist
						if(isset($file[$i])){
							$destinationPath = public_path().'/upload/quizquestions/';
							$originalFile = $file[$i]->getClientOriginalName();
							$filename = strtotime(date('Y-m-d-H:isa')).$originalFile;
							$file[$i]->move($destinationPath, $filename);
						}else{
							$filename = NULL;
						}

						$Quizquestions = new Quizquestions();
						$Quizquestions->quizId = $QuizId;
						$Quizquestions->questions = $question[$i];
						$Quizquestions->image = $filename;
						$Quizquestions->currect_option = $Currectoption[$i][0];
						$Quizquestions->marking = ($marks[$i]) ? $marks[$i] : 0;
						$Quizquestions->solution = $solution[$i];
						$Quizquestions->save();
						$QuizQuestionId=$Quizquestions->id;

						if(isset($optionfile[$i])){
							for ($j=0; $j <count($optionfile[$i]); $j++) {
								//echo '<pre />'; print_r($optionfile[$i]); die;
								if(isset($optionfile[$i][$j])){
									$destinationPath = public_path().'/upload/quizquestions/';
									$optionoriginalFile = $optionfile[$i][$j]->getClientOriginalName();
									$optionfilename = strtotime(date('Y-m-d-H:isa')).$optionoriginalFile;
									$optionfile[$i][$j]->move($destinationPath, $optionfilename);
								}else{
									$optionfilename = NULL;
								}
								
								$Quizoption = new Quizoption();
								$Quizoption->questionId=$QuizQuestionId;
								$Quizoption->quizoption=$optionfilename;
								$Quizoption->val_type=0;
								$Quizoption->save();
							}
						}else{
							for ($j=0; $j <count($option1[$i]) ; $j++) { 
								// print_r($option1[$i]);
								if($option1[$i][$j]!=''){
									$Quizoption = new Quizoption();
									$Quizoption->questionId = $QuizQuestionId;
									$Quizoption->quizoption = $option1[$i][$j];
									$Quizoption->val_type = 1;
									$Quizoption->save();
								}
							}
						}
					}
				}
			}

			\Session::flash('msg', 'Quiz Created Successfully.');
			return redirect()->route('admin.editQuiz',$QuizId);
		}
	}

	public function show(Quiz $quizes)
	{
		//
	}

	public function edit(Quiz $quizes, $id)
	{
		$quiz    = Quiz::findOrFail($id);
		$course  = Courses::Where('deleted',0)->get();
		$lession = Lession::where('courseId',$quiz->courseId)->where('deleted',0)->get();
		$topic   = Chapter::where('courseId',$quiz->courseId)->where('lessionId',$quiz->lessionId)->where('deleted',0)->get();
		$Quizquestions = Quizquestions::where('quizId',$id)->get();
		$Response=array();
		foreach ($Quizquestions as $key => $value) {
			$Response[$key]['id']=$value->id;
			$Response[$key]['name']=$value->questions;
			$Response[$key]['image']=$value->image;
			$Response[$key]['currect_option']=$value->currect_option;
			$Response[$key]['marking']=$value->marking;
			$Response[$key]['solution']=$value->solution;
			$Quizoption = Quizoption::where('questionId',$value->id)->get();
			foreach ($Quizoption as $key1 => $value) {
				$Response[$key]['options'][$key1]['id']=$value->id;
				$Response[$key]['options'][$key1]['name']=$value->quizoption;
				$Response[$key]['options'][$key1]['val_type']=$value->val_type;
			}
		}

		return view('admin.quiz.edit',compact('quiz','course','lession','topic','Response'));
	}

	public function update(Request $request, Quiz $quizes, $id)
	{
		$validator = Validator::make($request->all(), [
			'name' => 'required',
			'type' => 'required',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			//echo "<pre>"; print_r($request->all()); die;
			$Quiz = Quiz::findOrFail($id);

			$name = $request->get('name');
			$type = $request->get('type');
			$courseId = $request->get('courseId');
			$lessionId = $request->get('lessionId');
			$topicId = $request->get('topicId');
			if($lessionId=='--Select--' || $lessionId==null)
			{
				$lessionId=0;
				$topicId=0;
			}
			if($topicId=='--Select--' || $topicId==null)
			{
				$topicId=0;
			}
			if($request->get('hours')==00){
				if($request->get('minutes')==00){
					\Session::flash('error', 'Quiz minimum time should be required!');
					return back();
				}
			}
			$duration = $request->get('hours').':'.$request->get('minutes').':00';
			$guideline = $request->get('guideline');
			$passing_percent = $request->get('passing_percent');
			$question = $request->get('question');
			$option1 = $request->get('option');
			$optionId = $request->get('optionId');
			$questionId = $request->get('questionId');
			$Currectoption = $request->get('Currectoption');
			$marks = $request->get('marks');
			$solution = $request->get('solution');
			$file = $request->file('image');
			$optionfile = $request->file('option');
			
			$QuizId = 0;
			$Quiz->name = $name;
			$Quiz->islession = $type;
			$Quiz->courseId = $courseId;
			$Quiz->lessionId = $lessionId;
			$Quiz->topicId = $topicId;
			$Quiz->duration = $duration;
			$Quiz->guideline = $guideline;
			$Quiz->passing_percent = $passing_percent;
			$Quiz->save();
			$QuizId = $id;
			
			//echo "<pre>"; print_r($request->all()); die;
			
			for ($i=0; $i <count($question); $i++) {
				if(!empty($question[$i]) && $question[$i]!='' && $marks[$i]!='' && $solution[$i]!=''){
				
					//echo '<pre />'; print_r($question[$i]);
					if(isset($questionId[$i]))
					{
						$Quizquestions=Quizquestions::find($questionId[$i]);
					}else{
						$Quizquestions=new Quizquestions();
					}

					//check question image exists
					if(isset($file[$i])){
						$destinationPath = public_path().'/upload/quizquestions/';
						$originalFile = $file[$i]->getClientOriginalName();
						$filename = strtotime(date('Y-m-d-H:isa')).$originalFile;
						$file[$i]->move($destinationPath, $filename);
					}else{
						$filename = $Quizquestions->image;
					}
				   
					$Quizquestions->quizId = $QuizId;
					$Quizquestions->questions = $question[$i];
					$Quizquestions->image = $filename;
					$Quizquestions->currect_option = isset($Currectoption[$i][0]) ? $Currectoption[$i][0] : '';
					$Quizquestions->marking = $marks[$i];
					$Quizquestions->solution = $solution[$i];
					$Quizquestions->save();
					$QuizQuestionId = $Quizquestions->id;
					
					if(isset($optionfile[$i])){
						for ($j=0; $j <count($optionfile[$i]); $j++) {
						    if (!empty($optionfile[$i][$j])) {
								$destinationPath = public_path().'/upload/quizquestions/';
								$optionoriginalFile = $optionfile[$i][$j]->getClientOriginalName();
								$optionfilename = strtotime(date('Y-m-d-H:isa')).$optionoriginalFile;
								$optionfile[$i][$j]->move($destinationPath, $optionfilename);

								if(isset($questionId[$i]) && isset($optionId[$i][$j]))
								{
									$Quizoption=Quizoption::find($optionId[$i][$j]);
								}else{
									$Quizoption=new Quizoption();
								}
								
								$Quizoption->questionId=$QuizQuestionId;
								$Quizoption->quizoption=$optionfilename;
								$Quizoption->val_type=0;
								$Quizoption->save();
							}
						}
					}else{
						if(isset($option1[$i])){
							for ($j=0; $j <count($option1[$i]); $j++) { 
							    if (!empty($option1[$i][$j])) {
									if(isset($questionId[$i]) && isset($optionId[$i][$j]))
									{
										$Quizoption=Quizoption::find($optionId[$i][$j]);
									}else{
										$Quizoption=new Quizoption();
									}

									$supported_image = array('gif', 'jpg', 'jpeg', 'png', 'webp');
									$src_file_name = $option1[$i][$j];
									$ext = strtolower(pathinfo($src_file_name, PATHINFO_EXTENSION)); // Using strtolower to overcome case sensitive
									if (in_array($ext, $supported_image)) {
										$val_type = 0;
									} else {
										$val_type = 1;
									}
							    	
							    	$Quizoption->questionId=$QuizQuestionId;
									$Quizoption->quizoption=$option1[$i][$j];
									$Quizoption->val_type=$val_type;
									$Quizoption->save();
								}
							}
						}
					}
				}
			}
			
			\Session::flash('msg', 'Quiz updated Successfully.');
			return redirect('/admin/quizs');
		}
	}

	public function addMoreQues(Request $request, $QuizId)
	{
		//echo $QuizId; die;
		//dd($request->all());
		$question = $request->get('question');
		$option1 = $request->get('option');
		$optionId = $request->get('optionId');
		$questionId = $request->get('questionId');
		$Currectoption = $request->get('Currectoption');
		$marks = $request->get('marks');
		$solution = $request->get('solution');
		$file = $request->file('image');
		$optionfile = $request->file('option');

		for ($i=0; $i <count($question); $i++) {
			if(!empty($question[$i]) && $question[$i]!='' && $marks[$i]!='' && $solution[$i]!=''){
				if(isset($questionId[$i]))
				{
					$Quizquestions = Quizquestions::find($questionId[$i]);
				}else{
					$Quizquestions = new Quizquestions();
					$quizQuestion = Quizquestions::where('quizId',$QuizId)->where('questions',$question[$i])->first();
					if(!empty($quizQuestion)) {
						$Quizquestions = Quizquestions::find($quizQuestion->id);
					}
				}

				//check question image exists
				if(isset($file[$i])){
					$destinationPath = public_path().'/upload/quizquestions/';
					$originalFile = $file[$i]->getClientOriginalName();
					$filename = strtotime(date('Y-m-d-H:isa')).$originalFile;
					$file[$i]->move($destinationPath, $filename);
				}else{
					$filename = $Quizquestions->image;
				}
			   
				$Quizquestions->quizId = $QuizId;
				$Quizquestions->questions = $question[$i];
				$Quizquestions->image = $filename;
				$Quizquestions->currect_option = isset($Currectoption[$i][0]) ? $Currectoption[$i][0] : '';
				$Quizquestions->marking = $marks[$i];
				$Quizquestions->solution = $solution[$i];
				$Quizquestions->save();
				$QuizQuestionId = $Quizquestions->id;
				
				if(isset($optionfile[$i])){
					for ($j=0; $j <count($optionfile[$i]); $j++) {
					    if (!empty($optionfile[$i][$j])) {
							$destinationPath = public_path().'/upload/quizquestions/';
							$optionoriginalFile = $optionfile[$i][$j]->getClientOriginalName();
							$optionfilename = strtotime(date('Y-m-d-H:isa')).$optionoriginalFile;
							$optionfile[$i][$j]->move($destinationPath, $optionfilename);

							if(isset($questionId[$i]) && isset($optionId[$i][$j]))
							{
								$Quizoption = Quizoption::find($optionId[$i][$j]);
							}else{
								$Quizoption = new Quizoption();
								$quizOption = Quizoption::where('questionId',$QuizQuestionId)->where('quizoption',$optionfilename)->first();
								if(!empty($quizOption)) {
									$Quizoption = Quizoption::find($quizOption->id);
								}
							}
							
							$Quizoption->questionId=$QuizQuestionId;
							$Quizoption->quizoption=$optionfilename;
							$Quizoption->val_type=0;
							$Quizoption->save();
						}
					}
				}else{
					if(isset($option1[$i])){
						for ($j=0; $j <count($option1[$i]); $j++) { 
						    if (!empty($option1[$i][$j])) {
								if(isset($questionId[$i]) && isset($optionId[$i][$j]))
								{
									$Quizoption = Quizoption::find($optionId[$i][$j]);
								}else{
									$Quizoption = new Quizoption();
									$quizOption = Quizoption::where('questionId',$QuizQuestionId)->where('quizoption',$option1[$i][$j])->first();
									if(!empty($quizOption)) {
										$Quizoption = Quizoption::find($quizOption->id);
									}
								}

								$supported_image = array('gif', 'jpg', 'jpeg', 'png', 'webp');
								$src_file_name = $option1[$i][$j];
								$ext = strtolower(pathinfo($src_file_name, PATHINFO_EXTENSION)); // Using strtolower to overcome case sensitive
								if (in_array($ext, $supported_image)) {
									$val_type = 0;
								} else {
									$val_type = 1;
								}
						    	
						    	$Quizoption->questionId=$QuizQuestionId;
								$Quizoption->quizoption=$option1[$i][$j];
								$Quizoption->val_type=$val_type;
								$Quizoption->save();
							}
						}
					}
				}
			}
		}
		echo 'Done';
		//echo $QuizQuestionId;
	}

	public function delete($id)
	{
		$user = Quiz::findOrFail($id);
		$user->deleted=1;
		$user->update();

		return redirect('/admin/quizs');
	}

	public function updateStatus($id,$status)
	{
		$user = Quiz::findOrFail($id);
		$user->status=$status;
		$user->update();

		return redirect('/admin/quizs');
	}
	
	public function deleteOption(Request $request)
	{
		$id=$request->get('id');
		$quizQues = Quizquestions::where('id',$id)->delete();
		$quizQuesOptions = Quizoption::where('questionId',$id)->delete();
		echo 1;
	}

	//Media & Import Quiz
	public function quizMedia(Request $request)
	{
		$quiz_id = ($request->quiz_id) ? $request->quiz_id : '';
		
		$quizes = Quiz::orderBy("id", "DESC")->get();
		$data = UploadMedia::orderBy("id", "DESC")
			 ->Where(function($query) use ($quiz_id) {
				if (isset($quiz_id) && !empty($quiz_id)) {
					$query->where("quiz_id", $quiz_id);
				}
			 });
		$totalResult = $data->count();
		$data = $data->paginate(20);
		
		return view('admin.quiz.quiz_media', compact('quizes','data','totalResult'));
	}
	public function quizMediaCreate()
	{
		$quizes = Quiz::orderBy("id", "DESC")->get();
		return view('admin.quiz.quiz_media_create', compact('quizes'));
	}
	public function quizMediaStore(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'quiz_id' => 'required',
			'image_files' => 'required',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$quiz_id = $request->get('quiz_id');
			$image_files = $request->file('image_files');
			$imageFilename = NULL;
			$destinationPath = public_path().'/upload/quiz_media/';
			if ($request->hasFile('image_files')) {
				foreach($image_files as $image_file) {
					if (isset($image_file)) {
						$originalFile = $image_file->getClientOriginalName();
						$imageFilename = time().'_'.$originalFile;
						$image_file->move($destinationPath, $imageFilename);

						//S3 Upload
						$s3 = AWS::createClient('s3');
						$s3->putObject(array(
						    'Bucket'     => env('AWS_BUCKET'),
						    'Key'        => "upload_media/".$imageFilename,
						    'SourceFile' => $destinationPath.$imageFilename,
						));
						$imageUrl = 'https://s3.' . env('AWS_REGION') . '.amazonaws.com/' . env('AWS_BUCKET') . '/upload_media/'. $imageFilename;
						if(!empty($image_file) && file_exists( $destinationPath.$imageFilename )) {
							unlink( $destinationPath.$imageFilename );
						}
						$end = explode('.', $imageFilename);
				    	$title = strtolower($end[0]);

						$uploadMedia = new UploadMedia();
						$uploadMedia->quiz_id = $quiz_id;
						$uploadMedia->title = $title;
						$uploadMedia->image = $imageUrl;
						$uploadMedia->save();
					}
				}

				\Session::flash('msg', 'Upload Media Added Successfully.');
				return back();
			}
		}
	}
	public function quizImportCreate()
	{
		$quizes = Quiz::orderBy("id", "DESC")->get();
		return view('admin.quiz.quiz_import_create', compact('quizes'));
	}
	public function quizImport(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'quiz_id' => 'required',
			'csv' => 'required',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		}else {
			$quizId = $request->get('quiz_id');

			if ($request->hasFile('csv')) {
				//print_r(explode('.', $_FILES['csv']['name'])); die;
				if ($_FILES['csv']['error'] == 0) {
				    $name = $_FILES['csv']['name'];
				    //$ext = strtolower(end(explode('.', $_FILES['csv']['name'])));
				    $end = explode('.', $name);
				    $ext = strtolower($end[1]);
				    $type = $_FILES['csv']['type'];
				    $tmpName = $_FILES['csv']['tmp_name'];

				    // check the file is a csv
				    if ($ext === 'csv') {
				        if (($handle = fopen($tmpName, 'r')) !== FALSE) {
				            // necessary if a large csv file
				            set_time_limit(0);
				            //echo '<pre />'; print_r(fgetcsv($handle, 1000, ',')); die;
				            $addNew = 0;
				            $row = 0;
				            $insert_data = array();
				            $headers = fgetcsv($handle, 1000, ",");
				            while(($data = fgetcsv($handle, 1000, ',')) !== FALSE) {
				                // number of fields in the csv
				                $col_count = count($data);
				           
				                /*$csv[$row]['col1'] = $data[0];
				                $csv[$row]['col2'] = $data[1];
				                $csv[$row]['col3'] = $data[2];*/
				                
				                $questionText = $data[0];
				                $questionImage = $data[1];
				                $option1Type = $data[2];
				                $option1Value = $data[3];
				                $option2Type = $data[4];
				                $option2Value = $data[5];
				                $option3Type = $data[6];
				                $option3Value = $data[7];
				                $option4Type = $data[8];
				                $option4Value = $data[9];
				                $rightOption = $data[10];
				                $marks = $data[11];
				                $solution = $data[12];
				                //echo $num .' '.$questionText .' '.$questionImage .' '.$option1Type .' '.$option1Value .' '.$option2Type .' '.$option2Value .' '.$option3Type .' '.$option3Value .' '.$option4Type .' '.$option4Value .' '.$rightOption .' '.$marks .' '.$solution .'<br>';

								$checkQuizQuestion = Quizquestions::where('quizId',$quizId)->where('questions',$questionText)->first();
								$questionId = !empty($checkQuizQuestion) ? $checkQuizQuestion->id : 0;
				                if($questionId > 0)
								{
									$quizQuestion = Quizquestions::find($questionId);
								}else{
									$addNew++;
									$quizQuestion = new Quizquestions();
								}
								$quizQuestion->quizId = $quizId;
								$quizQuestion->questions = $questionText;
								$quizQuestion->image = !empty($questionImage) ? $questionImage : NULL;
								$quizQuestion->currect_option = $rightOption;
								$quizQuestion->marking = $marks;
								$quizQuestion->solution = $solution;
								$quizQuestion->save();
								$quizQuestionId = $quizQuestion->id;

								$quizOptionDelete = Quizoption::where('questionId',$quizQuestionId)->delete();
								if(!empty($option1Value)){
									$quizOption = new Quizoption();
							    	$quizOption->questionId = $quizQuestionId;
									$quizOption->quizoption = $option1Value;
									$quizOption->val_type = ($option1Type == 1) ? 1 : 0;
									$quizOption->save();
								}
								if(!empty($option2Value)){
									$quizOption = new Quizoption();
							    	$quizOption->questionId = $quizQuestionId;
									$quizOption->quizoption = $option2Value;
									$quizOption->val_type = ($option2Type == 1) ? 1 : 0;
									$quizOption->save();
								}
								if(!empty($option3Value)){
									$quizOption = new Quizoption();
							    	$quizOption->questionId = $quizQuestionId;
									$quizOption->quizoption = $option3Value;
									$quizOption->val_type = ($option3Type == 1) ? 1 : 0;
									$quizOption->save();
								}
								if(!empty($option4Value)){
									$quizOption = new Quizoption();
							    	$quizOption->questionId = $quizQuestionId;
									$quizOption->quizoption = $option4Value;
									$quizOption->val_type = ($option4Type == 1) ? 1 : 0;
									$quizOption->save();
								}
				  
				                // inc the row
				                $row++;
				            }//die;
				            fclose($handle);
				            
				            \Session::flash('msg', $addNew.' out of '.$row.' records has been successfully imported.');
							return back();
				        }
				    } else {
				    	\Session::flash('msg', 'Please upload only csv file!');
						return back();
				    } 
				}
			}

		}
	}
	
}