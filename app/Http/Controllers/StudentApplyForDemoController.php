<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use App\Models\Ages;
use App\Http\Helper as Helper;
use App\Models\StudentApplyForDemo;
use App\Models\StudentBookingFrees;
use App\Models\User;
use App\Models\TeachingDetails;
use App\Models\TeacherAvailabiltyOfSlots;
use App\Models\TeacherAvailabiltyOfSlotsDetails;
use App\Models\StudentDemoClassFeedback;
use App\Models\StudentClass;
use App\Models\Subject;
use App\Models\Category;
use App\Models\Courses;
use App\Models\Teacherassignbyadmin;
use App\Models\Teacher_apply_for_demo;
use App\Models\Notification;
use App\Models\Enroll;
use App\Models\Plan;
use DB;
use Carbon\Carbon;
use DateTime;
use DateTimeZone;
use Symfony\Component\HttpFoundation\StreamedResponse;

use Google\Auth\Credentials\ServiceAccountCredentials;
use Google\Auth\CredentialsLoader;
use Google\Auth\OAuth2;

class StudentApplyForDemoController extends Controller
{	
	
protected $helper;

	public function __construct(Helper $helper)
	{
		$this->helper = $helper;
	}
	public function index(Request $request)
	{

		// if($request->user_name){
		// 	$user = User::where('name', 'like',  '%' . $request->user_name .'%')->pluck('id');
		// 	// dd($user);
		// 	$data = StudentApplyForDemo::where('user_id',$user);
		// 	// dd($data);
		// }else{
		// 	// dd('dd');
		// 	$data = StudentApplyForDemo::orderBy('id', 'desc');
		// }
		// if($request->type == 'unassign'){
		// 	$data = StudentApplyForDemo::whereNull('teacher_assign_id');
		// 	// dd($data);
		// }else{
		// 	$data = StudentApplyForDemo::orderBy('id', 'desc');
		// }
		// // $data = StudentApplyForDemo::orderBy('id', 'desc');
		//  $totalData = $data->count();
		// $data = $data->paginate(10);
		 // dd($data);
		$data = StudentApplyForDemo::query();

		// Filter by user_name if provided
		if($request->user_name) {
		    $userIds = User::where('name', 'like', '%' . $request->user_name . '%')->pluck('id');
		    $data->whereIn('user_id', $userIds);
		}

		// Additional filter for unassigned teacher if 'type' is 'unassign'
		if($request->type == 'unassign') {
		    $data->whereNull('teacher_assign_id')->orderBy('id', 'desc');
		} else {
		    $data->orderBy('id', 'desc');
		}

		// Get total data count
		$totalData = $data->count();

		// Paginate the results
		$data = $data->paginate(10);
		foreach($data as $datas){
			if($datas->cancel_by_teacher_status==0){
			$teacher_id = isset($datas->teacher_assign_id) ? $datas->teacher_assign_id:null;
			$t_name = User::where('id',$teacher_id)->first();
			 // dd($t_name);
			$datas->teacher_name = isset($t_name->name) ? $t_name->name:null;
			}else{
				$datas->teacher_name = null;
			}
			$currency = StudentBookingFrees::select('currency')->where('studentapplyfordemo_id',$datas->id)->first();
			$datas->currency_name = isset($currency->currency) ? $currency->currency:"";
			$datas->feedbckcount = StudentDemoClassFeedback::join('studentapplyfordemo',"studentapplyfordemo.id","=","student_demo_class_feedback.student_apply_for_demo_id")->where('student_apply_for_demo_id',$datas->id)->count();
			$datas->enroll_count = Enroll::where('student_apply_for_demo_id',$datas->id)->count();
			$plan = Plan::where('id',$datas->plan_id)->first();
			$plan_name = isset($plan->title) ? $plan->title :'';
			$datas->p_name = $plan_name;
		}
    	   // dd($data);
		return view('admin.student_apply_for_demo.index', compact('data'));
	}

	public function student_enroll_class(Request $request,$demo_id){
		$data = Enroll::where('student_apply_for_demo_id',$demo_id)->get();
		return view('admin.student_apply_for_demo.student_enroll_class', compact('data'));
	}

	public function student_demo_feedback($id){

		$data = StudentDemoClassFeedback::join('studentapplyfordemo',"studentapplyfordemo.id","=","student_demo_class_feedback.student_apply_for_demo_id")->where('student_apply_for_demo_id',$id)
		->get(['student_demo_class_feedback.*', 'studentapplyfordemo.class_id', 'studentapplyfordemo.subject_id', 'studentapplyfordemo.category_id', 'studentapplyfordemo.course_id']);
		// dd($data);

			foreach ($data as $key => $value) {
			if($value->class_id!=0)
			{
				$value->class_name	 = isset(StudentClass::findOrFail($value->class_id)->class_name) ? StudentClass::findOrFail($value->class_id)->class_name : '-';
				$value->subject_name = isset(Subject::findOrFail($value->subject_id)->title) ? Subject::findOrFail($value->subject_id)->title :'-';
			}
			else
			{
				$value->class_name="-";
				$value->subject_name="-";

			}

			if($value->category_id!=0)
			{
				$value->category_name	 = isset(Category::findOrFail($value->category_id)->name) ? Category::findOrFail($value->category_id)->name :'-';
				$value->course_name = isset(Courses::findOrFail($value->course_id)->name) ? Courses::findOrFail($value->course_id)->name :'-';
			}
			else
			{
				$value->category_name='-';
				$value->course_name='-';
			}
		}
		 // dd($data);
			return view('admin.student_apply_for_demo.student_demo_feedback', compact('data'));
	

	}
	public function edit($id){
		
		$get_StudentApplyForDemo = StudentApplyForDemo::where("id",$id)->first();
		if(!empty($get_StudentApplyForDemo)){
			$get_StudentApplyForDemo->student_name  =  isset($get_StudentApplyForDemo->getUser->name) ? $get_StudentApplyForDemo->getUser->name:'';
			if($get_StudentApplyForDemo->class_id!=0){
				$get_StudentApplyForDemo->class_name = isset($get_StudentApplyForDemo->class->class_name) ? $get_StudentApplyForDemo->class->class_name : '';
				$get_StudentApplyForDemo->subject_name = isset($get_StudentApplyForDemo->subject->title)  ?  $get_StudentApplyForDemo->subject->title : '';
				
			}
			if($get_StudentApplyForDemo->course_id!=0)
			{
				$get_StudentApplyForDemo->course_name  =  isset($get_StudentApplyForDemo->courses->name) ? $get_StudentApplyForDemo->courses->name :'';
				$get_StudentApplyForDemo->category_name  =  isset($get_StudentApplyForDemo->category_id->name) ? $get_StudentApplyForDemo->category_id->name :'';
			}
			$get_StudentApplyForDemo->parent_name  =  isset($get_StudentApplyForDemo->getUser->parent_name) ? $get_StudentApplyForDemo->getUser->parent_name:'';
			$get_StudentApplyForDemo->dial_code  =  isset($get_StudentApplyForDemo->getUser->dial_code) ? $get_StudentApplyForDemo->getUser->dial_code:'';
			$get_StudentApplyForDemo->country  =  isset($get_StudentApplyForDemo->getUser->country_flag) ? $get_StudentApplyForDemo->getUser->country_flag:'';
			$get_StudentApplyForDemo->timezone  =  isset($get_StudentApplyForDemo->getUser->timezone) ? $get_StudentApplyForDemo->getUser->timezone:'';
			$get_StudentApplyForDemo->phone  =  isset($get_StudentApplyForDemo->getUser->phone) ? $get_StudentApplyForDemo->getUser->phone:'';
			$get_StudentApplyForDemo->email  =  isset($get_StudentApplyForDemo->getUser->email) ? $get_StudentApplyForDemo->getUser->email:'';

			$currency_data = StudentBookingFrees::where('studentapplyfordemo_id',$id)->first();
			$get_StudentApplyForDemo->scurrency  =  isset($currency_data->currency) ? $currency_data->currency:'';
			$get_StudentApplyForDemo->preferred_topic  =  isset($currency_data->preferred_topic) ? $currency_data->preferred_topic:'';
			$get_StudentApplyForDemo->preferred_time  =  isset($currency_data->preferred_time) ? $currency_data->preferred_time:'';
		}

		$plan = Plan::where('id',$get_StudentApplyForDemo->plan_id)->first();
		$plan_name = isset($plan->title) ? $plan->title:'';
		// echo "<pre>";
		// print_r($get_StudentApplyForDemo);
		// die;
// dd($get_StudentApplyForDemo);
		$data=	StudentBookingFrees::where("studentapplyfordemo_id",$id);
			$data = $data->paginate(10);

	//	$data1= StudentApplyForDemo::where('reschedule_status',$get_StudentApplyForDemo->reschedule_status)->orderBy('id', 'desc')->get();
		$data1= StudentApplyForDemo::where('reschedule_status',1)->orderBy('id', 'desc')->get();

		foreach($data as $dk=>$da){
			$utc_date = $da->utc_select_date;

			$utc_select_time = $da->utc_select_time;
			$utc_select_end_time = $da->utc_select_end_time;
			date_default_timezone_set('UTC');
            $LocalTime_start_time = new DateTime($utc_select_time);
            $tz_start = new DateTimeZone('Asia/Kolkata' );
            $LocalTime_start_time->setTimezone( $tz_start );
            $start_date_time = (array) $LocalTime_start_time;
            $StartDateTime = $start_date_time['date'];
            $time_ist = date('g:i A', strtotime($StartDateTime));
                // dd($time_ist);
            $da->select_time_ist = $time_ist;


            $LocalTime_start_time1 = new DateTime($utc_select_end_time);
            $tz_start1 = new DateTimeZone('Asia/Kolkata' );
            $LocalTime_start_time1->setTimezone( $tz_start1 );
            $start_date_time1 = (array) $LocalTime_start_time1;
            $StartDateTime1 = $start_date_time1['date'];
            $time_ist1 = date('g:i A', strtotime($StartDateTime1));
            $da->select_end_time_ist = $time_ist1;
             // dd($time_ist1);


            $LocalTime_start_time2 = new DateTime($utc_date);
            $tz_start2 = new DateTimeZone('Asia/Kolkata' );
            $LocalTime_start_time2->setTimezone( $tz_start2 );
            $start_date_time2 = (array) $LocalTime_start_time2;
            $StartDateTime2 = $start_date_time2['date'];
            $date_ist2 = date('Y-m-d', strtotime($StartDateTime2));
            $da->select_utc_date = $date_ist2;
	}
		// $teacher_request=[];
		
	
		

			if(!is_null($get_StudentApplyForDemo)){
				$subject_id_arr=[];
				$class_id_arr=[];

			// foreach ($get_StudentApplyForDemo as $key => $value) 
			// {

				if($get_StudentApplyForDemo->class_id!=0)
				{
					$subject_id_arr[]=$get_StudentApplyForDemo->subject_id;
					$class_id_arr[]= $get_StudentApplyForDemo->class_id;
				}
				else if($get_StudentApplyForDemo->course_id!=0) //category and courses
				{
					$subject_id_arr[]=$get_StudentApplyForDemo->course_id;
					$class_id_arr[]=$get_StudentApplyForDemo->category_id;
				}

				$teacher_request = Teacher_apply_for_demo::where('studentapplyfordemo_id',$id)->get();
				foreach($teacher_request as $teacher_requests){
					$tname = User::where('id',$teacher_requests->teacher_id)->first();
					$teaching = TeachingDetails::where('user_id',$teacher_requests->teacher_id)->first();
					$teacher_requests->teacher_name  = isset($tname->name) ? $tname->name:"";
					$teacher_requests->t_currency  = isset($tname->currency) ? $tname->currency:"";
					$teacher_requests->fees  = isset($teaching->fees) ? $teaching->fees:"";



			$utc_date1 = $teacher_requests->select_date;
			$utc_select_time = $teacher_requests->start_time;
			$utc_select_end_time = $teacher_requests->end_time;
			date_default_timezone_set('UTC');
            $LocalTime_start_time = new DateTime($utc_select_time);
            $tz_start = new DateTimeZone('Asia/Kolkata' );
            $LocalTime_start_time->setTimezone( $tz_start );
            $start_date_time = (array) $LocalTime_start_time;
            $StartDateTime = $start_date_time['date'];
            $time_ist = date('g:i A', strtotime($StartDateTime));
            // dd($select_time_ist);
            $teacher_requests->select_time_ist = $time_ist;


            $LocalTime_start_time1 = new DateTime($utc_select_end_time);
            $tz_start1 = new DateTimeZone('Asia/Kolkata' );
            $LocalTime_start_time1->setTimezone( $tz_start1 );
            $start_date_time1 = (array) $LocalTime_start_time1;
            $StartDateTime1 = $start_date_time1['date'];
            $time_ist1 = date('g:i A', strtotime($StartDateTime1));
            $teacher_requests->select_end_time_ist = $time_ist1;


            $LocalTime_start_time2 = new DateTime($utc_date1);
            $tz_start2 = new DateTimeZone('Asia/Kolkata' );
            $LocalTime_start_time2->setTimezone( $tz_start2 );
            $start_date_time2 = (array) $LocalTime_start_time2;
            $StartDateTime2 = $start_date_time2['date'];
            $date_ist2 = date('Y-m-d', strtotime($StartDateTime2));
            $teacher_requests->select_utc_date1 = $date_ist2;
		}
				  // dd($studentfeesdata);
//			}
			// $teacher_slot_details_ids = explode(",", $get_StudentApplyForDemo->teacher_slot_details_id);// teacher apply for demo select slot

			// $subids = implode(",", $subject_id_arr);
			// $clscatids = implode(",", $class_id_arr);
			
			// if(!empty($subject_id_arr) && !empty($class_id_arr))
			// {
			// 	 $ss="SELECT *,users.name as teacher_name,teacher_availabilty_of_slots.user_id as teacher_id, teaching_details.fees,
			// 	teacher_availabilty_of_slots_details.id as teacher_availabilty_of_slots_details_id FROM `teaching_details` 

			// 	inner join users on users.id = teaching_details.user_id 
				
			// 	inner join teacher_availabilty_of_slots on teacher_availabilty_of_slots.user_id = teaching_details.user_id 
			// 	inner join teacher_availabilty_of_slots_details on teacher_availabilty_of_slots_details.teacher_availabilty_of_slots_id = teacher_availabilty_of_slots.id 
			// 	WHERE teacher_availabilty_of_slots_details.teacher_id = teaching_details.user_id 
				
			// 	and teacher_availabilty_of_slots_details.request_status=1 OR teacher_availabilty_of_slots_details.request_status=2 OR teacher_availabilty_of_slots_details.request_status=3
			// 	and teaching_details.subject_or_course_id in ($subids) 
			// 	and teaching_details.class_or_category_id in($clscatids)";
			
			// 	$get_TeachingDetails = DB::Select($ss);
				// $disabled =false; //isset($get_StudentApplyForDemo->disabled) ? $get_StudentApplyForDemo->disabled :false;
			// 	$teacher_request[] = $get_TeachingDetails;
			// }

		}

		//reschedule class
		$rescheduleclass = [];


		if($get_StudentApplyForDemo->reschedule_status!=0)
		{
			 $get_TeacherAvailabiltyOfSlotsDetails=	TeacherAvailabiltyOfSlotsDetails::findOrFail($get_StudentApplyForDemo->teacher_availabilty_of_slots_details_id);
			  $teacher_current_start_time = $get_TeacherAvailabiltyOfSlotsDetails->start_time;
	  			$teacher_current_end_time = $get_TeacherAvailabiltyOfSlotsDetails->end_time;				 
			

			$get_StudentApplyForDemo2= StudentBookingFrees::where('studentapplyfordemo_id',$get_StudentApplyForDemo->id)->where('select_time',"!=",$teacher_current_start_time)->where('select_end_time',"!=",$teacher_current_end_time)->get();
				

				if($get_StudentApplyForDemo2)
					{
						foreach($get_StudentApplyForDemo2 as $key => $value) 
						{
					 		$student_start_time= $value->select_time;
							$student_end_time= $value->select_end_time;

							$timestamp = strtotime($value->created_at);
							 $day = date('w', $timestamp);
						
							$get_TeacherAvailabiltyOfSlots = TeacherAvailabiltyOfSlotsDetails::join("teacher_availabilty_of_slots","teacher_availabilty_of_slots_details.teacher_availabilty_of_slots_id","=","teacher_availabilty_of_slots.id")
							->join('users',"teacher_availabilty_of_slots_details.teacher_id","=","users.id")
							->where("teacher_availabilty_of_slots.day",$day)
							->where("teacher_availabilty_of_slots_details.start_time",">=",$student_start_time)
							->where("teacher_availabilty_of_slots_details.end_time","<=",$student_end_time)
							->where('teacher_availabilty_of_slots_details.request_status',"!=",2)
							->where('teacher_availabilty_of_slots_details.teacher_id',"=",$get_StudentApplyForDemo->teacher_assign_id)
							->select([
								'users.name as teacher_name',
								'teacher_availabilty_of_slots_details.teacher_id',
								'teacher_availabilty_of_slots_details.start_time',
								'teacher_availabilty_of_slots_details.end_time',
								'teacher_availabilty_of_slots_details.id as teacher_availabilty_of_slots_details_id',
								'teacher_availabilty_of_slots.*'])
							->get();
							array_push($rescheduleclass, $get_TeacherAvailabiltyOfSlots);
						}

					}	
			}
			
			    // dd($teacher_request);

		return view('admin.student_apply_for_demo.View_StudentBookingFrees',compact('plan_name','data','get_StudentApplyForDemo','teacher_request','id','rescheduleclass'));	
	}

	public function admin_teacher_assign($demo_id,$class_id,$subject_id){
		$applydata = StudentApplyForDemo::where('id',$demo_id)->first();

		$type  = "";
		if($applydata->class_id !==0 && $applydata->class_id !==null){
			$type = 1; 
		}else{
		 // dd('dd');
			$type = 2;
		}
		$teachers = TeachingDetails::join('users','users.id','=','teaching_details.user_id')->select('teaching_details.*','users.name','users.currency')->where(['teaching_details.class_or_category_id'=>$class_id,'teaching_details.subject_or_course_id'=>$subject_id,'teaching_details.type'=>$type,'users.status'=>1])->get();
		// $totalData = $teachers->count();
		// $data = $teachers->paginate(10);
		
		  // dd($teachers);
		return view('admin.student_apply_for_demo.admin_teacher_assign',compact('teachers','demo_id'));	
	}

	public function getTeacherInfo(Request $request, $teacherID) {
    $teacher = User::where('id', $teacherID)->first();
    $rating = StudentDemoClassFeedback::where('teacher_id',$teacherID)->avg('rating');
    if (isset($rating)) {
    $formattedRating = number_format($rating, 2);
    $teacher->rat = $formattedRating;
	} else {
	    $teacher->rat = 0;
	}
    if (!$teacher) {
        return response()->json(['error' => 'Teacher not found'], 404);
    }

    return response()->json($teacher);
}

	public function send_techer(Request $request){
		$subject = "Assign new Demo Class";
		// $message = "demo class assign";

		$assign=[];
		$applydata = StudentApplyForDemo::where('id',$request->demo_id)->first();
		$sdata = StudentBookingFrees::where('id',$request->demo_id)->first();
         $ids = explode(',',$request->ids);
         $all_ubchecked_ids = explode(',',$request->join_unselected_values);
         $classDAta = StudentApplyForDemo::where('id',$request->demo_id)->first();
	    if($classDAta->class_id!=0){
		   $class_name= 	StudentClass::where("id",$classDAta->class_id)->first()->class_name;
		   $subject_name= Subject::where("id",$classDAta->subject_id)->first()->title;
	    }
	    if($classDAta->course_id!=0){
		   $class_name= 	Category::where("id",$classDAta->category_id)->first()->name;
		  $subject_name =	Courses::where("id",$classDAta->course_id)->first()->name;
		}
		$deviceToken=[];
         foreach($ids as $key=>$id){
         	$user = User::where('id',$id)->first();
         	if($id!=''){
         		$var = 	Teacherassignbyadmin::where(['teacher_id'=>$id,'studentapplyfordemo_id'=>$request->demo_id,'user_id'=>$applydata->user_id])->first();
         	if(!isset($var)){
         		$assign = new Teacherassignbyadmin();
		        $assign->teacher_id = $id;
		        $assign->studentapplyfordemo_id = $request->demo_id;
		        $assign->user_id = $applydata->user_id;
		        $assign->zoom_link = $user->class_url;
		        $assign->status = 1;
		        $assign->save();
         	 }
         	}
         	$teacher = User::where('id',$id)->first();
			$utcTime = isset($sdata->utc_select_time) ?$sdata->utc_select_time :'';
			$select_Date = isset($sdata->select_date) ? $sdata->select_date:'';
			// dd($utcTime);
			$dateTimeUtc = new DateTime($utcTime, new DateTimeZone('UTC'));
			$targetTimeZone = new DateTimeZone($teacher->timezone); // Replace with your desired time zone
			$dateTimeUtc->setTimezone($targetTimeZone);
			$convertedTime = $dateTimeUtc->format('H:i:s');
         	// $message = "demo class assign ".$user->name."class name ".$class_name."subject name ".$subject_name." date ".$select_Date." Class time ".$convertedTime." ";
         	// $this->sendmailtoemp($user->email,$subject,$message);
         	$country = $teacher->dial_code;
         	$phone = $teacher->phone;
         	$template_name = "new_demo_request_tutor";
         	$body_values = [];
         	$this->helper->send_whatsapp_message($country,$phone,$template_name,$body_values);
         	$deviceToken[] = $teacher->deviceToken;
         	$this->notificationsendd($deviceToken, "New Demo Request","Hi ".$teacher->name.", a new Demo request is received.");
						$notification = new Notification();
                        $notification->user_id = $applydata->user_id;
                        $notification->teacher_id = $id;
                        $notification->title = "New Demo Request";
                        $notification->message = "Hi ".$teacher->name.", a new Demo request is received.";
                        $notification->status = 2;
                        // $notification->url = "https://guru.appic.tech/"
                        $notification->save();
       
       }
       $deleteuncheckedrows = Teacherassignbyadmin::whereIn('teacher_id',$all_ubchecked_ids)->where(['studentapplyfordemo_id'=>$request->demo_id,'user_id'=>$applydata->user_id])->delete();
        $result = array('status'=>true, 'message'=>'Admin assign by Teacher','data'=>$assign);
        echo json_encode($result);
    }

	public function admin_by_teacher_request_assign(Request $Request){
		
	  $subject = "Demo is confirmed";
	  if($Request->all()){
		 $teacher_assign_id  =  $Request->teacher_id;
		 $student_apply_for_demo_id = $Request->student_apply_for_demo_id;
		 $request_status_update = $Request->request_status_update;
		 // $teacher_availabilty_of_slots_details_id = $Request->teacher_availabilty_of_slots_details_id;
		 $date = date('y-m-d');
		 $disabled= false;
		   Teacher_apply_for_demo::where("studentapplyfordemo_id",$student_apply_for_demo_id)->where('admin_assign_class_status',2)->update(['admin_assign_class_status'=>3]);

	     Teacher_apply_for_demo::where("id",$Request->teacher_demo_id)->update(['admin_assign_class_status'=>$Request->request_status,'demo_class_approve'=>'Approve']);
						 $disabled =0;
						StudentApplyForDemo::where("id",$student_apply_for_demo_id)->update(array('teacher_assign_id'=>$teacher_assign_id,'disabled'=>$disabled,
							'assign_teacher_date'=>$date,'cancel_by_teacher_status'=>0));
					if($Request->request_status==1){
						$demo = StudentApplyForDemo::where('id',$student_apply_for_demo_id)->update(['teacher_assign_id'=>NULL]);
					}
					if($Request->request_status==3){
						 $assgin_teacher = Teacher_apply_for_demo::where('studentapplyfordemo_id',$student_apply_for_demo_id)->delete();
						 $demo = StudentApplyForDemo::where('id',$student_apply_for_demo_id)->update(['teacher_assign_id'=>NULL]);
					}
				$deviceToken=[];
				$teacher = User::where('id',$teacher_assign_id)->first();
				$deviceToken[] = $teacher->deviceToken;
				$demo = StudentApplyForDemo::where('id',$student_apply_for_demo_id)->first();
				$datetime = StudentBookingFrees::where('studentapplyfordemo_id',$demo->id)->first();
				// $selectedTime = Carbon::createFromFormat('H:i:s', $datetime->select_time)->format('h:i A');
				$dateTimeUtc = new DateTime($datetime->utc_select_date, new DateTimeZone('UTC'));
				$targetTimeZone = new DateTimeZone($teacher->timezone); // Replace with your desired time zone
				$dateTimeUtc->setTimezone($targetTimeZone);
				$convertedTime = $dateTimeUtc->format('g:i A');
				$convertedDate = $dateTimeUtc->format('m/d/Y');


				$student = User::where('id',$demo->user_id)->first();

				$dateTimeUtc1 = new DateTime($datetime->utc_select_date, new DateTimeZone('UTC'));
				$targetTimeZone1 = new DateTimeZone($student->timezone); // Replace with your desired time zone
				$dateTimeUtc1->setTimezone($targetTimeZone1);
				$convertedTime1 = $dateTimeUtc1->format('g:i A');
				$convertedDate1 = $dateTimeUtc1->format('m/d/Y');

				$deviceToken[] = $student->deviceToken;
				if($demo->class_id!=0){
                    $class_name=    StudentClass::where("id",$demo->class_id)->first()->class_name;
                    $subject_name= Subject::where("id",$demo->subject_id)->first()->title;
                }
                if($demo->course_id!=0){
                    $class_name=    Category::where("id",$demo->category_id)->first()->name;
                    $subject_name = Courses::where("id",$demo->course_id)->first()->name;
                }

				// if($student->id){
				//    $country = $student->dial_code;
				//    $phone = $student->phone;
				//    $template_name = 'student_demo_teacher_assign';
				//    $body_values = [$student->parent_name, $student->name , $teacher->name , $convertedDate1 , $convertedTime1];
				//    $this->helper->send_whatsapp_message($country,$phone,$template_name,$body_values);
				// }
				if($Request->request_status == 2){
					if($teacher->id){
						$country = $teacher->dial_code;
						$phone = $teacher->phone;
						$template_name = 'tutor_demo_confirm';
						$body_values = [$teacher->name ,$student->name,$class_name, $subject_name, $convertedDate, $convertedTime];
						$this->helper->send_whatsapp_message($country,$phone,$template_name,$body_values);
					}
					
					
				// if(!empty($request_status_update)){
				// $message = "Hi sir/ma’am, the demo class for '".$student->name."' is scheduled with '".$teacher->name."', date '".$datetime->select_date."' time '".$datetime->select_time."'";
				// $message = "<!DOCTYPE html><html><head><style>body,html{margin:0;padding:0;border:0}body{font-family:Arial,sans-serif;background-color:#f0f0f0}.container{max-width:600px;margin:0auto;padding:20px;background-color:#fff;border-radius:5px;box-shadow:0 2px 5px rgba(0,0,0,.1)}.header{background-color:#375dbc;color:#fff;padding:20px;border-top-left-radius:5px;border-top-right-radius:5px}.logo{text-align:center;padding-bottom:20px;padding-top:20px}.logo img{max-width:200px}.content{padding:20px;color:#333}h2{color:#fff}p{font-size:16px;line-height:1.5}.button{display:inline-block;padding:10px 20px;background-color:#357DBC;color:#fff;text-decoration:none;border-radius:5px}.website{color:#007bff;text-decoration:none}.button:hover{background-color:#0056b3}</style></head><body><div class='container'><div class='header'><h2>Demo Class Schedule Confirmation</h2></div><div class='logo'><img src='https://guru.appicsoftwares.in/static/media/logo.ee3dc851ce5164c5bcd3.png' alt='Guru At Home Logo'></div><div class='content'><p>Dear sir/ma'am,</p><p>We are delighted to confirm the scheduled demo class for<b> ".$student->name."</b>, which will be conducted by our mentor,<b> ".$teacher->name."</b>, on<b> ".$selectedTime." </b>at<b> ".$datetime->select_time."</b></p><a class='button' style='color:#fff'; background-color='#375DBC' href='https://guru.appic.tech/student/notification'>Join Class</a><p>If you have any questions or require further assistance, please feel free to contact us at +91 86196 29658.</p><p>Best Regards,</p><p>Team<b> Guru At Home</b></p><a class='website' href='https://guruathome.org/'>www.guruathome.org</a></div></div></body></html>";
				$message = "<!doctypehtml><style>body,html{margin:0;padding:0;border:0}body{font-family:Arial,sans-serif;background-color:#f0f0f0}.container{max-width:600px;margin:0 auto;padding:20px;background-color:#fff;border-radius:5px;box-shadow:0 2px 5px rgba(0,0,0,.1)}.header{background-color:#007bff;color:#fff;padding:20px;border-top-left-radius:5px;border-top-right-radius:5px}.logo{text-align:center;padding-bottom:20px;padding-top:20px}.logo img{max-width:200px}.content{padding:20px;color:#333}h2{color:#007bff}p{font-size:16px;line-height:1.5}.button{display:inline-block;padding:10px 20px;background-color:#007bff;color:#fff;text-decoration:none;border-radius:5px}.button:hover{background-color:#0056b3}</style><div class=container><div class=logo><img alt='Guru At Home Logo'src=https://guru.appicsoftwares.in/static/media/logo.ee3dc851ce5164c5bcd3.png></div><div class=content><p>Congrats !! We have successfully booked a demo session with one of our top tutors, <strong>".$teacher->name."</strong> for <strong>".$student->name."</strong> ".$class_name.", ".$subject_name." ".$convertedDate1." ".$convertedTime1.". <br>Happy Learning 🙂</div></div>";


				// foreach ($request_status_update as $key => $value) {
				// 		 // $id = $teacher_availabilty_of_slots_details_id[$key];
				// 		 if(!is_null($value)){

				// 			 // Teacher_apply_for_demo::where("studentapplyfordemo_id",$student_apply_for_demo_id)->where('admin_assign_class_status',2)->update(['admin_assign_class_status'=>3]);
				// 			 Teacher_apply_for_demo::where("id",$Request->teacher_demo_id)->update(['admin_assign_class_status'=>$value]);
				// 			 $disabled = ($value==2) ? $value :0;
				// 			StudentApplyForDemo::where("id",$student_apply_for_demo_id)->update(array('teacher_assign_id'=>$teacher_assign_id,'disabled'=>$disabled,
				// 				'assign_teacher_date'=>$date));
				// 		}
				// 	}
				// 		\Session::flash('msg', "Request Status Update Successfully");
				// 			return redirect()->back();
				// }

							

							// $this->sendmailtoemp($student->email,$subject,$message);
				$this->helper->sendmailtoemp($student->email,$subject,$message);
							$this->notificationsendd($deviceToken,"Class assign","Congrats !! We have successfully booked a demo session with one of our top tutors, ".$teacher->name." for ".$student->name."  ".$class_name.", ".$subject_name." .Happy Learning 🙂");
							$notification = new Notification();
				                  $notification->user_id = $demo->user_id;
				                  $notification->teacher_id = $teacher_assign_id;
				                  $notification->title = "Class assign";
				                  $notification->message = "Congrats !! We have successfully booked a demo session with one of our top tutors, ".$teacher->name." for ".$student->name."  ".$class_name.", ".$subject_name." .Happy Learning";

				                  $notification->status = 1;
				                  // $notification->url = "https://guru.appic.tech/"
				                  $notification->save();

							$this->notificationsendd($deviceToken,"Class assigned","Congrats!! your demo for ".$student->name.", ".$class_name.", ".$subject_name." is confirmed for ".$convertedDate." ".$convertedTime);
							$notification = new Notification();
							$notification->user_id = $demo->user_id;
							$notification->teacher_id = $teacher_assign_id;
							$notification->title = "Class assign";
							$notification->message = "Congrats!! your demo for ".$student->name.", ".$class_name.", ".$subject_name." is confirmed for ".$convertedDate." ".$convertedTime." ";
							$notification->status = 2;
							// $notification->url = "https://guru.appic.tech/"
							$notification->save();
				}
				
						\Session::flash('msg', "Request Status Updated Successfully");
						return redirect()->back();
		}
	}
// 	public function admin_by_teacher_request_assign(Request $request) {
//     $subject = "Demo is confirmed";
//     $teacherAssignId = $request->teacher_id;
//     $studentApplyForDemoId = $request->student_apply_for_demo_id;
//     $requestStatus = $request->request_status;
//     $date = now()->format('y-m-d');

//     // Update teacher and student demo assignment status
//     Teacher_apply_for_demo::where("studentapplyfordemo_id", $studentApplyForDemoId)
//         ->where('admin_assign_class_status', 2)
//         ->update(['admin_assign_class_status' => 3]);
//     Teacher_apply_for_demo::where("id", $request->teacher_demo_id)
//         ->update(['admin_assign_class_status' => $requestStatus]);

//     // Update student demo assignment details
//     $disabled = ($requestStatus == 0) ? true : false;
//     StudentApplyForDemo::where("id", $studentApplyForDemoId)->update([
//         'teacher_assign_id' => $teacherAssignId,
//         'disabled' => $disabled,
//         'assign_teacher_date' => $date,
//         'cancel_by_teacher_status' => 0,
//     ]);

//     // Update student demo if request status is 1
//     if ($requestStatus == 1) {
//         StudentApplyForDemo::where('id', $studentApplyForDemoId)->update(['teacher_assign_id' => null]);
//     }

//     // Fetch teacher and student details
//     $teacher = User::findOrFail($teacherAssignId);
//     $demo = StudentApplyForDemo::with(['bookingFree', 'studentClass', 'subject', 'category', 'course'])
//         ->findOrFail($studentApplyForDemoId);
//     $student = User::findOrFail($demo->user_id);

//     // Prepare message and notifications
//     $classInfo = ($demo->class_id != 0) ? $demo->studentClass->class_name : $demo->category->name;
//     $subjectInfo = ($demo->class_id != 0) ? $demo->subject->title : $demo->course->name;
//     $message = "Hi sir/ma’am, the demo class for '{$student->name}' is scheduled with '{$teacher->name}', date '{$demo->bookingFree->select_date}' time '{$demo->bookingFree->select_time}'";

//     // Send email and notifications
//     $this->sendmailtoemp($teacher->email, $subject, $message);
//     $deviceTokens = [$teacher->deviceToken, $student->deviceToken];
//     $this->notificationsendd("Class assign", "Demo Class confirmed by admin", $deviceTokens);
//     $this->saveNotification($demo->user_id, $teacherAssignId, "Class assign", $message, 1);
//     $this->notificationsendd("Class assign", "Hi '{$teacher->name}', new demo request received. When demo is confirmed by admin", [$teacher->deviceToken]);
//     $this->saveNotification($demo->user_id, $teacherAssignId, "Class assign", "Hi {$teacher->name}, the demo has been confirmed for {$student->name} {$subjectInfo} and {$classInfo}", 2);

//     \Session::flash('msg', "Request Status Update Successfully");
//     return redirect()->back();
// }


	
	// public function sendmailtoemp($too,$stubject,$message){
 //        $to =$too;
 //        $subject = $stubject;
 //        $from='<EMAIL>';
 //        $headers = "From: " . strip_tags($from) . "\r\n";
 //        $headers .= "MIME-Version: 1.0\r\n";
 //        $headers .= "Content-type:text/html;charset=UTF-8\r\n";
 //        $curl = curl_init();
 //        curl_setopt_array($curl, array(
 //        CURLOPT_URL => 'https://api.sendgrid.com/v3/mail/send',
 //        CURLOPT_RETURNTRANSFER => true,
 //        CURLOPT_ENCODING => '',
 //        CURLOPT_MAXREDIRS => 10,
 //        CURLOPT_TIMEOUT => 0,
 //        CURLOPT_FOLLOWLOCATION => true,
 //        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
 //        CURLOPT_CUSTOMREQUEST => 'POST',
 //        CURLOPT_POSTFIELDS =>'{"personalizations":[{"to":[{"email":"'.$to.'","name":"Guruathome"}],"subject":"'.$subject.'"}],"content": [{"type": "text/html", "value": "'.$message.'"}],"from":{"email":"<EMAIL>","name":"Guruathome"}}',
 //        CURLOPT_HTTPHEADER => array(
 //        'Authorization: Bearer *********************************************************************',
 //        'Content-Type: application/json'
 //        ),
 //        ));

 //        $response = curl_exec($curl);
          
 //        curl_close($curl);
 //         // print_r($response);die;
 //         return 1;
 //    }	


public function notificationsendd($deviceTokens, $title, $body)
{
    // Path to your service account JSON file
    $serviceAccountFile = 'https://api.guruathome.com/guru-at-home-firebase-adminsdk-4hw51-b9a6b5855f.json';

    // Fetch the access token
    $accessToken = $this->getAccessToken($serviceAccountFile);

    // FCM v1 API endpoint
    $fcmEndpoint = 'https://fcm.googleapis.com/v1/projects/guru-at-home/messages:send';

    // Loop through all the device tokens and send notifications
    foreach ($deviceTokens as $deviceToken) {
        $notificationData = [
            "message" => [
                "token" => $deviceToken,
                "notification" => [
                    "title" => $title,
                    "body" => $body,
                ],
                "data" => [
                    'type' => 'type',
                    'screen' => 'Routes.chatCurrentView',
                    'sound' => 'notification',
                ],
                "android" => [
                    "priority" => "high",
                    "notification" => [
                        "channelId" => "astrologer_channel_id",
                        "sound" => "notification",
                    ],
                ],
                "apns" => [
                    "headers" => [
                        "apns-priority" => "10",
                    ],
                    "payload" => [
                        "aps" => [
                            "alert" => [
                                "title" => $title,
                                "body" => $body,
                            ],
                            "sound" => "notification",
                            "content_available" => true,
                        ],
                    ],
                ],
            ]
        ];

        // Send the notification using GuzzleHTTP client
        $client = new \GuzzleHttp\Client();
        try {
            $response = $client->post($fcmEndpoint, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $accessToken,
                    'Content-Type' => 'application/json',
                ],
                'body' => json_encode($notificationData),
            ]);

            // Handle success response
            if ($response->getStatusCode() === 200) {
                // Successfully sent notification
                // You can optionally log the response for debugging purposes
            } else {
                // Log failure or handle the error in response
                $errorResponse = $response->getBody()->getContents();
                // Handle the error accordingly
            }
        } catch (\GuzzleHttp\Exception\ClientException $e) {
            // Capture the error if the notification fails
            $errorBody = $e->getResponse()->getBody()->getContents();
            // Log the error for debugging purposes
            return response()->json(['error' => 'Failed to send notification', 'details' => $errorBody], 500);
        }
    }

    return response()->json(['message' => 'Notifications sent successfully!']);
}

private function getAccessToken($serviceAccountFile)
{
    $credentials = json_decode(file_get_contents($serviceAccountFile), true);
    $scopes = ['https://www.googleapis.com/auth/cloud-platform'];
    $oauth2 = new OAuth2([
        'audience' => 'https://oauth2.googleapis.com/token',
        'issuer' => $credentials['client_email'],
        'sub' => $credentials['client_email'],
        'signingAlgorithm' => 'RS256',
        'signingKey' => $credentials['private_key'],
        'scope' => $scopes,
        'tokenCredentialUri' => 'https://oauth2.googleapis.com/token',
    ]);
    $authToken = $oauth2->fetchAuthToken();
    return $authToken['access_token'];
}


	public function Demo_csv_download(Request $request)
{
	$time = " 00:00:00";
	$endtime = " 23:59:59";
    $startDate = $request->input('start_date'). $time;
    $endDate = $request->input('end_date') . $endtime;
    // $startDate = date('Y-m-d', strtotime('+1 day', strtotime($request->start_date)));
    // $endDate = date('Y-m-d', strtotime('+1 day', strtotime($request->end_date)));

    $users = StudentApplyForDemo::whereBetween('created_at', [$startDate, $endDate])->get();

    $headers = array(
        "Content-type" => "text/csv",
        "Content-Disposition" => "attachment; filename=demo.csv",
        "Pragma" => "no-cache",
        "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
        "Expires" => "0"
    );

    $callback = function () use ($users) {
        $file = fopen('php://output', 'w');
        fputcsv($file, ['Query No','Email', 'Student Name', 'Parent Name' , 'Country Code', 'Phone', 'Class/Category','Subject/Courses','Student Select plan','Price Range','Assign Teacher','Preferred Topic','Preferred Time','Curriculum','Currency','Enroll Purchase Class','Feedback Status']);

        foreach ($users as $user) {
        	$enroll_count = Enroll::where('student_apply_for_demo_id',$user->id)->count();
        	if($enroll_count >0){
        		$enroll_purchase_class = 'Yes';	
        	}else{
        		$enroll_purchase_class = 'No';	
        	}
            $student_name = User::find($user->user_id);
            $class_name = null;
            $s_name = null;
            if (!empty($user->class_id)) {
                $class_name = StudentClass::find($user->class_id);
                $c_name = $class_name->class_name;
                $s_name = Subject::find($user->subject_id);
                $subject = $s_name->title;
            } else {
                $class_name = Category::find($user->category_id);
                $c_name = $class_name->name;
                $s_name = Courses::find($user->course_id);
                $subject = $s_name->name;
            }
            $plan = Plan::find($user->plan_id);
            $teacher_name = $user->teacher_assign_id ? User::find($user->teacher_assign_id) : "Not Assign";
            $sbooking = StudentBookingFrees::where('studentapplyfordemo_id', $user->id)->first();
            if($sbooking->feedback_status == 1){
            	$feedback = 'Yes';
            }else{
            	$feedback = 'No';
            }
            fputcsv($file, [
                $user->id,
                $student_name ? $student_name->email : "N/A",
                $student_name ? $student_name->name : "N/A",
                $student_name ? $student_name->parent_name : "N/A",
                $student_name ? $student_name->dial_code : "N/A",
                $student_name ? $student_name->phone : "N/A",
                $c_name ? $c_name : "N/A",
                $subject ? $subject : "N/A",
                $plan ? $plan->title : "N/A",
                $user->start_price_range.'-'.$user->end_price_range,
                $teacher_name ? ($teacher_name !== "Not Assign" ? $teacher_name->name : "Not Assign") : "N/A",
                $sbooking ? $sbooking->preferred_topic : "N/A",
                $sbooking ? $sbooking->preferred_time : "N/A",
                $sbooking ? $sbooking->curriculum : "N/A",
                $sbooking ? $sbooking->currency : "N/A",
                $enroll_purchase_class ? $enroll_purchase_class : "N/A",
                $feedback ? $feedback : "N/A",
            ]);
        }

        fclose($file);
    };

    return new StreamedResponse($callback, 200, $headers);
}



    }