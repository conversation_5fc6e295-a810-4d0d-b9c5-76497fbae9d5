<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Subject;
use App\Models\Category;
use App\Models\SubCategory;
use App\Models\StudentApplyForDemo;
use App\Models\StudentClass;

use App\Models\Planfrequencys;
use App\Models\Plan;

class SubjectController extends Controller
{
	public function index(){
		// dd('dddd');
		$data = Subject::where("deleted",0)->orderBy("id", "DESC")->paginate(10);
		if(!empty($data)){
			foreach($data as $d){
				if($d->class_id!=0){
					$d->class_name = $d->class->class_name;
				}else{
				  $d->class_name="-";
				}
				if(isset($d->image)){
					$d->image  = url('/public/').$d->image;
				}
				
			}
		}
		// dd($data);
		$classDetails = StudentClass::where('deleted',0)->where("status",1)->get();
		return view('admin.subject.index', compact('data','classDetails'));
	}
	public function getSubjectByCateogrySubCateogry(Request $request)
	{
		if($request->all())
		{
			$getSubject = subject::where('category_id',$request->category_id)->where('sub_category_id',$request->sub_category_id)->where('status',1)->get();
			if(!empty($getSubject))
            {
                $output = '<option value="">Select Subject</option>';
                foreach ($getSubject as $key => $value) {
                    $output .= '<option value="'.$value->id.'">'.$value->title.'</option>';
                }
                echo $output;
            }
		}
	}

	public function create()
	{

			$classDetails = StudentClass::where('deleted',0)->where("status",1)->get();
		$category = Category::where(['deleted'=>0,'status'=>1])->get();
		return view('admin.subject.create',compact('category','classDetails'));
	}

	public function store(Request $request)
	{


		$validator = Validator::make($request->all(), [
			'title' => 'required',
			'class_id'=>'required',
			'image'=>'required',
			// 'description'=>'required',
		]);
		
		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$subject = new Subject();
			$subject->class_id = $request->class_id;
			$subject->title = $request->input('title1');
			$subject->class_id = $request->input('class_id');			
			$fileimage = "";
             $cate_url = '';
             if ($request->hasfile('image')){
	             $file_image = $request->file('image');
	             $fileimage = rand(00000, 99999) . date('d').".". $file_image->getClientOriginalExtension();
	             $destination = public_path("upload/subject");
                 $file_image->move($destination, $fileimage);
                 $cate_url = '/upload/subject' . '/' . $fileimage;
                }
               $subject->category_id = 0;
               $subject->sub_category_id  = 0;
			$subject->status = 1;
			$subject->image= $cate_url;
			$subject->description = $request->description;

			$subject->save();

			foreach ($request->plan as $fkey => $fvalue) {


			$countryarr = array();
			$plan = new Plan();
			$plan->subject_id = $subject->id;
			$plan->class_id = $subject->class_id;
			$plan->title = $request->title[$fkey];
			$plan->description = $request->plan_description[$fkey];
			$plan->plan_type = $request->plan_type[$fkey];
			$plan->save();
			foreach ($request->one_session[$fkey+1] as $okey => $ovalue) {
				if(!in_array($request->currency[$fkey+1][$okey], $countryarr)){
				   $planfreq = new Planfrequencys();
				   $planfreq->subject_id = $subject->id;
				   $planfreq->class_id = $subject->class_id;
				   $planfreq->plan_id = $plan->id;
				   $planfreq->start_range = $request->start_range[$fkey+1][$okey];
			       $planfreq->end_range = $request->end_range[$fkey+1][$okey];
				   $planfreq->currency = $request->currency[$fkey+1][$okey];
				   $planfreq->one_session = $ovalue;
				   $planfreq->second_session = $request->second_session[$fkey+1][$okey];
				   $planfreq->third_session = $request->third_session[$fkey+1][$okey];
				   $planfreq->four_session = $request->four_session[$fkey+1][$okey];
				   $planfreq->five_session = $request->five_session[$fkey+1][$okey];
				   $planfreq->six_session = $request->six_session[$fkey+1][$okey];
				   $planfreq->save();
				   $countryarr[] = $request->currency[$fkey+1][$okey];
				}
				
			}
		}

			\Session::flash('msg', 'Subject Added Successfully.');
			return redirect('/admin/subjects');
		}
	}

	public function show(Subject $subject)
	{
		//
	}

	public function edit($id)
	{
		$subject = Subject::find($id);
		$subject->image = !empty($subject->image) ? url('/').$subject->image :'';
		$category= Category::where(['deleted'=>0,'status'=>1])->get();
		$classDetails = StudentClass::where('deleted',0)->where("status",1)->get();
		$subCategory= SubCategory::where(array('deleted'=>0,'status'=>1,'category_id'=>$subject->category_id))->get();
		$plans1 = Plan::where('subject_id',$id)->where('plan_type',1)->first();
		$plans2 = Plan::where('subject_id',$id)->where('plan_type',2)->first();
		$plans3 = Plan::where('subject_id',$id)->where('plan_type',3)->first();

		$pid = isset($plans1->id) ? $plans1->id:"";
		$pid2 = isset($plans2->id) ? $plans2->id:"";
		$pid3 = isset($plans3->id) ? $plans3->id:"";
		$frequency1 = Planfrequencys::where(['subject_id'=>$subject->id,'plan_id'=>$pid])->get();
		$frequency2 = Planfrequencys::where(['subject_id'=>$subject->id,'plan_id'=>$pid2])->get();
		$frequency3 = Planfrequencys::where(['subject_id'=>$subject->id,'plan_id'=>$pid3])->get();
		$Planfrequencys = Planfrequencys::where('subject_id',$id)->get();
	   // dd($Planfrequencys);
		return view('admin.subject.edit',compact('frequency1','frequency2','frequency3','subject','category','subCategory','classDetails','plans1','plans2','plans3','Planfrequencys'));
	}

	public function update(Request $request, $id)
	{
		  // dd($request->all());
		$validator = Validator::make($request->all(), [
			'title' => 'required',
			'class_id'=>'required',
			// 'description'=>'required',
			//'sub_category_id'=>'required',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		} else {
			$subject = Subject::find($id);
			$subject->class_id = $request->class_id;
			$subject->title = $request->input('title1');
			$subject->class_id = $request->input('class_id');			
			$fileimage = "";
             $cate_url = '';
             if ($request->hasfile('image')) {

             	 if(file_exists(url('/').$subject->image))
             	
                {
                    unlink(url('/').$subject->image);
                }
	                $file_image = $request->file('image');
	                $fileimage = rand(00000, 99999) . date('d').".". $file_image->getClientOriginalExtension();
	                $destination = public_path("upload/subject");
	                $file_image->move($destination, $fileimage);
	                $cate_url = '/upload/subject' . '/' . $fileimage;

                }
                else
                {
                	$cate_url = $subject->image;
                }
                $subject->image = $cate_url;
            	$subject->description = $request->description;
			    $subject->save();
			     $planid_arr = Plan::where("subject_id",$id)->pluck('id')->toArray();
			    $subject_session = Plan::where("subject_id",$id)->delete();
			    $subject_session = Planfrequencys::where("subject_id",$id)->delete();

			    if(isset($request->plan)){
			    	foreach ($request->plan as $fkey => $fvalue) {
							 $countryarr = array();
							 $plan = new Plan();
							 $plan->subject_id = $subject->id;
							 $plan->class_id = $subject->class_id;
							 $plan->title = $request->title[$fkey];
							 $plan->description = $request->plan_description[$fkey];
							 $plan->plan_type = $request->plan_type[$fkey];
							 $plan->save();

							$studentapp = StudentApplyForDemo::where('plan_id',$planid_arr[$fkey])->update(['plan_id'=>$plan->id]);
			                 foreach ($request->one_session[$fkey+1] as $okey => $ovalue) {
				                      // if(!in_array($request->currency[$fkey+1][$okey], $countryarr)){
									   $planfreq = new Planfrequencys();
									   $planfreq->subject_id = $subject->id;
									   $planfreq->class_id = $subject->class_id;
									   $planfreq->plan_id = $plan->id;
									   $planfreq->start_range = $request->start_range[$fkey+1][$okey];
								       $planfreq->end_range = $request->end_range[$fkey+1][$okey];
									   $planfreq->currency = $request->currency[$fkey+1][$okey];
									   $planfreq->one_session = $ovalue;
									   $planfreq->second_session = $request->second_session[$fkey+1][$okey];
									   $planfreq->third_session = $request->third_session[$fkey+1][$okey];
									   $planfreq->four_session = $request->four_session[$fkey+1][$okey];
									   $planfreq->five_session = $request->five_session[$fkey+1][$okey];
									   $planfreq->six_session = $request->six_session[$fkey+1][$okey];
									   $planfreq->save();
				   // $countryarr[] = $request->currency[$fkey+1][$okey];
				
				
			}
		}
			    }
			

			\Session::flash('msg', 'Subject updated Successfully.');
			return redirect('/admin/subjects');
		}
	}

	public function delete($id)
	{
		$data = Subject::findOrFail($id);
		$data->deleted=1;
		$data->update();
		\Session::flash('msg', 'Subject deleted Successfully.');
	    return redirect('/admin/subjects');
	}

	public function updateStatus($id,$status)
	{
		$data = Subject::findOrFail($id);
		$data->status=$status;
		$data->update();
			\Session::flash('msg', 'Status Changes Successfully.');
	    return redirect()->back();
	}

}
