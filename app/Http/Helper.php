<?php
namespace App\Http;

use App\Models\Assignment;
use App\Models\AssignmentSubmission;
use App\Models\BadWord;
use App\Models\Banner;
use App\Models\Chapter;
use App\Models\ChatGroup;
use App\Models\ChatGroupUser;
use App\Models\CommunityPost;
use App\Models\ContinueStudy;
use App\Models\CouponCode;
use App\Models\Courses;
use App\Models\Lession;
use App\Models\Notification;
use App\Models\NotificationSend;
use App\Models\Quiz;
use App\Models\Quizoption;
use App\Models\Quizquestions;
use App\Models\StudentClass;
use App\Models\StudentExam;
use App\Models\StudentExamAnswer;
use App\Models\Subscription;
use App\Models\User;
use App\Models\ReferPoint;
use App\Models\UserPoint;
use App\Models\UserSubscription;
use App\Models\UserTracking;
use App\Models\LoginSession;
use Carbon\Carbon;
use DB;
use GuzzleHttp\Client;
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;
// use Illuminate\Http\Request;
use GuzzleHttp\Psr7\Request;
use App\Models\TeacherAvailabiltyOfSlots;

class Helper
{
	public static function functionName()
	{
		 return "mydata";
	}

	public function sendmailtoemp1($stubject,$message,$too){

                    $to =$too;// ;$too.",<EMAIL>"
                    //dd($to);
                    $subject = $stubject;
                    $from='<EMAIL>';
                    $headers = "From: " . strip_tags($from) . "\r\n";
                    $headers .= "MIME-Version: 1.0\r\n";
                    $headers .= "Content-Type: text/html; charset=ISO-8859-1\r\n";
                    $curl = curl_init();
                    curl_setopt_array($curl, array(
                    CURLOPT_URL => 'https://api.sendgrid.com/v3/mail/send',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS =>'{"personalizations":[{"to":[{"email":"'.$to.'","name":"Guruathome"}],"subject":"'.$subject.'"}],"content": [{"type": "text/html", "value": "'.$message.'"}],"from":{"email":"<EMAIL>","name":"Guruathome"}}',
                    CURLOPT_HTTPHEADER => array(
                    'Authorization: Bearer *********************************************************************',
                    'Content-Type: application/json'
                    ),
                    ));
                    $response = curl_exec($curl);
                    // print_r($response);die;
                    curl_close($curl);
         return 1;
        }

        public function sendmailtoemp11($too,$stubject,$message){
        $to =$too;
        $subject = $stubject;
        $from='<EMAIL>';
        $headers = "From: " . strip_tags($from) . "\r\n";
        $headers .= "MIME-Version: 1.0\r\n";
        $headers .= "Content-type:text/html;charset=UTF-8\r\n";
        $curl = curl_init();
        curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://api.sendgrid.com/v3/mail/send',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS =>'{"personalizations":[{"to":[{"email":"'.$to.'","name":"Guruathome"}],"subject":"'.$subject.'"}],"content": [{"type": "text/html", "value": "'.$message.'"}],"from":{"email":"<EMAIL>","name":"Guruathome"}}',
        CURLOPT_HTTPHEADER => array(
        'Authorization: Bearer *********************************************************************',
        'Content-Type: application/json'
        ),
        ));

        $response = curl_exec($curl);
          
        curl_close($curl);
         // print_r($response);die;
         return 1;
    }
    public function sendmailtoemp($too,$subject,$message)
    {
        $to =$too;
        $subject = $subject;
        $fromEmail = '<EMAIL>';
        $fromName = 'Guru At Home';
        $apiKey = 'mlsn.6dfe534bb39a770d4a94760d2a660fc28cce2492aec5a515267409d42e5c5737';
        
        // MailerSend API endpoint
        $url = 'https://api.mailersend.com/v1/email';

        // Create the request payload
        $payload = [
            'from' => [
                'email' => $fromEmail,
                'name' => $fromName
            ],
            'to' => [
                [
                    'email' => $to,
                    'name' => 'Recipient Name' // Optionally replace with the recipient's name
                ]
            ],
            'subject' => $subject,
            'html' => $message
        ];

        // Initialize GuzzleHTTP client
        $client = new Client();

        try {
            // Send the request to MailerSend API
            $response = $client->post($url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $apiKey,
                    'Content-Type' => 'application/json'
                ],
                'json' => $payload
            ]);
            // dd($response);
            // Get the response
            $responseBody = json_decode($response->getBody(), true);
            // dd($responseBody);
            // Return success message or handle the response as needed
            return response()->json(['message' => 'Email sent successfully!', 'data' => $responseBody]);

        } catch (\Exception $e) {
            // Handle errors
            return response()->json(['message' => 'Failed to send email.', 'error' => $e->getMessage()], 500);
        }
    }

	   public  function get_availableTeacherslot($userId,$day,$start_time,$end_time)
	    {
	      
	    	$sql ="SELECT * FROM `teacher_availabilty_of_slots` inner join teacher_availabilty_of_slots_details on teacher_availabilty_of_slots_details.teacher_availabilty_of_slots_id = teacher_availabilty_of_slots.id where teacher_availabilty_of_slots_details.teacher_id = $userId and teacher_availabilty_of_slots.day =$day and teacher_availabilty_of_slots_details.start_time >= '".$start_time."' and teacher_availabilty_of_slots_details.end_time <='".$end_time."'";
	    	
	        $result = DB::select($sql);

	        if(count($result)!=0)
	        {
	            return 'true';
	        }
	        else
	        {
	            return 'false';
	        }
	    }

	public function userSubscription($userId)
	{
		$today = date('Y-m-d');
		$subscription = UserSubscription::where("user_id", $userId)->where("end_date", ">=", $today)->where("paymentStatus", 1)->orderBy('id', 'DESC')->first();
		return !empty($subscription) ? 1 : 0;
	}

	public function getUserSubscriptionStatus($userId)
	{
		$today = date('Y-m-d');
		$subscription = UserSubscription::where("user_id", $userId)->where("end_date", ">=", $today)->where("paymentStatus", 1)->orderBy('id', 'DESC')->first();
		$userSubscription = !empty($subscription) ? 1 : 0;
		$subscriptionMode = !empty($subscription) ? $subscription->mode : 'FREE';
		if($userSubscription==1 && $subscriptionMode!='FREE') {
			$userSubscriptionStatus = 'Paid';
		}
		if($userSubscription==0) {
			$userSubscriptionStatus = 'Free';
		}
		if($userSubscription==1 && $subscriptionMode=='FREE') {
			$userSubscriptionStatus = 'Trial';
		}
		return $userSubscriptionStatus;
	}

	public function addUserReferPoints($userId,$referPointId,$moduleId=0)
	{
		$referPoint = ReferPoint::where("id", $referPointId)->first();
		$data = array(
			'user_id'  			=> $userId,
			'module_id'  		=> $moduleId,
			'refer_point_id'  	=> $referPointId,
			'points'  			=> !empty($referPoint) ? $referPoint->points : 0,
			'description'  		=> !empty($referPoint) ? $referPoint->description : NULL,
			'transaction_type'  => 502,
			'view_status'		=> 2,
			'created_at'        => date('Y-m-d H:i:s'),
			'updated_at'        => date('Y-m-d H:i:s'),
		);
		$insertId = UserPoint::insertGetId($data);
		return $insertId;
	}

	public function getUserPoint($userId)
	{
		return $userPoint = UserPoint::where("user_id", $userId)->where("transaction_type", 502)->where("view_status", 2)->first();
	}

	public function profileComplete($userId)
	{
		$profileComplete = '40';
		$user = User::where("id", $userId)->first();
		if($user->image!='' && $user->gender!='' && $user->dob!='' && $user->school_college!='' && $user->city!='' && $user->postal_code!='' && $user->state!='' && $user->parents_phone!=''){
			$profileComplete = '100';
		}
		//Refer point on Complete Profile
		if ($profileComplete == 100) {
			$userPoint = UserPoint::where("user_id", $userId)->where("refer_point_id", 2)->first();
			if (empty($userPoint)) {
				$this->addUserReferPoints($userId,2);
			}
		}
		return $profileComplete;
	}

	public function checkBadWords($value, $replacement='*****')
	{
		$badwords = BadWord::get();
		$words = [];
		$replace = [];
		foreach ($badwords as $badword) {
			array_push($words, $badword->bad_word);
			array_push($replace, $replacement);
		}
		return str_ireplace($words, $replace, $value);
	}

	public function getBannerModuleTitle($id)
	{
		$banner = Banner::find($id);
		$module_id = $banner->module_id;
		$moduleDataArr = [];
		if ($banner->module_name=='COURSE') {
			$getModuleData = Courses::where("id", $module_id)->first();
			$bannerModuleTitle = !empty($getModuleData) ? $getModuleData->name : '';
		} elseif ($banner->module_name=='LESSON') {
			$getModuleData = Lession::with('courses')->where("id", $module_id)->first();
			$bannerModuleTitle = !empty($getModuleData) ? $getModuleData->name.' - ( '.$getModuleData->courses->name.' )' : '';
		} elseif ($banner->module_name=='TOPIC') {
			$getModuleData = Chapter::with('courses','lession')->where("id", $module_id)->first();
			$bannerModuleTitle = !empty($getModuleData) ? $getModuleData->name.' - ( '.$getModuleData->courses->name.' - '.$getModuleData->lession->name.' )' : '';
		} elseif ($banner->module_name=='ASSIGNMENT') {
			$getModuleData = Assignment::with('course')->where("id", $module_id)->first();
			$bannerModuleTitle = !empty($getModuleData) ? $getModuleData->title.' - ( '.$getModuleData->course->name.' )' : '';
		} elseif ($banner->module_name=='QUIZ') {
			$getModuleData = Quiz::with('courses','lession','topic')->where("id", $module_id)->where("id", ">", 0)->first();
			$courseName = $lessonName = $topicName = '';
			if(!empty($getModuleData) && $getModuleData->courseId>0){ $courseName = $getModuleData->courses->name; }
			if(!empty($getModuleData) && $getModuleData->lessionId>0){ $lessonName = $getModuleData->lession->name; }
			if(!empty($getModuleData) && $getModuleData->topicId>0){ $topicName = $getModuleData->topic->name; }
			$bannerModuleTitle = !empty($getModuleData) ? $getModuleData->name.' - ( '.$courseName.' - '.$lessonName.' - '.$topicName.' )' : '';
		} elseif ($banner->module_name=='CHAT') {
			$getModuleData = ChatGroup::where("id", $module_id)->first();
			$bannerModuleTitle = !empty($getModuleData) ? $getModuleData->title : '';
		} elseif ($banner->module_name=='COMMUNITY_POST') {
			$getModuleData = CommunityPost::where("id", $module_id)->first();
			$bannerModuleTitle = !empty($getModuleData) ? $getModuleData->title : '';
		} elseif ($banner->module_name=='SUBSCRIPTION') {
			$getModuleData = Subscription::where("id", $module_id)->first();
			$bannerModuleTitle = !empty($getModuleData) ? $getModuleData->name : '';
		} else {
			$bannerModuleTitle = '';
		}

		return $bannerModuleTitle;
	}

	public function createSlug($text)
	{
		// replace non letter or digits by -
		$text = preg_replace('~[^\pL\d]+~u', '-', $text);

		// transliterate
		$text = iconv('utf-8', 'us-ascii//TRANSLIT', $text);

		// remove unwanted characters
		$text = preg_replace('~[^-\w]+~', '', $text);

		// trim
		$text = trim($text, '-');

		// remove duplicated - symbols
		$text = preg_replace('~-+~', '-', $text);

		// lowercase
		$text = strtolower($text);

		if (empty($text)) {
			return 'n-a';
		}

		return $text;
	}

	public function genrateOtp($digits = 4)
	{
		$number = rand (1000,9999);
		return (int)$number;
	}

	public function getcouponcode()
	{
		$length = 10;
		$characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
		$charactersLength = strlen($characters);
		$randomString = '';
		for ($i = 0; $i < $length; $i++) {
			$randomString .= $characters[rand(0, $charactersLength - 1)];
		}
		$checkCoupon = CouponCode::where('coupon', $randomString)->first();
		if (empty($checkCoupon)) {
			return $randomString;
		} else {
			for ($i = 0; $i < $length; $i++) {
				$randomString .= $characters[rand(0, $charactersLength - 1)];
			}
			return $randomString;
		}
	}

	public function addNotification($userId,$message, $click_action='', $module_id='')
	{
		$data = array(
			'user_id'		=> $userId,
			'message'		=> $message,
			'created_at'	=> date('Y-m-d H:i:s'),
		);
		$inserId = Notification::insertGetId($data);
		$user = User::where("id", $userId)->first();
		$token = isset($user->deviceToken) ? $user->deviceToken : '';
		if ($token!='') {
			//$title = 'BrainyWood';//ak
			$title = 'Guruathome';
			//$this->notificationsend($token, $title, $message, $click_action, $module_id); //ak
		}
	}
	/*public function notificationsend($token, $title, $body, $click_action='', $module_id='')
	{
		$url = "https://fcm.googleapis.com/fcm/send";
		$token = $token;
		$serverKey = 'AAAAqt_CbVU:APA91bH1KAkCHGHbgQEtQYxUldBupx4_7y42dNa1hOPGz8IFePdzSXWu4uC1CudCTuowek2O01KScKbHgoROAscE8mCiy-53rcxcQOABsLvrp1JB14kbGNVT7sGqT53Qh1sjeAflTqC2';
		$title = $title;
		$body = $body;
		$notification = array('title' => $title, 'body' => $body, 'sound' => 'default', 'click_action' => $click_action, 'ModuleId' => $module_id, 'badge' => '1');
		$arrayToSend = array('to' => $token, 'notification' => $notification, 'priority' => 'high');
		$json = json_encode($arrayToSend);
		$headers = array();
		$headers[] = 'Content-Type: application/json';
		$headers[] = 'Authorization: key=' . $serverKey;
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
		curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
		curl_setopt($ch, CURLOPT_POSTFIELDS, $json);
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		//Send the request
		$result = curl_exec($ch);
		curl_close($ch);
		$r = json_decode($result);
		return $r;
	}*/

	public function notificationsend($device_token,$title,$massage,$click_action='',$module_id='',$name='')
	{
		try {
			if (!empty($device_token)) {
				$massage = $massage.'.';
				$fcmUrl = 'https://fcm.googleapis.com/fcm/send';
			   
				$notification = [
					'title' =>$title,
					'body' => $massage,
					'icon' =>'myIcon',
					'sound' => 'mySound.wav',
					'click_action' => $click_action,
					'ModuleId' => $module_id,
					'name' => $name,
					'badge' => '1',
				];
				
				$moredata = [
					'module_id' => $module_id,
					'name' => $name,
				];
				
				$extraNotificationData = ["message" => $notification,"moredata" => $moredata];
				
				$fcmNotification = [
					//'registration_ids' => $tokenList, //multple token array
					'to'        => $device_token, //single token
					'notification' => $notification,
					'data' => $notification
				];

				$headers = [
					'Authorization: key=AAAAqt_CbVU:APA91bH1KAkCHGHbgQEtQYxUldBupx4_7y42dNa1hOPGz8IFePdzSXWu4uC1CudCTuowek2O01KScKbHgoROAscE8mCiy-53rcxcQOABsLvrp1JB14kbGNVT7sGqT53Qh1sjeAflTqC2',
					'Content-Type: application/json'
				];

				$ch = curl_init();
				curl_setopt($ch, CURLOPT_URL, $fcmUrl);
				curl_setopt($ch, CURLOPT_POST, true);
				curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
				curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
				curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
				curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fcmNotification));
				$result = curl_exec($ch);
			   
				curl_close($ch);

				$r = json_decode($result);

				return $r;
				// dd($r);
				// return $this->sendResponse($r, 'Massage send successfully');
			} else {
				return 'token not found';
				// return $this->sendError('User name not found');
			}
		}catch (\Exception $e) {
			return $e->getMessage();
		}
	}

	public function sms($phone, $otp)// comment ak
	{
		$curl = curl_init();

		// curl_setopt_array($curl, array(
		// 	CURLOPT_URL => 'http://2factor.in/API/V1/e0268e22-d023-11eb-8089-0200cd936042/ADDON_SERVICES/SEND/TSMS',
		// 	CURLOPT_RETURNTRANSFER => true,
		// 	CURLOPT_ENCODING => '',
		// 	CURLOPT_MAXREDIRS => 10,
		// 	CURLOPT_TIMEOUT => 0,
		// 	CURLOPT_FOLLOWLOCATION => true,
		// 	CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		// 	CURLOPT_CUSTOMREQUEST => 'POST',
		// 	CURLOPT_POSTFIELDS => array('From' => 'BRNYWD','To' => $phone,'TemplateName' => 'customtemplate','VAR1' => $otp),
		// 	CURLOPT_HTTPHEADER => array(
		// 		'token: gfLOYYpyKqGfyZ@vGMbeUhTcP%Erta#JnoDKWZffIVM@IVR'
		// 	),
		// ));

		// $response = curl_exec($curl);

		// curl_close($curl);
		//echo $response;
	}

	public function smsWithTemplate($phone, $templateName, $var1='', $var2='', $var3='', $var4='')
	{
		/*$curl = curl_init();

		curl_setopt_array($curl, array(
			CURLOPT_URL => 'http://2factor.in/API/V1/e0268e22-d023-11eb-8089-0200cd936042/ADDON_SERVICES/SEND/TSMS',
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'POST',
			CURLOPT_POSTFIELDS => array('From'=>'BRNYWD', 'To'=>$phone, 'TemplateName'=>$templateName, 'VAR1'=>$var1, 'VAR2'=>$var2, 'VAR3'=>$var3, 'VAR4'=>$var4),
			CURLOPT_HTTPHEADER => array(
				'token: gfLOYYpyKqGfyZ@vGMbeUhTcP%Erta#JnoDKWZffIVM@IVR'
			),
		));
		$response = curl_exec($curl);
		curl_close($curl);*/

		$senderId = 'BRNYWD';

		// $ch = curl_init();
		
		// curl_setopt($ch, CURLOPT_URL, "http://2factor.in/API/V1/e0268e22-d023-11eb-8089-0200cd936042/ADDON_SERVICES/SEND/TSMS");
		// curl_setopt($ch, CURLOPT_POST, 1);
		// curl_setopt($ch, CURLOPT_POSTFIELDS, "From=$senderId&To=$phone&TemplateName=$templateName&VAR1=$var1&VAR2=$var2&VAR3=$var3&VAR4=$var4");
		// curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		// $response = curl_exec($ch);
		// //print_r($response); exit;
		// curl_close($ch);
		// //echo $response;
	}

	public function sendEmail($to, $subject, $data)
	{
		$curl = curl_init();
		curl_setopt_array($curl, array(
			CURLOPT_URL => 'https://api.sendgrid.com/v3/mail/send',
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'POST',
			CURLOPT_POSTFIELDS => '{"personalizations":[{"to":[{"email":"' . $to . '","name":"ByWood"}],"subject":"' . $subject . '"}],"content": [{"type": "text/html", "value": "<div style=\'background-color:#272b34 !important; text-align:center;display:block ;height: 43px;padding-top: 29px;\'>  <img src=\'http://brainscienceindia.in/public/front/assets/img/web-logo.png\' hight=\'50px\' width=\'100px\'> </div> <br>Hi  ' . $data['userName'] . ', <br><br> ' . $data['message'] . ' <br> <br>BrainyWood Team <br> <br><span style=\'font-size:9px\'>You are receiving this email because  <br> <br><center style=\'font-size:9px\'>  Copyright © 2021 | BrainyWood | All rights reserved </center></span>"}],"from":{"email":"<EMAIL>","name":"BrainyWood"}}',
			CURLOPT_HTTPHEADER => array(
				'Authorization: Bearer *********************************************************************',
				'Content-Type: application/json'
			),
		));
		$response = curl_exec($curl);


		curl_close($curl);
		//return $response;
	}

	public function getQuizDetails($examId, $userId)
	{
		$getQuiz = array();
		$getStudentExam = StudentExam::where("id", $examId)->where("user_id", $userId)->first();
		if (!empty($getStudentExam)) {
			$courseId = $getStudentExam->course_id;
			$lessionId = $getStudentExam->lession_id;
			$topicId = $getStudentExam->topic_id;
			if ($lessionId > 0) {
				if ($topicId > 0) {
					$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where("topicId", $topicId)->where('islession', 2)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
				} else {
					$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 0)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
				}
			} else {
				$getQuiz = Quiz::where("courseId", $courseId)->where("lessionId", $lessionId)->where('islession', 1)->where('status', 1)->where('deleted', 0)->orderBy("id", "DESC")->first();
			}
		}
		return !empty($getQuiz) ? $getQuiz : [];
	}

	public function getQuizExamScore($quizId=0, $examId=0)
	{
		//return $quizId.' - '.$examId; //die;
		$percentage = 0;
		if ($quizId > 0 && $examId > 0) {
			$examQuestions = Quizquestions::where("quizId", $quizId)->get();
			$total_questions = count($examQuestions);
			$total_marking = $score = $right = $wrong = $total_solved = $not_solved = 0;
			if (!empty($examQuestions)) {
				foreach ($examQuestions as $key => $val) {
					$total_marking += $val->marking;
					$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $val->id)->first();
					if (!empty($studentAns)) {
						$quizAnswers = Quizoption::where("questionId", $val->id)->orderBy("id", "ASC")->get();
						$answer = '';
						foreach ($quizAnswers as $key1 => $value) {
							if ($key1 == ($val->currect_option - 1)){
								$answer = $value->id;
							}
						}
						if ($answer == $studentAns->answer) {
							$right++;
							$score += $val->marking;
						} else {
							$wrong++;
						}
					}
				}
			}
			$total_solved = $right + $wrong;
			$not_solved = $total_questions - $total_solved;
			$percentage = (($score * 100) / $total_marking);
		}
		return number_format($percentage, 2);
	}

	public function trackingApi($userId, $module, $subModule=NULL, $moduleId=0, $watchType=NULL, $deviceType, $logout=0)
	{
		//dd($request->all());
		/*$userId  = $request->userId;
		$module = $request->module;
		$subModule = ($request->subModule) ? $request->subModule : NULL;
		$moduleId = ($request->moduleId) ? $request->moduleId : 0;
		$watchType = ($request->watchType) ? $request->watchType : NULL;
		$deviceType = $request->deviceType;*/
		if (!empty($userId) && !empty($module) && !empty($deviceType)) {
			$trackData = UserTracking::where('user_id',$userId)->where('device_type',$deviceType)->whereNull('out_time')->orderBy('id','DESC')->first();
			if (!empty($trackData)) {
				$data = array(
					'out_time' 		=> date('Y-m-d H:i:s'),
					'updated_at'	=> date('Y-m-d H:i:s'),
				);
				$update = UserTracking::where('id',$trackData->id)->update($data);
			}
			if ($logout==0) {
				$userSubscriptionStatus = $this->getUserSubscriptionStatus($userId);
				$insertData = array(
					'user_id'		=> $userId,
					'module'		=> $module,
					'sub_module'	=> $subModule,
					'module_id'		=> $moduleId,
					'watch_type'	=> $watchType,
					'device_type'	=> $deviceType,
					'subscription_status' => $userSubscriptionStatus,
					'in_time'		=> date('Y-m-d H:i:s'),
					'created_at'	=> date('Y-m-d H:i:s'),
				);
				$trackId = UserTracking::insertGetId($insertData);
				$userUpd = User::where('id',$userId)->where('isLogin',2)->update(['deviceType' => $deviceType, 'isLogin' => 1]);
			} else {
				/*$userUpd = User::where('id',$userId)->update(['api_token' => NULL, 'deviceType' => $deviceType, 'isLogin' => 2]);*/
				$loginSession = LoginSession::where("user_id",$userId)->orderBy('id', 'DESC')->first();
				if (!empty($loginSession)) {
					LoginSession::where("id",$loginSession->id)->update(['logout_time' => date('Y-m-d H:i:s')]);
				}
			}
			return true;
		} else {
			return false;
		}
	}

	public function getTrackingTime($user_type='All', $userId=NULL)
	{
		$diff_in_hours = $totalMinutes = $totalSecondsDiff = 0;
		if ($user_type!='All') {
		//if (!empty($ids_arr) || is_array($ids_arr)) {
			//$userTracking = UserTracking::whereIn('user_id',$ids_arr)->orderBy('id', 'DESC')->get();
			$userTracking = UserTracking::orderBy('id', 'DESC')
				 ->Where(function($query) use ($userId) {
					if (isset($userId) && !empty($userId)) {
						$query->whereIn("user_id",$userId);
					}
				 })->where('subscription_status',$user_type)->get();
		} else {
			$userTracking = UserTracking::orderBy('id', 'DESC')
				 ->Where(function($query) use ($userId) {
					if (isset($userId) && !empty($userId)) {
						$query->whereIn("user_id",$userId);
					}
				 })->get();
		}
		if (!empty($userTracking)) {
			foreach ($userTracking as $tracking) {
				//$totalSecondsDiff = 0;
				if (!empty($tracking->out_time) && $tracking->out_time!='') {
					$totalSecondsDiff += abs(strtotime($tracking->out_time) - strtotime($tracking->in_time));
				}
			}
			$totalMinutes = round($totalSecondsDiff/60);
		}
		return $totalMinutes;
		//return $diff_in_hours;
	}

	public function getModuleWiseTime($userIds=NULL, $moduleName='', $from, $to)
	{
		$userTracking = [];
		$diff_in_hours = $totalMinutes = $totalSecondsDiff = 0;
		if (!empty($userIds) || is_array($userIds)) {
			$userTracking = UserTracking::whereIn('user_id',$userIds)->where('module',$moduleName)
							 ->Where(function($query) use ($from, $to) {
								if (isset($from) && !empty($from)) {
									$query->whereBetween("created_at",[$from, $to]);
								}
							 })->orderBy('id', 'DESC')->get();
		}
		if (!empty($userTracking)) {
			foreach ($userTracking as $tracking) {
				//$totalSecondsDiff = 0;
				if (!empty($tracking->out_time) && $tracking->out_time!='') {
					$totalSecondsDiff += abs(strtotime($tracking->out_time) - strtotime($tracking->in_time));
				}
			}
			$totalMinutes = round($totalSecondsDiff/60%60);
		}
		return $totalMinutes;
	}

	public function getDateWiseTime($userId, $date=NULL, $module='', $device_type='')
	{
		$userTracking = [];
		$diff_in_hours = $totalMinutes = $totalSecondsDiff = 0;
		if (!empty($date)) {
			if (is_numeric($userId) && $userId > 0) {
				$userTracking = UserTracking::where('user_id',$userId)->whereDate('created_at',$date)
							 ->Where(function($query) use ($module) {
								if (isset($module) && !empty($module)) {
									$query->where("module",$module);
								}
							 })
							 ->Where(function($query) use ($device_type) {
								if (isset($device_type) && !empty($device_type)) {
									$query->where("device_type",$device_type);
								}
							 })
							 ->orderBy('id', 'DESC')->get();
			}
		}
		if (!empty($userTracking)) {
			foreach ($userTracking as $tracking) {
				if (!empty($tracking->out_time) && $tracking->out_time!='') {
					$totalSecondsDiff += abs(strtotime($tracking->out_time) - strtotime($tracking->in_time));
				}
			}
			$diff_in_hours = round($totalSecondsDiff/3600%24);
		}
		//return $totalMinutes;
		return $diff_in_hours;
	}

	public function getTrackingTimeDaysWise($userId, $days='')
	{
		$userTracking = [];
		$diff_in_hours = $totalMinutes = $totalSecondsDiff = 0;
		$from = '';
		$to = date('Y-m-d H:i:s');
		if (is_numeric($days) && $days!='') {
			$today = date('Y-m-d');
			$date = strtotime($today);
			$date = strtotime("-".$days." days", $date);
			$end_date = date('Y-m-d', $date);
			$from = $end_date.' 00:00:00';
		}
		if (is_numeric($userId) && $userId > 0) {
			$userTracking = UserTracking::where('user_id',$userId)
							 ->Where(function($query) use ($from, $to) {
								if (isset($from) && !empty($from)) {
									$query->whereBetween("created_at",[$from, $to]);
								}
							 })->orderBy('id', 'DESC')->get();
		}
		if (!empty($userTracking)) {
			foreach ($userTracking as $tracking) {
				if (!empty($tracking->out_time) && $tracking->out_time!='') {
					$totalSecondsDiff += abs(strtotime($tracking->out_time) - strtotime($tracking->in_time));
				}
			}
			$diff_in_hours = round($totalSecondsDiff/3600%24);
		}
		return $diff_in_hours;
	}

	public function getTrackingTimeUser($user_type='All', $device_type, $userIds=NULL)
	{
		$userTracking = [];
		$diff_in_hours = $totalMinutes = $totalSecondsDiff = 0;
		if (is_numeric($user_type)) {
			$userId = $user_type;
			if (isset($userId) && $userId>0) {
				$userTracking = UserTracking::where('user_id',$userId)->where('device_type',$device_type)->orderBy('id', 'DESC')->get();
			}
		} else {
			if ($user_type!='All') {
			//if (!empty($userId) && $userId != 'all' && is_array($userId)) {
					$userTracking = UserTracking::where('subscription_status',$user_type)->where('device_type',$device_type)
									 ->Where(function($query) use ($userIds) {
										if (isset($userIds) && !empty($userIds)) {
											$query->whereIn("user_id",$userIds);
										}
									 })->orderBy('id', 'DESC')->get();
			} else {
				$userTracking = UserTracking::where('device_type',$device_type)
									 ->Where(function($query) use ($userIds) {
										if (isset($userIds) && !empty($userIds)) {
											$query->whereIn("user_id",$userIds);
										}
									 })->orderBy('id', 'DESC')->get();
			}
		}
		if (!empty($userTracking)) {
			foreach ($userTracking as $tracking) {
				if (!empty($tracking->out_time) && $tracking->out_time!='') {
					$totalSecondsDiff += abs(strtotime($tracking->out_time) - strtotime($tracking->in_time));
					/*$to = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $tracking->in_time);
					$from = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $tracking->out_time);
					$diff_in_hours += $to->diffInHours($from);*/
				}
			}
			$totalMinutes = round($totalSecondsDiff/60);
		}
		return $totalMinutes;
		//return $diff_in_hours;
	}

	public function getTrackingTimeUserSessions($userId)
	{
		$totalSessions = $totalSecondsDiff = 0;
		if (isset($userId) && $userId>0) {
			$userTracking = UserTracking::where('user_id',$userId)->groupBy('created_at')->orderBy('id', 'DESC')->count();
			if (!empty($userTracking)) {
				foreach ($userTracking as $tracking) {
					if (!empty($tracking->out_time) && $tracking->out_time!='') {
						$totalSecondsDiff += abs(strtotime($tracking->out_time) - strtotime($tracking->in_time));
						/*$to = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $tracking->in_time);
						$from = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $tracking->out_time);
						$diff_in_hours += $to->diffInHours($from);*/
					}
				}
				$totalMinutes = round($totalSecondsDiff/60%60);
			}
		}
		return $totalMinutes;
		//return $diff_in_hours;
	}

	public function getStudentsList($gender, $school, $class, $state, $city, $refer_code, $from, $to, $lastDate='', $userIds_arr=[])
	{
		return $users = User::orderBy("id", "DESC")
					 ->Where(function($query) use ($gender) {
						  if (isset($gender) && !empty($gender)) {
								$query->where("gender",$gender);
						  }
					 })
					 ->Where(function($query) use ($school) {
						  if (isset($school) && !empty($school)) {
								$query->where("school_college",$school);
						  }
					 })
					 ->Where(function($query) use ($class) {
						  if (isset($class) && !empty($class)) {
								$query->where("class_id",$class);
						  }
					 })
					 ->Where(function($query) use ($state) {
						  if (isset($state) && !empty($state)) {
								$query->where("state",$state);
						  }
					 })
					 ->Where(function($query) use ($city) {
						  if (isset($city) && !empty($city)) {
								$query->where("city",$city);
						  }
					 })
					 ->Where(function($query) use ($refer_code) {
						  if (isset($refer_code) && !empty($refer_code)) {
								$query->where("refer_code",$refer_code);
						  }
					 })
					 ->Where(function($query) use ($from, $to) {
						  if (isset($from) && !empty($from)) {
								$query->whereBetween("created_at",[$from, $to]);
						  }
					 })
					 ->Where(function($query) use ($lastDate) {
						  if (isset($lastDate) && !empty($lastDate)) {
								$query->where("updated_at", "<=", $lastDate);
						  }
					 })
					 ->Where(function($query) use ($userIds_arr) {
						  if (isset($userIds_arr) && !empty($userIds_arr)) {
								$query->whereIn("id",$userIds_arr);
						  }
					 })
					 ->where("role_id",3)->where("status",1)->where("deleted",0)->get();
	}

	/*public function getTrackingDaywise($day, $month='', $ids_arr='')
	{
		//$month = 12;
		$diff_in_hours = $totalMinutes = $totalSecondsDiff = 0;
		if (!empty($day)) {
			if (!empty($ids_arr) || is_array($ids_arr)) {
				$userTracking = UserTracking::whereIn('user_id',$ids_arr)->orderBy('id', 'DESC');
				if ($month != '') {
					$userTracking = $userTracking->whereMonth('created_at', '=', $month);
				}
				$userTracking = $userTracking->get();
			} else {
				$userTracking = UserTracking::orderBy('id', 'DESC');
				if ($month != '') {
					$userTracking = $userTracking->whereMonth('created_at', '=', $month);
				}
				$userTracking = $userTracking->get();
			}
			if (!empty($userTracking)) {
				foreach ($userTracking as $tracking) {
					//$totalSecondsDiff = 0;
					if (!empty($tracking->out_time) && $tracking->out_time!='') {
						if (Carbon::parse($tracking->in_time)->format('l')==ucfirst($day)) {
							$totalSecondsDiff += abs(strtotime($tracking->out_time) - strtotime($tracking->in_time));
						}
					}
				}
				$totalMinutes = round($totalSecondsDiff/60%60);
			}
		}
		return $totalMinutes;
		//return $diff_in_hours;
	}*/

	public function getTrackingDaywise($day, $status='', $from='', $to='', $month='', $user_type='All')
	{
		if ($day=='monday') {
			$betweenDay = 0;
		} elseif ($day=='tuesday') {
			$betweenDay = 1;
		} elseif ($day=='wednesday') {
			$betweenDay = 2;
		} elseif ($day=='thursday') {
			$betweenDay = 3;
		} elseif ($day=='friday') {
			$betweenDay = 4;
		} elseif ($day=='saturday') {
			$betweenDay = 5;
		} elseif ($day=='sunday') {
			$betweenDay = 6;
		} else {
			$betweenDay = 0;
		}
		$diff_in_hours = $totalMinutes = $totalSecondsDiff = 0;
		if (!empty($day)) {
			if ($user_type!='All') {
				$userTracking = UserTracking::where('subscription_status',$user_type)->whereRaw('WEEKDAY(in_time) = '.$betweenDay)->whereNotNull('out_time')->orderBy('id', 'DESC');
				if ($status != '') {
					$userTracking = $userTracking->where('subscription_status',$status);
				}
				if ($from != '') {
					$userTracking = $userTracking->whereBetween("created_at",[$from, $to]);
				}
				if ($month != '') {
					$userTracking = $userTracking->whereMonth('created_at', '=', $month);
				}
				$userTracking = $userTracking->get();
			} else {
				$userTracking = UserTracking::whereRaw('WEEKDAY(in_time) = '.$betweenDay)->whereNotNull('out_time')->orderBy('id', 'DESC');
				if ($status != '') {
					$userTracking = $userTracking->where('subscription_status',$status);
				}
				if ($from != '') {
					$userTracking = $userTracking->whereBetween("created_at",[$from, $to]);
				}
				if ($month != '') {
					$userTracking = $userTracking->whereMonth('created_at', '=', $month);
				}
				$userTracking = $userTracking->get();
				//$userTracking = DB::select("SELECT A.user_id, B.email, A.out_time, A.in_time FROM `user_tracking` AS A LEFT JOIN `users` AS B ON A.user_id = B.id WHERE A.out_time IS NOT NULL AND WEEKDAY(CONCAT(A.in_time)) BETWEEN $betweenDay AND $betweenDay ORDER BY A.`id` desc");
				//$userTracking = UserTracking::whereRaw('WEEKDAY(in_time) = '.$betweenDay)->whereNotNull('out_time')->orderBy('id', 'DESC')->get();
			}
			if (!empty($userTracking)) {
				foreach ($userTracking as $tracking) {
					//$totalSecondsDiff = 0;
					if (!empty($tracking->out_time) && $tracking->out_time!='') {
						//if (Carbon::parse($tracking->in_time)->format('l')==ucfirst($day)) {
							$totalSecondsDiff += abs(strtotime($tracking->out_time) - strtotime($tracking->in_time));
						//}
					}
				}
				$totalMinutes = round($totalSecondsDiff/60);
			}
		}
		return $totalMinutes;
		//return $diff_in_hours;
	}

	public function getTrackingTimeRow($trackId='')
	{
		$totalMinutes = $totalSecondsDiff = 0;
		$tracking = UserTracking::where('id',$trackId)->first();
		if (!empty($tracking)) {
			if (!empty($tracking->out_time) && $tracking->out_time!='') {
				$totalSecondsDiff = abs(strtotime($tracking->out_time) - strtotime($tracking->in_time));
			}
			$totalMinutes = round($totalSecondsDiff/60);
		}
		return $totalMinutes;
	}

	public function getModuleTrackingTime($module='home',$moduleId=0,$watch_type='')
	{
		$totalMinutes = $totalSecondsDiff = 0;
		if ($watch_type=='video') {
			if ($module=='course') {
				$column = 'course_id';
			} elseif ($module=='lesson') {
				$column = 'lession_id';
			} elseif ($module=='topic') {
				$column = 'topic_id';
			} else{
				$column = 'course_id';
			}
			$courseComplete = ContinueStudy::select("course_id", DB::raw('SUM(video_total_time) as total_time') , DB::raw('SUM(video_viewed_time) as total_viewed'))
			->where($column,$moduleId)->where('course_id','>',0)
			->groupBy('course_id')
			->get();
			foreach ($courseComplete as $key => $val) {
				$totalSecondsDiff += abs(($val['total_viewed'] / 1000));
			}
		} else {
			$userTracking = UserTracking::where('module',$module)->where('module_id',$moduleId)->where('watch_type',$watch_type)->orderBy('id', 'DESC')->get();
			if (!empty($userTracking)) {
				foreach ($userTracking as $tracking) {
					if (!empty($tracking->out_time) && $tracking->out_time!='') {
						$totalSecondsDiff += abs(strtotime($tracking->out_time) - strtotime($tracking->in_time));
					}
				}
				//$totalMinutes = round($totalSecondsDiff/60);
			}
		}
		//return $totalSecondsDiff;
		//return sprintf('%d Days %02d Hours %02d Minutes %02d Seconds', $totalSecondsDiff/86400, $totalSecondsDiff/3600%24, $totalSecondsDiff/60%60, $totalSecondsDiff%60);
		return sprintf('%02d Hours %02d Minutes %02d Seconds', $totalSecondsDiff/3600%24, $totalSecondsDiff/60%60, $totalSecondsDiff%60);
	}

	public function getModuleName($trackingId)
	{
		$moduleName = '';
		if ($trackingId > 0) {
			$tracking = UserTracking::where('id',$trackingId)->first();
			if ($tracking->module_id>0) {
				if ($tracking->module=='user') {
					$moduleName = ucwords($tracking->sub_module);
				} elseif($tracking->module=='course') {
					$moduleName = $tracking->course->name;
				} elseif($tracking->module=='lesson') {
					$moduleName = $tracking->lesson->name;
				} elseif($tracking->module=='topic') {
					$moduleName = $tracking->topic->name;
				} elseif($tracking->module=='assignment') {
					$moduleName = $tracking->assignment->title;
				} elseif($tracking->module=='user_assignment') {
					$moduleName = $tracking->user_assignment->assignment->title;
				} elseif($tracking->module=='community_post') {
					$moduleName = $tracking->community_post->title;
				} elseif($tracking->module=='community_post_user') {
					$moduleName = $tracking->community_post_user->title;
				} elseif($tracking->module=='quiz') {
					$moduleName = $tracking->quiz->name;
				} elseif($tracking->module=='exam') {
					$moduleName = $tracking->exam->id;
				} elseif($tracking->module=='q&a') {
					$moduleName = $tracking->question->question;
				} elseif($tracking->module=='subscription') {
					$moduleName = $tracking->subscription->name;
				} elseif($tracking->module=='contactus') {
					$moduleName = $tracking->contactus->name;
				} else {
					$moduleName = $tracking->module_id;
				}
			} else {
				$moduleName = 'Page Listing';
			}
		}
		return $moduleName;
	}

	public function getModuleType($trackingId)
	{
		$classType = $moduleType = '';
		if ($trackingId > 0) {
			$tracking = UserTracking::where('id',$trackingId)->first();
			if ($tracking->module_id>0) {
				if($tracking->module=='course') {
					$classType = $tracking->course->class_type;
				} elseif($tracking->module=='lesson') {
					$classType = $tracking->lesson->courses->class_type;
				} elseif($tracking->module=='topic') {
					$classType = $tracking->topic->courses->class_type;
				} else {
					$classType = '';
				}
			} else {
				$classType = '';
			}
		}
		$moduleType = ($classType!='') ? ($classType==301) ? 'Academic' : 'Skill Development' : '';
		return $moduleType;
	}

	public function getModuleCourseName($moduleName, $moduleId)
	{
		$courseName = '';
		if ($moduleId > 0) {
			if ($moduleName == 'lesson') {
				$module = Lession::where("id", $moduleId)->first();
			} else {
				$module = Chapter::where("id", $moduleId)->first();
			}
			if (!empty($module)) {
				$courseName = ($module->courseId > 0) ? $module->courses->name : '';
			}
		}
		return $courseName;
	}

	public function getModuleLessonName($moduleName='topic', $moduleId)
	{
		$lessonName = '';
		if ($moduleId > 0) {
			if ($moduleName == 'topic') {
				$module = Chapter::where("id", $moduleId)->first();
			} else {
				$module = '';
			}
			if (!empty($module)) {
				$lessonName = ($module->lessionId > 0) ? $module->lession->name : '';
			}
		}
		return $lessonName;
	}

	public function getQuizClassesName($quizId)
	{
		$class_name = '';
		$classIds = array();
		if ($quizId > 0) {
			$getQuiz = Quiz::where("id", $quizId)->first();
			if (!empty($getQuiz)) {
				if (!empty($getQuiz->courses->class_ids)) {
					$classIds = explode(",", $getQuiz->courses->class_ids);
					$classIdsCount = count($classIds);
				}
				$cls = 1;
				$classes = StudentClass::where('status',1)->where('deleted',0)->orderBy('id', 'ASC')->get();
				foreach ($classes as $stclass) {
					if (!empty($getQuiz->courses->class_ids) && in_array($stclass->id, $classIds)) {
						$class_name .= $stclass->class_name;
						if ($cls>0 && $cls<$classIdsCount) {$class_name .= ','; }
						$cls++;
					}
				}
			}
		}
		return $class_name;
	}

	public function getQuizCourseName($quizId)
	{
		$courseName = '';
		if ($quizId > 0) {
			$getQuiz = Quiz::where("id", $quizId)->first();
			if (!empty($getQuiz)) {
				$courseName = ($getQuiz->courseId > 0) ? $getQuiz->courses->name : '';
			}
		}
		return $courseName;
	}

	public function getQuizLessonTopicName($quizId)
	{
		$lessonTopicName = '';
		if ($quizId > 0) {
			$getQuiz = Quiz::where("id", $quizId)->first();
			if (!empty($getQuiz)) {
				if ($getQuiz->topicId > 0) {
					$lessonTopicName = ($getQuiz->topicId > 0) ? $getQuiz->lession->name.' / '.$getQuiz->topic->name : '';
				} else {
					if ($getQuiz->lessionId > 0) {
						$lessonTopicName = ($getQuiz->lessionId > 0) ? $getQuiz->lession->name : '';
					}
				}
			}
		}
		return $lessonTopicName;
	}

	public function getQuizGivenTotal($quizId)
	{
		$quizGivenTotal = 0;
		if ($quizId > 0) {
			$getQuiz = Quiz::where("id", $quizId)->first();
			$courseId = $getQuiz->courseId;
			$lessionId = $getQuiz->lessionId;
			$topicId = $getQuiz->topicId;
			$quizGivenTotal = StudentExam::where("course_id", $courseId)->where("lession_id", $lessionId)->where("topic_id", $topicId)->where("is_complete", 1)->count();
		}
		return $quizGivenTotal;
	}

	public function getQuizAverageTime($quizId)
	{
		$quizAverageTime = 0; // date('H', $timeInSec) .' Hours '. date('i', $timeInSec) .' Minutes '. date('s', $timeInSec) .' Seconds';
		if ($quizId > 0) {
			$getQuiz = Quiz::where("id", $quizId)->first();
			$courseId = $getQuiz->courseId;
			$lessionId = $getQuiz->lessionId;
			$topicId = $getQuiz->topicId;
			$quizGivenExams = StudentExam::where("course_id", $courseId)->where("lession_id", $lessionId)->where("topic_id", $topicId)->where("is_complete", 1)->get();
			$totalCount = count($quizGivenExams);
			$timeInSec = 0;
			foreach ($quizGivenExams as $exam) {
				if (!empty($exam->total_time)) {
					$timeInSec += strtotime($exam->total_time);
				}
			}
			if ($timeInSec > $totalCount) {
				$timeInSec = $timeInSec / $totalCount;
			}
			$quizAverageTime = ($timeInSec > 0) ? date('H', $timeInSec) .' Hours '. date('i', $timeInSec) .' Minutes '. date('s', $timeInSec) .' Seconds' : 0;
		}
		return $quizAverageTime;
	}

	public function getQuizAverageScore($quizId)
	{
		$quizAverageScore = 0;
		$percentage_arr = [];
		if ($quizId > 0) {
			$getQuiz = Quiz::where("id", $quizId)->first();
			if (!empty($getQuiz)) {
				$courseId = $getQuiz->courseId;
				$lessionId = $getQuiz->lessionId;
				$topicId = $getQuiz->topicId;
				$quizExamGiven = StudentExam::where("course_id", $courseId)->where("lession_id", $lessionId)->where("topic_id", $topicId)->where("is_complete", 1)->get();
				if (!empty($quizExamGiven)) {
					foreach ($quizExamGiven as $exam) {
						$examId = $exam->id;
						$passing_percent = $getQuiz->passing_percent;
						$examQuestions = Quizquestions::where("quizId", $getQuiz->id)->get();
						$total_questions = count($examQuestions);
						$total_marking = $score = $right = $wrong = $total_solved = $not_solved = 0;
						foreach ($examQuestions as $key => $val) {
							$total_marking += $val->marking;

							$studentAns = StudentExamAnswer::where("exam_id", $examId)->where("ques_id", $val->id)->first();
							if (!empty($studentAns)) {
								$quizAnswers = Quizoption::where("questionId", $val->id)->orderBy("id", "ASC")->get();
								$answer = '';
								foreach ($quizAnswers as $key1 => $value) {
									if ($key1 == ($val->currect_option - 1)){
										$answer = $value->id;
									}
								}
								if ($answer == $studentAns->answer) {
									$right++;
									$score += $val->marking;
								} else {
									$wrong++;
								}
							}
						}
						$total_solved = $right + $wrong;
						$not_solved = $total_questions - $total_solved;
						$percentage_arr[] = number_format((($score * 100) / $total_marking), 2);
					}
				}
			}
		}
		if (!empty($percentage_arr)) {
			$total = count($percentage_arr);
			$totalPercent = 0;
			foreach ($percentage_arr as $percent) {
				$totalPercent += $percent;
			}
			$quizAverageScore = $totalPercent / $total;
		}
		return number_format($quizAverageScore, 2);
	}

	public function getQuizAverageResult($quizId)
	{
		$quizAverageResult = '';
		if ($quizId > 0) {
			$getQuiz = Quiz::where("id", $quizId)->first();
			$courseId = $getQuiz->courseId;
			$lessionId = $getQuiz->lessionId;
			$topicId = $getQuiz->topicId;
			$quizGivenTotal = StudentExam::where("course_id", $courseId)->where("lession_id", $lessionId)->where("topic_id", $topicId)->where("is_complete", 1)->count();
			$quizPassedTotal = StudentExam::where("course_id", $courseId)->where("lession_id", $lessionId)->where("topic_id", $topicId)->where("is_complete", 1)->whereNotNull("certificate")->count();
			$quizFailedTotal = 0;
			if ($quizGivenTotal > $quizPassedTotal) {
				$quizFailedTotal = $quizGivenTotal - $quizPassedTotal;
			}
			if ($quizPassedTotal > $quizFailedTotal) {
				$quizAverageResult = 'Pass';
			} else {
				$quizAverageResult = 'Fail';
			}
		}
		return $quizAverageResult;
	}

	public function getAssignmentCourseName($assignmentId)
	{
		$courseName = '';
		if ($assignmentId > 0) {
			$assignment = Assignment::where("id", $assignmentId)->first();
			if (!empty($assignment)) {
				$courseName = ($assignment->course_id > 0) ? $assignment->course->name : '';
			}
		}
		return $courseName;
	}

	public function getAssignmentSubmittedTotal($assignmentId)
	{
		$submittedAssignments = 0;
		if ($assignmentId > 0) {
			$submittedAssignments = AssignmentSubmission::where("assignment_id", $assignmentId)->where("user_status", 1)->where("deleted", 0)->orderBy("id","DESC")->count();
		}
		return $submittedAssignments;
	}

	public function userJoinedGroup($groupId, $userId)
	{
		$data = ChatGroupUser::where("group_id", $groupId)->where("user_id", $userId)->first();
		return $result = !empty($data) ? 1 : 0;
	}

	public function getNotificationSent($notif_id)
	{
		$totalUsers = User::where('role_id', 3)->where('status', 1)->where('deleted', 0)->count();
		$sentNotifs = NotificationSend::where('notification_id', $notif_id)->count();
		$sentPercent = ($sentNotifs / $totalUsers) * 100;
		return number_format($sentPercent,2).'%';
	}







	

	public function sendfirbasenotficationadmin($device_token,$title,$click_action,$massage,$user_id,$name)
	{
		try {
			if (!empty($device_token)) {
				$massage = $massage.'.';
				$fcmUrl = 'https://fcm.googleapis.com/fcm/send';

				$notification = [
					'title' =>$title,
					'body' => $massage,
					'icon' =>'myIcon',
					'sound' => 'mySound.wav',
					'click_action' => $click_action,
					'UserId' => $user_id,
					'name' => $name,
					'badge' => '1',
				];
				
				$userdata = [
					'user_id' => $user_id,
					'name' => $name,
				];
				
				$extraNotificationData = ["message" => $notification,"moredata" =>$userdata];
				
				$fcmNotification = [
					//'registration_ids' => $tokenList, //multple token array
					'to'        => $device_token, //single token
					'notification' => $notification,
					'data' => $notification
				];

				

				$headers = [
					'Authorization: key=AAAA3RSzpIM:APA91bEPBLWyaUhgoa9PZZB4Ho8NAMlY4j_3JY1y-Ubt1ZexIEesBBfxmGN7BMWRR8ikjMuDZdfv8LsPOhfqDn3deERO9W9t55qHQtVsLp04otAfdscWlBVMwBCAkyIhZJIo2ddHJa5g',
					'Content-Type: application/json'
				];


				$ch = curl_init();
				curl_setopt($ch, CURLOPT_URL, $fcmUrl);
				curl_setopt($ch, CURLOPT_POST, true);
				curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
				curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
				curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
				curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fcmNotification));
				$result = curl_exec($ch);

				curl_close($ch);

				$r = json_decode($result);

				return $r;
				// dd($r);
				// return $this->sendResponse($r, 'Massage send successfully');
			} else {
				return 'token not found';
				// return $this->sendError('User name not found');
			}
		}catch (\Exception $e) {
			return $e->getMessage();
		}
	}

	public function distance($lat1, $lon1, $lat2, $lon2, $unit) {
		if (($lat1 == $lat2) && ($lon1 == $lon2)) {
			return 0;
		} else {
			$theta = $lon1 - $lon2;
			$dist = sin(deg2rad($lat1)) * sin(deg2rad($lat2)) +  cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * cos(deg2rad($theta));
			$dist = acos($dist);
			$dist = rad2deg($dist);
			$miles = $dist * 60 * 1.1515;
			$unit = strtoupper($unit);

			if ($unit == "K") {
				return ($miles * 1.609344);
			} else if ($unit == "N") {
				return ($miles * 0.8684);
			} else {
				return $miles;
			}
		}
	}


	 public function sendMail($email, $stubject = NULL, $message = NULL)
    {

        require base_path("vendor/autoload.php");
        $mail = new PHPMailer(true);     // Passing `true` enables exceptions
        try {
            $mail->SMTPDebug = 0;
            $mail->isSMTP();
            $mail->Host = "smtp.gmail.com";
            $mail->Port = 587;
            $mail->SMTPSecure = "tls";
            $mail->SMTPAuth = true;
            $mail->Username = "<EMAIL>";
            $mail->Password = "audnjvohywazsdqo";
            $mail->addAddress($email, "User Name");
            $mail->Subject = $stubject;
            $mail->isHTML();
            $mail->Body = $message;
            $mail->setFrom("<EMAIL>");
            $mail->FromName = "Guru At Home";

            if ($mail->send()) {
                return 1;
            } else {
                return 0;
            }
        } catch (Exception $e) {
            return 0;
        }
    }

    public function timezone()
	{
		return $timezones = array(
		    // 'Pacific/Midway'       => "(GMT-11:00) Midway Island",
		    // 'US/Samoa'             => "(GMT-11:00) Samoa",
		    // 'US/Hawaii'            => "(GMT-10:00) Hawaii",
		    // 'US/Alaska'            => "(GMT-09:00) Alaska",
		    // 'US/Pacific'           => "(GMT-08:00) Pacific Time (US &amp; Canada)",
		    // 'America/Tijuana'      => "(GMT-08:00) Tijuana",
		    // 'US/Arizona'           => "(GMT-07:00) Arizona",
		    // 'US/Mountain'          => "(GMT-07:00) Mountain Time (US &amp; Canada)",
		    // 'America/Chihuahua'    => "(GMT-07:00) Chihuahua",
		    // 'America/Mazatlan'     => "(GMT-07:00) Mazatlan",
		    // 'America/Mexico_City'  => "(GMT-06:00) Mexico City",
		    // 'America/Monterrey'    => "(GMT-06:00) Monterrey",
		    // 'Canada/Saskatchewan'  => "(GMT-06:00) Saskatchewan",
		    // 'US/Central'           => "(GMT-06:00) Central Time (US &amp; Canada)",
		    // 'US/Eastern'           => "(GMT-05:00) Eastern Time (US &amp; Canada)",
		    // 'US/East-Indiana'      => "(GMT-05:00) Indiana (East)",
		    // 'America/Bogota'       => "(GMT-05:00) Bogota",
		    // 'America/Lima'         => "(GMT-05:00) Lima",
		    // 'America/Caracas'      => "(GMT-04:30) Caracas",
		    // 'Canada/Atlantic'      => "(GMT-04:00) Atlantic Time (Canada)",
		    // 'America/La_Paz'       => "(GMT-04:00) La Paz",
		    // 'America/Santiago'     => "(GMT-04:00) Santiago",
		    // 'Canada/Newfoundland'  => "(GMT-03:30) Newfoundland",
		    // 'America/Buenos_Aires' => "(GMT-03:00) Buenos Aires",
		    // 'Greenland'            => "(GMT-03:00) Greenland",
		    // 'Atlantic/Stanley'     => "(GMT-02:00) Stanley",
		    // 'Atlantic/Azores'      => "(GMT-01:00) Azores",
		    // 'Atlantic/Cape_Verde'  => "(GMT-01:00) Cape Verde Is.",
		    // 'Africa/Casablanca'    => "(GMT) Casablanca",
		    // 'Europe/Dublin'        => "(GMT) Dublin",
		    // 'Europe/Lisbon'        => "(GMT) Lisbon",
		    // 'Europe/London'        => "(GMT) London",
		    // 'Africa/Monrovia'      => "(GMT) Monrovia",
		    // 'Europe/Amsterdam'     => "(GMT+01:00) Amsterdam",
		    // 'Europe/Belgrade'      => "(GMT+01:00) Belgrade",
		    // 'Europe/Berlin'        => "(GMT+01:00) Berlin",
		    // 'Europe/Bratislava'    => "(GMT+01:00) Bratislava",
		    // 'Europe/Brussels'      => "(GMT+01:00) Brussels",
		    // 'Europe/Budapest'      => "(GMT+01:00) Budapest",
		    // 'Europe/Copenhagen'    => "(GMT+01:00) Copenhagen",
		    // 'Europe/Ljubljana'     => "(GMT+01:00) Ljubljana",
		    // 'Europe/Madrid'        => "(GMT+01:00) Madrid",
		    // 'Europe/Paris'         => "(GMT+01:00) Paris",
		    // 'Europe/Prague'        => "(GMT+01:00) Prague",
		    // 'Europe/Rome'          => "(GMT+01:00) Rome",
		    // 'Europe/Sarajevo'      => "(GMT+01:00) Sarajevo",
		    // 'Europe/Skopje'        => "(GMT+01:00) Skopje",
		    // 'Europe/Stockholm'     => "(GMT+01:00) Stockholm",
		    // 'Europe/Vienna'        => "(GMT+01:00) Vienna",
		    // 'Europe/Warsaw'        => "(GMT+01:00) Warsaw",
		    // 'Europe/Zagreb'        => "(GMT+01:00) Zagreb",
		    // 'Europe/Athens'        => "(GMT+02:00) Athens",
		    // 'Europe/Bucharest'     => "(GMT+02:00) Bucharest",
		    // 'Africa/Cairo'         => "(GMT+02:00) Cairo",
		    // 'Africa/Harare'        => "(GMT+02:00) Harare",
		    // 'Europe/Helsinki'      => "(GMT+02:00) Helsinki",
		    // 'Europe/Istanbul'      => "(GMT+02:00) Istanbul",
		    // 'Asia/Jerusalem'       => "(GMT+02:00) Jerusalem",
		    // 'Europe/Kiev'          => "(GMT+02:00) Kyiv",
		    // 'Europe/Minsk'         => "(GMT+02:00) Minsk",
		    // 'Europe/Riga'          => "(GMT+02:00) Riga",
		    // 'Europe/Sofia'         => "(GMT+02:00) Sofia",
		    // 'Europe/Tallinn'       => "(GMT+02:00) Tallinn",
		    // 'Europe/Vilnius'       => "(GMT+02:00) Vilnius",
		    // 'Asia/Baghdad'         => "(GMT+03:00) Baghdad",
		    // 'Asia/Kuwait'          => "(GMT+03:00) Kuwait",
		    // 'Africa/Nairobi'       => "(GMT+03:00) Nairobi",
		    // 'Asia/Riyadh'          => "(GMT+03:00) Riyadh",
		    // 'Europe/Moscow'        => "(GMT+03:00) Moscow",
		    // 'Asia/Tehran'          => "(GMT+03:30) Tehran",
		    // 'Asia/Baku'            => "(GMT+04:00) Baku",
		    // 'Europe/Volgograd'     => "(GMT+04:00) Volgograd",
		    // 'Asia/Muscat'          => "(GMT+04:00) Muscat",
		    // 'Asia/Tbilisi'         => "(GMT+04:00) Tbilisi",
		    // 'Asia/Yerevan'         => "(GMT+04:00) Yerevan",
		    // 'Asia/Kabul'           => "(GMT+04:30) Kabul",
		    // 'Asia/Karachi'         => "(GMT+05:00) Karachi",
		    // 'Asia/Tashkent'        => "(GMT+05:00) Tashkent",
		    'Asia/Kolkata'         => "(GMT+05:30) Kolkata",
		    // 'Asia/Kathmandu'       => "(GMT+05:45) Kathmandu",
		    // 'Asia/Yekaterinburg'   => "(GMT+06:00) Ekaterinburg",
		    // 'Asia/Almaty'          => "(GMT+06:00) Almaty",
		    // 'Asia/Dhaka'           => "(GMT+06:00) Dhaka",
		    // 'Asia/Novosibirsk'     => "(GMT+07:00) Novosibirsk",
		    // 'Asia/Bangkok'         => "(GMT+07:00) Bangkok",
		    // 'Asia/Jakarta'         => "(GMT+07:00) Jakarta",
		    // 'Asia/Krasnoyarsk'     => "(GMT+08:00) Krasnoyarsk",
		    // 'Asia/Chongqing'       => "(GMT+08:00) Chongqing",
		    // 'Asia/Hong_Kong'       => "(GMT+08:00) Hong Kong",
		    // 'Asia/Kuala_Lumpur'    => "(GMT+08:00) Kuala Lumpur",
		    // 'Australia/Perth'      => "(GMT+08:00) Perth",
		    // 'Asia/Singapore'       => "(GMT+08:00) Singapore",
		    // 'Asia/Taipei'          => "(GMT+08:00) Taipei",
		    // 'Asia/Ulaanbaatar'     => "(GMT+08:00) Ulaan Bataar",
		    // 'Asia/Urumqi'          => "(GMT+08:00) Urumqi",
		    // 'Asia/Irkutsk'         => "(GMT+09:00) Irkutsk",
		    // 'Asia/Seoul'           => "(GMT+09:00) Seoul",
		    // 'Asia/Tokyo'           => "(GMT+09:00) Tokyo",
		    // 'Australia/Adelaide'   => "(GMT+09:30) Adelaide",
		    // 'Australia/Darwin'     => "(GMT+09:30) Darwin",
		    // 'Asia/Yakutsk'         => "(GMT+10:00) Yakutsk",
		    // 'Australia/Brisbane'   => "(GMT+10:00) Brisbane",
		    // 'Australia/Canberra'   => "(GMT+10:00) Canberra",
		    // 'Pacific/Guam'         => "(GMT+10:00) Guam",
		    // 'Australia/Hobart'     => "(GMT+10:00) Hobart",
		    // 'Australia/Melbourne'  => "(GMT+10:00) Melbourne",
		    // 'Pacific/Port_Moresby' => "(GMT+10:00) Port Moresby",
		    // 'Australia/Sydney'     => "(GMT+10:00) Sydney",
		    // 'Asia/Vladivostok'     => "(GMT+11:00) Vladivostok",
		    // 'Asia/Magadan'         => "(GMT+12:00) Magadan",
		    // 'Pacific/Auckland'     => "(GMT+12:00) Auckland",
		    // 'Pacific/Fiji'         => "(GMT+12:00) Fiji",
		);
	}

public function send_whatsapp_message($country, $phone, $template_name, $body_values) {

    $client = new Client();
    $headers = [
        'Authorization' => 'Basic NnRrdE9SZktmUExKNkNWV0NHOWpNVVgxT3VOc21qSENpU2JNWUs1ekl6UTo=',
        'Content-Type' => 'application/json'
    ];

    $body = [
        "countryCode" => $country,
        "phoneNumber" => $phone,
        "type" => 'Template',
        "template" => [
            "name" => $template_name,
            "languageCode" => 'en',
            "bodyValues" => $body_values
        ]
    ];

    $body_json = json_encode($body);
    // dd($body_json);
    $request = new Request('POST', 'https://api.interakt.ai/v1/public/message/', $headers, $body_json);
     try {
        $response = $client->sendAsync($request)->wait();
        $statusCode = $response->getStatusCode();
        
        if ($statusCode != 201) {
            // Status code is 200, which means the request was successful
            // echo $response->getBody();
            return false;
        } 
         return true;
        
    } catch (\Exception $e) {
        // Handle any exceptions that occur during the request
        // echo 'Exception: ' . $e->getMessage();
        return false;
    }
    
     // dd($res);
    
    echo $res->getBody();
}
}