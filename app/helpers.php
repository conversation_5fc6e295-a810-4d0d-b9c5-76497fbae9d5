<?php
namespace App\Http;

use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\SMTP;
use P<PERSON><PERSON>ailer\PHPMailer\Exception;
use App\Models\TeacherAvailabiltyOfSlots;
use App\Models\User;

use DB;

class Helper
{
    public function test()
    {
        echo "test";

    }
public function sendmailtoemp1($stubject,$message,$too){

                    $to =$too;
                    $subject = $stubject;
                    $from='<EMAIL>';
                    $headers = "From: " . strip_tags($from) . "\r\n";
                    $headers .= "MIME-Version: 1.0\r\n";
                    $headers .= "Content-Type: text/html; charset=ISO-8859-1\r\n";
                    $curl = curl_init();
                    curl_setopt_array($curl, array(
                    CURLOPT_URL => 'https://api.sendgrid.com/v3/mail/send',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS =>'{"personalizations":[{"to":[{"email":"'.$to.'","name":"Guruathome"}],"subject":"'.$subject.'"}],"content": [{"type": "text/html", "value": "'.$message.'"}],"from":{"email":"<EMAIL>","name":"Guruathome"}}',
                    CURLOPT_HTTPHEADER => array(
                    'Authorization: Bearer *********************************************************************',
                    'Content-Type: application/json'
                    ),
                    ));
                    $response = curl_exec($curl);
                    // print_r($response);die;
                    curl_close($curl);
         return 1;
        }
    public  function get_availableTeacherslot($userId,$day,$start_time,$end_time)
    {
        $sql ="SELECT * FROM `teacher_availabilty_of_slots` inner join teacher_availabilty_of_slots_details on teacher_availabilty_of_slots_details.teacher_availabilty_of_slots_id = teacher_availabilty_of_slots.id where teacher_availabilty_of_slots.user_id = $userId and teacher_availabilty_of_slots.day =$day";
    
        $result = DB::select($sql);
        if(!is_null($result))
        {
            return $result;
        }
        else
        {
            return false;
        }
    }

public function sendmailtoemp($too,$subject,$message)
    {
        $to =$too;
        $subject = $subject;
        $fromEmail = '<EMAIL>';
        $fromName = 'Guru At Home';
        $apiKey = 'mlsn.6dfe534bb39a770d4a94760d2a660fc28cce2492aec5a515267409d42e5c5737';
        $url = 'https://api.mailersend.com/v1/email';
        $payload = [
            'from' => [
                'email' => $fromEmail,
                'name' => $fromName
            ],
            'to' => [
                [
                    'email' => $to,
                    'name' => 'Recipient Name'
                ]
            ],
            'subject' => $subject,
            'html' => $message
        ];
        $client = new Client();

        try {
            $response = $client->post($url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $apiKey,
                    'Content-Type' => 'application/json'
                ],
                'json' => $payload
            ]);
            $responseBody = json_decode($response->getBody(), true);
            return response()->json(['message' => 'Email sent successfully!', 'data' => $responseBody]);

        } catch (\Exception $e) {
            return response()->json(['message' => 'Failed to send email.', 'error' => $e->getMessage()], 500);
        }
    }

}

public function send_whatsapp_message($country, $phone, $template_name, $body_values) {

    $client = new Client();
    $headers = [
        'Authorization' => 'Basic NnRrdE9SZktmUExKNkNWV0NHOWpNVVgxT3VOc21qSENpU2JNWUs1ekl6UTo=',
        'Content-Type' => 'application/json'
    ];

    $body = [
        "countryCode" => $country,
        "phoneNumber" => $phone,
        "type" => 'Template',
        "template" => [
            "name" => $template_name,
            "languageCode" => 'en',
            "bodyValues" => $body_values
        ]
    ];

    $body_json = json_encode($body);
    // dd($body_json);
    $request = new Request('POST', 'https://api.interakt.ai/v1/public/message/', $headers, $body_json);
    $res = $client->sendAsync($request)->wait();
    
    echo $res->getBody();
}

}
?>