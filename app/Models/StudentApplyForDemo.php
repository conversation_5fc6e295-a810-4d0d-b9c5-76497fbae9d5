<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

use App\Models\StudentClass;
use App\Models\Subject;
use App\Models\Category;
use App\Models\Courses;
use App\User;
use App\Models\StudentBookingFrees;
use App\Models\TeacherAvailabiltyOfSlotsDetails;

class StudentApplyForDemo extends Model
{
	protected $table = 'studentapplyfordemo';
    protected $guarded=[];
	public function getUser(){
        return $this->belongsTo(User::class,'user_id');
    }
    public function class(){
        return $this->belongsTo(StudentClass::class,'class_id');
    }
    public function subject(){
        return $this->belongsTo(Subject::class,'subject_id');
    }
    public function category(){
        return $this->belongsTo(Category::class,'category_id');
    }
    public function courses(){
        return $this->belongsTo(Courses::class,'course_id');
    }
    public function get_StudentBookingFrees(){
        return $this->belongsTo(StudentBookingFrees::class,'id');
    }
    public function get_TeacherAvailabiltyOfSlotsDetails(){
        return $this->belongsTo(TeacherAvailabiltyOfSlotsDetails::class,'id');
    }
	
}


