<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Model;
use App\Models\StudentClass;
use App\Models\Category;
class Courses extends Model
{

    public function subject_data()
    {
        return $this->belongsTo(Subject::class, 'subject_id');
    }
    
    public function course_features_data()
    {
        return $this->hasMany(Coursefeature::class, 'courseId', 'id');
    }

    public function course_faqs_data()
    {
        return $this->hasMany(Coursefeq::class, 'courseId', 'id');
    }
    
    public function course_pdfs_data()
    {
        return $this->hasMany(CoursePdf::class, 'course_id', 'id')->where('status',1)->orderBy('sort_id', 'ASC');
    }
    
    public function class(){
        return $this->belongsTo(StudentClass::class,'class_id');
    }
    public function category(){
        return $this->belongsTo(Category::class,'category_id');
    }
}
