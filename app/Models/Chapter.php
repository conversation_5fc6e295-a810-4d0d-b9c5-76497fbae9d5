<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\Courses;
use App\Models\Lession;

class Chapter extends Model
{
	protected $table = 'lession_chapters';
	
    public function courses(){
        return $this->hasOne('App\Models\Courses','id','courseId');
    }

	/*public function lession()
	{
		return $this->belongsTo(Lession::class, 'lessionId');
	}*/
    
    public function lession(){
        return $this->hasOne('App\Models\Lession','id','lessionId');
    }
}
