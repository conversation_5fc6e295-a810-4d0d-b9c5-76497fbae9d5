<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\PostEntry;
use App\Models\User;

class PostEntryComment extends Model
{
    protected $table = 'post_entries_comments';
    protected $primaryKey = 'id';
    
    public function post_entry_data(){
        return $this->hasOne('App\Models\PostEntry','id','post_entry_id');
    }

    public function user_data(){
        return $this->hasOne('App\Models\User','id','user_id');
    }
}
