<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\CommunityPost;
use App\Models\User;

class PostEntry extends Model
{
    protected $primaryKey = 'id';
    
    public function community_post_data(){
        return $this->hasOne('App\Models\CommunityPost','id','community_post_id');
    }

    public function user_data(){
        return $this->hasOne('App\Models\User','id','user_id');
    }

    public function post_likes_data(){
        return $this->hasMany(PostEntryLike::class, 'post_entry_id', 'id');
    }

    public function post_comments_data(){
        return $this->hasMany(PostEntryComment::class, 'post_entry_id', 'id');
    }
}
