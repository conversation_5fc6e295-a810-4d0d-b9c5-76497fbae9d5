<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\Assignment;
use App\Models\User;

class AssignmentSubmission extends Model
{
    public function assignment(){
        return $this->hasOne('App\Models\Assignment','id','assignment_id');
    }

    public function user(){
        return $this->hasOne('App\Models\User','id','user_id');
    }
    
    public function teacher(){
        return $this->hasOne('App\Models\User','id','assigned_to');
    }
}
