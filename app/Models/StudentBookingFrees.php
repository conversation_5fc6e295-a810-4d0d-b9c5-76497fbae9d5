<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

use App\Models\StudentClass;
use App\Models\StudentApplyForDemo;
class StudentBookingFrees extends Model
{
	protected $table = 'student_booking_frees';

	protected $guarded = [];
	
    public function class(){
        return $this->belongsTo(StudentClass::class,'class_id');
    }

	public function studentApplyForDemo()
	{
		return $this->belongsTo(StudentApplyForDemo::class,'studentapplyformdemo_id');
	}
}