<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserTracking extends Model
{

	protected $table = 'user_tracking';

	public function user()
	{
		return $this->hasOne(User::class, 'id', 'user_id');
	}

	public function course()
	{
		return $this->hasOne(Courses::class, 'id', 'module_id');
	}

	public function lesson()
	{
		return $this->hasOne(Lession::class, 'id', 'module_id');
	}

	public function topic()
	{
		return $this->hasOne(Chapter::class, 'id', 'module_id');
	}

	public function assignment()
	{
		return $this->hasOne(Assignment::class, 'id', 'module_id');
	}

	public function user_assignment()
	{
		return $this->hasOne(AssignmentSubmission::class, 'id', 'module_id');
	}

	public function community_post()
	{
		return $this->hasOne(CommunityPost::class, 'id', 'module_id');
	}

	public function community_post_user()
	{
		return $this->hasOne(PostEntry::class, 'id', 'module_id');
	}

	public function quiz()
	{
		return $this->hasOne(Quiz::class, 'id', 'module_id');
	}

	public function exam()
	{
		return $this->hasOne(StudentExam::class, 'id', 'module_id');
	}

	public function question()
	{
		return $this->hasOne(QuestionAsk::class, 'id', 'module_id');
	}

	public function subscription()
	{
		return $this->hasOne(Subscription::class, 'id', 'module_id');
	}

	public function contactus()
	{
		return $this->hasOne(Contactus::class, 'id', 'module_id');
	}

}