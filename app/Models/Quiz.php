<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Model;
use App\Models\Courses;
use App\Models\Lession;

class Quiz extends Model
{
	protected $table = 'quizs';

	public function courses(){
        return $this->hasOne('App\Models\Courses','id','courseId');
    }
    public function lession(){
        return $this->hasOne('App\Models\Lession','id','lessionId');
    }
    public function topic(){
        return $this->hasOne('App\Models\Chapter','id','topicId');
    }
     
}
