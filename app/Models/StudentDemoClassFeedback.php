<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\User;

use App\Models\StudentClass;
use App\Models\Subject;
use App\Models\Category;
use App\Models\Courses;

class StudentDemoClassFeedback extends Model
{
    protected $table= 'student_demo_class_feedback';
    protected $guarded=[];

    public function getTeacher(){
        return $this->belongsTo(User::class,'teacher_id');
    }
    public function class(){
        return $this->belongsTo(StudentClass::class,'class_id');
    }
    public function subject(){
        return $this->belongsTo(Subject::class,'subject_id');
    }
    public function category(){
        return $this->belongsTo(Category::class,'category_id');
    }
    public function courses(){
        return $this->belongsTo(Courses::class,'course_id');
    }
}
