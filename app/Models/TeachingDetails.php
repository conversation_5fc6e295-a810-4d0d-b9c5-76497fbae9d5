<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use  App\Models\Category;
use App\Models\StudentClass;
use App\Models\Subject;
use App\Models\Courses;
use App\Models\TeacherAvailabiltyOfSlots;

class TeachingDetails extends Model
{
    protected $table= 'teaching_details';
    protected $guarded=[];


    public function category(){
        return $this->belongsTo(Category::class,'class_or_category_id');
    }

    
    public function class(){
        return $this->belongsTo(StudentClass::class,'class_or_category_id');
    }

    public function subject(){
        return $this->belongsTo(Subject::class,'subject_or_course_id');
    }

    public function courses(){
        return $this->belongsTo(Courses::class,'subject_or_course_id');
    }
    
    public function get_TeacherAvailabiltyOfSlots()
    {
        return $this->belongsTo(TeacherAvailabiltyOfSlots::class,'user_id');
    }
}
