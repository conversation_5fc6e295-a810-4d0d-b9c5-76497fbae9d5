<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use  App\Models\Category;
use  App\Models\SubCategory;
use App\Models\StudentClass;
class Subject extends Model
{
	protected $table = 'subjects';

	public function category(){
        return $this->belongsTo(Category::class,'category_id');
    }

	public function sub_category(){
        return $this->belongsTo(SubCategory::class,'sub_category_id');
    }
    public function class(){
        return $this->belongsTo(StudentClass::class,'class_id');
    }
	
}
