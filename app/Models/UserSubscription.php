<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserSubscription extends Model
{

	protected $table = 'user_subscriptions';

	public function user()
	{
		return $this->hasOne(User::class, 'id', 'user_id');
	}

	public function subscription()
	{
		return $this->hasOne(Subscription::class, 'id', 'subscription_id');
	}

	public function plan_data()
	{
		return $this->hasOne(Subscription::class, 'id', 'subscription_id');
	}

}