<?php
namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;
use Hash;
use App\Models\StudentClass;
use App\Models\Profile_details;

/**
 * Class User
 *
 * @package App\Models
 * @property string $name
 * @property string $email
 * @property string $password
 * @property string $remember_token
*/


use Tymon\JWTAuth\Contracts\JWTSubject;
    class User extends Authenticatable implements JWTSubject


//class User extends Authenticatable
{
    use Notifiable;
    use HasRoles;   

    


    protected $table = 'users';

    protected $fillable = [
        'role_id', 'name', 'email', 'country_code', 'country_flag', 'phone', 'password', 'userpass', 'image', 'gender', 'dob', 'board_id', 'class_id', 'school_college', 'address', 'city', 'postal_code', 'state', 'parents_phone', 'allow_courses', 'allow_groups', 'franchise_user_id', 'refer_code', 'remember_token', 'api_token', 'deviceToken', 'deviceType', 'isLogin', 'otp_match', 'status', 'deleted'
    ];

    public function franchise_data()
    {
        return $this->hasOne(Franchise::class, 'user_id', 'franchise_user_id');
    }

    public function subscription_data()
    {
        return $this->hasOne(UserSubscription::class, 'user_id', 'id')->where('paymentStatus', 1)->orderBy('id', 'DESC');
    }

    public function class_data()
    {
        return $this->hasOne('App\Models\StudentClass', 'id', 'class_id');
    }

    public function user_points_debit()
    {
        return $this->hasMany(UserPoint::class, 'user_id')->where('transaction_type',config('constant.TRANSACTION_TYPE.DEBIT'));
    }

    public function user_points_credit()
    {
        return $this->hasMany(UserPoint::class, 'user_id')->where('transaction_type',config('constant.TRANSACTION_TYPE.CREDIT'));
    }

    /**
     * Hash password
     * @param $input
     */
    public function setPasswordAttribute($input)
    {
        if ($input)
            $this->attributes['password'] = app('hash')->needsRehash($input) ? Hash::make($input) : $input;
    }
    
    
    public function role()
    {
        return $this->belongsToMany(Role::class, 'role_user');
    }

    public function getrole()
    {
        return $this->hasMany(Role::class, 'role_user');
    }

    public function generateToken()
    {
        $this->api_token = str_random(60);
        $this->save();

        return $this->api_token;
    }


     public function user_profile_details()
    {
        return $this->hasMany(Profile_details::class, 'user_id');
    }

     public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     *
     * @return array
     */
    public function getJWTCustomClaims()
    {
        return [];
    }


    
    
    
}
