<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ChatMessage extends Model
{
	use SoftDeletes;
	
	protected $fillable = [
		'parent_id','group_id','user_id','image','video','message','status'
	];

	public function user_data(){
		return $this->hasOne(User::class,'id','user_id');
	}

	public function group_data(){
		return $this->hasOne(ChatGroup::class,'id','group_id');
	}

	public function oldmsg_data(){
		return $this->hasOne(ChatMessage::class,'id','parent_id');
	}
}
