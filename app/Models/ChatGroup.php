<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ChatGroup extends Model
{
    use SoftDeletes;
    
    protected $fillable = [
        'class_type','class_ids','type','title','image','description','status','sort_id'
    ];

    public function joined_user_data(){
        return $this->hasMany(ChatGroupUser::class, 'group_id', 'id');
    }

    public function chat_messages_data(){
        return $this->hasMany(ChatMessage::class, 'group_id', 'id');
    }
}
