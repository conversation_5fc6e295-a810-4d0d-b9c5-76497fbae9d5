<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\Courses;
use App\Models\User;

class Assignment extends Model
{
    public function user(){
        return $this->hasOne('App\Models\User','id','added_by');
    }

    public function course(){
        return $this->hasOne('App\Models\Courses','id','course_id');
    }

    public function submitted_data()
    {
        return $this->hasMany(AssignmentSubmission::class, 'assignment_id')->where('user_status',1);
    }
}
