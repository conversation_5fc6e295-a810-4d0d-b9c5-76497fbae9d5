<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;
use PDF;

class TestEmail extends Mailable
{
    use Queueable, SerializesModels;

    public $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function build()
    {
    //    dd($this->data['invoice_number']);
        $address = '<EMAIL>';
        $subject = 'GURU AT HOME';
        $name = 'Invoice-'.$this->data['invoice_number'];
        $data=array(
                'logo' =>$this->data['logo'],
                'invoiceDate'=>$this->data['invoiceDate'],
                'name'=>$this->data['name'],
                'address'=>$this->data['address'],
                'phone' =>$this->data['phone'],
                'no_of_class' =>$this->data['no_of_class'],
                'fees' =>$this->data['fees'],
                'vat' =>$this->data['vat'],
                'invoice_number' =>$this->data['invoice_number'],
                'actual_price'=>$this->data['actual_price'],
                'gst_amount'=>$this->data['gst_amount'],
                'amount_to_pay'=>$this->data['amount_to_pay'],
                'currency'=>$this->data['currency'],
                'parent_name'=>$this->data['parent_name'],
                'course_and_subject_name'=>$this->data['course_and_subject_name'],
                'discount_amount'=>$this->data['discount_amount'],
                'per_session_amount'=>$this->data['per_session_amount']
        );    
        $pdf = PDF::loadView('email.invoice', $data);
        $path = public_path('invoice');
        $fileName =  'invoiced.pdf' ;

        return  $this->view('email.invoice')
                ->from($address, $name)
                ->subject($subject)
                
                ->attachData($pdf->output(), $name.".pdf")
                ->with([
                   'logo' =>$this->data['logo'],
                    'invoiceDate'=>$this->data['invoiceDate'],
                    'name'=>$this->data['name'],
                    'address'=>$this->data['address'],
                    'phone' =>$this->data['phone'],
                    'no_of_class' =>$this->data['no_of_class'],
                    'fees' =>$this->data['fees'],
                    'vat' =>$this->data['vat'],
                    'invoice_number' =>$this->data['invoice_number'],
                    'actual_price'=>$this->data['actual_price'],
                    'gst_amount'=>$this->data['gst_amount'],
                    'amount_to_pay'=>$this->data['amount_to_pay'],
                    'currency'=>$this->data['currency'],
                    'parent_name'=>$this->data['parent_name'],
                    'course_and_subject_name'=>$this->data['course_and_subject_name'],
                    'discount_amount'=>$this->data['discount_amount'],
                    'per_session_amount'=>$this->data['per_session_amount']
                ]);
    }
}