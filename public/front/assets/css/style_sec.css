
/* ============================================================
  GLOBAL
============================================================ */
	
	.effects .img {
	  position: relative;
	  float: left;
	  width: 100%;
	  overflow: hidden;
	}
	.effects .img:nth-child(n) {
	  margin-right: 0;
	}
	.effects .img:first-child {
	  margin-left: 0;
	}
	.effects .img:last-child {
	  margin-right: 0;
	}
	.effects .img img {
	  display: block;
	  margin: 0;
	  padding: 0;
	  max-width: 100%;
	  height: auto;
	}
	.overlay {
	  display: block;
	  position: absolute;
	  z-index: 20;
	  background: rgba(0, 0, 0, 0.5);
	  overflow: hidden;
	  -webkit-transition: all 0.5s;
	  -moz-transition: all 0.5s;
	  -o-transition: all 0.5s;
	  transition: all 0.5s;
	}
	
	.float{
	position:fixed;
	width:60px;
	height:60px;
	bottom:40px;
	right:40px;
	background-color:#25d366;
	color:#FFF;
	border-radius:50px;
	text-align:center;
  font-size:30px;
	box-shadow: 2px 2px 3px #999;
  z-index:100;
}

.my-float{
	margin-top:16px;
}

/* ============================================================
  EFFECT 3 - SLIDE IN LEFT
============================================================ */
	#grid .overlay {
	  top: 0;
	  bottom: 0;
	  left: 0;
	  width: 0;
	  height: 0;
	}
	
	 #grid .img.hover .overlay  {
	  width: 100%;
	  height: 100%;
	}

	.social-media {
		list-style: none;
		display: inline-block;
		margin-left:10px;
		
	}
	

.outer {
    display: table;
    position: absolute;
    height: 100%;
    width: 100%;
	
}

.middle {
    display: table-cell;
    vertical-align: middle;
}

.inner {
    margin-left: auto;
    margin-right: auto; 
   }

