/*================= MASTER STYLESHEET =================================

	Project     :	LEAD Page
	Version     :	1.0
	Last Change : 	17/06/2017
	Primary Use :   LEAD HTML Page

=======================================================================*/
/*========================================== LAYOUT ===================================================================

		1.GLOBAL CSS START
		2.NAVIGATION
		3.HEADER-FORM
		4.HEADER-DEFAULT
		5.HEADER-SLIDER
		6.HEADER VIDEO
		7.OUR SERVICES
		8.OUR AWESOME SERVICES
		9.COUNTER
		10.WHAT WE DO
		11.GALLERY
		12.MARKET ANALYSIS
		13.OUR TEAM
		14.FREQUENTLY ASKED QUESTIONS
		15.BUILDER
		16.PRICE TABLE
		17.LATEST BLOG
		18.TWO-COLUMN-SECTION
		19.EMAIL SUBSCRIPTION
		20.TESTIMONIAL
		21.FOOTER-MAP
		22.FOOTER

=========================================================================================================*/
/*========================================== COLOR CODES ==============================================


	Theme Color	       :#3598db
	Main Heading Color :#333333
	SubHeading Color   :#333333
	Content Color      :#666666

=====================================================================================================*/
/*========================================== TYPOGRAPHY ==============================================
	
	HEADING    :font-family: 'Raleway', Helvathika, Arial sans-serif;
	CONTENT    :font-family: 'Open Sans', Helvathika, Arial sans-serif;

====================================================================================================*/
/*========================================== 1.GLOBAL CSS START ===========================================*/
/* CUSTOM STYLES */

@import url('https://fonts.googleapis.com/css?family=Raleway:300,300i,400,400i,500,500i,600,600i,700,700i,800,800i,900,900i');
@import url('https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i,800,800i');
body {
    font-family: 'Open Sans', Helvathika, Arial, sans-serif;
    font-size: 14px;
}
a {
    outline: none !important;
    text-decoration: none !important;
}
h1,
h2,
h3 {
    margin: 0px;
}
/* elements */

p {
    margin: 0px;
}
h1,
h2,
h3,
h4,
.header-head1 {
    font-family: 'Raleway', Helvathika, Arial, sans-serif;
}
h1,
h2,
h3,
h4,
.menu-fs,
.header-head1,
.header-head2,
.bgimage-head {
    text-transform: capitalize;
}
h1,
h2,
h3,
h4 {
    color: #333333;
    letter-spacing: 1px;
}
h1 {
    font-size: 34px;
    font-weight: 600;
    line-height: 42px;
    margin-bottom: 40px;
}
h2 {
    font-size: 24px;
    font-weight: 600;
    line-height: 32px;
}
h3 {
    font-size: 20px;
    font-weight: 600;
    line-height: 26px;
}
h4 {
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
}
.h2-bottom {
    margin-bottom: 10px;
}
.h3-bottom {
    margin-bottom: 10px;
}
p {
    color: #666666;
    font-size: 14px;
    font-weight: 400;
    line-height: 24px;
}
.p-bottom {
    margin-bottom: 12px;
}
a {
    color: inherit;
}
a:hover {
    color: inherit;
    text-decoration: none;
}
a:visited {
    color: inherit;
    text-decoration: none;
}
.section-space {
    padding: 80px 0 80px 0;
}
.image-center {
    margin: 0 auto;
}
.image-right {
    margin: 0 0 0 auto;
}
.image-left {
    margin: 0 auto 0 0;
}
.image-radius {
    border-radius: 3px;
}
.center {
    text-align: center;
}
.left {
    text-align: left;
}
.right {
    text-align: right;
}
.btn-1 {
    background-color: #3598db;
    border: 0;
    border-radius: 3px;
    color: #f3f3f3;
    display: inline-block;
    font-size: 12px;
    font-weight: 700;
    padding: 10px 20px 10px 20px;
    letter-spacing: 1px;
    text-transform: uppercase;
    transition: 300ms ease-in;
}
.btn-1:hover {
    background-color: #333333;
}
.btn-ant:hover {
    background-color: #f3f3f3;
    color: #333333;
}
.btn-top {
    margin-top: 30px;
}
.btn-top-1 {
    margin-top: 20px;
}
.btn:hover,
.btn:focus {
    color: #ffffff;
}
.image-bottom {
    margin: 0 0 20px 0;
}
.column-center {
    float: none;
    margin: 0 auto;
}
.no-padding {
    padding: 0;
}
.no-margin {
    margin: 0;
}
.no-fs {
    font-size: 0;
}
.section-bg-1 {
    background-color: #ffffff;
}
.section-bg-2 {
    background-color: #f6f6f6;
}
iframe {
    border: 0px;
}
.white-text h1,
.white-text h2,
.white-text h3,
.white-text h4,
.white-text p,
.white-text-separate {
    color: #f3f3f3;
}
.bgimage-property {
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}
.parallax {
    background-attachment: fixed;
}
.ls {
    letter-spacing: 1px;
}
.distab {
    display: table;
}
.distab-cell-middle {
    display: table-cell;
    vertical-align: middle;
}
.distab-cell-top {
    display: table-cell;
    vertical-align: top;
}
.distab-cell {
    display: table-cell;
}
.underline-bottom {
    margin: 0 0 40px 0;
}
.link {
    color: #333333;
    font-size: 13px;
    font-weight: 700;
    line-height: 20px;
    text-transform: uppercase;
}
.link-top {
    margin-top: 10px;
}
.link-icon-left {
    padding-left: 5px;
}
.image-grow {
    -webkit-transition: .3s all ease;
    transition: .3s all ease;
}
.image-grow:hover {
    -webkit-transform: scale(1.03);
    transform: scale(1.03);
}
.bgimage-head {
    font-size: 30px;
    font-weight: 600;
    line-height: 38px;
}
.bgimage-head-bottom {
    margin-bottom: 20px;
}
/*========================================== NAVIGATION START ===========================================*/

.menu-underline::before {
    background-color: #333333;
    border-radius: 3px;
    content: "";
    height: 50px;
    position: absolute;
    right: 0;
    left: 0;
    bottom: 0;
    top: 0;
    opacity: 0;
    transform: translateY(100%);
    transition: transform 0.5s ease 0s, opacity 0.33s ease 0s;
}
.menu-underline:hover::before {
    opacity: 1;
    transform: translateY(0px);
}
.menu-fs,
.menu-fs li {
    color: #666666;
    font-size: 14px;
    font-weight: 600;
    line-height: 22px;
    letter-spacing: 1px;
}
.menu-bg {
    background: rgb(255, 255, 255) none repeat scroll 0 0;
}
.navbar {
    margin-bottom: 0;
}
.navbar-default .navbar-nav > li > a {
    color: #666666 !important;
    background-color: transparent !important;
    margin: 0 0;
    padding: 15px 15px 15px 15px;
}
.navbar-default .navbar-nav > li > a:hover {
    color: #f3f3f3 !important;
    background-color: transparent !important;
}
.navbar-default .navbar-nav > .active > a,
.navbar-default .navbar-nav > .active > a:focus,
.navbar-default .navbar-nav > .active > a:hover {
    color: #3598db !important;
}
.navigation-tb {
    margin: 10px 0;
}
.nav > li {
    display: table;
}
.navbar-right {
    margin-right: 0;
}
/*========================================== NAVIGATION END ===========================================*/
/*========================================== HEADER-FORM START ===========================================*/

.header-form-heading-top {
    padding-top: 100px;
}
.note-text {
    color: #999999 !important;
    font-size: 13px;
}
.header-form-text {
    padding: 5px 0 20px 0;
}
.note-text {
    padding: 10px 0 15px 0;
}
/*=========== HEADER-FORM-ONE-BGIMAGE =================*/

.header-form-bgimage-1 {
    background-image: url("../../images/1500x650x1.jpg");
}
/*=========== HEADER-FORM-TWO-BGIMAGE =================*/

.header-form-bgimage-2 {
   /* background-image: url("https://brainywoodindia.com/front/assets/img/1500x650x2.jpg");*/
    background-color: #fff;
}
/*=========== HEADER-FORM-THREE-BGIMAGE =================*/

.header-form-bgimage-3 {
    background-image: url("../../images/1500x650x3.jpg");
}
/*=========== HEADER-FORM-WITH-IMAGE-BGIMAGE-ONE =================*/

.header-form-image-bgimage-1 {
    background-image: url("../../images/1500x700x1.jpg");
}
/*=========== HEADER-FORM-WITH-IMAGE-BGIMAGE-TWO =================*/

.header-form-image-bgimage-2 {
    background-image: url("../../images/1500x700x2.jpg");
}
/*=========== HEADER-FORM-ONLY-BGIMAGE-ONE =================*/

.header-form-only-bgimage-1 {
    background-image: url("../../images/1500x650x10.jpg");
}
/*=========== HEADER-FORM-ONLY-BGIMAGE-TWO =================*/

.header-form-only-bgimage-2 {
    background-image: url("../../images/1500x650x11.jpg");
}
.header-section-space-form-1 {
    padding: 80px 0 80px 0;
}
.header-section-space-form-single {
    padding: 120px 0 120px 0;
}
.header-space-image-form {
    padding: 95px 0 0 0;
}
.header-space-form-only {
    padding: 80px 0 80px 0;
}
.header-contact-form select,
.header-contact-form input {
	color: #f3f3f3;
	font-size:14px;
	font-weight:400;
    height: 50px;
    background: transparent;
    border: 0;
	letter-spacing:1px;
    opacity: 1;
    /*-moz-appearance: none;*/
    /* Firefox */
    
    /*-webkit-appearance: none;*/
    /* Safari and Chrome */
}
.form-div.form-bottom-1 input[type="checkbox"] {
    position: relative;
    top: 20px;
}

.form-control {
    box-shadow: 0 0 0 rgba(0, 0, 0, 0) inset !important;
	border-radius:0;
	font-size:13px;
}
.header-contact-form select,
.header-contact-form input {
    border-bottom: 1px solid #999999;
}
.form-bottom-1 {
    margin-bottom: 0px;
}
.phoneno-bottom {
    margin-bottom: 15px;
}
.form-text {
    height: 40px;
}
.form-div input.error,
.form-div-1 textarea.error {
    border-color: #ff0000 !important;
}
.contact-div label {
    color: #ff0000;
    font-size: 12px;
    font-weight: 600;
}
.errorClass {
    border: 1px solid #ff0000 !important;
}
.form-success,
.form-failure {
    font-size: 14px;
    font-weight: 600;
}
.form-success {
    color: #50cd88;
}
.form-failure {
    color: #ff0000;
}
.form-error-top {
    padding-top: 10px;
    padding-bottom: 10px;
}
.header-contact-form::-webkit-input-placeholder {
    /* Chrome/Opera/Safari */
    
    color: #f3f3f3;
    font-size: 14px;
    font-weight: 300;
    letter-spacing: 1px;
    opacity: 0.4;
}
.header-contact-form::-moz-placeholder {
    /* Firefox 19+ */
    
    color: #f3f3f3;
	font-size: 14px;
    font-weight: 300;
    letter-spacing: 1px;
    opacity: 0.4;
}
.header-contact-form:-ms-input-placeholder {
    /* IE 10+ */
    
    color: #f3f3f3;
	font-size: 14px;
    font-weight: 300;
    letter-spacing: 1px;
}
.header-contact-form:-moz-placeholder {
    /* Firefox 18- */
    
    color: #f3f3f3;
	font-size: 14px;
    font-weight: 300;
    letter-spacing: 1px;
}
/*.header-contact-bg {
    background-color: rgba(0, 0, 0, 0.8);
}*/
.header-contact-bg {
    background-color: rgb(255 255 255 / 80%);
    padding: 15px;
    box-shadow: 0px 0px 15px #a7a7a7;
    border-radius: 15px;
}
/*.header-contact-bg-pad {
    padding: 20px 20px 0 20px;
}*/
.header-form-heading-bg {
    /* background-color: rgba(0, 0, 0, 0.5); */
    padding: 15px 20px 15px 20px;
}
.header-form-heading-bg {
    padding: 15px 20px 15px 20px;
}
.form-control:focus {
    border-color: #66afe9;
    box-shadow: 0 0 0 rgba(0, 0, 0, 0) inset, 0 0 0 rgba(102, 175, 233, 0);
    outline: 0 none;
}
.header-form-text-bottom {
    padding-bottom: 20px;
}
.form-div {
    position: relative;
}
.form-div i {
    color: #999999;
    font-size: 16px;
    right: 20px;
    position: absolute;
    top: 15px;
}
.form-div .fa-phone {
    font-size: 20px;
}
.form-div img {
    right: 20px;
    position: absolute;
    top: 15px;
}
/*========================================== HEADER-FORM END ===========================================*/
/*========================================== HEADER-DEFAULT START ===========================================*/

.header-bgimage {
    background-image: url("../../images/1500x650x4.jpg");
}
.header-section-space {
    padding: 185px 0 185px 0;
}
.header-head1 {
    font-size: 50px;
    font-weight: 400;
    line-height: 60px;
    letter-spacing: 2px;
}
.header-head1-bottom {
    margin-bottom: 20px;
}
.header-head2 {
    font-size: 20px;
    font-weight: 600;
    line-height: 24px;
    letter-spacing: 2px;
}
.header-head2-bottom {
    margin-bottom: 20px;
}
.header-text-bottom {
    margin-bottom: 30px;
}
.header-btn {
    background-color: transparent;
    border: 1px solid #f3f3f3;
    border-radius: 3px;
    color: #f3f3f3;
    display: inline-block;
    font-size: 12px;
    font-weight: 700;
    padding: 10px 20px 10px 20px;
    letter-spacing: 1px;
    text-transform: uppercase;
    transition: 300ms ease-in;
}
.header-btn:hover {
    color: #333333;
    background-color: #f3f3f3;
    border: 1px solid transparent;
}
.header-btn-black {
    color: #333333;
    border: 1px solid #333333;
    transition: 300ms ease-in;
}
.header-btn-black:hover {
    color: #f3f3f3;
    background-color: #333333;
    border: 1px solid transparent;
}
.submit-btn {
    background-color: #3598db !important;
    border: 0 !important;
    color: #f3f3f3 !important;
    font-size: 12px !important;
    font-weight: 700 !important;
    letter-spacing: 1px !important;
    margin-bottom: 0 !important;
    text-transform: uppercase !important;
    transition: 300ms ease-in !important;
    width: 100%;
}
/*========================================== HEADER-DEFAULT END ===========================================*/
/*========================================== HEADER-SLIDER START ===========================================*/
/*=========== HEADER-SLIDER-ONE-BGIMAGE =================*/

.header-slider-bgimage1 {
    background-image: url("../../images/1500x650x5.jpg");
}
/*=========== HEADER-SLIDER-TWO-BGIMAGE =================*/

.header-slider-bgimage2 {
    background-image: url("../../images/1500x650x6.jpg");
}
/*=========== HEADER-SLIDER-THREE-BGIMAGE =================*/

.header-slider-bgimage3 {
    background-image: url("../../images/1500x650x7.jpg");
}
.header-section-slider-space {
    padding: 50px 0 50px 0;
}
.header-section-slider-height {
    height: 500px;
    overflow: hidden;
}
.outer {
    display: table;
    position: absolute;
    height: 100%;
    width: 100%;
}
.middle {
    display: table-cell;
    vertical-align: middle;
}
.inner {
    margin-left: auto;
    margin-right: auto;
}
/*========================================== HEADER-SLIDER END ===========================================*/
/*========================================== HEADER VIDEO START ===========================================*/

.video-bgimage {
    background-image: url("../../images/1500x650x8.jpg");
}
.pattern-overlay {
    background-color: rgba(0, 0, 0, 0.6);
}
.video-section .buttonBar {
    display: none;
}
.player {
    font-size: 1px;
}
/*========================================== HEADER VIDEO END ===========================================*/
/*========================================== OUR SERVICES START ===========================================*/

.services-icon {
    float: left;
}
.services-pad {
    padding-left: 84px;
}
.services-bottom {
    padding-bottom: 30px;
}
/*========================================== OUR SERVICES END ===========================================*/
/*========================================== OUR AWESOME SERVICES START ===========================================*/

.services-row-space {
    padding-bottom: 30px;
}
.services-row-space:last-child {
    padding-bottom: 0;
}
/*========================================== OUR AWESOME SERVICES END ===========================================*/
/*========================================== COUNTER START ===========================================*/

.counter-num {
    font-size: 40px;
    font-weight: 700;
    line-height: 46px;
    margin-bottom: 10px;
}
.counter-sub {
    font-size: 16px;
    font-weight: 700;
    line-height: 22px;
}
.counter-bgimage {
    background-image: none;
    /*background-image: url("https://brainywoodindia.com/front/assets/img/1500x500x2.jpg");*/
    background-color: white !important;
}
.counter-section-space {
    padding: 50px 0 50px 0;
}
/*========================================== COUNTER END ===========================================*/
/*========================================== WHAT WE DO START ===========================================*/

.list-bottom {
    margin-bottom: 25px;
}
.list-bottom:last-child {
    margin-bottom: 0;
}
.list-top {
    margin-top: 20px;
}
.what-left-pad {
    padding-left: 15px;
}
.what-list {
    font-size: 15px;
    font-weight: 600;
    line-height: 22px;
}
/*========================================== WHAT WE DO END ===========================================*/
/*========================================== GALLERY START ===========================================*/

.gallery-image-lr {
    padding: 0 5px 0 5px;
}
.gallery-fa {
    color: #f3f3f3;
    font-size: 30px;
}
.gallery-fa-pad-left {
    padding-left: 10px;
}
.ekko-lightbox-nav-overlay a {
    color: #f3f3f3;
}
.gallery-row-bottom {
    padding-bottom: 10px;
}
.overlay ul {
    list-style: none;
}
/*========================================== GALLERY END ===========================================*/
/*========================================== MARKET ANALYSIS START ===========================================*/

.analysis-row-space .row {
    padding-bottom: 40px;
}
.analysis-row-space .row:last-child {
    padding-bottom: 0;
}
/*========================================== MARKET ANALYSIS END ===========================================*/
/*========================================== OUR TEAM START ===========================================*/

.teambg-author {
    color: #333333;
    font-size: 18px;
    font-weight: 600;
    line-height: 24px;
    margin-bottom: 10px;
}
.teambg-design {
    font-size: 13px;
    font-weight: 600;
    line-height: 20px;
}
/*========================================== OUR TEAM END ===========================================*/
/*========================================== FREQUENTLY ASKED QUESTIONS START ===========================================*/

.down-arrow {
    margin-top: -8px;
    text-align: center;
}
.down-arrow:before {
    display: block;
    content: '\f078';
    color: #333333;
    font-family: "FontAwesome";
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    transition: .2s;
}
.down-arrow-left {
    padding-left: 20px;
}
.faq-title.active .down-arrow:before {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
}
.faq-bg {
    background-color: #f3f3f3;
    border: 1px solid #333333;
    padding: 15px;
}
.faq-bg-width {
    width: 100%;
}
.faq-answer-pad {
    padding: 15px;
}
.faq-row-bottom {
    padding-bottom: 30px;
}
.faq-row-bottom:last-child {
    padding-bottom: 0;
}
.faq-answer-bg {
    background-color: #f3f3f3;
}
/*========================================== FREQUENTLY ASKED QUESTIONS END ===========================================*/
/*========================================== BUILDER START =========================================*/

.builder-content-height {
    height: 428px;
    overflow: hidden;
}
.builder-content-pad {
    padding: 0 30px 0 30px;
}
.builder-bgimage-pad {
    padding: 80px 0 80px 0;
}
.builder-bgimage {
    background-image: url("../../images/680x590x1.jpg");
}
.builder-bgimage-height {
    width: 50%;
    height: 588px;
    position: absolute;
    left: 0;
}
.why-content-pad {
    padding-left: 20px;
}
.why-row-bottom {
    padding-bottom: 30px;
}
/*========================================== BUILDER END ===========================================*/
/*========================================== PRICE TABLE START ===========================================*/

.price-bgcolor-1 {
    background-color: #ffffff;
    border-radius: 3px;
}
.price-bgcolor-2 {
    background-color: #3598DB;
}
.price-bgcolor-2 {
    border-radius: 3px;
}
.price-head-pad {
    padding: 40px 10px 20px 10px;
}
.price-body-pad {
    padding: 20px 10px 40px 10px;
}
.pricig-uline {
    background-color: #3598DB;
    height: 1px;
    width: 40%;
    transition: width 2s;
    -ms-transition: width 2s;
    -webkit-transition: width 2s;
}
.pricig-uline-white {
    background-color: #f3f3f3;
}
.pricig-br:hover .pricig-uline {
    width: 100%;
}
.price-tag {
    font-size: 50px;
    font-weight: 600;
    line-height: 60px;
    padding-bottom: 10px;
}
.dollor,
.month {
    font-size: 18px;
    font-weight: 600;
    line-height: 60px;
}
.dollor {
    vertical-align: middle;
}
.price-list-bottom p {
    margin-bottom: 10px;
}
.price-list-bottom:last-child {
    margin-bottom: 0;
}
.price-pos-rel {
    position: relative;
}
.price-pos-abs {
    position: absolute;
    right: 0;
    top: 0;
}
/*========================================== PRICE TABLE END ===========================================*/
/*========================================== LATEST BLOG START ===========================================*/

.blog-row-bottom {
    padding-bottom: 40px;
}
.blog-row-bottom:last-child {
    padding-bottom: 0;
}
.blog-link-top .link-top {
    margin-top: 20px;
}
.blog-heading-top {
    padding-top: 55px;
}
/*========================================== LATEST BLOG END ===========================================*/
/*========================================== TWO-COLUMN-SECTION START =========================================*/

.two-column-bgimage-1 {
    background-image: url("../../images/680x410x1.jpg");
}
.two-column-bgimage-2 {
    background-image: url("../../images/680x410x2.jpg");
}
.two-column-content-height {
    height: 250px;
    overflow: hidden;
}
.two-column-bgimage-1-height {
    width: 50%;
    height: 410px;
    position: absolute;
    left: 0;
}
.two-column-bgimage-2-height {
    width: 50%;
    height: 410px;
    position: absolute;
    right: 0;
}
.two-column-bgimage-pad-1 {
    padding: 80px 0 80px 0;
}
.content-pad-1 {
    padding: 0 30px 0 30px;
}
/*========================================== TWO-COLUMN-SECTION END ===========================================*/
/*========================================== EMAIL SUBSCRIPTION START ===========================================*/

.subscription-bgimage {
    background-image: url("../../images/1500x500x1.jpg");
}
.subscription-section-space {
    padding: 100px 0 100px 0;
}
.email-success,
.email-failure {
    color: #f3f3f3;
    font-size: 14px;
    font-weight: 600;
}
.indicator-top {
    padding-top: 15px;
}
.email-text-bottom {
    margin-bottom: 30px;
}
.errorClass {
    border: 1px solid #ff0000 !important;
}
/*========================================== EMAIL SUBSCRIPTION END ===========================================*/
/*========================================== TESTIMONIAL START ===========================================*/

.quote-bottom {
    margin-bottom: 20px;
}
.testimonial-author-pad {
    padding-left: 20px;
}
.testimonial-author {
    font-size: 16px;
    font-weight: 600;
}
.testimonial-text {
    font-style: italic;
}
/*========================================== TESTIMONIAL END ===========================================*/
/*========================================== FOOTER-MAP START ===========================================*/

.footer-row-space {
    margin-bottom: 20px;
}
.follow-heading-bottom {
    margin-bottom: 15px;
}
.venue-map {
    width: 100%;
    height: 355px;
}
/*========================================== FOOTER-MAP END ===========================================*/
/*========================================== FOOTER START ===========================================*/

.footer-bg {
    background-color: #333333;
}
.footer-section-space {
    padding: 80px 0 40px 0;
}
.posts-bottom {
    margin-bottom: 25px;
}
.posts-bottom:last-child {
    margin-bottom: 0;
}
.posts-heading-bottom {
    margin-bottom: 20px;
}
.footer-list-bk li {
    list-style: none;
    display: block;
}
.footer-list-bottom li {
    padding-bottom: 15px;
}
.footer-list-bottom li:last-child {
    padding-bottom: 0;
}
.footer-dot-right {
    padding-right: 10px;
}
.footer-contact-left {
    padding-left: 15px;
}
.footer-contact-bottom {
    margin-bottom: 25px;
}
.footer-br {
    border-bottom: 1px solid #555555;
    margin-top: 40px;
}
.footer-br-bottom {
    margin-bottom: 40px;
}
.footer-icon li {
    list-style: none;
    display: inline-block;
}
.footer-left-pad li {
    padding-left: 15px;
}
.footer-left-pad li:first-child {
    padding-left: 0;
}
/*========================================== FOOTER END ===========================================*/

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .res-width-full {
        width: 100%;
    }
    .blog-heading-top {
        padding-top: 25px;
    }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .res-image-bottom {
        padding-bottom: 20px;
    }
    .common-res-bottom {
        padding-bottom: 40px;
    }
    .header-form-heading-top {
        padding-top: 0;
    }
    .res-header-heading-bottom {
        padding-bottom: 40px;
    }
    .res-services-bottom {
        padding-bottom: 30px;
    }
    .res-text-center p,
    .res-text-center h3,
    .res-text-center-single,
    .res-services-center p,
    .res-services-center h3 {
        text-align: center;
    }
    .res-image-center {
        margin: 0 auto;
    }
    .res-clear {
        clear: left;
    }
    .builder-bgimage-height {
        width: 100%;
        position: relative;
    }
    .builder-content-height {
        height: auto;
    }
    .builder-bgimage-pad {
        padding: 40px 0 80px 0;
    }
    .builder-content-pad {
        padding: 0 15px 0 15px;
    }
    .two-column-bgimage-1-height,
    .two-column-bgimage-2-height {
        width: 100%;
        position: relative;
    }
    .two-column-bgimage-pad-1 {
        padding: 20px 0 80px 0;
    }
    .two-column-content-height {
        height: auto;
    }
    .content-pad-1 {
        padding: 0 15px 0 15px;
    }
    .blog-heading-top {
        padding-top: 0;
    }
    .res-submit-tb {
        padding: 20px 15px;
    }
}
@media only screen and (min-width: 250px) and (max-width: 767px) {
    .navbar-toggle {
        margin-right: 0;
    }
    .navbar-right {
        padding-top: 10px;
    }
    .res-image-bottom-1 {
        padding-bottom: 20px;
    }
    .common-res-bottom-1,
    .counter-res-bottom {
        padding-bottom: 40px;
    }
    .faq-res-bottom {
        padding-bottom: 30px;
    }
    .res-header-heading-bottom {
        padding-bottom: 40px;
    }
    .header-form-heading-top {
        padding-top: 0;
    }
    .res-submit-tb {
        padding: 20px 15px;
    }
    .services-res-bottom {
        padding-bottom: 30px;
    }
    .res-text-center-1 p,
    .res-text-center-1 h3,
    .res-text-center-1 h2,
    .res-text-center-single-1,
    .res-text-center-single,
    .res-services-center p,
    .res-services-center h3 {
        text-align: center;
    }
    .res-gallery-row-bottom {
        padding-bottom: 10px;
    }
    .price-fixed {
        margin: 0 auto;
        width: 400px !important;
    }
    .price-res-bottom {
        margin-bottom: 40px;
    }
    .builder-bgimage-height {
        width: 100%;
        position: relative;
    }
    .builder-content-height {
        height: auto;
    }
    .builder-bgimage-pad {
        padding: 40px 0 80px 0;
    }
    .builder-content-pad {
        padding: 0 15px 0 15px;
    }
    .two-column-bgimage-1-height,
    .two-column-bgimage-2-height {
        width: 100%;
        position: relative;
    }
    .two-column-bgimage-pad-1 {
        padding: 20px 0 80px 0;
    }
    .two-column-content-height {
        height: auto;
    }
    .content-pad-1 {
        padding: 0 15px 0 15px;
    }
    .blog-heading-top {
        padding-top: 0;
    }
    .blog-link-top .link-top {
        margin-top: 10px;
    }
}
@media only screen and (min-width: 250px) and (max-width: 639px) {
    .common-full {
        width: 100% !important;
    }
    .res-footer-links-bottom {
        padding-bottom: 40px;
    }
}
@media only screen and (min-width: 250px) and (max-width: 479px) {
    .price-fixed {
        width: 100% !important;
    }
    .header-section-slider-height {
        height: 700px;
        overflow: hidden;
    }
	
	h1 {
    font-size: 26px;
    line-height: 32px;
	}
	h2 {
		font-size: 20px;
		line-height: 28px;
	}
	h3 {
		font-size: 18px;
		line-height: 24px;
	}
	
	.header-head1 {
    font-size: 30px;
    line-height: 40px;
	}
	
	.bgimage-head {
    font-size: 24px;
    line-height: 32px;
	}
}
@media only screen and (min-width: 250px) and (max-width: 359px) {
    .common-full-1 {
        width: 100% !important;
    }
    .counter-res-bottom-1,
    .team-res-bottom {
        padding-bottom: 40px;
    }
}