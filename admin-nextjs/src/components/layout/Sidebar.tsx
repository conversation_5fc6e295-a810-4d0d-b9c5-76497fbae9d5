'use client';

import React, { useState } from 'react';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Collapse,
  Box,
  Typography,
  Divider,
} from '@mui/material';
import {
  Dashboard,
  People,
  School,
  Category,
  Assignment,
  Quiz,
  VideoLibrary,
  LiveTv,
  Forum,
  Settings,
  ExpandLess,
  ExpandMore,
  Person,
  SupervisorAccount,
  Class,
  Subject,
  Notifications,
  ContactSupport,
  Description,
  Payment,
  TrendingUp,
} from '@mui/icons-material';
import { useRouter, usePathname } from 'next/navigation';

const drawerWidth = 230;

interface MenuItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  path?: string;
  children?: MenuItem[];
  permission?: string;
}

const menuItems: MenuItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: <Dashboard />,
    path: '/admin/dashboard',
    permission: 'dashboard',
  },
  {
    id: 'user-management',
    label: 'User Management',
    icon: <People />,
    permission: 'users_manage',
    children: [
      {
        id: 'permissions',
        label: 'Permissions',
        icon: <Settings />,
        path: '/admin/permissions',
        permission: 'Permission',
      },
      {
        id: 'roles',
        label: 'Roles',
        icon: <SupervisorAccount />,
        path: '/admin/roles',
        permission: 'role',
      },
      {
        id: 'users',
        label: 'Administrator Users',
        icon: <Person />,
        path: '/admin/users',
      },
    ],
  },
  {
    id: 'teacher-management',
    label: 'Teacher Management',
    icon: <School />,
    path: '/admin/teachers',
    permission: 'teacher_manage',
  },
  {
    id: 'student-management',
    label: 'Student Management',
    icon: <People />,
    path: '/admin/students',
    permission: 'student_management',
  },
  {
    id: 'course-management',
    label: 'Course Management',
    icon: <School />,
    permission: 'student_class',
    children: [
      {
        id: 'classes',
        label: 'Class Details',
        icon: <Class />,
        path: '/admin/classes',
      },
      {
        id: 'subjects',
        label: 'Subjects Details',
        icon: <Subject />,
        path: '/admin/subjects',
      },
    ],
  },
  {
    id: 'course-category',
    label: 'Course & Category',
    icon: <Category />,
    permission: 'category',
    children: [
      {
        id: 'categories',
        label: 'Category Details',
        icon: <Category />,
        path: '/admin/categories',
      },
      {
        id: 'courses',
        label: 'Course Details',
        icon: <School />,
        path: '/admin/courses',
      },
    ],
  },
  {
    id: 'demo-requests',
    label: 'Demo Request',
    icon: <Assignment />,
    path: '/admin/demo-requests',
    permission: 'student_apply_for_demo',
  },
  {
    id: 'assignments',
    label: 'Assignments',
    icon: <Assignment />,
    path: '/admin/assignments',
    permission: 'assignments',
  },
  {
    id: 'quizzes',
    label: 'Quizzes',
    icon: <Quiz />,
    path: '/admin/quizzes',
    permission: 'quiz',
  },
  {
    id: 'live-classes',
    label: 'Live Classes',
    icon: <LiveTv />,
    path: '/admin/live-classes',
    permission: 'liveclasses',
  },
  {
    id: 'community-posts',
    label: 'Community Posts',
    icon: <Forum />,
    path: '/admin/community-posts',
    permission: 'community_posts',
  },
  {
    id: 'notifications',
    label: 'Notifications',
    icon: <Notifications />,
    path: '/admin/notifications',
    permission: 'notifications',
  },
  {
    id: 'reports',
    label: 'Reports',
    icon: <TrendingUp />,
    path: '/admin/reports',
    permission: 'reports',
  },
];

interface SidebarProps {
  open: boolean;
  onClose: () => void;
}

export default function Sidebar({ open, onClose }: SidebarProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const handleItemClick = (item: MenuItem) => {
    if (item.children) {
      const isExpanded = expandedItems.includes(item.id);
      if (isExpanded) {
        setExpandedItems(expandedItems.filter(id => id !== item.id));
      } else {
        setExpandedItems([...expandedItems, item.id]);
      }
    } else if (item.path) {
      router.push(item.path);
    }
  };

  const isItemSelected = (path: string) => {
    return pathname === path;
  };

  const renderMenuItem = (item: MenuItem, level = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.id);
    const isSelected = item.path ? isItemSelected(item.path) : false;

    return (
      <React.Fragment key={item.id}>
        <ListItem disablePadding sx={{ pl: level * 2 }}>
          <ListItemButton
            onClick={() => handleItemClick(item)}
            selected={isSelected}
            sx={{
              minHeight: 48,
              color: '#b8c7ce',
              '&:hover': {
                backgroundColor: '#1e282c',
              },
              '&.Mui-selected': {
                backgroundColor: '#1e282c',
                borderLeft: '3px solid #3c8dbc',
                color: '#ffffff',
              },
            }}
          >
            <ListItemIcon sx={{ color: 'inherit', minWidth: 40 }}>
              {item.icon}
            </ListItemIcon>
            <ListItemText 
              primary={item.label} 
              primaryTypographyProps={{
                fontSize: '14px',
                fontWeight: isSelected ? 600 : 400,
              }}
            />
            {hasChildren && (
              isExpanded ? <ExpandLess /> : <ExpandMore />
            )}
          </ListItemButton>
        </ListItem>
        {hasChildren && (
          <Collapse in={isExpanded} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {item.children!.map(child => renderMenuItem(child, level + 1))}
            </List>
          </Collapse>
        )}
      </React.Fragment>
    );
  };

  return (
    <Drawer
      variant="persistent"
      anchor="left"
      open={open}
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
          backgroundColor: '#222d32',
          color: '#b8c7ce',
          borderRight: 'none',
        },
      }}
    >
      <Box sx={{ p: 2, backgroundColor: '#367fa9' }}>
        <Typography variant="h6" sx={{ color: 'white', fontWeight: 'bold' }}>
          Admin Panel
        </Typography>
      </Box>
      <Divider sx={{ backgroundColor: '#1a252f' }} />
      <Box sx={{ overflow: 'auto', height: '100%' }}>
        <List>
          {menuItems.map(item => renderMenuItem(item))}
        </List>
      </Box>
    </Drawer>
  );
}
