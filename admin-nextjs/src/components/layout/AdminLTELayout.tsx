"use client";

import React, { useState, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";

interface AdminLTELayoutProps {
  children: React.ReactNode;
  title?: string;
}

export default function AdminLTELayout({
  children,
  title,
}: AdminLTELayoutProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [openTreeviews, setOpenTreeviews] = useState<string[]>([]);

  const handleSidebarToggle = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const handleLogout = () => {
    localStorage.removeItem("auth_token");
    localStorage.removeItem("user_role");
    router.push("/login");
  };

  const isActive = (path: string) => {
    return pathname === path;
  };

  const toggleTreeview = (id: string) => {
    setOpenTreeviews((prev) =>
      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]
    );
  };

  const isTreeviewOpen = (id: string) => {
    return openTreeviews.includes(id);
  };

  // Initialize treeview states based on current path
  useEffect(() => {
    if (
      pathname.includes("/admin/permissions") ||
      pathname.includes("/admin/roles") ||
      pathname.includes("/admin/users")
    ) {
      setOpenTreeviews((prev) => [...prev, "user-management"]);
    }
    if (
      pathname.includes("/admin/classes") ||
      pathname.includes("/admin/subjects")
    ) {
      setOpenTreeviews((prev) => [...prev, "course-management"]);
    }
    if (
      pathname.includes("/admin/categories") ||
      pathname.includes("/admin/courses")
    ) {
      setOpenTreeviews((prev) => [...prev, "course-category"]);
    }
    if (
      pathname.includes("/admin/about-us") ||
      pathname.includes("/admin/privacy-policy") ||
      pathname.includes("/admin/terms-condition")
    ) {
      setOpenTreeviews((prev) => [...prev, "cms"]);
    }
  }, [pathname]);

  return (
    <div
      className={`wrapper skin-blue ${
        sidebarCollapsed ? "sidebar-collapse" : ""
      }`}
    >
      {/* Main Header */}
      <header className="main-header">
        {/* Logo */}
        <a
          href="#"
          className="logo"
          style={{ fontSize: "16px", color: "#fff", textDecoration: "none" }}
        >
          <b>Admin</b>LTE
        </a>

        {/* Header Navbar */}
        <nav
          className="navbar navbar-static-top"
          style={{ display: "block", padding: 0 }}
        >
          {/* Sidebar toggle button */}
          <a
            href="#"
            className="sidebar-toggle"
            onClick={(e) => {
              e.preventDefault();
              handleSidebarToggle();
            }}
            role="button"
          >
            <span className="sr-only">Toggle navigation</span>
            <span className="icon-bar"></span>
            <span className="icon-bar"></span>
            <span className="icon-bar"></span>
          </a>
        </nav>
      </header>

      {/* Left side column. contains the sidebar */}
      <aside className="main-sidebar">
        {/* sidebar: style can be found in sidebar.less */}
        <section className="sidebar">
          <ul className="sidebar-menu scroll-table scroll-table-two">
            {/* Dashboard */}
            <li className={isActive("/admin/dashboard") ? "active" : ""}>
              <a href="/admin/dashboard">
                <i className="fa fa-dashboard"></i>
                <span className="title">Dashboard</span>
              </a>
            </li>

            {/* User Management */}
            <li
              className={`treeview ${
                isTreeviewOpen("user-management") ? "active menu-open" : ""
              }`}
            >
              <a
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  toggleTreeview("user-management");
                }}
              >
                <i className="fa fa-users"></i>
                <span className="title">User Management</span>
                <span className="pull-right-container">
                  <i
                    className={`fa fa-angle-left pull-right ${
                      isTreeviewOpen("user-management") ? "fa-angle-down" : ""
                    }`}
                  ></i>
                </span>
              </a>
              <ul
                className="treeview-menu"
                style={{
                  display: isTreeviewOpen("user-management") ? "block" : "none",
                }}
              >
                <li
                  className={
                    isActive("/admin/permissions") ? "active active-sub" : ""
                  }
                >
                  <a href="/admin/permissions">
                    <i className="fa fa-briefcase"></i>
                    <span className="title">Permissions</span>
                  </a>
                </li>
                <li
                  className={
                    isActive("/admin/roles") ? "active active-sub" : ""
                  }
                >
                  <a href="/admin/roles">
                    <i className="fa fa-briefcase"></i>
                    <span className="title">Roles</span>
                  </a>
                </li>
                <li
                  className={
                    isActive("/admin/users") ? "active active-sub" : ""
                  }
                >
                  <a href="/admin/users">
                    <i className="fa fa-user"></i>
                    <span className="title">Administrator Users</span>
                  </a>
                </li>
              </ul>
            </li>

            {/* Fees Update */}
            <li className={isActive("/admin/fees") ? "active active-sub" : ""}>
              <a href="/admin/fees">
                <i className="fa fa-briefcase"></i>
                <span className="title">Fees Update</span>
              </a>
            </li>

            {/* Teacher Management */}
            <li
              className={isActive("/admin/teachers") ? "active active-sub" : ""}
            >
              <a href="/admin/teachers">
                <i className="fa fa-user"></i>
                <span className="title">Teacher Management</span>
              </a>
            </li>

            {/* Student Management */}
            <li
              className={isActive("/admin/students") ? "active active-sub" : ""}
            >
              <a href="/admin/students">
                <i className="fa fa-user"></i>
                <span className="title">Student Management</span>
              </a>
            </li>

            {/* Course Management */}
            <li className="treeview">
              <a href="#">
                <i className="fa fa-graduation-cap"></i>
                <span className="title">Course Management</span>
                <span className="pull-right-container">
                  <i className="fa fa-angle-left pull-right"></i>
                </span>
              </a>
              <ul className="treeview-menu">
                <li
                  className={
                    isActive("/admin/classes") ? "active active-sub" : ""
                  }
                >
                  <a href="/admin/classes">
                    <i className="fa fa-graduation-cap"></i>
                    <span className="title">Class Details</span>
                  </a>
                </li>
                <li
                  className={
                    isActive("/admin/subjects") ? "active active-sub" : ""
                  }
                >
                  <a href="/admin/subjects">
                    <i className="fa fa-graduation-cap"></i>
                    <span className="title">Subjects Details</span>
                  </a>
                </li>
              </ul>
            </li>

            {/* Course & Category */}
            <li className="treeview">
              <a href="#">
                <i className="fa fa-graduation-cap"></i>
                <span className="title">Course & Category</span>
                <span className="pull-right-container">
                  <i className="fa fa-angle-left pull-right"></i>
                </span>
              </a>
              <ul className="treeview-menu">
                <li
                  className={
                    isActive("/admin/categories") ? "active active-sub" : ""
                  }
                >
                  <a href="/admin/categories">
                    <i className="fa fa-graduation-cap"></i>
                    <span className="title">Category Details</span>
                  </a>
                </li>
                <li
                  className={
                    isActive("/admin/courses") ? "active active-sub" : ""
                  }
                >
                  <a href="/admin/courses">
                    <i className="fa fa-graduation-cap"></i>
                    <span className="title">Course Details</span>
                  </a>
                </li>
              </ul>
            </li>

            {/* Demo Request */}
            <li
              className={
                isActive("/admin/demo-requests") ? "active active-sub" : ""
              }
            >
              <a href="/admin/demo-requests">
                <i className="fa fa-graduation-cap"></i>
                <span className="title">Demo Request</span>
              </a>
            </li>

            {/* Withdrawal */}
            <li
              className={
                isActive("/admin/withdrawal") ? "active active-sub" : ""
              }
            >
              <a href="/admin/withdrawal">
                <i className="fa fa-graduation-cap"></i>
                <span className="title">Withdrawal</span>
              </a>
            </li>

            {/* Incomplete Classes */}
            <li
              className={
                isActive("/admin/incomplete-classes") ? "active active-sub" : ""
              }
            >
              <a href="/admin/incomplete-classes">
                <i className="fa fa-graduation-cap"></i>
                <span className="title">Incomplete Classes</span>
              </a>
            </li>

            {/* Student Purchase Class */}
            <li
              className={
                isActive("/admin/student-purchase-class")
                  ? "active active-sub"
                  : ""
              }
            >
              <a href="/admin/student-purchase-class">
                <i className="fa fa-graduation-cap"></i>
                <span className="title">Student Purchase Class</span>
              </a>
            </li>

            {/* Offers */}
            <li
              className={isActive("/admin/offers") ? "active active-sub" : ""}
            >
              <a href="/admin/offers">
                <i className="fa fa-graduation-cap"></i>
                <span className="title">Offers Details</span>
              </a>
            </li>

            {/* CMS */}
            <li className="treeview">
              <a href="#">
                <i className="fa fa-graduation-cap"></i>
                <span className="title">CMS</span>
                <span className="pull-right-container">
                  <i className="fa fa-angle-left pull-right"></i>
                </span>
              </a>
              <ul className="treeview-menu">
                <li
                  className={
                    isActive("/admin/about-us") ? "active active-sub" : ""
                  }
                >
                  <a href="/admin/about-us">
                    <i className="fa fa-graduation-cap"></i>
                    <span className="title">About Us</span>
                  </a>
                </li>
                <li
                  className={
                    isActive("/admin/privacy-policy") ? "active active-sub" : ""
                  }
                >
                  <a href="/admin/privacy-policy">
                    <i className="fa fa-graduation-cap"></i>
                    <span className="title">Privacy Policy</span>
                  </a>
                </li>
                <li
                  className={
                    isActive("/admin/terms-condition")
                      ? "active active-sub"
                      : ""
                  }
                >
                  <a href="/admin/terms-condition">
                    <i className="fa fa-graduation-cap"></i>
                    <span className="title">Terms & Condition</span>
                  </a>
                </li>
              </ul>
            </li>
          </ul>
        </section>
      </aside>

      {/* Content Wrapper. Contains page content */}
      <div className="content-wrapper">
        {/* Main content */}
        <section className="content">
          {title && (
            <h3
              className="page-title"
              style={{
                margin: "20px 0",
                color: "#444444",
                fontSize: "24px",
                fontWeight: 400,
              }}
            >
              {title}
            </h3>
          )}

          <div className="row">
            <div className="col-md-12">{children}</div>
          </div>
        </section>
      </div>
    </div>
  );
}
