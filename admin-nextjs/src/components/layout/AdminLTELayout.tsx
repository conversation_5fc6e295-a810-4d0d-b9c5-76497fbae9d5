"use client";

import React, { useState, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import Image from "next/image";

interface AdminLTELayoutProps {
  children: React.ReactNode;
  title?: string;
}

export default function AdminLTELayout({
  children,
  title,
}: AdminLTELayoutProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [openTreeview, setOpenTreeview] = useState<string | null>(null);

  const handleSidebarToggle = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const handleLogout = () => {
    localStorage.removeItem("auth_token");
    localStorage.removeItem("user_role");
    router.push("/login");
  };

  const isActive = (path: string) => {
    return pathname === path;
  };

  const toggleTreeview = (id: string) => {
    // Laravel behavior: only one treeview can be open at a time
    setOpenTreeview((prev) => (prev === id ? null : id));
  };

  const isTreeviewOpen = (id: string) => {
    return openTreeview === id;
  };

  // Initialize treeview state based on current path (Laravel behavior: only one open)
  useEffect(() => {
    if (
      pathname.includes("/admin/permissions") ||
      pathname.includes("/admin/roles") ||
      pathname.includes("/admin/users")
    ) {
      setOpenTreeview("user-management");
    } else if (
      pathname.includes("/admin/classes") ||
      pathname.includes("/admin/subjects")
    ) {
      setOpenTreeview("course-management");
    } else if (
      pathname.includes("/admin/categories") ||
      pathname.includes("/admin/courses")
    ) {
      setOpenTreeview("course-category");
    } else if (pathname.includes("/admin/quizzes")) {
      setOpenTreeview("quizzes");
    } else if (pathname.includes("/admin/assignments")) {
      setOpenTreeview("assignments");
    } else if (pathname.includes("/admin/live-classes")) {
      setOpenTreeview("live-classes");
    } else if (pathname.includes("/admin/notifications")) {
      setOpenTreeview("notifications");
    } else if (
      pathname.includes("/admin/about-us") ||
      pathname.includes("/admin/privacy-policy") ||
      pathname.includes("/admin/terms-condition")
    ) {
      setOpenTreeview("cms");
    } else {
      // Close all treeviews if not on a treeview page
      setOpenTreeview(null);
    }
  }, [pathname]);

  return (
    <>
      <style jsx>{`
        .treeview-menu {
          max-height: ${openTreeview ? "1000px" : "0"};
          transition: max-height 0.3s ease-in-out;
        }
        .treeview-menu li {
          opacity: ${openTreeview ? "1" : "0"};
          transition: opacity 0.2s ease-in-out;
        }
      `}</style>
      <div
        className={`wrapper skin-blue sidebar-mini ${
          sidebarCollapsed ? "sidebar-collapse" : ""
        }`}
      >
        {/* Main Header */}
        <header className="main-header">
          {/* Logo */}
          <a
            href="#"
            className="logo"
            style={{ color: "#fff", textDecoration: "none" }}
          >
            {/* mini logo for sidebar mini 50x50 pixels */}
            <span className="logo-mini">
              <Image
                src=""
                alt="Logo"
                width={30}
                height={30}
                style={{ marginRight: "5px" }}
              />
            </span>
            {/* logo for regular state and mobile devices */}
            <span className="logo-lg">
              <Image
                src="/logo.png"
                alt="Logo"
                width={30}
                height={30}
                style={{ marginRight: "5px" }}
              />
              <b>Admin</b>LTE
            </span>
          </a>

          {/* Header Navbar */}
          <nav
            className="navbar navbar-static-top"
            style={{ display: "block", padding: 0 }}
          >
            {/* Sidebar toggle button */}
            <a
              href="#"
              className="sidebar-toggle"
              onClick={(e) => {
                e.preventDefault();
                handleSidebarToggle();
              }}
              role="button"
            >
              <span className="sr-only">Toggle navigation</span>
              <span className="icon-bar"></span>
              <span className="icon-bar"></span>
              <span className="icon-bar"></span>
            </a>
          </nav>
        </header>

        {/* Left side column. contains the sidebar */}
        <aside className="main-sidebar">
          {/* sidebar: style can be found in sidebar.less */}
          <section className="sidebar">
            <ul className="sidebar-menu scroll-table scroll-table-two">
              {/* Dashboard */}
              <li className={isActive("/admin/dashboard") ? "active" : ""}>
                <a href="/admin/dashboard" title="Dashboard">
                  <i className="fa fa-dashboard"></i>
                  <span className="title">Dashboard</span>
                </a>
              </li>

              {/* User Management */}
              <li
                className={`treeview ${
                  isTreeviewOpen("user-management") ? "active menu-open" : ""
                }`}
              >
                <a
                  href="#"
                  title="User Management"
                  onClick={(e) => {
                    e.preventDefault();
                    toggleTreeview("user-management");
                  }}
                >
                  <i className="fa fa-users"></i>
                  <span className="title">User Management</span>
                  <span className="pull-right-container">
                    <i
                      className={`fa fa-angle-left pull-right ${
                        isTreeviewOpen("user-management") ? "fa-angle-down" : ""
                      }`}
                    ></i>
                  </span>
                </a>
                <ul
                  className="treeview-menu"
                  style={{
                    display: isTreeviewOpen("user-management")
                      ? "block"
                      : "none",
                    transition: "all 0.3s ease-in-out",
                    overflow: "hidden",
                  }}
                >
                  <li
                    className={
                      isActive("/admin/permissions") ? "active active-sub" : ""
                    }
                  >
                    <a href="/admin/permissions">
                      <i className="fa fa-briefcase"></i>
                      <span className="title">Permissions</span>
                    </a>
                  </li>
                  <li
                    className={
                      isActive("/admin/roles") ? "active active-sub" : ""
                    }
                  >
                    <a href="/admin/roles">
                      <i className="fa fa-briefcase"></i>
                      <span className="title">Roles</span>
                    </a>
                  </li>
                  <li
                    className={
                      isActive("/admin/users") ? "active active-sub" : ""
                    }
                  >
                    <a href="/admin/users">
                      <i className="fa fa-user"></i>
                      <span className="title">Administrator Users</span>
                    </a>
                  </li>
                </ul>
              </li>

              {/* Fees Update */}
              <li
                className={isActive("/admin/fees") ? "active active-sub" : ""}
              >
                <a href="/admin/fees">
                  <i className="fa fa-briefcase"></i>
                  <span className="title">Fees Update</span>
                </a>
              </li>

              {/* Teacher Management */}
              <li
                className={
                  isActive("/admin/teachers") ? "active active-sub" : ""
                }
              >
                <a href="/admin/teachers">
                  <i className="fa fa-user"></i>
                  <span className="title">Teacher Management</span>
                </a>
              </li>

              {/* Student Management */}
              <li
                className={
                  isActive("/admin/students") ? "active active-sub" : ""
                }
              >
                <a href="/admin/students">
                  <i className="fa fa-user"></i>
                  <span className="title">Student Management</span>
                </a>
              </li>

              {/* Course Management */}
              <li
                className={`treeview ${
                  isTreeviewOpen("course-management") ? "active menu-open" : ""
                }`}
              >
                <a
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    toggleTreeview("course-management");
                  }}
                >
                  <i className="fa fa-graduation-cap"></i>
                  <span className="title">Course Management</span>
                  <span className="pull-right-container">
                    <i
                      className={`fa fa-angle-left pull-right ${
                        isTreeviewOpen("course-management")
                          ? "fa-angle-down"
                          : ""
                      }`}
                    ></i>
                  </span>
                </a>
                <ul
                  className="treeview-menu"
                  style={{
                    display: isTreeviewOpen("course-management")
                      ? "block"
                      : "none",
                    transition: "all 0.3s ease-in-out",
                    overflow: "hidden",
                  }}
                >
                  <li
                    className={
                      isActive("/admin/classes") ? "active active-sub" : ""
                    }
                  >
                    <a href="/admin/classes">
                      <i className="fa fa-graduation-cap"></i>
                      <span className="title">Class Details</span>
                    </a>
                  </li>
                  <li
                    className={
                      isActive("/admin/subjects") ? "active active-sub" : ""
                    }
                  >
                    <a href="/admin/subjects">
                      <i className="fa fa-graduation-cap"></i>
                      <span className="title">Subjects Details</span>
                    </a>
                  </li>
                </ul>
              </li>

              {/* Course & Category */}
              <li
                className={`treeview ${
                  isTreeviewOpen("course-category") ? "active menu-open" : ""
                }`}
              >
                <a
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    toggleTreeview("course-category");
                  }}
                >
                  <i className="fa fa-graduation-cap"></i>
                  <span className="title">Course & Category</span>
                  <span className="pull-right-container">
                    <i
                      className={`fa fa-angle-left pull-right ${
                        isTreeviewOpen("course-category") ? "fa-angle-down" : ""
                      }`}
                    ></i>
                  </span>
                </a>
                <ul
                  className="treeview-menu"
                  style={{
                    display: isTreeviewOpen("course-category")
                      ? "block"
                      : "none",
                    transition: "all 0.3s ease-in-out",
                    overflow: "hidden",
                  }}
                >
                  <li
                    className={
                      isActive("/admin/categories") ? "active active-sub" : ""
                    }
                  >
                    <a href="/admin/categories">
                      <i className="fa fa-graduation-cap"></i>
                      <span className="title">Category Details</span>
                    </a>
                  </li>
                  <li
                    className={
                      isActive("/admin/courses") ? "active active-sub" : ""
                    }
                  >
                    <a href="/admin/courses">
                      <i className="fa fa-graduation-cap"></i>
                      <span className="title">Course Details</span>
                    </a>
                  </li>
                </ul>
              </li>

              {/* Demo Request */}
              <li
                className={
                  isActive("/admin/demo-requests") ? "active active-sub" : ""
                }
              >
                <a href="/admin/demo-requests">
                  <i className="fa fa-graduation-cap"></i>
                  <span className="title">Demo Request</span>
                </a>
              </li>

              {/* Withdrawal */}
              <li
                className={
                  isActive("/admin/withdrawal") ? "active active-sub" : ""
                }
              >
                <a href="/admin/withdrawal">
                  <i className="fa fa-graduation-cap"></i>
                  <span className="title">Withdrawal</span>
                </a>
              </li>

              {/* Incomplete Classes */}
              <li
                className={
                  isActive("/admin/incomplete-classes")
                    ? "active active-sub"
                    : ""
                }
              >
                <a href="/admin/incomplete-classes">
                  <i className="fa fa-graduation-cap"></i>
                  <span className="title">Incomplete Classes</span>
                </a>
              </li>

              {/* Student Purchase Class */}
              <li
                className={
                  isActive("/admin/student-purchase-class")
                    ? "active active-sub"
                    : ""
                }
              >
                <a href="/admin/student-purchase-class">
                  <i className="fa fa-graduation-cap"></i>
                  <span className="title">Student Purchase Class</span>
                </a>
              </li>

              {/* Offers */}
              <li
                className={isActive("/admin/offers") ? "active active-sub" : ""}
              >
                <a href="/admin/offers">
                  <i className="fa fa-graduation-cap"></i>
                  <span className="title">Offers Details</span>
                </a>
              </li>

              {/* Student Invoice */}
              <li
                className={
                  isActive("/admin/student-invoice") ? "active active-sub" : ""
                }
              >
                <a href="/admin/student-invoice">
                  <i className="fa fa-graduation-cap"></i>
                  <span className="title">Student Invoice</span>
                </a>
              </li>

              {/* Quizzes */}
              <li
                className={`treeview ${
                  isTreeviewOpen("quizzes") ? "active menu-open" : ""
                }`}
              >
                <a
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    toggleTreeview("quizzes");
                  }}
                >
                  <i className="fa fa-quora"></i>
                  <span className="title">Quizes</span>
                  <span className="pull-right-container">
                    <i
                      className={`fa fa-angle-left pull-right ${
                        isTreeviewOpen("quizzes") ? "fa-angle-down" : ""
                      }`}
                    ></i>
                  </span>
                </a>
                <ul
                  className="treeview-menu"
                  style={{
                    display: isTreeviewOpen("quizzes") ? "block" : "none",
                    transition: "all 0.3s ease-in-out",
                    overflow: "hidden",
                  }}
                >
                  <li
                    className={
                      isActive("/admin/quizzes") ? "active active-sub" : ""
                    }
                  >
                    <a href="/admin/quizzes">
                      <i className="fa fa-quora"></i>
                      <span className="title">Quizes</span>
                    </a>
                  </li>
                </ul>
              </li>

              {/* Assignment */}
              <li
                className={`treeview ${
                  isTreeviewOpen("assignments") ? "active menu-open" : ""
                }`}
              >
                <a
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    toggleTreeview("assignments");
                  }}
                >
                  <i className="fa fa-list"></i>
                  <span className="title">Assignment</span>
                  <span className="pull-right-container">
                    <i
                      className={`fa fa-angle-left pull-right ${
                        isTreeviewOpen("assignments") ? "fa-angle-down" : ""
                      }`}
                    ></i>
                  </span>
                </a>
                <ul
                  className="treeview-menu"
                  style={{
                    display: isTreeviewOpen("assignments") ? "block" : "none",
                    transition: "all 0.3s ease-in-out",
                    overflow: "hidden",
                  }}
                >
                  <li
                    className={
                      isActive("/admin/assignments") ? "active active-sub" : ""
                    }
                  >
                    <a href="/admin/assignments">
                      <i className="fa fa-list"></i>
                      <span className="title">Assignment Details</span>
                    </a>
                  </li>
                </ul>
              </li>

              {/* Live Classes */}
              <li
                className={`treeview ${
                  isTreeviewOpen("live-classes") ? "active menu-open" : ""
                }`}
              >
                <a
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    toggleTreeview("live-classes");
                  }}
                >
                  <i className="fa fa-video-camera"></i>
                  <span className="title">Live Classes</span>
                  <span className="pull-right-container">
                    <i
                      className={`fa fa-angle-left pull-right ${
                        isTreeviewOpen("live-classes") ? "fa-angle-down" : ""
                      }`}
                    ></i>
                  </span>
                </a>
                <ul
                  className="treeview-menu"
                  style={{
                    display: isTreeviewOpen("live-classes") ? "block" : "none",
                    transition: "all 0.3s ease-in-out",
                    overflow: "hidden",
                  }}
                >
                  <li
                    className={
                      isActive("/admin/live-classes") ? "active active-sub" : ""
                    }
                  >
                    <a href="/admin/live-classes">
                      <i className="fa fa-graduation-cap"></i>
                      <span className="title">Live Classes Details</span>
                    </a>
                  </li>
                </ul>
              </li>

              {/* Banners */}
              <li
                className={
                  isActive("/admin/banners") ? "active active-sub" : ""
                }
              >
                <a href="/admin/banners">
                  <i className="fa fa-picture-o"></i>
                  <span className="title">Banners</span>
                </a>
              </li>

              {/* Referral */}
              <li
                className={
                  isActive("/admin/referral") ? "active active-sub" : ""
                }
              >
                <a href="/admin/referral">
                  <i className="fa fa-picture-o"></i>
                  <span className="title">Referral</span>
                </a>
              </li>

              {/* Contact Us */}
              <li className={isActive("/admin/contact-us") ? "active" : ""}>
                <a href="/admin/contact-us">
                  <i className="fa fa-envelope-open-o"></i>
                  <span className="title">Contact Us</span>
                </a>
              </li>

              {/* Notification */}
              <li
                className={`treeview ${
                  isTreeviewOpen("notifications") ? "active menu-open" : ""
                }`}
              >
                <a
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    toggleTreeview("notifications");
                  }}
                >
                  <i className="fa fa-bell"></i>
                  <span className="title">Notification</span>
                  <span className="pull-right-container">
                    <i
                      className={`fa fa-angle-left pull-right ${
                        isTreeviewOpen("notifications") ? "fa-angle-down" : ""
                      }`}
                    ></i>
                  </span>
                </a>
                <ul
                  className="treeview-menu"
                  style={{
                    display: isTreeviewOpen("notifications") ? "block" : "none",
                    transition: "all 0.3s ease-in-out",
                    overflow: "hidden",
                  }}
                >
                  <li
                    className={
                      isActive("/admin/notifications")
                        ? "active active-sub"
                        : ""
                    }
                  >
                    <a href="/admin/notifications">
                      <i className="fa fa-bell"></i>
                      <span className="title">Notifications</span>
                    </a>
                  </li>
                </ul>
              </li>

              {/* Reports */}
              <li className={isActive("/admin/reports") ? "active" : ""}>
                <a href="/admin/reports">
                  <i className="fa fa-bar-chart"></i>
                  <span className="title">Reports</span>
                </a>
              </li>

              {/* CMS */}
              <li
                className={`treeview ${
                  isTreeviewOpen("cms") ? "active menu-open" : ""
                }`}
              >
                <a
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    toggleTreeview("cms");
                  }}
                >
                  <i className="fa fa-graduation-cap"></i>
                  <span className="title">CMS</span>
                  <span className="pull-right-container">
                    <i
                      className={`fa fa-angle-left pull-right ${
                        isTreeviewOpen("cms") ? "fa-angle-down" : ""
                      }`}
                    ></i>
                  </span>
                </a>
                <ul
                  className="treeview-menu"
                  style={{
                    display: isTreeviewOpen("cms") ? "block" : "none",
                    transition: "all 0.3s ease-in-out",
                    overflow: "hidden",
                  }}
                >
                  <li
                    className={
                      isActive("/admin/about-us") ? "active active-sub" : ""
                    }
                  >
                    <a href="/admin/about-us">
                      <i className="fa fa-graduation-cap"></i>
                      <span className="title">About Us</span>
                    </a>
                  </li>
                  <li
                    className={
                      isActive("/admin/privacy-policy")
                        ? "active active-sub"
                        : ""
                    }
                  >
                    <a href="/admin/privacy-policy">
                      <i className="fa fa-graduation-cap"></i>
                      <span className="title">Privacy Policy</span>
                    </a>
                  </li>
                  <li
                    className={
                      isActive("/admin/terms-condition")
                        ? "active active-sub"
                        : ""
                    }
                  >
                    <a href="/admin/terms-condition">
                      <i className="fa fa-graduation-cap"></i>
                      <span className="title">Terms & Condition</span>
                    </a>
                  </li>
                </ul>
              </li>

              {/* Logout */}
              <li>
                <a
                  href="#"
                  title="Logout"
                  onClick={(e) => {
                    e.preventDefault();
                    handleLogout();
                  }}
                >
                  <i className="fa fa-arrow-left"></i>
                  <span className="title">Logout</span>
                </a>
              </li>
            </ul>
          </section>
        </aside>

        {/* Content Wrapper. Contains page content */}
        <div className="content-wrapper">
          {/* Main content */}
          <section className="content">
            {title && (
              <h3
                className="page-title"
                style={{
                  margin: "20px 0",
                  color: "#444444",
                  fontSize: "24px",
                  fontWeight: 400,
                }}
              >
                {title}
              </h3>
            )}

            <div className="row">
              <div className="col-md-12">{children}</div>
            </div>
          </section>
        </div>
      </div>
    </>
  );
}
