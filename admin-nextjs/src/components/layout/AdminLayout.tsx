'use client';

import React, { useState } from 'react';
import { Box, Toolbar, Container } from '@mui/material';
import TopBar from './TopBar';
import Sidebar from './Sidebar';

interface AdminLayoutProps {
  children: React.ReactNode;
  title?: string;
}

const drawerWidth = 230;

export default function AdminLayout({ children, title }: AdminLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(true);

  const handleDrawerToggle = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      <TopBar onMenuClick={handleDrawerToggle} sidebarOpen={sidebarOpen} />
      
      <Sidebar open={sidebarOpen} onClose={() => setSidebarOpen(false)} />
      
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          backgroundColor: '#ecf0f5',
          minHeight: '100vh',
          transition: (theme) =>
            theme.transitions.create(['margin', 'width'], {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.leavingScreen,
            }),
          marginLeft: sidebarOpen ? 0 : `-${drawerWidth}px`,
          width: sidebarOpen ? `calc(100% - ${drawerWidth}px)` : '100%',
        }}
      >
        <Toolbar />
        
        <Container 
          maxWidth={false} 
          sx={{ 
            mt: 3, 
            mb: 3,
            px: 3,
          }}
        >
          {title && (
            <Box sx={{ mb: 3 }}>
              <h3 style={{ 
                margin: 0, 
                color: '#444444', 
                fontSize: '24px', 
                fontWeight: 400 
              }}>
                {title}
              </h3>
            </Box>
          )}
          
          {children}
        </Container>
      </Box>
    </Box>
  );
}
