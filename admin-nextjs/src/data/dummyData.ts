import {
  User,
  Teacher,
  Student,
  Category,
  Course,
  Assignment,
  Quiz,
  LiveClass,
  CommunityPost,
  Notification,
  DashboardStats,
  CourseRequest,
} from "@/types";

// Dashboard Statistics
export const dashboardStats: DashboardStats = {
  total_students: 1250,
  total_teachers: 85,
  total_courses: 45,
  total_assignments: 128,
  pending_assignments: 23,
  live_classes_today: 8,
  new_registrations_today: 12,
  revenue_this_month: 45000,
};

// Categories
export const categories = [
  {
    id: 1,
    name: "Mathematics",
    class_name: "Grade 10-12",
    description: "Advanced mathematics courses",
    status: 1,
    sort_order: 1,
    image: "math.jpg",
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-15T10:00:00Z",
  },
  {
    id: 2,
    name: "Science",
    class_name: "Grade 9-12",
    description: "Physics, Chemistry, Biology courses",
    status: 1,
    sort_order: 2,
    image: "science.jpg",
    created_at: "2024-01-16T10:00:00Z",
    updated_at: "2024-01-16T10:00:00Z",
  },
  {
    id: 3,
    name: "English",
    class_name: "Grade 8-12",
    description: "English language and literature",
    status: 1,
    sort_order: 3,
    image: "english.jpg",
    created_at: "2024-01-17T10:00:00Z",
    updated_at: "2024-01-17T10:00:00Z",
  },
  {
    id: 4,
    name: "Computer Science",
    class_name: "Grade 11-12",
    description: "Programming and computer science",
    status: 1,
    sort_order: 4,
    image: "cs.jpg",
    created_at: "2024-01-18T10:00:00Z",
    updated_at: "2024-01-18T10:00:00Z",
  },
];

// Teachers
export const teachers: Teacher[] = [
  {
    id: 1,
    name: "Dr. Sarah Johnson",
    email: "<EMAIL>",
    role: "teacher",
    status: "active",
    subject: "Mathematics",
    experience: 8,
    qualification: "PhD in Mathematics",
    phone: "+1234567890",
    address: "123 Main St, City",
    fees_per_hour: 50,
    created_at: "2024-01-10T10:00:00Z",
    updated_at: "2024-01-10T10:00:00Z",
  },
  {
    id: 2,
    name: "Prof. Michael Chen",
    email: "<EMAIL>",
    role: "teacher",
    status: "active",
    subject: "Physics",
    experience: 12,
    qualification: "PhD in Physics",
    phone: "+1234567891",
    address: "456 Oak Ave, City",
    fees_per_hour: 60,
    created_at: "2024-01-11T10:00:00Z",
    updated_at: "2024-01-11T10:00:00Z",
  },
  {
    id: 3,
    name: "Ms. Emily Davis",
    email: "<EMAIL>",
    role: "teacher",
    status: "active",
    subject: "English",
    experience: 6,
    qualification: "MA in English Literature",
    phone: "+1234567892",
    address: "789 Pine St, City",
    fees_per_hour: 45,
    created_at: "2024-01-12T10:00:00Z",
    updated_at: "2024-01-12T10:00:00Z",
  },
];

// Students
export const students: Student[] = [
  {
    id: 4,
    name: "John Smith",
    email: "<EMAIL>",
    role: "student",
    status: "active",
    class: "Grade 10",
    parent_name: "Robert Smith",
    parent_phone: "+1234567893",
    date_of_birth: "2008-05-15",
    address: "321 Elm St, City",
    created_at: "2024-01-20T10:00:00Z",
    updated_at: "2024-01-20T10:00:00Z",
  },
  {
    id: 5,
    name: "Emma Wilson",
    email: "<EMAIL>",
    role: "student",
    status: "active",
    class: "Grade 11",
    parent_name: "Lisa Wilson",
    parent_phone: "+1234567894",
    date_of_birth: "2007-08-22",
    address: "654 Maple Ave, City",
    created_at: "2024-01-21T10:00:00Z",
    updated_at: "2024-01-21T10:00:00Z",
  },
];

// Courses
export const courses = [
  {
    id: 1,
    title: "Advanced Calculus",
    name: "Advanced Calculus",
    description: "Comprehensive calculus course for advanced students",
    category_id: 1,
    category: categories[0],
    category_name: "Mathematics",
    price: 299,
    duration: "12 weeks",
    level: "Advanced",
    status: 1,
    featured: true,
    is_featured: true,
    image: "calculus.jpg",
    created_at: "2024-01-25T10:00:00Z",
    updated_at: "2024-01-25T10:00:00Z",
  },
  {
    id: 2,
    title: "Quantum Physics",
    name: "Quantum Physics",
    description: "Introduction to quantum mechanics and physics",
    category_id: 2,
    category: categories[1],
    category_name: "Science",
    price: 399,
    duration: "16 weeks",
    level: "Intermediate",
    status: 1,
    featured: false,
    is_featured: false,
    image: "physics.jpg",
    created_at: "2024-01-26T10:00:00Z",
    updated_at: "2024-01-26T10:00:00Z",
  },
  {
    id: 3,
    title: "Creative Writing",
    name: "Creative Writing",
    description: "Develop your creative writing skills",
    category_id: 3,
    category: categories[2],
    category_name: "English",
    price: 199,
    duration: "8 weeks",
    level: "Beginner",
    status: 1,
    featured: true,
    is_featured: true,
    image: "writing.jpg",
    created_at: "2024-01-27T10:00:00Z",
    updated_at: "2024-01-27T10:00:00Z",
  },
];

// Course Requests
export const courseRequests: CourseRequest[] = [
  {
    id: 1,
    course_id: 1,
    course: courses[0],
    instructor_id: 1,
    instructor: teachers[0],
    status: "pending",
    created_at: "2024-02-01T10:00:00Z",
    waiting_time: "2 hours ago",
  },
  {
    id: 2,
    course_id: 2,
    course: courses[1],
    instructor_id: 2,
    instructor: teachers[1],
    builder_id: 3,
    builder: {
      id: 3,
      name: "Course Builder",
      email: "<EMAIL>",
      role: "course_builder",
      status: "active",
      created_at: "2024-01-01T10:00:00Z",
      updated_at: "2024-01-01T10:00:00Z",
    },
    status: "assigned",
    created_at: "2024-01-30T10:00:00Z",
    waiting_time: "2 days ago",
  },
];

// Assignments
export const assignments: Assignment[] = [
  {
    id: 1,
    title: "Calculus Problem Set 1",
    description: "Solve the given calculus problems",
    course_id: 1,
    course: courses[0],
    due_date: "2024-02-15T23:59:59Z",
    max_marks: 100,
    status: "active",
    created_at: "2024-02-01T10:00:00Z",
  },
  {
    id: 2,
    title: "Physics Lab Report",
    description: "Submit your quantum physics lab report",
    course_id: 2,
    course: courses[1],
    due_date: "2024-02-20T23:59:59Z",
    max_marks: 50,
    status: "active",
    created_at: "2024-02-02T10:00:00Z",
  },
];

// Live Classes
export const liveClasses: LiveClass[] = [
  {
    id: 1,
    title: "Advanced Calculus - Derivatives",
    description: "Live session on derivatives and applications",
    course_id: 1,
    course: courses[0],
    teacher_id: 1,
    teacher: teachers[0],
    scheduled_at: "2024-02-10T14:00:00Z",
    duration: 60,
    meeting_link: "https://zoom.us/j/123456789",
    status: "scheduled",
    created_at: "2024-02-01T10:00:00Z",
  },
  {
    id: 2,
    title: "Quantum Physics - Wave Functions",
    description: "Understanding wave functions in quantum mechanics",
    course_id: 2,
    course: courses[1],
    teacher_id: 2,
    teacher: teachers[1],
    scheduled_at: "2024-02-11T15:00:00Z",
    duration: 90,
    meeting_link: "https://zoom.us/j/987654321",
    status: "scheduled",
    created_at: "2024-02-02T10:00:00Z",
  },
];

// Notifications
export const notifications: Notification[] = [
  {
    id: 1,
    title: "New Student Registration",
    message: "A new student has registered for the platform",
    type: "info",
    is_read: false,
    created_at: "2024-02-05T10:00:00Z",
  },
  {
    id: 2,
    title: "Assignment Submitted",
    message: "John Smith submitted Calculus Problem Set 1",
    type: "success",
    is_read: false,
    created_at: "2024-02-05T11:00:00Z",
  },
  {
    id: 3,
    title: "Live Class Starting Soon",
    message: "Advanced Calculus class starts in 30 minutes",
    type: "warning",
    is_read: true,
    created_at: "2024-02-05T13:30:00Z",
  },
];

// Admin Users
export const adminUsers: User[] = [
  {
    id: 1,
    name: "Admin User",
    email: "<EMAIL>",
    role: "admin",
    status: "active",
    created_at: "2024-01-01T10:00:00Z",
    updated_at: "2024-01-01T10:00:00Z",
  },
  {
    id: 2,
    name: "Super Admin",
    email: "<EMAIL>",
    role: "super_admin",
    status: "active",
    created_at: "2024-01-01T10:00:00Z",
    updated_at: "2024-01-01T10:00:00Z",
  },
];

// All Users (combining admin users, teachers, and students)
export const users: User[] = [
  ...adminUsers,
  ...teachers.map((teacher) => ({
    id: teacher.id,
    name: teacher.name,
    email: teacher.email,
    role: teacher.role,
    status: teacher.status,
    phone: teacher.phone,
    roles: ["Teacher"],
    created_at: teacher.created_at,
    updated_at: teacher.updated_at,
  })),
  ...students.map((student) => ({
    id: student.id,
    name: student.name,
    email: student.email,
    role: student.role,
    status: student.status,
    roles: ["Student"],
    created_at: student.created_at,
    updated_at: student.updated_at,
  })),
];

// Roles
export const roles = [
  {
    id: 1,
    name: "Admin",
    permissions: [
      "users_access",
      "users_create",
      "users_edit",
      "users_delete",
      "roles_access",
      "roles_create",
      "roles_edit",
      "roles_delete",
      "courses_access",
      "courses_create",
      "courses_edit",
      "courses_delete",
      "categories_access",
      "categories_create",
      "categories_edit",
      "categories_delete",
      "subjects_access",
      "subjects_create",
      "subjects_edit",
      "subjects_delete",
    ],
    created_at: "2024-01-01T10:00:00Z",
    updated_at: "2024-01-01T10:00:00Z",
  },
  {
    id: 2,
    name: "Director",
    permissions: [
      "users_access",
      "users_edit",
      "courses_access",
      "courses_create",
      "courses_edit",
      "categories_access",
      "categories_create",
      "categories_edit",
      "subjects_access",
      "subjects_create",
      "subjects_edit",
    ],
    created_at: "2024-01-01T10:00:00Z",
    updated_at: "2024-01-01T10:00:00Z",
  },
  {
    id: 3,
    name: "Course Builder",
    permissions: [
      "courses_access",
      "courses_create",
      "courses_edit",
      "subjects_access",
      "subjects_create",
      "subjects_edit",
    ],
    created_at: "2024-01-01T10:00:00Z",
    updated_at: "2024-01-01T10:00:00Z",
  },
];

// Subjects
export const subjects = [
  {
    id: 1,
    name: "Calculus I",
    description: "Introduction to differential and integral calculus",
    course_id: 1,
    code: "MATH101",
    credits: 3,
    status: 1,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-15T10:00:00Z",
  },
  {
    id: 2,
    name: "Quantum Mechanics",
    description: "Fundamentals of quantum physics",
    course_id: 2,
    code: "PHYS201",
    credits: 4,
    status: 1,
    created_at: "2024-01-16T10:00:00Z",
    updated_at: "2024-01-16T10:00:00Z",
  },
  {
    id: 3,
    name: "Creative Writing Workshop",
    description: "Hands-on creative writing practice",
    course_id: 3,
    code: "ENG301",
    credits: 2,
    status: 1,
    created_at: "2024-01-17T10:00:00Z",
    updated_at: "2024-01-17T10:00:00Z",
  },
];

// Classes
export const classes = [
  {
    id: 1,
    name: "Advanced Calculus - Morning",
    description: "Morning session for advanced calculus course",
    course_id: 1,
    teacher_id: 1,
    schedule: "Mon-Wed-Fri 9:00-10:30",
    room: "Room 101",
    capacity: 30,
    status: 1,
    created_at: "2024-01-20T10:00:00Z",
    updated_at: "2024-01-20T10:00:00Z",
  },
  {
    id: 2,
    name: "Quantum Physics Lab",
    description: "Laboratory sessions for quantum physics",
    course_id: 2,
    teacher_id: 2,
    schedule: "Tue-Thu 14:00-16:00",
    room: "Physics Lab A",
    capacity: 20,
    status: 1,
    created_at: "2024-01-21T10:00:00Z",
    updated_at: "2024-01-21T10:00:00Z",
  },
  {
    id: 3,
    name: "Creative Writing Circle",
    description: "Interactive creative writing sessions",
    course_id: 3,
    teacher_id: 3,
    schedule: "Wed 16:00-18:00",
    room: "Room 205",
    capacity: 15,
    status: 1,
    created_at: "2024-01-22T10:00:00Z",
    updated_at: "2024-01-22T10:00:00Z",
  },
];

// Quizzes
export const quizzes = [
  {
    id: 1,
    title: "Calculus Fundamentals Quiz",
    description: "Test your understanding of basic calculus concepts",
    subject_id: 1,
    duration: 60,
    total_marks: 100,
    passing_marks: 60,
    instructions: "Answer all questions. Show your work for partial credit.",
    status: 1,
    created_at: "2024-01-25T10:00:00Z",
    updated_at: "2024-01-25T10:00:00Z",
  },
  {
    id: 2,
    title: "Quantum Physics Midterm",
    description: "Comprehensive midterm examination",
    subject_id: 2,
    duration: 120,
    total_marks: 150,
    passing_marks: 90,
    instructions:
      "Read all questions carefully. Use the provided formula sheet.",
    status: 1,
    created_at: "2024-01-26T10:00:00Z",
    updated_at: "2024-01-26T10:00:00Z",
  },
  {
    id: 3,
    title: "Creative Writing Assessment",
    description: "Portfolio-based writing assessment",
    subject_id: 3,
    duration: 90,
    total_marks: 50,
    passing_marks: 30,
    instructions:
      "Submit your best creative writing pieces. Focus on originality and style.",
    status: 1,
    created_at: "2024-01-27T10:00:00Z",
    updated_at: "2024-01-27T10:00:00Z",
  },
];
