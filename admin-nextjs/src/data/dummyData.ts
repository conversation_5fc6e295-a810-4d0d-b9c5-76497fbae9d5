import {
  User,
  Teacher,
  Student,
  Category,
  Course,
  Assignment,
  Quiz,
  LiveClass,
  CommunityPost,
  Notification,
  DashboardStats,
  CourseRequest,
} from '@/types';

// Dashboard Statistics
export const dashboardStats: DashboardStats = {
  total_students: 1250,
  total_teachers: 85,
  total_courses: 45,
  total_assignments: 128,
  pending_assignments: 23,
  live_classes_today: 8,
  new_registrations_today: 12,
  revenue_this_month: 45000,
};

// Categories
export const categories: Category[] = [
  {
    id: 1,
    name: 'Mathematics',
    description: 'Advanced mathematics courses',
    status: 'active',
    sort_order: 1,
    created_at: '2024-01-15T10:00:00Z',
  },
  {
    id: 2,
    name: 'Science',
    description: 'Physics, Chemistry, Biology courses',
    status: 'active',
    sort_order: 2,
    created_at: '2024-01-16T10:00:00Z',
  },
  {
    id: 3,
    name: 'English',
    description: 'English language and literature',
    status: 'active',
    sort_order: 3,
    created_at: '2024-01-17T10:00:00Z',
  },
  {
    id: 4,
    name: 'Computer Science',
    description: 'Programming and computer science',
    status: 'active',
    sort_order: 4,
    created_at: '2024-01-18T10:00:00Z',
  },
];

// Teachers
export const teachers: Teacher[] = [
  {
    id: 1,
    name: 'Dr. <PERSON>',
    email: '<EMAIL>',
    role: 'teacher',
    status: 'active',
    subject: 'Mathematics',
    experience: 8,
    qualification: 'PhD in Mathematics',
    phone: '+1234567890',
    address: '123 Main St, City',
    fees_per_hour: 50,
    created_at: '2024-01-10T10:00:00Z',
    updated_at: '2024-01-10T10:00:00Z',
  },
  {
    id: 2,
    name: 'Prof. Michael Chen',
    email: '<EMAIL>',
    role: 'teacher',
    status: 'active',
    subject: 'Physics',
    experience: 12,
    qualification: 'PhD in Physics',
    phone: '+1234567891',
    address: '456 Oak Ave, City',
    fees_per_hour: 60,
    created_at: '2024-01-11T10:00:00Z',
    updated_at: '2024-01-11T10:00:00Z',
  },
  {
    id: 3,
    name: 'Ms. Emily Davis',
    email: '<EMAIL>',
    role: 'teacher',
    status: 'active',
    subject: 'English',
    experience: 6,
    qualification: 'MA in English Literature',
    phone: '+1234567892',
    address: '789 Pine St, City',
    fees_per_hour: 45,
    created_at: '2024-01-12T10:00:00Z',
    updated_at: '2024-01-12T10:00:00Z',
  },
];

// Students
export const students: Student[] = [
  {
    id: 4,
    name: 'John Smith',
    email: '<EMAIL>',
    role: 'student',
    status: 'active',
    class: 'Grade 10',
    parent_name: 'Robert Smith',
    parent_phone: '+1234567893',
    date_of_birth: '2008-05-15',
    address: '321 Elm St, City',
    created_at: '2024-01-20T10:00:00Z',
    updated_at: '2024-01-20T10:00:00Z',
  },
  {
    id: 5,
    name: 'Emma Wilson',
    email: '<EMAIL>',
    role: 'student',
    status: 'active',
    class: 'Grade 11',
    parent_name: 'Lisa Wilson',
    parent_phone: '+1234567894',
    date_of_birth: '2007-08-22',
    address: '654 Maple Ave, City',
    created_at: '2024-01-21T10:00:00Z',
    updated_at: '2024-01-21T10:00:00Z',
  },
];

// Courses
export const courses: Course[] = [
  {
    id: 1,
    name: 'Advanced Calculus',
    description: 'Comprehensive calculus course for advanced students',
    category_id: 1,
    category: categories[0],
    price: 299,
    duration: '12 weeks',
    status: 'active',
    is_featured: true,
    created_at: '2024-01-25T10:00:00Z',
  },
  {
    id: 2,
    name: 'Quantum Physics',
    description: 'Introduction to quantum mechanics and physics',
    category_id: 2,
    category: categories[1],
    price: 399,
    duration: '16 weeks',
    status: 'active',
    is_featured: false,
    created_at: '2024-01-26T10:00:00Z',
  },
  {
    id: 3,
    name: 'Creative Writing',
    description: 'Develop your creative writing skills',
    category_id: 3,
    category: categories[2],
    price: 199,
    duration: '8 weeks',
    status: 'active',
    is_featured: true,
    created_at: '2024-01-27T10:00:00Z',
  },
];

// Course Requests
export const courseRequests: CourseRequest[] = [
  {
    id: 1,
    course_id: 1,
    course: courses[0],
    instructor_id: 1,
    instructor: teachers[0],
    status: 'pending',
    created_at: '2024-02-01T10:00:00Z',
    waiting_time: '2 hours ago',
  },
  {
    id: 2,
    course_id: 2,
    course: courses[1],
    instructor_id: 2,
    instructor: teachers[1],
    builder_id: 3,
    builder: {
      id: 3,
      name: 'Course Builder',
      email: '<EMAIL>',
      role: 'course_builder',
      status: 'active',
      created_at: '2024-01-01T10:00:00Z',
      updated_at: '2024-01-01T10:00:00Z',
    },
    status: 'assigned',
    created_at: '2024-01-30T10:00:00Z',
    waiting_time: '2 days ago',
  },
];

// Assignments
export const assignments: Assignment[] = [
  {
    id: 1,
    title: 'Calculus Problem Set 1',
    description: 'Solve the given calculus problems',
    course_id: 1,
    course: courses[0],
    due_date: '2024-02-15T23:59:59Z',
    max_marks: 100,
    status: 'active',
    created_at: '2024-02-01T10:00:00Z',
  },
  {
    id: 2,
    title: 'Physics Lab Report',
    description: 'Submit your quantum physics lab report',
    course_id: 2,
    course: courses[1],
    due_date: '2024-02-20T23:59:59Z',
    max_marks: 50,
    status: 'active',
    created_at: '2024-02-02T10:00:00Z',
  },
];

// Live Classes
export const liveClasses: LiveClass[] = [
  {
    id: 1,
    title: 'Advanced Calculus - Derivatives',
    description: 'Live session on derivatives and applications',
    course_id: 1,
    course: courses[0],
    teacher_id: 1,
    teacher: teachers[0],
    scheduled_at: '2024-02-10T14:00:00Z',
    duration: 60,
    meeting_link: 'https://zoom.us/j/123456789',
    status: 'scheduled',
    created_at: '2024-02-01T10:00:00Z',
  },
  {
    id: 2,
    title: 'Quantum Physics - Wave Functions',
    description: 'Understanding wave functions in quantum mechanics',
    course_id: 2,
    course: courses[1],
    teacher_id: 2,
    teacher: teachers[1],
    scheduled_at: '2024-02-11T15:00:00Z',
    duration: 90,
    meeting_link: 'https://zoom.us/j/987654321',
    status: 'scheduled',
    created_at: '2024-02-02T10:00:00Z',
  },
];

// Notifications
export const notifications: Notification[] = [
  {
    id: 1,
    title: 'New Student Registration',
    message: 'A new student has registered for the platform',
    type: 'info',
    is_read: false,
    created_at: '2024-02-05T10:00:00Z',
  },
  {
    id: 2,
    title: 'Assignment Submitted',
    message: 'John Smith submitted Calculus Problem Set 1',
    type: 'success',
    is_read: false,
    created_at: '2024-02-05T11:00:00Z',
  },
  {
    id: 3,
    title: 'Live Class Starting Soon',
    message: 'Advanced Calculus class starts in 30 minutes',
    type: 'warning',
    is_read: true,
    created_at: '2024-02-05T13:30:00Z',
  },
];

// Admin Users
export const adminUsers: User[] = [
  {
    id: 1,
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'admin',
    status: 'active',
    created_at: '2024-01-01T10:00:00Z',
    updated_at: '2024-01-01T10:00:00Z',
  },
  {
    id: 2,
    name: 'Super Admin',
    email: '<EMAIL>',
    role: 'super_admin',
    status: 'active',
    created_at: '2024-01-01T10:00:00Z',
    updated_at: '2024-01-01T10:00:00Z',
  },
];
