'use client';

import { createTheme } from '@mui/material/styles';

// Create a theme that matches the Laravel admin design
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#3c8dbc', // AdminLTE blue color
      light: '#5ba7d6',
      dark: '#2a6496',
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#f39c12', // AdminLTE orange
      light: '#f5b041',
      dark: '#d68910',
      contrastText: '#ffffff',
    },
    background: {
      default: '#ecf0f5', // AdminLTE background
      paper: '#ffffff',
    },
    text: {
      primary: '#444444',
      secondary: '#666666',
    },
    error: {
      main: '#dd4b39', // AdminLTE red
    },
    warning: {
      main: '#f39c12', // AdminLTE orange
    },
    info: {
      main: '#00c0ef', // AdminLTE light blue
    },
    success: {
      main: '#00a65a', // AdminLTE green
    },
  },
  typography: {
    fontFamily: '"Source Sans Pro", "Helvetica Neue", Helvetica, Arial, sans-serif',
    h1: {
      fontSize: '2.125rem',
      fontWeight: 400,
      lineHeight: 1.2,
    },
    h2: {
      fontSize: '1.875rem',
      fontWeight: 400,
      lineHeight: 1.2,
    },
    h3: {
      fontSize: '1.5rem',
      fontWeight: 400,
      lineHeight: 1.2,
    },
    h4: {
      fontSize: '1.25rem',
      fontWeight: 400,
      lineHeight: 1.2,
    },
    h5: {
      fontSize: '1.125rem',
      fontWeight: 400,
      lineHeight: 1.2,
    },
    h6: {
      fontSize: '1rem',
      fontWeight: 400,
      lineHeight: 1.2,
    },
    body1: {
      fontSize: '14px',
      lineHeight: 1.42857143,
    },
    body2: {
      fontSize: '12px',
      lineHeight: 1.42857143,
    },
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          backgroundColor: '#ecf0f5',
          fontFamily: '"Source Sans Pro", "Helvetica Neue", Helvetica, Arial, sans-serif',
          fontSize: '14px',
          lineHeight: 1.42857143,
          color: '#444444',
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: '#3c8dbc',
          boxShadow: 'none',
          borderBottom: '1px solid #d2d6de',
        },
      },
    },
    MuiDrawer: {
      styleOverrides: {
        paper: {
          backgroundColor: '#222d32',
          color: '#b8c7ce',
          width: 230,
        },
      },
    },
    MuiListItem: {
      styleOverrides: {
        root: {
          '&:hover': {
            backgroundColor: '#1e282c',
          },
          '&.Mui-selected': {
            backgroundColor: '#1e282c',
            borderLeft: '3px solid #3c8dbc',
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: '3px',
        },
        containedPrimary: {
          backgroundColor: '#3c8dbc',
          '&:hover': {
            backgroundColor: '#2a6496',
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: '3px',
          boxShadow: '0 1px 1px rgba(0,0,0,0.1)',
        },
      },
    },
    MuiTableHead: {
      styleOverrides: {
        root: {
          backgroundColor: '#f4f4f4',
        },
      },
    },
  },
});

export default theme;
