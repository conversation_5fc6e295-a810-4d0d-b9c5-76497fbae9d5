'use client';

import React, { useState } from 'react';
import AdminLTELayout from '@/components/layout/AdminLTELayout';

// Demo requests data
const demoRequestsData = [
  {
    id: 1,
    query_no: 'DM001',
    student_name: '<PERSON>',
    parent_name: '<PERSON>',
    email: '<EMAIL>',
    country_code: '+1',
    phone: '234567890',
    country: 'United States',
    timezone: 'EST',
    class_name: 'Grade 10',
    subject: 'Mathematics',
    plan_name: 'Premium Plan',
    price_range: '$50-$100',
    assigned_teacher: 'Dr. <PERSON>',
    preferred_topic: 'Algebra',
    preferred_time: '4:00 PM - 5:00 PM',
    curriculum: 'US Common Core',
    currency: 'USD',
    enroll_status: 'Yes',
    feedback_status: 'Completed',
    status: 'assigned',
    created_at: '2024-02-01T10:00:00Z'
  },
  {
    id: 2,
    query_no: 'DM002',
    student_name: '<PERSON>',
    parent_name: '<PERSON>',
    email: 'emma.w<PERSON><PERSON>@example.com',
    country_code: '+44',
    phone: '7123456789',
    country: 'United Kingdom',
    timezone: 'GMT',
    class_name: 'Grade 11',
    subject: 'Physics',
    plan_name: 'Standard Plan',
    price_range: '$30-$60',
    assigned_teacher: 'Not Assign',
    preferred_topic: 'Quantum Mechanics',
    preferred_time: '2:00 PM - 3:00 PM',
    curriculum: 'UK GCSE',
    currency: 'GBP',
    enroll_status: 'No',
    feedback_status: 'Pending',
    status: 'pending',
    created_at: '2024-02-02T10:00:00Z'
  },
  {
    id: 3,
    query_no: 'DM003',
    student_name: 'Alex Johnson',
    parent_name: 'Mark Johnson',
    email: '<EMAIL>',
    country_code: '+91',
    phone: '9876543210',
    country: 'India',
    timezone: 'IST',
    class_name: 'Grade 12',
    subject: 'Computer Science',
    plan_name: 'Premium Plan',
    price_range: '$40-$80',
    assigned_teacher: 'Dr. Robert Wilson',
    preferred_topic: 'Data Structures',
    preferred_time: '6:00 PM - 7:00 PM',
    curriculum: 'CBSE',
    currency: 'INR',
    enroll_status: 'Yes',
    feedback_status: 'Completed',
    status: 'completed',
    created_at: '2024-02-03T10:00:00Z'
  },
  {
    id: 4,
    query_no: 'DM004',
    student_name: 'Sophie Brown',
    parent_name: 'Jennifer Brown',
    email: '<EMAIL>',
    country_code: '+61',
    phone: '*********',
    country: 'Australia',
    timezone: 'AEST',
    class_name: 'Grade 9',
    subject: 'English',
    plan_name: 'Basic Plan',
    price_range: '$25-$50',
    assigned_teacher: 'Not Assign',
    preferred_topic: 'Creative Writing',
    preferred_time: '3:00 PM - 4:00 PM',
    curriculum: 'Australian Curriculum',
    currency: 'AUD',
    enroll_status: 'No',
    feedback_status: 'Pending',
    status: 'pending',
    created_at: '2024-02-04T10:00:00Z'
  }
];

export default function DemoRequestsPage() {
  const [demoRequestList, setDemoRequestList] = useState(demoRequestsData);
  const [filters, setFilters] = useState({
    start_date: '',
    end_date: '',
    status: ''
  });

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleAssignTeacher = (requestId: number) => {
    // Handle teacher assignment logic
    console.log('Assigning teacher for request:', requestId);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: '2-digit'
    });
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending':
        return <span className="label label-warning">Pending</span>;
      case 'assigned':
        return <span className="label label-info">Assigned</span>;
      case 'completed':
        return <span className="label label-success">Completed</span>;
      default:
        return <span className="label label-default">{status}</span>;
    }
  };

  return (
    <AdminLTELayout>
      <div className="row">
        <div className="col-md-12">
          <div className="panel panel-default">
            <div className="panel-heading">All Demo Requests</div>
            <div className="panel-body">
              <div id="message"></div>
              
              {/* Filters */}
              <form name="filterfrm" method="GET">
                <div className="row">
                  <div className="form-group col-md-3">
                    <label>Start Date</label>
                    <input 
                      type="date" 
                      name="start_date" 
                      className="form-control" 
                      value={filters.start_date} 
                      onChange={handleFilterChange}
                    />
                  </div>
                  
                  <div className="form-group col-md-3">
                    <label>End Date</label>
                    <input 
                      type="date" 
                      name="end_date" 
                      className="form-control" 
                      value={filters.end_date} 
                      onChange={handleFilterChange}
                    />
                  </div>
                  
                  <div className="form-group col-md-3">
                    <label>Status</label>
                    <select name="status" className="form-control" value={filters.status} onChange={handleFilterChange}>
                      <option value="">All Status</option>
                      <option value="pending">Pending</option>
                      <option value="assigned">Assigned</option>
                      <option value="completed">Completed</option>
                    </select>
                  </div>
                  
                  <div className="form-group col-md-3">
                    <label>&nbsp;</label>
                    <div>
                      <button type="submit" className="btn btn-primary">Filter</button>
                      {' '}
                      <button type="button" className="btn btn-success">Download CSV</button>
                    </div>
                  </div>
                </div>
              </form>
            </div>
            
            <div className="panel-body table-responsive scroll-table">
              <table className={`table table-bordered table-striped dt-select ${demoRequestList.length > 0 ? 'datatable1' : ''}`}>
                <thead>
                  <tr>
                    <th>Query No</th>
                    <th>Email</th>
                    <th>Student Name</th>
                    <th>Parent Name</th>
                    <th>Country Code</th>
                    <th>Phone</th>
                    <th>Class/Category</th>
                    <th>Subject/Courses</th>
                    <th>Student Select Plan</th>
                    <th>Price Range</th>
                    <th>Assign Teacher</th>
                    <th>Preferred Topic</th>
                    <th>Preferred Time</th>
                    <th>Curriculum</th>
                    <th>Currency</th>
                    <th>Enroll Purchase Class</th>
                    <th>Feedback Status</th>
                    <th>Status</th>
                    <th>Created At</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  {demoRequestList.length > 0 ? (
                    demoRequestList.map((request, index) => (
                      <tr key={request.id}>
                        <td>{request.query_no}</td>
                        <td>
                          <a href={`mailto:${request.email}`} target="_blank" rel="noopener noreferrer">
                            {request.email}
                          </a>
                        </td>
                        <td>{request.student_name}</td>
                        <td>{request.parent_name}</td>
                        <td>{request.country_code}</td>
                        <td>
                          <a 
                            href={`https://api.whatsapp.com/send?phone=${request.country_code.replace('+', '')}${request.phone}`} 
                            target="_blank" 
                            rel="noopener noreferrer"
                          >
                            {request.country_code} {request.phone}
                          </a>
                        </td>
                        <td>{request.class_name}</td>
                        <td>{request.subject}</td>
                        <td>{request.plan_name}</td>
                        <td>{request.price_range}</td>
                        <td>{request.assigned_teacher}</td>
                        <td>{request.preferred_topic}</td>
                        <td>{request.preferred_time}</td>
                        <td>{request.curriculum}</td>
                        <td>{request.currency}</td>
                        <td>{request.enroll_status}</td>
                        <td>{request.feedback_status}</td>
                        <td>{getStatusLabel(request.status)}</td>
                        <td>{formatDate(request.created_at)}</td>
                        <td>
                          <a href={`/admin/demo-requests/view/${request.id}`} className="btn btn-info btn-sm">
                            View
                          </a>
                          {' '}
                          {request.assigned_teacher === 'Not Assign' && (
                            <button 
                              className="btn btn-primary btn-sm"
                              onClick={() => handleAssignTeacher(request.id)}
                            >
                              Assign Teacher
                            </button>
                          )}
                          {' '}
                          <a href={`/admin/demo-requests/feedback/${request.id}`} className="btn btn-warning btn-sm">
                            Feedback
                          </a>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={20}>No entries in table</td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </AdminLTELayout>
  );
}
