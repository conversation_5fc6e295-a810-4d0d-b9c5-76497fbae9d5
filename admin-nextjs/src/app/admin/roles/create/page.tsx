'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import AdminLTELayout from '@/components/layout/AdminLTELayout';

interface RoleFormData {
  name: string;
  permissions: string[];
}

const availablePermissions = [
  { id: 'users_access', name: 'Users Access' },
  { id: 'users_create', name: 'Users Create' },
  { id: 'users_edit', name: 'Users Edit' },
  { id: 'users_delete', name: 'Users Delete' },
  { id: 'roles_access', name: 'Roles Access' },
  { id: 'roles_create', name: 'Roles Create' },
  { id: 'roles_edit', name: 'Roles Edit' },
  { id: 'roles_delete', name: 'Roles Delete' },
  { id: 'courses_access', name: 'Courses Access' },
  { id: 'courses_create', name: 'Courses Create' },
  { id: 'courses_edit', name: 'Courses Edit' },
  { id: 'courses_delete', name: 'Courses Delete' },
  { id: 'categories_access', name: 'Categories Access' },
  { id: 'categories_create', name: 'Categories Create' },
  { id: 'categories_edit', name: 'Categories Edit' },
  { id: 'categories_delete', name: 'Categories Delete' },
  { id: 'subjects_access', name: 'Subjects Access' },
  { id: 'subjects_create', name: 'Subjects Create' },
  { id: 'subjects_edit', name: 'Subjects Edit' },
  { id: 'subjects_delete', name: 'Subjects Delete' },
];

export default function CreateRolePage() {
  const router = useRouter();
  const [formData, setFormData] = useState<RoleFormData>({
    name: '',
    permissions: []
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handlePermissionChange = (permissionId: string) => {
    setFormData(prev => ({
      ...prev,
      permissions: prev.permissions.includes(permissionId)
        ? prev.permissions.filter(p => p !== permissionId)
        : [...prev.permissions, permissionId]
    }));
    
    if (errors.permissions) {
      setErrors(prev => ({
        ...prev,
        permissions: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Role name is required';
    } else if (formData.name.length < 2) {
      newErrors.name = 'Role name must be at least 2 characters';
    }

    if (formData.permissions.length === 0) {
      newErrors.permissions = 'At least one permission is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // In a real app, you would make an API call here
      console.log('Creating role:', formData);
      
      // Show success message
      alert('Role created successfully!');
      
      // Redirect to roles list
      router.push('/admin/roles');
    } catch (error) {
      console.error('Error creating role:', error);
      alert('Error creating role. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <AdminLTELayout>
      <h3 className="page-title">Create Role</h3>

      <div className="panel panel-default">
        <div className="panel-heading">
          Create new role
        </div>

        <div className="panel-body">
          <form onSubmit={handleSubmit}>
            <div className="row">
              <div className="col-xs-12 form-group">
                <label htmlFor="name" className="control-label">Role Name*</label>
                <input
                  type="text"
                  name="name"
                  className="form-control"
                  placeholder="Enter role name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                />
                {errors.name && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.name}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-xs-12 form-group">
                <label className="control-label">Permissions*</label>
                <div style={{ border: '1px solid #ddd', padding: '10px', borderRadius: '4px', maxHeight: '300px', overflowY: 'auto' }}>
                  {availablePermissions.map(permission => (
                    <div key={permission.id} className="checkbox" style={{ marginBottom: '5px' }}>
                      <label style={{ fontWeight: 'normal', cursor: 'pointer' }}>
                        <input
                          type="checkbox"
                          checked={formData.permissions.includes(permission.id)}
                          onChange={() => handlePermissionChange(permission.id)}
                          style={{ marginRight: '8px' }}
                        />
                        {permission.name}
                      </label>
                    </div>
                  ))}
                </div>
                {errors.permissions && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.permissions}
                  </p>
                )}
              </div>
            </div>

            <div>
              <button 
                type="submit" 
                className="btn btn-danger"
                disabled={loading}
                style={{
                  backgroundColor: '#375dbc',
                  borderColor: '#375dbc'
                }}
              >
                {loading ? 'Creating...' : 'Save'}
              </button>
              {' '}
              <a href="/admin/roles" className="btn btn-default">
                Cancel
              </a>
            </div>
          </form>
        </div>
      </div>
    </AdminLTELayout>
  );
}
