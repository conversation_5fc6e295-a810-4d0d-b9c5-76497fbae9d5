'use client';

import React from 'react';
import AdminLTELayout from '@/components/layout/AdminLTELayout';

// Dummy roles data that matches Laravel structure
const roles = [
  {
    id: 1,
    name: 'Admin',
    permissions: ['User Management', 'Permission', 'Role', 'Teacher Management', 'Student Management', 'Dashboard']
  },
  {
    id: 2,
    name: 'Teacher',
    permissions: ['Student Management', 'Assignments', 'Quiz', 'Live Classes', 'Dashboard']
  },
  {
    id: 3,
    name: 'Student',
    permissions: ['Dashboard', 'Assignments', 'Quiz']
  },
  {
    id: 4,
    name: 'Course Builder',
    permissions: ['Category', 'Assignments', 'Quiz', 'Dashboard']
  },
  {
    id: 5,
    name: 'Director',
    permissions: ['Teacher Management', 'Student Management', 'Reports', 'Dashboard']
  }
];

export default function RolesPage() {
  return (
    <AdminLTELayout>
      <h3 className="page-title">Roles</h3>
      <p>
        <a href="/admin/roles/create" className="btn btn-success">Add New</a>
      </p>

      <div className="panel panel-default">
        <div style={{ fontSize: '20px' }} className="panel-heading">
          List
        </div>

        <div className="panel-body scroll-table table-responsive">
          <table className={`table table-bordered table-striped ${roles.length > 0 ? 'datatable1' : ''} dt-select`}>
            <thead>
              <tr>
                <th style={{ fontSize: '15px' }}>Sr.No</th>
                <th style={{ fontSize: '15px' }}>Name</th>
                <th style={{ fontSize: '15px' }}>Permissions</th>
                <th style={{ fontSize: '15px' }}>Action</th>
              </tr>
            </thead>
            
            <tbody>
              {roles.length > 0 ? (
                roles.map((role, index) => (
                  <tr key={role.id} data-entry-id={role.id}>
                    <td>{index + 1}</td>
                    <td>{role.name}</td>
                    <td>
                      {role.permissions.map((permission, permIndex) => (
                        <span key={permIndex} className="label label-info label-many" style={{ marginRight: '5px', marginBottom: '2px', display: 'inline-block' }}>
                          {permission}
                        </span>
                      ))}
                    </td>
                    <td>
                      <a 
                        style={{ fontSize: '15px' }} 
                        href={`/admin/roles/edit/${role.id}`} 
                        className="btn btn-primary btn-sm"
                      >
                        Edit
                      </a>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4}>No entries in table</td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </AdminLTELayout>
  );
}
