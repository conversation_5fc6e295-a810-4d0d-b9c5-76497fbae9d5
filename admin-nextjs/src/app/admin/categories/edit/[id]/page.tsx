'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import AdminLTELayout from '@/components/layout/AdminLTELayout';
import { categories } from '@/data/dummyData';

interface CategoryFormData {
  name: string;
  description: string;
  status: string;
}

export default function EditCategoryPage() {
  const router = useRouter();
  const params = useParams();
  const categoryId = params.id as string;
  
  const [formData, setFormData] = useState<CategoryFormData>({
    name: '',
    description: '',
    status: '1'
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);

  useEffect(() => {
    // Load category data
    const loadCategory = async () => {
      try {
        // In a real app, you would fetch from API
        const category = categories.find(c => c.id === parseInt(categoryId));
        
        if (category) {
          setFormData({
            name: category.name,
            description: category.description || '',
            status: category.status.toString()
          });
        } else {
          alert('Category not found');
          router.push('/admin/categories');
        }
      } catch (error) {
        console.error('Error loading category:', error);
        alert('Error loading category');
      } finally {
        setInitialLoading(false);
      }
    };

    if (categoryId) {
      loadCategory();
    }
  }, [categoryId, router]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Category name is required';
    } else if (formData.name.length < 2) {
      newErrors.name = 'Category name must be at least 2 characters';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    } else if (formData.description.length < 10) {
      newErrors.description = 'Description must be at least 10 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // In a real app, you would make an API call here
      console.log('Updating category:', { id: categoryId, ...formData });
      
      // Show success message
      alert('Category updated successfully!');
      
      // Redirect to categories list
      router.push('/admin/categories');
    } catch (error) {
      console.error('Error updating category:', error);
      alert('Error updating category. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <AdminLTELayout>
        <div className="text-center">
          <p>Loading category...</p>
        </div>
      </AdminLTELayout>
    );
  }

  return (
    <AdminLTELayout>
      <h3 className="page-title">Edit Category</h3>

      <div className="panel panel-default">
        <div className="panel-heading">
          Edit category
        </div>

        <div className="panel-body">
          <form onSubmit={handleSubmit}>
            <div className="row">
              <div className="col-xs-12 form-group">
                <label htmlFor="name" className="control-label">Category Name*</label>
                <input
                  type="text"
                  name="name"
                  className="form-control"
                  placeholder="Enter category name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                />
                {errors.name && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.name}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-xs-12 form-group">
                <label htmlFor="description" className="control-label">Description*</label>
                <textarea
                  name="description"
                  className="form-control"
                  placeholder="Enter category description"
                  rows={4}
                  value={formData.description}
                  onChange={handleChange}
                  required
                />
                {errors.description && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.description}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-xs-12 form-group">
                <label htmlFor="status" className="control-label">Status*</label>
                <select
                  name="status"
                  className="form-control"
                  value={formData.status}
                  onChange={handleChange}
                  required
                >
                  <option value="1">Active</option>
                  <option value="0">Inactive</option>
                </select>
              </div>
            </div>

            <div>
              <button 
                type="submit" 
                className="btn btn-danger"
                disabled={loading}
                style={{
                  backgroundColor: '#375dbc',
                  borderColor: '#375dbc'
                }}
              >
                {loading ? 'Updating...' : 'Update'}
              </button>
              {' '}
              <a href="/admin/categories" className="btn btn-default">
                Cancel
              </a>
            </div>
          </form>
        </div>
      </div>
    </AdminLTELayout>
  );
}
