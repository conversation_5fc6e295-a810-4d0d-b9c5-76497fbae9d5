'use client';

import React, { useState } from 'react';
import AdminLTELayout from '@/components/layout/AdminLTELayout';

// Classes data
const classesData = [
  {
    id: 1,
    name: 'Grade 6',
    description: 'Elementary level education',
    status: 1,
    created_at: '2024-01-10T10:00:00Z',
    sort_id: 1
  },
  {
    id: 2,
    name: 'Grade 7',
    description: 'Middle school level education',
    status: 1,
    created_at: '2024-01-11T10:00:00Z',
    sort_id: 2
  },
  {
    id: 3,
    name: 'Grade 8',
    description: 'Middle school advanced level',
    status: 1,
    created_at: '2024-01-12T10:00:00Z',
    sort_id: 3
  },
  {
    id: 4,
    name: 'Grade 9',
    description: 'High school freshman level',
    status: 1,
    created_at: '2024-01-13T10:00:00Z',
    sort_id: 4
  },
  {
    id: 5,
    name: 'Grade 10',
    description: 'High school sophomore level',
    status: 1,
    created_at: '2024-01-14T10:00:00Z',
    sort_id: 5
  },
  {
    id: 6,
    name: 'Grade 11',
    description: 'High school junior level',
    status: 1,
    created_at: '2024-01-15T10:00:00Z',
    sort_id: 6
  },
  {
    id: 7,
    name: 'Grade 12',
    description: 'High school senior level',
    status: 1,
    created_at: '2024-01-16T10:00:00Z',
    sort_id: 7
  },
  {
    id: 8,
    name: 'Pre-K',
    description: 'Pre-kindergarten level',
    status: 0,
    created_at: '2024-01-17T10:00:00Z',
    sort_id: 8
  }
];

export default function ClassesPage() {
  const [classList, setClassList] = useState(classesData);

  const handleStatusToggle = (classId: number, currentStatus: number) => {
    const newStatus = currentStatus === 1 ? 2 : 1;
    setClassList(prev => 
      prev.map(classItem => 
        classItem.id === classId ? { ...classItem, status: newStatus } : classItem
      )
    );
  };

  const handleDelete = (classId: number) => {
    if (confirm('Are you sure you want to delete this?')) {
      setClassList(prev => prev.filter(classItem => classItem.id !== classId));
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: '2-digit'
    });
  };

  return (
    <AdminLTELayout>
      <div className="row">
        <div className="col-md-12">
          <div className="panel panel-default">
            <div style={{ fontSize: '20px' }} className="panel-heading">All Classes</div>
            <div className="panel-body">
              <div id="message"></div>
              
              <div className="row">
                <div className="col-md-12">
                  <div className="col-md-6 text-right">
                    
                  </div>
                  <div className="col-md-6">
                    <div className="box-default text-right">
                      <a className="btn btn-bitbucket float-right" href="/admin/classes/create">Add New Class</a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="panel-body table-responsive scroll-table">
              <table className={`table table-bordered table-striped dt-select ${classList.length > 0 ? 'datatable1' : ''}`}>
                <thead>
                  <tr>
                    <th>Sr.No</th>
                    <th>Class Name</th>
                    <th>Description</th>
                    <th>Status</th>
                    <th>Created At</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  {classList.length > 0 ? (
                    classList.map((classItem, index) => (
                      <tr key={classItem.id}>
                        <td>{index + 1}</td>
                        <td>{classItem.name}</td>
                        <td>{classItem.description}</td>
                        <td>
                          {classItem.status === 1 ? (
                            <a 
                              href="#" 
                              className="label label-success"
                              onClick={(e) => {
                                e.preventDefault();
                                handleStatusToggle(classItem.id, classItem.status);
                              }}
                            >
                              Active
                            </a>
                          ) : (
                            <a 
                              href="#" 
                              className="label label-danger"
                              onClick={(e) => {
                                e.preventDefault();
                                handleStatusToggle(classItem.id, classItem.status);
                              }}
                            >
                              Inactive
                            </a>
                          )}
                        </td>
                        <td>{formatDate(classItem.created_at)}</td>
                        <td>
                          <a href={`/admin/classes/edit/${classItem.id}`} className="btn btn-primary btn-sm">
                            Edit
                          </a>
                          {' '}
                          <button 
                            className="btn btn-primary btn-sm"
                            onClick={() => handleDelete(classItem.id)}
                          >
                            Delete
                          </button>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={6}>No entries in table</td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </AdminLTELayout>
  );
}
