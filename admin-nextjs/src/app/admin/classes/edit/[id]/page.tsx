'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import AdminLTELayout from '@/components/layout/AdminLTELayout';
import { classes, courses, teachers } from '@/data/dummyData';

interface ClassFormData {
  name: string;
  description: string;
  course_id: string;
  teacher_id: string;
  schedule: string;
  room: string;
  capacity: string;
  status: string;
}

export default function EditClassPage() {
  const router = useRouter();
  const params = useParams();
  const classId = params.id as string;
  
  const [formData, setFormData] = useState<ClassFormData>({
    name: '',
    description: '',
    course_id: '',
    teacher_id: '',
    schedule: '',
    room: '',
    capacity: '',
    status: '1'
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);

  useEffect(() => {
    // Load class data
    const loadClass = async () => {
      try {
        // In a real app, you would fetch from API
        const classItem = classes.find(c => c.id === parseInt(classId));
        
        if (classItem) {
          setFormData({
            name: classItem.name,
            description: classItem.description || '',
            course_id: classItem.course_id.toString(),
            teacher_id: classItem.teacher_id.toString(),
            schedule: classItem.schedule || '',
            room: classItem.room || '',
            capacity: classItem.capacity?.toString() || '',
            status: classItem.status.toString()
          });
        } else {
          alert('Class not found');
          router.push('/admin/classes');
        }
      } catch (error) {
        console.error('Error loading class:', error);
        alert('Error loading class');
      } finally {
        setInitialLoading(false);
      }
    };

    if (classId) {
      loadClass();
    }
  }, [classId, router]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Class name is required';
    } else if (formData.name.length < 2) {
      newErrors.name = 'Class name must be at least 2 characters';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    } else if (formData.description.length < 10) {
      newErrors.description = 'Description must be at least 10 characters';
    }

    if (!formData.course_id) {
      newErrors.course_id = 'Course is required';
    }

    if (!formData.teacher_id) {
      newErrors.teacher_id = 'Teacher is required';
    }

    if (!formData.schedule.trim()) {
      newErrors.schedule = 'Schedule is required';
    }

    if (!formData.room.trim()) {
      newErrors.room = 'Room is required';
    }

    if (!formData.capacity.trim()) {
      newErrors.capacity = 'Capacity is required';
    } else if (isNaN(Number(formData.capacity)) || Number(formData.capacity) < 1) {
      newErrors.capacity = 'Capacity must be a valid number greater than 0';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // In a real app, you would make an API call here
      console.log('Updating class:', { id: classId, ...formData });
      
      // Show success message
      alert('Class updated successfully!');
      
      // Redirect to classes list
      router.push('/admin/classes');
    } catch (error) {
      console.error('Error updating class:', error);
      alert('Error updating class. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <AdminLTELayout>
        <div className="text-center">
          <p>Loading class...</p>
        </div>
      </AdminLTELayout>
    );
  }

  return (
    <AdminLTELayout>
      <h3 className="page-title">Edit Class</h3>

      <div className="panel panel-default">
        <div className="panel-heading">
          Edit class
        </div>

        <div className="panel-body">
          <form onSubmit={handleSubmit}>
            <div className="row">
              <div className="col-xs-12 form-group">
                <label htmlFor="name" className="control-label">Class Name*</label>
                <input
                  type="text"
                  name="name"
                  className="form-control"
                  placeholder="Enter class name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                />
                {errors.name && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.name}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-xs-12 form-group">
                <label htmlFor="description" className="control-label">Description*</label>
                <textarea
                  name="description"
                  className="form-control"
                  placeholder="Enter class description"
                  rows={4}
                  value={formData.description}
                  onChange={handleChange}
                  required
                />
                {errors.description && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.description}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-md-6 form-group">
                <label htmlFor="course_id" className="control-label">Course*</label>
                <select
                  name="course_id"
                  className="form-control"
                  value={formData.course_id}
                  onChange={handleChange}
                  required
                >
                  <option value="">Select Course</option>
                  {courses.map(course => (
                    <option key={course.id} value={course.id}>
                      {course.title}
                    </option>
                  ))}
                </select>
                {errors.course_id && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.course_id}
                  </p>
                )}
              </div>

              <div className="col-md-6 form-group">
                <label htmlFor="teacher_id" className="control-label">Teacher*</label>
                <select
                  name="teacher_id"
                  className="form-control"
                  value={formData.teacher_id}
                  onChange={handleChange}
                  required
                >
                  <option value="">Select Teacher</option>
                  {teachers.map(teacher => (
                    <option key={teacher.id} value={teacher.id}>
                      {teacher.name}
                    </option>
                  ))}
                </select>
                {errors.teacher_id && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.teacher_id}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-md-6 form-group">
                <label htmlFor="schedule" className="control-label">Schedule*</label>
                <input
                  type="text"
                  name="schedule"
                  className="form-control"
                  placeholder="e.g., Mon-Wed-Fri 10:00-11:30"
                  value={formData.schedule}
                  onChange={handleChange}
                  required
                />
                {errors.schedule && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.schedule}
                  </p>
                )}
              </div>

              <div className="col-md-6 form-group">
                <label htmlFor="room" className="control-label">Room*</label>
                <input
                  type="text"
                  name="room"
                  className="form-control"
                  placeholder="e.g., Room 101, Lab A"
                  value={formData.room}
                  onChange={handleChange}
                  required
                />
                {errors.room && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.room}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-md-6 form-group">
                <label htmlFor="capacity" className="control-label">Capacity*</label>
                <input
                  type="number"
                  name="capacity"
                  className="form-control"
                  placeholder="Enter maximum students"
                  value={formData.capacity}
                  onChange={handleChange}
                  min="1"
                  required
                />
                {errors.capacity && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.capacity}
                  </p>
                )}
              </div>

              <div className="col-md-6 form-group">
                <label htmlFor="status" className="control-label">Status*</label>
                <select
                  name="status"
                  className="form-control"
                  value={formData.status}
                  onChange={handleChange}
                  required
                >
                  <option value="1">Active</option>
                  <option value="0">Inactive</option>
                </select>
              </div>
            </div>

            <div>
              <button 
                type="submit" 
                className="btn btn-danger"
                disabled={loading}
                style={{
                  backgroundColor: '#375dbc',
                  borderColor: '#375dbc'
                }}
              >
                {loading ? 'Updating...' : 'Update'}
              </button>
              {' '}
              <a href="/admin/classes" className="btn btn-default">
                Cancel
              </a>
            </div>
          </form>
        </div>
      </div>
    </AdminLTELayout>
  );
}
