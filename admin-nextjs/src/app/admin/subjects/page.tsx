'use client';

import React, { useState } from 'react';
import AdminLTELayout from '@/components/layout/AdminLTELayout';

// Subjects data
const subjectsData = [
  {
    id: 1,
    name: 'Mathematics',
    class_name: 'Grade 10-12',
    description: 'Advanced mathematics including algebra, geometry, and calculus',
    status: 1,
    created_at: '2024-01-10T10:00:00Z',
    sort_id: 1
  },
  {
    id: 2,
    name: 'Physics',
    class_name: 'Grade 11-12',
    description: 'Classical and modern physics concepts',
    status: 1,
    created_at: '2024-01-11T10:00:00Z',
    sort_id: 2
  },
  {
    id: 3,
    name: 'Chemistry',
    class_name: 'Grade 10-12',
    description: 'Organic, inorganic, and physical chemistry',
    status: 1,
    created_at: '2024-01-12T10:00:00Z',
    sort_id: 3
  },
  {
    id: 4,
    name: 'Biology',
    class_name: 'Grade 9-12',
    description: 'Life sciences and biological processes',
    status: 1,
    created_at: '2024-01-13T10:00:00Z',
    sort_id: 4
  },
  {
    id: 5,
    name: 'English Literature',
    class_name: 'Grade 6-12',
    description: 'Reading, writing, and literary analysis',
    status: 1,
    created_at: '2024-01-14T10:00:00Z',
    sort_id: 5
  },
  {
    id: 6,
    name: 'History',
    class_name: 'Grade 8-12',
    description: 'World history and historical analysis',
    status: 1,
    created_at: '2024-01-15T10:00:00Z',
    sort_id: 6
  },
  {
    id: 7,
    name: 'Computer Science',
    class_name: 'Grade 11-12',
    description: 'Programming, algorithms, and computer systems',
    status: 1,
    created_at: '2024-01-16T10:00:00Z',
    sort_id: 7
  },
  {
    id: 8,
    name: 'Art',
    class_name: 'Grade 6-12',
    description: 'Visual arts and creative expression',
    status: 0,
    created_at: '2024-01-17T10:00:00Z',
    sort_id: 8
  }
];

export default function SubjectsPage() {
  const [subjectList, setSubjectList] = useState(subjectsData);

  const handleStatusToggle = (subjectId: number, currentStatus: number) => {
    const newStatus = currentStatus === 1 ? 2 : 1;
    setSubjectList(prev => 
      prev.map(subject => 
        subject.id === subjectId ? { ...subject, status: newStatus } : subject
      )
    );
  };

  const handleDelete = (subjectId: number) => {
    if (confirm('Are you sure you want to delete this?')) {
      setSubjectList(prev => prev.filter(subject => subject.id !== subjectId));
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: '2-digit'
    });
  };

  return (
    <AdminLTELayout>
      <div className="row">
        <div className="col-md-12">
          <div className="panel panel-default">
            <div style={{ fontSize: '20px' }} className="panel-heading">All Subjects</div>
            <div className="panel-body">
              <div id="message"></div>
              
              <div className="row">
                <div className="col-md-12">
                  <div className="col-md-6 text-right">
                    
                  </div>
                  <div className="col-md-6">
                    <div className="box-default text-right">
                      <a className="btn btn-bitbucket float-right" href="/admin/subjects/create">Add New Subject</a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="panel-body table-responsive scroll-table">
              <table className={`table table-bordered table-striped dt-select ${subjectList.length > 0 ? 'datatable1' : ''}`}>
                <thead>
                  <tr>
                    <th>Sr.No</th>
                    <th>Subject Name</th>
                    <th>Class</th>
                    <th>Description</th>
                    <th>Status</th>
                    <th>Created At</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  {subjectList.length > 0 ? (
                    subjectList.map((subject, index) => (
                      <tr key={subject.id}>
                        <td>{index + 1}</td>
                        <td>{subject.name}</td>
                        <td>{subject.class_name}</td>
                        <td>
                          <textarea 
                            cols={30} 
                            rows={3} 
                            style={{ overflow: 'scroll' }} 
                            readOnly
                            value={subject.description}
                          />
                        </td>
                        <td>
                          {subject.status === 1 ? (
                            <a 
                              href="#" 
                              className="label label-success"
                              onClick={(e) => {
                                e.preventDefault();
                                handleStatusToggle(subject.id, subject.status);
                              }}
                            >
                              Active
                            </a>
                          ) : (
                            <a 
                              href="#" 
                              className="label label-danger"
                              onClick={(e) => {
                                e.preventDefault();
                                handleStatusToggle(subject.id, subject.status);
                              }}
                            >
                              Inactive
                            </a>
                          )}
                        </td>
                        <td>{formatDate(subject.created_at)}</td>
                        <td>
                          <a href={`/admin/subjects/edit/${subject.id}`} className="btn btn-primary btn-sm">
                            Edit
                          </a>
                          {' '}
                          <button 
                            className="btn btn-primary btn-sm"
                            onClick={() => handleDelete(subject.id)}
                          >
                            Delete
                          </button>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={7}>No entries in table</td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </AdminLTELayout>
  );
}
