'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import AdminLTELayout from '@/components/layout/AdminLTELayout';
import { courses } from '@/data/dummyData';

interface SubjectFormData {
  name: string;
  description: string;
  course_id: string;
  code: string;
  credits: string;
  status: string;
}

export default function CreateSubjectPage() {
  const router = useRouter();
  const [formData, setFormData] = useState<SubjectFormData>({
    name: '',
    description: '',
    course_id: '',
    code: '',
    credits: '',
    status: '1'
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Subject name is required';
    } else if (formData.name.length < 2) {
      newErrors.name = 'Subject name must be at least 2 characters';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    } else if (formData.description.length < 10) {
      newErrors.description = 'Description must be at least 10 characters';
    }

    if (!formData.course_id) {
      newErrors.course_id = 'Course is required';
    }

    if (!formData.code.trim()) {
      newErrors.code = 'Subject code is required';
    } else if (formData.code.length < 2) {
      newErrors.code = 'Subject code must be at least 2 characters';
    }

    if (!formData.credits.trim()) {
      newErrors.credits = 'Credits is required';
    } else if (isNaN(Number(formData.credits)) || Number(formData.credits) < 0) {
      newErrors.credits = 'Credits must be a valid number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // In a real app, you would make an API call here
      console.log('Creating subject:', formData);
      
      // Show success message
      alert('Subject created successfully!');
      
      // Redirect to subjects list
      router.push('/admin/subjects');
    } catch (error) {
      console.error('Error creating subject:', error);
      alert('Error creating subject. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <AdminLTELayout>
      <h3 className="page-title">Create Subject</h3>

      <div className="panel panel-default">
        <div className="panel-heading">
          Create new subject
        </div>

        <div className="panel-body">
          <form onSubmit={handleSubmit}>
            <div className="row">
              <div className="col-xs-12 form-group">
                <label htmlFor="name" className="control-label">Subject Name*</label>
                <input
                  type="text"
                  name="name"
                  className="form-control"
                  placeholder="Enter subject name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                />
                {errors.name && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.name}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-xs-12 form-group">
                <label htmlFor="description" className="control-label">Description*</label>
                <textarea
                  name="description"
                  className="form-control"
                  placeholder="Enter subject description"
                  rows={4}
                  value={formData.description}
                  onChange={handleChange}
                  required
                />
                {errors.description && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.description}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-md-6 form-group">
                <label htmlFor="course_id" className="control-label">Course*</label>
                <select
                  name="course_id"
                  className="form-control"
                  value={formData.course_id}
                  onChange={handleChange}
                  required
                >
                  <option value="">Select Course</option>
                  {courses.map(course => (
                    <option key={course.id} value={course.id}>
                      {course.title}
                    </option>
                  ))}
                </select>
                {errors.course_id && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.course_id}
                  </p>
                )}
              </div>

              <div className="col-md-6 form-group">
                <label htmlFor="code" className="control-label">Subject Code*</label>
                <input
                  type="text"
                  name="code"
                  className="form-control"
                  placeholder="e.g., CS101, MATH201"
                  value={formData.code}
                  onChange={handleChange}
                  required
                />
                {errors.code && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.code}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-md-6 form-group">
                <label htmlFor="credits" className="control-label">Credits*</label>
                <input
                  type="number"
                  name="credits"
                  className="form-control"
                  placeholder="Enter credits"
                  value={formData.credits}
                  onChange={handleChange}
                  min="0"
                  step="0.5"
                  required
                />
                {errors.credits && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.credits}
                  </p>
                )}
              </div>

              <div className="col-md-6 form-group">
                <label htmlFor="status" className="control-label">Status*</label>
                <select
                  name="status"
                  className="form-control"
                  value={formData.status}
                  onChange={handleChange}
                  required
                >
                  <option value="1">Active</option>
                  <option value="0">Inactive</option>
                </select>
              </div>
            </div>

            <div>
              <button 
                type="submit" 
                className="btn btn-danger"
                disabled={loading}
                style={{
                  backgroundColor: '#375dbc',
                  borderColor: '#375dbc'
                }}
              >
                {loading ? 'Creating...' : 'Save'}
              </button>
              {' '}
              <a href="/admin/subjects" className="btn btn-default">
                Cancel
              </a>
            </div>
          </form>
        </div>
      </div>
    </AdminLTELayout>
  );
}
