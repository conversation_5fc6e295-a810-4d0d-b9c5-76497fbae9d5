'use client';

import React, { useState } from 'react';
import AdminLTELayout from '@/components/layout/AdminLTELayout';
import { liveClasses, teachers } from '@/data/dummyData';

// Extended live classes data
const liveClassesData = [
  {
    id: 1,
    title: 'Advanced Calculus - Derivatives',
    description: 'Live session on derivatives and applications',
    added_by: 'Dr. <PERSON>',
    class_type: 'Regular',
    class_name: 'Grade 12',
    subject: 'Mathematics',
    meeting_id: 'CALC001',
    pass_code: 'calc123',
    start_time: '2024-02-10T14:00:00Z',
    end_time: '2024-02-10T15:00:00Z',
    duration: 60,
    meeting_link: 'https://zoom.us/j/123456789',
    status: 1,
    created_at: '2024-02-01T10:00:00Z'
  },
  {
    id: 2,
    title: 'Quantum Physics - Wave Functions',
    description: 'Understanding wave functions in quantum mechanics',
    added_by: 'Prof. <PERSON>',
    class_type: 'Advanced',
    class_name: 'Grade 11',
    subject: 'Physics',
    meeting_id: 'PHYS002',
    pass_code: 'phys456',
    start_time: '2024-02-11T15:00:00Z',
    end_time: '2024-02-11T16:30:00Z',
    duration: 90,
    meeting_link: 'https://zoom.us/j/987654321',
    status: 1,
    created_at: '2024-02-02T10:00:00Z'
  },
  {
    id: 3,
    title: 'Creative Writing Workshop',
    description: 'Interactive writing session',
    added_by: 'Ms. Emily Davis',
    class_type: 'Regular',
    class_name: 'Grade 10',
    subject: 'English',
    meeting_id: 'ENG003',
    pass_code: 'eng789',
    start_time: '2024-02-12T16:00:00Z',
    end_time: '2024-02-12T17:00:00Z',
    duration: 60,
    meeting_link: 'https://zoom.us/j/456789123',
    status: 0,
    created_at: '2024-02-03T10:00:00Z'
  },
  {
    id: 4,
    title: 'Data Structures Live Coding',
    description: 'Live coding session on data structures',
    added_by: 'Dr. Robert Wilson',
    class_type: 'Advanced',
    class_name: 'Grade 12',
    subject: 'Computer Science',
    meeting_id: 'CS004',
    pass_code: 'cs101',
    start_time: '2024-02-13T17:00:00Z',
    end_time: '2024-02-13T18:30:00Z',
    duration: 90,
    meeting_link: 'https://zoom.us/j/789123456',
    status: 1,
    created_at: '2024-02-04T10:00:00Z'
  }
];

export default function LiveClassesPage() {
  const [liveClassList, setLiveClassList] = useState(liveClassesData);
  const [filters, setFilters] = useState({
    class_type: '',
    class_id: '',
    subject_id: ''
  });

  const handleStatusToggle = (classId: number, currentStatus: number) => {
    const newStatus = currentStatus === 1 ? 0 : 1;
    setLiveClassList(prev => 
      prev.map(liveClass => 
        liveClass.id === classId ? { ...liveClass, status: newStatus } : liveClass
      )
    );
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  return (
    <AdminLTELayout>
      <div className="row">
        <div className="col-md-12">
          <div className="panel panel-default">
            <div className="panel-heading">All Live Classes</div>
            <div className="panel-body">
              <div id="message"></div>
              
              {/* Filters */}
              <form name="filterfrm" method="GET">
                <div className="row">
                  <div className="form-group col-md-2">
                    <select name="class_type" className="form-control" value={filters.class_type} onChange={handleFilterChange}>
                      <option value="">--Select Class Type--</option>
                      <option value="Regular">Regular</option>
                      <option value="Advanced">Advanced</option>
                    </select>
                  </div>
                  
                  <div className="form-group col-md-2">
                    <select name="class_id" className="form-control select2" value={filters.class_id} onChange={handleFilterChange}>
                      <option value="">--select class--</option>
                      <option value="10">Grade 10</option>
                      <option value="11">Grade 11</option>
                      <option value="12">Grade 12</option>
                    </select>
                  </div>
                  
                  <div className="form-group col-md-2">
                    <select name="subject_id" className="form-control select2" value={filters.subject_id} onChange={handleFilterChange}>
                      <option value="">--select subject--</option>
                      <option value="1">Mathematics</option>
                      <option value="2">Physics</option>
                      <option value="3">English</option>
                      <option value="4">Computer Science</option>
                    </select>
                  </div>
                  
                  <div className="form-group col-md-2">
                    <button type="submit" className="btn btn-primary">Filter</button>
                  </div>
                </div>
              </form>
              
              <div className="row">
                <div className="col-md-12">
                  <div className="col-md-6 text-right">
                    
                  </div>
                  <div className="col-md-6">
                    <div className="box-default text-right">
                      <a className="btn btn-bitbucket float-right" href="/admin/live-classes/create">Add New Live Class</a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="panel-body table-responsive">
              <table className={`table table-bordered table-striped dt-select ${liveClassList.length > 0 ? 'datatable1' : ''}`} id="courses1">
                <thead>
                  <tr>
                    <th>#</th>
                    <th>Added By</th>
                    <th>Class Type</th>
                    <th>Class</th>
                    <th>Subject</th>
                    <th>Title</th>
                    <th>Start Time</th>
                    <th>End Time</th>
                    <th>Status</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  {liveClassList.length > 0 ? (
                    liveClassList.map((liveClass, index) => (
                      <tr key={liveClass.id}>
                        <td>{index + 1}</td>
                        <td>{liveClass.added_by}</td>
                        <td>{liveClass.class_type}</td>
                        <td>{liveClass.class_name}</td>
                        <td>{liveClass.subject}</td>
                        <td>{liveClass.title}</td>
                        <td>{formatDateTime(liveClass.start_time)}</td>
                        <td>{formatDateTime(liveClass.end_time)}</td>
                        <td>
                          {liveClass.status === 1 ? (
                            <a 
                              href="#" 
                              className="label label-success"
                              onClick={(e) => {
                                e.preventDefault();
                                handleStatusToggle(liveClass.id, liveClass.status);
                              }}
                            >
                              Active
                            </a>
                          ) : (
                            <a 
                              href="#" 
                              className="label label-danger"
                              onClick={(e) => {
                                e.preventDefault();
                                handleStatusToggle(liveClass.id, liveClass.status);
                              }}
                            >
                              Inactive
                            </a>
                          )}
                        </td>
                        <td>
                          <a href={`/admin/live-classes/edit/${liveClass.id}`} className="btn btn-primary btn-sm">Edit</a>
                          {' '}
                          <a href={`/admin/live-classes/view/${liveClass.id}`} className="btn btn-info btn-sm">View</a>
                          {' '}
                          <a href={liveClass.meeting_link} target="_blank" rel="noopener noreferrer" className="btn btn-success btn-sm">
                            Join
                          </a>
                          {' '}
                          <button 
                            className="btn btn-danger btn-sm"
                            onClick={() => {
                              if (confirm('Are you sure you want to delete this live class?')) {
                                setLiveClassList(prev => prev.filter(lc => lc.id !== liveClass.id));
                              }
                            }}
                          >
                            Delete
                          </button>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={10}>No entries in table</td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </AdminLTELayout>
  );
}
