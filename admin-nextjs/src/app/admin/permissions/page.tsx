'use client';

import React from 'react';
import AdminLTELayout from '@/components/layout/AdminLTELayout';

// Dummy permissions data that matches Laravel structure
const permissions = [
  { id: 1, name: 'users_manage', display_name: 'User Management' },
  { id: 2, name: 'Permission', display_name: 'Permission' },
  { id: 3, name: 'role', display_name: 'Role' },
  { id: 4, name: 'teacher_manage', display_name: 'Teacher Management' },
  { id: 5, name: 'student_management', display_name: 'Student Management' },
  { id: 6, name: 'student_class', display_name: 'Student Class' },
  { id: 7, name: 'category', display_name: 'Category' },
  { id: 8, name: 'student_apply_for_demo', display_name: 'Student Apply For Demo' },
  { id: 9, name: 'assignments', display_name: 'Assignments' },
  { id: 10, name: 'quiz', display_name: 'Quiz' },
  { id: 11, name: 'liveclasses', display_name: 'Live Classes' },
  { id: 12, name: 'community_posts', display_name: 'Community Posts' },
  { id: 13, name: 'notifications', display_name: 'Notifications' },
  { id: 14, name: 'reports', display_name: 'Reports' },
  { id: 15, name: 'dashboard', display_name: 'Dashboard' },
];

export default function PermissionsPage() {
  return (
    <AdminLTELayout>
      <h3 className="page-title">Permissions</h3>
      <p style={{ display: 'none' }}>
        <a href="/admin/permissions/create" className="btn btn-success">Add New</a>
      </p>

      <div className="panel panel-default">
        <div style={{ fontSize: '20px' }} className="panel-heading">
          List
        </div>

        <div className="panel-body scroll-table table-responsive">
          <table className={`table table-bordered table-striped ${permissions.length > 0 ? 'datatable1' : ''} dt-select`}>
            <thead>
              <tr>
                <th style={{ fontSize: '15px' }}>Sr.No</th>
                <th style={{ fontSize: '15px' }}>Name</th>
                <th style={{ fontSize: '15px' }}>Display Name</th>
                <th style={{ display: 'none' }}>&nbsp;</th>
              </tr>
            </thead>
            
            <tbody>
              {permissions.length > 0 ? (
                permissions.map((permission, index) => (
                  <tr key={permission.id} data-entry-id={permission.id}>
                    <td>{index + 1}</td>
                    <td>{permission.name}</td>
                    <td>{permission.display_name}</td>
                    
                    <td style={{ display: 'none' }}>
                      <a href={`/admin/permissions/edit/${permission.id}`} className="btn btn-xs btn-info">
                        Edit
                      </a>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={3}>No entries in table</td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </AdminLTELayout>
  );
}
