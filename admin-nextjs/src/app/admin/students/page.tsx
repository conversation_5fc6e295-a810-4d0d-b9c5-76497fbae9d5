'use client';

import React, { useState } from 'react';
import AdminLTELayout from '@/components/layout/AdminLTELayout';
import { students } from '@/data/dummyData';

// Extended students data
const studentsData = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+1234567893',
    class: 'Grade 10',
    parent_name: '<PERSON>',
    parent_phone: '+1234567894',
    date_of_birth: '2008-05-15',
    status: 1,
    created_at: '2024-01-20T10:00:00Z',
    role_id: 3
  },
  {
    id: 2,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+1234567895',
    class: 'Grade 11',
    parent_name: '<PERSON>',
    parent_phone: '+1234567896',
    date_of_birth: '2007-08-22',
    status: 1,
    created_at: '2024-01-21T10:00:00Z',
    role_id: 3
  },
  {
    id: 3,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+1234567897',
    class: 'Grade 9',
    parent_name: '<PERSON>',
    parent_phone: '+1234567898',
    date_of_birth: '2009-03-10',
    status: 0,
    created_at: '2024-01-22T10:00:00Z',
    role_id: 3
  },
  {
    id: 4,
    name: 'Sophie <PERSON>',
    email: '<EMAIL>',
    phone: '+1234567899',
    class: 'Grade 12',
    parent_name: '<PERSON>',
    parent_phone: '+1234567900',
    date_of_birth: '2006-11-05',
    status: 1,
    created_at: '2024-01-23T10:00:00Z',
    role_id: 3
  }
];

export default function StudentsPage() {
  const [studentList, setStudentList] = useState(studentsData);

  const handleStatusToggle = (studentId: number, currentStatus: number) => {
    const newStatus = currentStatus === 1 ? 0 : 1;
    setStudentList(prev => 
      prev.map(student => 
        student.id === studentId ? { ...student, status: newStatus } : student
      )
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  };

  return (
    <AdminLTELayout>
      <div className="admn">
        <h3 className="page-title">Student Users</h3>
        
        <p>
          <a href="/admin/students/create" className="btn btn-success" style={{ display: 'none' }}>Add New</a>
        </p>
      </div>

      <div className="panel panel-default">
        <div style={{ fontSize: '20px' }} className="panel-heading">
          List
        </div>

        <div className="panel-body scroll-table table-responsive">
          <table className={`table table-bordered table-striped ${studentList.length > 0 ? 'datatable1' : ''} dt-select`}>
            <thead>
              <tr>
                <th style={{ fontSize: '15px' }}>Sr.No</th>
                <th style={{ fontSize: '15px' }}>Name</th>
                <th style={{ fontSize: '15px' }}>Email</th>
                <th style={{ fontSize: '15px' }}>Phone</th>
                <th style={{ fontSize: '15px' }}>Class</th>
                <th style={{ fontSize: '15px' }}>Age</th>
                <th style={{ fontSize: '15px' }}>Parent Name</th>
                <th style={{ fontSize: '15px' }}>Parent Phone</th>
                <th style={{ fontSize: '15px' }}>Status</th>
                <th style={{ fontSize: '15px' }}>Created At</th>
                <th style={{ fontSize: '15px' }}>Action</th>
              </tr>
            </thead>
            
            <tbody>
              {studentList.length > 0 ? (
                studentList.map((student, index) => (
                  <tr key={student.id} data-entry-id={student.id}>
                    <td>{index + 1}</td>
                    <td>{student.name}</td>
                    <td>{student.email}</td>
                    <td>{student.phone}</td>
                    <td>{student.class}</td>
                    <td>{calculateAge(student.date_of_birth)} years</td>
                    <td>{student.parent_name}</td>
                    <td>{student.parent_phone}</td>
                    <td>
                      {student.status === 1 ? (
                        <a 
                          href="#" 
                          className="label label-success"
                          onClick={(e) => {
                            e.preventDefault();
                            handleStatusToggle(student.id, student.status);
                          }}
                        >
                          Active
                        </a>
                      ) : (
                        <a 
                          href="#" 
                          className="label label-danger"
                          onClick={(e) => {
                            e.preventDefault();
                            handleStatusToggle(student.id, student.status);
                          }}
                        >
                          Inactive
                        </a>
                      )}
                    </td>
                    <td>{formatDate(student.created_at)}</td>
                    <td>
                      <a href={`/admin/students/add-trial/${student.id}`} className="btn btn-xs btn-warning" style={{ display: 'none' }}>
                        Add Trial
                      </a>
                      <a href={`/admin/students/view/${student.id}`} className="btn btn-primary btn-sm">
                        View
                      </a>
                      {' '}
                      <a href={`/admin/students/edit/${student.id}`} className="btn btn-xs btn-info" style={{ display: 'none' }}>
                        Edit
                      </a>
                      {student.role_id !== 1 && (
                        <>
                          {' '}
                          <button 
                            className="btn btn-xs btn-danger"
                            onClick={() => {
                              if (confirm('Are you sure you want to delete this student?')) {
                                setStudentList(prev => prev.filter(s => s.id !== student.id));
                              }
                            }}
                          >
                            Delete
                          </button>
                        </>
                      )}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={11}>No entries in table</td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </AdminLTELayout>
  );
}
