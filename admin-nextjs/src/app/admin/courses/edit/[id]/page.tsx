'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import AdminLTELayout from '@/components/layout/AdminLTELayout';
import { courses, categories } from '@/data/dummyData';

interface CourseFormData {
  title: string;
  description: string;
  category_id: string;
  price: string;
  duration: string;
  level: string;
  status: string;
  featured: string;
}

export default function EditCoursePage() {
  const router = useRouter();
  const params = useParams();
  const courseId = params.id as string;
  
  const [formData, setFormData] = useState<CourseFormData>({
    title: '',
    description: '',
    category_id: '',
    price: '',
    duration: '',
    level: 'Beginner',
    status: '1',
    featured: '0'
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);

  useEffect(() => {
    // Load course data
    const loadCourse = async () => {
      try {
        // In a real app, you would fetch from API
        const course = courses.find(c => c.id === parseInt(courseId));
        
        if (course) {
          setFormData({
            title: course.title,
            description: course.description || '',
            category_id: course.category_id.toString(),
            price: course.price.toString(),
            duration: course.duration || '',
            level: course.level || 'Beginner',
            status: course.status.toString(),
            featured: course.featured ? '1' : '0'
          });
        } else {
          alert('Course not found');
          router.push('/admin/courses');
        }
      } catch (error) {
        console.error('Error loading course:', error);
        alert('Error loading course');
      } finally {
        setInitialLoading(false);
      }
    };

    if (courseId) {
      loadCourse();
    }
  }, [courseId, router]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Course title is required';
    } else if (formData.title.length < 3) {
      newErrors.title = 'Course title must be at least 3 characters';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    } else if (formData.description.length < 20) {
      newErrors.description = 'Description must be at least 20 characters';
    }

    if (!formData.category_id) {
      newErrors.category_id = 'Category is required';
    }

    if (!formData.price.trim()) {
      newErrors.price = 'Price is required';
    } else if (isNaN(Number(formData.price)) || Number(formData.price) < 0) {
      newErrors.price = 'Price must be a valid number';
    }

    if (!formData.duration.trim()) {
      newErrors.duration = 'Duration is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // In a real app, you would make an API call here
      console.log('Updating course:', { id: courseId, ...formData });
      
      // Show success message
      alert('Course updated successfully!');
      
      // Redirect to courses list
      router.push('/admin/courses');
    } catch (error) {
      console.error('Error updating course:', error);
      alert('Error updating course. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <AdminLTELayout>
        <div className="text-center">
          <p>Loading course...</p>
        </div>
      </AdminLTELayout>
    );
  }

  return (
    <AdminLTELayout>
      <h3 className="page-title">Edit Course</h3>

      <div className="panel panel-default">
        <div className="panel-heading">
          Edit course
        </div>

        <div className="panel-body">
          <form onSubmit={handleSubmit}>
            <div className="row">
              <div className="col-xs-12 form-group">
                <label htmlFor="title" className="control-label">Course Title*</label>
                <input
                  type="text"
                  name="title"
                  className="form-control"
                  placeholder="Enter course title"
                  value={formData.title}
                  onChange={handleChange}
                  required
                />
                {errors.title && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.title}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-xs-12 form-group">
                <label htmlFor="description" className="control-label">Description*</label>
                <textarea
                  name="description"
                  className="form-control"
                  placeholder="Enter course description"
                  rows={5}
                  value={formData.description}
                  onChange={handleChange}
                  required
                />
                {errors.description && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.description}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-md-6 form-group">
                <label htmlFor="category_id" className="control-label">Category*</label>
                <select
                  name="category_id"
                  className="form-control"
                  value={formData.category_id}
                  onChange={handleChange}
                  required
                >
                  <option value="">Select Category</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
                {errors.category_id && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.category_id}
                  </p>
                )}
              </div>

              <div className="col-md-6 form-group">
                <label htmlFor="price" className="control-label">Price*</label>
                <input
                  type="number"
                  name="price"
                  className="form-control"
                  placeholder="Enter price"
                  value={formData.price}
                  onChange={handleChange}
                  min="0"
                  step="0.01"
                  required
                />
                {errors.price && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.price}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-md-6 form-group">
                <label htmlFor="duration" className="control-label">Duration*</label>
                <input
                  type="text"
                  name="duration"
                  className="form-control"
                  placeholder="e.g., 8 weeks, 3 months"
                  value={formData.duration}
                  onChange={handleChange}
                  required
                />
                {errors.duration && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.duration}
                  </p>
                )}
              </div>

              <div className="col-md-6 form-group">
                <label htmlFor="level" className="control-label">Level*</label>
                <select
                  name="level"
                  className="form-control"
                  value={formData.level}
                  onChange={handleChange}
                  required
                >
                  <option value="Beginner">Beginner</option>
                  <option value="Intermediate">Intermediate</option>
                  <option value="Advanced">Advanced</option>
                </select>
              </div>
            </div>

            <div className="row">
              <div className="col-md-6 form-group">
                <label htmlFor="status" className="control-label">Status*</label>
                <select
                  name="status"
                  className="form-control"
                  value={formData.status}
                  onChange={handleChange}
                  required
                >
                  <option value="1">Active</option>
                  <option value="0">Inactive</option>
                </select>
              </div>

              <div className="col-md-6 form-group">
                <label htmlFor="featured" className="control-label">Featured</label>
                <select
                  name="featured"
                  className="form-control"
                  value={formData.featured}
                  onChange={handleChange}
                >
                  <option value="0">No</option>
                  <option value="1">Yes</option>
                </select>
              </div>
            </div>

            <div>
              <button 
                type="submit" 
                className="btn btn-danger"
                disabled={loading}
                style={{
                  backgroundColor: '#375dbc',
                  borderColor: '#375dbc'
                }}
              >
                {loading ? 'Updating...' : 'Update'}
              </button>
              {' '}
              <a href="/admin/courses" className="btn btn-default">
                Cancel
              </a>
            </div>
          </form>
        </div>
      </div>
    </AdminLTELayout>
  );
}
