'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import AdminLTELayout from '@/components/layout/AdminLTELayout';
import { categories } from '@/data/dummyData';

interface CourseFormData {
  title: string;
  description: string;
  category_id: string;
  price: string;
  duration: string;
  level: string;
  status: string;
  featured: string;
}

export default function CreateCoursePage() {
  const router = useRouter();
  const [formData, setFormData] = useState<CourseFormData>({
    title: '',
    description: '',
    category_id: '',
    price: '',
    duration: '',
    level: 'Beginner',
    status: '1',
    featured: '0'
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Course title is required';
    } else if (formData.title.length < 3) {
      newErrors.title = 'Course title must be at least 3 characters';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    } else if (formData.description.length < 20) {
      newErrors.description = 'Description must be at least 20 characters';
    }

    if (!formData.category_id) {
      newErrors.category_id = 'Category is required';
    }

    if (!formData.price.trim()) {
      newErrors.price = 'Price is required';
    } else if (isNaN(Number(formData.price)) || Number(formData.price) < 0) {
      newErrors.price = 'Price must be a valid number';
    }

    if (!formData.duration.trim()) {
      newErrors.duration = 'Duration is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // In a real app, you would make an API call here
      console.log('Creating course:', formData);
      
      // Show success message
      alert('Course created successfully!');
      
      // Redirect to courses list
      router.push('/admin/courses');
    } catch (error) {
      console.error('Error creating course:', error);
      alert('Error creating course. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <AdminLTELayout>
      <h3 className="page-title">Create Course</h3>

      <div className="panel panel-default">
        <div className="panel-heading">
          Create new course
        </div>

        <div className="panel-body">
          <form onSubmit={handleSubmit}>
            <div className="row">
              <div className="col-xs-12 form-group">
                <label htmlFor="title" className="control-label">Course Title*</label>
                <input
                  type="text"
                  name="title"
                  className="form-control"
                  placeholder="Enter course title"
                  value={formData.title}
                  onChange={handleChange}
                  required
                />
                {errors.title && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.title}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-xs-12 form-group">
                <label htmlFor="description" className="control-label">Description*</label>
                <textarea
                  name="description"
                  className="form-control"
                  placeholder="Enter course description"
                  rows={5}
                  value={formData.description}
                  onChange={handleChange}
                  required
                />
                {errors.description && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.description}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-md-6 form-group">
                <label htmlFor="category_id" className="control-label">Category*</label>
                <select
                  name="category_id"
                  className="form-control"
                  value={formData.category_id}
                  onChange={handleChange}
                  required
                >
                  <option value="">Select Category</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
                {errors.category_id && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.category_id}
                  </p>
                )}
              </div>

              <div className="col-md-6 form-group">
                <label htmlFor="price" className="control-label">Price*</label>
                <input
                  type="number"
                  name="price"
                  className="form-control"
                  placeholder="Enter price"
                  value={formData.price}
                  onChange={handleChange}
                  min="0"
                  step="0.01"
                  required
                />
                {errors.price && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.price}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-md-6 form-group">
                <label htmlFor="duration" className="control-label">Duration*</label>
                <input
                  type="text"
                  name="duration"
                  className="form-control"
                  placeholder="e.g., 8 weeks, 3 months"
                  value={formData.duration}
                  onChange={handleChange}
                  required
                />
                {errors.duration && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.duration}
                  </p>
                )}
              </div>

              <div className="col-md-6 form-group">
                <label htmlFor="level" className="control-label">Level*</label>
                <select
                  name="level"
                  className="form-control"
                  value={formData.level}
                  onChange={handleChange}
                  required
                >
                  <option value="Beginner">Beginner</option>
                  <option value="Intermediate">Intermediate</option>
                  <option value="Advanced">Advanced</option>
                </select>
              </div>
            </div>

            <div className="row">
              <div className="col-md-6 form-group">
                <label htmlFor="status" className="control-label">Status*</label>
                <select
                  name="status"
                  className="form-control"
                  value={formData.status}
                  onChange={handleChange}
                  required
                >
                  <option value="1">Active</option>
                  <option value="0">Inactive</option>
                </select>
              </div>

              <div className="col-md-6 form-group">
                <label htmlFor="featured" className="control-label">Featured</label>
                <select
                  name="featured"
                  className="form-control"
                  value={formData.featured}
                  onChange={handleChange}
                >
                  <option value="0">No</option>
                  <option value="1">Yes</option>
                </select>
              </div>
            </div>

            <div>
              <button 
                type="submit" 
                className="btn btn-danger"
                disabled={loading}
                style={{
                  backgroundColor: '#375dbc',
                  borderColor: '#375dbc'
                }}
              >
                {loading ? 'Creating...' : 'Save'}
              </button>
              {' '}
              <a href="/admin/courses" className="btn btn-default">
                Cancel
              </a>
            </div>
          </form>
        </div>
      </div>
    </AdminLTELayout>
  );
}
