'use client';

import React, { useState } from 'react';
import AdminLTELayout from '@/components/layout/AdminLTELayout';
import { courses, categories } from '@/data/dummyData';

// Extended courses data
const coursesData = [
  {
    id: 1,
    name: 'Advanced Calculus',
    description: 'Comprehensive calculus course for advanced students',
    category_id: 1,
    category_name: 'Mathematics',
    image: null,
    video: null,
    price: 299,
    duration: '12 weeks',
    status: 1,
    is_featured: true,
    created_at: '2024-01-25T10:00:00Z',
    sort_id: 1
  },
  {
    id: 2,
    name: 'Quantum Physics',
    description: 'Introduction to quantum mechanics and physics',
    category_id: 2,
    category_name: 'Science',
    image: null,
    video: null,
    price: 399,
    duration: '16 weeks',
    status: 1,
    is_featured: false,
    created_at: '2024-01-26T10:00:00Z',
    sort_id: 2
  },
  {
    id: 3,
    name: 'Creative Writing',
    description: 'Develop your creative writing skills',
    category_id: 3,
    category_name: 'English',
    image: null,
    video: null,
    price: 199,
    duration: '8 weeks',
    status: 1,
    is_featured: true,
    created_at: '2024-01-27T10:00:00Z',
    sort_id: 3
  },
  {
    id: 4,
    name: 'Data Structures',
    description: 'Learn fundamental data structures and algorithms',
    category_id: 4,
    category_name: 'Computer Science',
    image: null,
    video: null,
    price: 349,
    duration: '14 weeks',
    status: 0,
    is_featured: false,
    created_at: '2024-01-28T10:00:00Z',
    sort_id: 4
  },
  {
    id: 5,
    name: 'World History',
    description: 'Comprehensive world history course',
    category_id: 5,
    category_name: 'History',
    image: null,
    video: null,
    price: 249,
    duration: '10 weeks',
    status: 1,
    is_featured: false,
    created_at: '2024-01-29T10:00:00Z',
    sort_id: 5
  }
];

export default function CoursesPage() {
  const [courseList, setCourseList] = useState(coursesData);

  const handleStatusToggle = (courseId: number, currentStatus: number) => {
    const newStatus = currentStatus === 1 ? 2 : 1;
    setCourseList(prev => 
      prev.map(course => 
        course.id === courseId ? { ...course, status: newStatus } : course
      )
    );
  };

  const handleDelete = (courseId: number) => {
    if (confirm('Are you sure you want to delete this?')) {
      setCourseList(prev => prev.filter(course => course.id !== courseId));
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: '2-digit'
    });
  };

  return (
    <AdminLTELayout>
      <div className="row">
        <div className="col-md-12">
          <div className="panel panel-default">
            <div style={{ fontSize: '20px' }} className="panel-heading">All Courses</div>
            <div className="panel-body">
              <div id="message"></div>
              
              <div className="row">
                <div className="col-md-12">
                  <div className="col-md-6 text-right">
                    
                  </div>
                  <div className="col-md-6">
                    <div className="box-default text-right">
                      <a className="btn btn-bitbucket float-right" href="/admin/courses/create">Add New Course</a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="panel-body table-responsive scroll-table" style={{ height: 'calc(75vh - 68px)' }}>
              <table className={`table table-bordered table-striped dt-select ${courseList.length > 0 ? 'datatable1' : ''}`} id="courses1">
                <thead>
                  <tr>
                    <th>Sr.No</th>
                    <th>Category Name</th>
                    <th>Course Image</th>
                    <th>Course Name</th>
                    <th>Status</th>
                    <th>Created At</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  {courseList.length > 0 ? (
                    courseList.map((course, index) => (
                      <tr key={course.id}>
                        <td>{index + 1}</td>
                        <td>{course.category_name}</td>
                        <td>
                          {course.image ? (
                            <img 
                              className="img-responsive" 
                              src={`/public/course/${course.image}`} 
                              width="100"
                            />
                          ) : (
                            'No image found'
                          )}
                        </td>
                        <td>{course.name}</td>
                        <td>
                          {course.status === 1 ? (
                            <a 
                              href="#" 
                              className="label label-success"
                              onClick={(e) => {
                                e.preventDefault();
                                handleStatusToggle(course.id, course.status);
                              }}
                            >
                              Active
                            </a>
                          ) : (
                            <a 
                              href="#" 
                              className="label label-danger"
                              onClick={(e) => {
                                e.preventDefault();
                                handleStatusToggle(course.id, course.status);
                              }}
                            >
                              Inactive
                            </a>
                          )}
                        </td>
                        <td>{formatDate(course.created_at)}</td>
                        <td>
                          <a href={`/admin/courses/edit/${course.id}`} className="btn btn-primary btn-sm">
                            Edit
                          </a>
                          {' '}
                          <a href={`/admin/courses/view/${course.id}`} className="btn btn-info btn-sm">
                            View
                          </a>
                          {' '}
                          <button 
                            className="btn btn-danger btn-sm"
                            onClick={() => handleDelete(course.id)}
                          >
                            Delete
                          </button>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={7}>No entries in table</td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </AdminLTELayout>
  );
}
