'use client';

import React, { useState } from 'react';
import AdminLTELayout from '@/components/layout/AdminLTELayout';
import { assignments, courses } from '@/data/dummyData';

// Extended assignments data
const assignmentsData = [
  {
    id: 1,
    title: 'Calculus Problem Set 1',
    description: 'Solve the given calculus problems',
    course_id: 1,
    course_name: 'Advanced Calculus',
    class_type: 'Regular',
    class_name: 'Grade 12',
    added_by: 'Dr. <PERSON>',
    due_date: '2024-02-15T23:59:59Z',
    max_marks: 100,
    status: 1,
    submitted_count: 15,
    assigned_count: 25,
    sort_id: 1,
    created_at: '2024-02-01T10:00:00Z'
  },
  {
    id: 2,
    title: 'Physics Lab Report',
    description: 'Submit your quantum physics lab report',
    course_id: 2,
    course_name: 'Quantum Physics',
    class_type: 'Advanced',
    class_name: 'Grade 11',
    added_by: 'Prof. <PERSON>',
    due_date: '2024-02-20T23:59:59Z',
    max_marks: 50,
    status: 1,
    submitted_count: 8,
    assigned_count: 20,
    sort_id: 2,
    created_at: '2024-02-02T10:00:00Z'
  },
  {
    id: 3,
    title: 'Creative Writing Essay',
    description: 'Write a creative essay on given topic',
    course_id: 3,
    course_name: 'Creative Writing',
    class_type: 'Regular',
    class_name: 'Grade 10',
    added_by: 'Ms. Emily Davis',
    due_date: '2024-02-25T23:59:59Z',
    max_marks: 75,
    status: 0,
    submitted_count: 0,
    assigned_count: 18,
    sort_id: 3,
    created_at: '2024-02-03T10:00:00Z'
  },
  {
    id: 4,
    title: 'Data Structures Assignment',
    description: 'Implement basic data structures',
    course_id: 4,
    course_name: 'Data Structures',
    class_type: 'Advanced',
    class_name: 'Grade 12',
    added_by: 'Dr. Robert Wilson',
    due_date: '2024-02-28T23:59:59Z',
    max_marks: 100,
    status: 1,
    submitted_count: 12,
    assigned_count: 22,
    sort_id: 4,
    created_at: '2024-02-04T10:00:00Z'
  }
];

export default function AssignmentsPage() {
  const [assignmentList, setAssignmentList] = useState(assignmentsData);
  const [filters, setFilters] = useState({
    added_by: '',
    class_type: '',
    class_id: '',
    course_id: ''
  });

  const handleStatusToggle = (assignmentId: number, currentStatus: number) => {
    const newStatus = currentStatus === 1 ? 0 : 1;
    setAssignmentList(prev => 
      prev.map(assignment => 
        assignment.id === assignmentId ? { ...assignment, status: newStatus } : assignment
      )
    );
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: '2-digit'
    });
  };

  return (
    <AdminLTELayout>
      <div className="row">
        <div className="col-md-12">
          <div className="panel panel-default">
            <div className="panel-heading">All Assignments</div>
            <div className="panel-body">
              <div id="message"></div>
              
              {/* Filters */}
              <form name="filterfrm" method="GET">
                <div className="row">
                  <div className="form-group col-md-2">
                    <select name="added_by" className="form-control select2" value={filters.added_by} onChange={handleFilterChange}>
                      <option value="">-select added by-</option>
                      <option value="1">Dr. Sarah Johnson</option>
                      <option value="2">Prof. Michael Chen</option>
                      <option value="3">Ms. Emily Davis</option>
                      <option value="4">Dr. Robert Wilson</option>
                    </select>
                  </div>
                  
                  <div className="form-group col-md-2">
                    <select name="class_type" className="form-control" value={filters.class_type} onChange={handleFilterChange}>
                      <option value="">--Select Class Type--</option>
                      <option value="Regular">Regular</option>
                      <option value="Advanced">Advanced</option>
                    </select>
                  </div>
                  
                  <div className="form-group col-md-2">
                    <select name="class_id" className="form-control select2" value={filters.class_id} onChange={handleFilterChange}>
                      <option value="">--select class--</option>
                      <option value="10">Grade 10</option>
                      <option value="11">Grade 11</option>
                      <option value="12">Grade 12</option>
                    </select>
                  </div>
                  
                  <div className="form-group col-md-2">
                    <select name="course_id" className="form-control select2" value={filters.course_id} onChange={handleFilterChange}>
                      <option value="">--select course--</option>
                      {courses.map(course => (
                        <option key={course.id} value={course.id}>{course.name}</option>
                      ))}
                    </select>
                  </div>
                  
                  <div className="form-group col-md-2">
                    <button type="submit" className="btn btn-primary">Filter</button>
                  </div>
                </div>
              </form>
              
              <div className="row">
                <div className="col-md-12">
                  <div className="col-md-6 text-right">
                    
                  </div>
                  <div className="col-md-6">
                    <div className="box-default text-right">
                      <a className="btn btn-bitbucket float-right" href="/admin/assignments/create">Add new Assignment</a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="panel-body table-responsive">
              <table className={`table table-bordered table-striped dt-select ${assignmentList.length > 0 ? 'datatable1' : ''}`} id="courses1">
                <thead>
                  <tr>
                    <th>#</th>
                    <th>Added By</th>
                    <th>Class Type</th>
                    <th>Class</th>
                    <th>Course Name</th>
                    <th>Assignment Name</th>
                    <th>Created At</th>
                    <th>Status</th>
                    <th>Submitted</th>
                    <th>Assigned</th>
                    <th>Sort Order</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  {assignmentList.length > 0 ? (
                    assignmentList.map((assignment, index) => (
                      <tr key={assignment.id}>
                        <td>{index + 1}</td>
                        <td>{assignment.added_by}</td>
                        <td>{assignment.class_type}</td>
                        <td>{assignment.class_name}</td>
                        <td>{assignment.course_name}</td>
                        <td>{assignment.title}</td>
                        <td>{formatDate(assignment.created_at)}</td>
                        <td>
                          {assignment.status === 1 ? (
                            <a 
                              href="#" 
                              className="label label-success"
                              onClick={(e) => {
                                e.preventDefault();
                                handleStatusToggle(assignment.id, assignment.status);
                              }}
                            >
                              Active
                            </a>
                          ) : (
                            <a 
                              href="#" 
                              className="label label-danger"
                              onClick={(e) => {
                                e.preventDefault();
                                handleStatusToggle(assignment.id, assignment.status);
                              }}
                            >
                              Inactive
                            </a>
                          )}
                        </td>
                        <td>
                          <a href={`/admin/assignments/submitted/${assignment.id}`} title="Submitted Assignments">
                            {assignment.submitted_count}
                          </a>
                        </td>
                        <td>{assignment.assigned_count}</td>
                        <td>
                          {assignment.sort_id > 1 && (
                            <a href="#" title="Up">
                              <i className="fa fa-arrow-circle-o-up"></i>
                            </a>
                          )}
                          {' '}
                          <a href="#" title="Down">
                            <i className="fa fa-arrow-circle-o-down"></i>
                          </a>
                        </td>
                        <td>
                          <a href={`/admin/assignments/edit/${assignment.id}`}>Edit</a>
                          {' '}
                          <a href={`/admin/assignments/submitted/${assignment.id}`} title="Submitted Assignments">View</a>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={12}>No entries in table</td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </AdminLTELayout>
  );
}
