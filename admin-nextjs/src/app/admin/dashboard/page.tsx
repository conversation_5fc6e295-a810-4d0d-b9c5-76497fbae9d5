"use client";

import React from "react";
import AdminLTELayout from "@/components/layout/AdminLTELayout";
import { dashboardStats } from "@/data/dummyData";

export default function DashboardPage() {
  return (
    <AdminLTELayout>
      <div className="panel panel-default">
        <div style={{ fontSize: "20px" }} className="panel-heading">
          Dashboard
        </div>
        <div className="panel-body">
          <div className="row text-center">
            {/* Teachers Card */}
            <div className="col-md-4">
              <a href="/admin/teachers">
                <div className="bg-info dashboard-bx-pm">
                  <h2>
                    <i className="fa fa-users"></i>{" "}
                    {dashboardStats.total_teachers}
                  </h2>
                  <span style={{ fontSize: "16px" }}>
                    Total Active Teachers{" "}
                    <i className="fa fa-arrow-circle-right"></i>
                  </span>
                </div>
              </a>
            </div>

            {/* Students Card */}
            <div className="col-md-4">
              <a href="/admin/students">
                <div className="bg-primary dashboard-bx-pm">
                  <h2>
                    <i className="fa fa-users"></i>{" "}
                    {dashboardStats.total_students}
                  </h2>
                  <span style={{ fontSize: "16px" }}>
                    Total Active Students{" "}
                    <i className="fa fa-arrow-circle-right"></i>
                  </span>
                </div>
              </a>
            </div>

            {/* Courses Card */}
            <div className="col-md-4">
              <a href="/admin/courses">
                <div className="bg-success dashboard-bx-pm">
                  <h2>
                    <i className="fa fa-book"></i>{" "}
                    {dashboardStats.total_courses}
                  </h2>
                  <span style={{ fontSize: "16px" }}>
                    Total Active Courses{" "}
                    <i className="fa fa-arrow-circle-right"></i>
                  </span>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </AdminLTELayout>
  );
}
