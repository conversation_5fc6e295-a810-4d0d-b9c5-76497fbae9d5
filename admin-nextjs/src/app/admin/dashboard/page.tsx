'use client';

import React from 'react';
import {
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Chip,
} from '@mui/material';
import {
  People,
  School,
  Assignment,
  TrendingUp,
  Schedule,
  AttachMoney,
} from '@mui/icons-material';
import AdminLayout from '@/components/layout/AdminLayout';
import { dashboardStats, courseRequests } from '@/data/dummyData';

// Stats Card Component
interface StatsCardProps {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  color: string;
}

function StatsCard({ title, value, icon, color }: StatsCardProps) {
  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography color="textSecondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="div" sx={{ fontWeight: 'bold', color }}>
              {value}
            </Typography>
          </Box>
          <Box sx={{ color, fontSize: '3rem' }}>
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
}

export default function DashboardPage() {
  const handleAssignCourseBuilder = (requestId: number) => {
    console.log('Assigning course builder for request:', requestId);
    // Handle assignment logic here
  };

  return (
    <AdminLayout title="Dashboard">
      <Grid container spacing={3}>
        {/* Stats Cards */}
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="Total Students"
            value={dashboardStats.total_students}
            icon={<People />}
            color="#00a65a"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="Total Teachers"
            value={dashboardStats.total_teachers}
            icon={<School />}
            color="#3c8dbc"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="Total Courses"
            value={dashboardStats.total_courses}
            icon={<Assignment />}
            color="#f39c12"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="Revenue This Month"
            value={`$${dashboardStats.revenue_this_month.toLocaleString()}`}
            icon={<AttachMoney />}
            color="#dd4b39"
          />
        </Grid>

        {/* Additional Stats */}
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="Pending Assignments"
            value={dashboardStats.pending_assignments}
            icon={<Assignment />}
            color="#f39c12"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="Live Classes Today"
            value={dashboardStats.live_classes_today}
            icon={<Schedule />}
            color="#00c0ef"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="New Registrations"
            value={dashboardStats.new_registrations_today}
            icon={<People />}
            color="#00a65a"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="Total Assignments"
            value={dashboardStats.total_assignments}
            icon={<Assignment />}
            color="#3c8dbc"
          />
        </Grid>

        {/* New eCourse Request Table */}
        <Grid item xs={12} lg={8}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom sx={{ color: '#444444', mb: 2 }}>
              New eCourse Request
            </Typography>
            <TableContainer>
              <Table>
                <TableHead sx={{ backgroundColor: '#f4f4f4' }}>
                  <TableRow>
                    <TableCell>#</TableCell>
                    <TableCell>Course Id</TableCell>
                    <TableCell>Course Name</TableCell>
                    <TableCell>Instructor</TableCell>
                    <TableCell>Waiting time</TableCell>
                    <TableCell>Action</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {courseRequests
                    .filter(request => request.status === 'pending')
                    .map((request, index) => (
                    <TableRow key={request.id}>
                      <TableCell>{index + 1}</TableCell>
                      <TableCell>{request.id}</TableCell>
                      <TableCell>{request.course.name}</TableCell>
                      <TableCell>{request.instructor.name}</TableCell>
                      <TableCell>{request.waiting_time}</TableCell>
                      <TableCell>
                        <Button
                          variant="contained"
                          color="primary"
                          size="small"
                          onClick={() => handleAssignCourseBuilder(request.id)}
                        >
                          Assign Course Builder
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                  {courseRequests.filter(request => request.status === 'pending').length === 0 && (
                    <TableRow>
                      <TableCell colSpan={6} align="center">
                        <Typography variant="h6" color="textSecondary">
                          No record found
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>

        {/* List of eCourses */}
        <Grid item xs={12} lg={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom sx={{ color: '#444444', mb: 2 }}>
              List of eCourses
            </Typography>
            <TableContainer>
              <Table size="small">
                <TableHead sx={{ backgroundColor: '#f4f4f4' }}>
                  <TableRow>
                    <TableCell>Course Id</TableCell>
                    <TableCell>Course Name</TableCell>
                    <TableCell>Status</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {courseRequests.map((request) => (
                    <TableRow key={request.id}>
                      <TableCell>{request.id}</TableCell>
                      <TableCell>{request.course.name}</TableCell>
                      <TableCell>
                        <Chip
                          label={request.status}
                          color={
                            request.status === 'completed' ? 'success' :
                            request.status === 'assigned' ? 'primary' :
                            request.status === 'in_progress' ? 'warning' : 'default'
                          }
                          size="small"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>

        {/* Chart Placeholder */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom sx={{ color: '#444444', mb: 2 }}>
              Courses by College: <span style={{ fontSize: '30px', fontWeight: 'bold', color: '#ec3535' }}>CHART</span>
            </Typography>
            <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', backgroundColor: '#f9f9f9' }}>
              <Typography variant="h6" color="textSecondary">
                Chart will be implemented here
              </Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </AdminLayout>
  );
}
