'use client';

import React from 'react';
import AdminLTELayout from '@/components/layout/AdminLTELayout';
import { dashboardStats, courseRequests } from '@/data/dummyData';

export default function DashboardPage() {
  const handleAssignCourseBuilder = (requestId: number) => {
    console.log('Assigning course builder for request:', requestId);
    // Handle assignment logic here
  };

  return (
    <AdminLTELayout>
      <div className="row">
        <div className="col-md-10">
          <div className="panel panel-default">
            <div className="panel-heading">New eCourse Request</div>
            
            <div className="panel-body">
              <table id="coursesTable" className="table table-bordered">
                <thead>
                  <tr>
                    <th>#</th>
                    <th>Course Id</th>
                    <th>Course Name</th>
                    <th>Instructor</th>
                    <th>Waiting time</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  {courseRequests
                    .filter(request => request.status === 'pending')
                    .map((request, index) => (
                    <tr key={request.id}>
                      <td>{index + 1}</td>
                      <td>{request.id}</td>
                      <td>{request.course.name}</td>
                      <td>{request.instructor.name}</td>
                      <td>{request.waiting_time}</td>
                      <td>
                        <button 
                          type="button" 
                          className="btn btn-primary"
                          onClick={() => handleAssignCourseBuilder(request.id)}
                        >
                          Assign Course Builder
                        </button>
                      </td>
                    </tr>
                  ))}
                  {courseRequests.filter(request => request.status === 'pending').length === 0 && (
                    <tr>
                      <td colSpan={6} style={{ textAlign: 'center' }}>
                        <h1 className="info">No record found</h1>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* List of eCourses */}
          <div className="panel panel-default">
            <div className="panel-heading">List of eCourses</div>
            
            <div className="panel-body">
              <table className="table table-bordered">
                <thead>
                  <tr>
                    <th>Course Id</th>
                    <th>Course Name</th>
                    <th>Instructor</th>
                    <th>Builder</th>
                    <th>Request Date</th>
                    <th>Status</th>
                  </tr>
                </thead>
                <tbody>
                  {courseRequests.map((request) => (
                    <tr key={request.id}>
                      <td>{request.id}</td>
                      <td>{request.course.name}</td>
                      <td>{request.instructor.name}</td>
                      <td>{request.builder?.name || '-'}</td>
                      <td>{new Date(request.created_at).toLocaleDateString()}</td>
                      <td>{request.status}</td>
                    </tr>
                  ))}
                  {courseRequests.length === 0 && (
                    <tr>
                      <td colSpan={6} style={{ textAlign: 'center' }}>
                        <h1 className="info">No record found</h1>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* Chart */}
          <div className="panel panel-default">
            <div className="panel-body">
              <div className="col-md-12">
                <div className="col-md-6">
                  <h2 className="text-capitalize">
                    Courses by collage: <span style={{ fontSize: '30px', fontWeight: 'bold', color: '#ec3535' }}>CHART</span>
                  </h2>
                  <canvas id="myChart" style={{ width: '100%', height: '300px' }}></canvas>
                </div>
                <div className="col-md-6"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLTELayout>
  );
}
