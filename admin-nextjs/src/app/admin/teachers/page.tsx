'use client';

import React, { useState } from 'react';
import AdminLTELayout from '@/components/layout/AdminLTELayout';
import { teachers } from '@/data/dummyData';

// Extended teachers data
const teachersData = [
  {
    id: 1,
    name: 'Dr. <PERSON>',
    email: '<EMAIL>',
    phone: '+1234567890',
    subject: 'Mathematics',
    experience: 8,
    qualification: 'PhD in Mathematics',
    status: 1,
    created_at: '2024-01-10T10:00:00Z',
    role_id: 2
  },
  {
    id: 2,
    name: 'Prof. <PERSON>',
    email: '<EMAIL>',
    phone: '+1234567891',
    subject: 'Physics',
    experience: 12,
    qualification: 'PhD in Physics',
    status: 1,
    created_at: '2024-01-11T10:00:00Z',
    role_id: 2
  },
  {
    id: 3,
    name: 'Ms. <PERSON>',
    email: '<EMAIL>',
    phone: '+1234567892',
    subject: 'English',
    experience: 6,
    qualification: 'MA in English Literature',
    status: 0,
    created_at: '2024-01-12T10:00:00Z',
    role_id: 2
  },
  {
    id: 4,
    name: 'Dr. <PERSON>',
    email: '<EMAIL>',
    phone: '+1234567893',
    subject: 'Chemistry',
    experience: 10,
    qualification: 'PhD in Chemistry',
    status: 1,
    created_at: '2024-01-15T10:00:00Z',
    role_id: 2
  }
];

export default function TeachersPage() {
  const [teacherList, setTeacherList] = useState(teachersData);

  const handleStatusToggle = (teacherId: number, currentStatus: number) => {
    const newStatus = currentStatus === 1 ? 0 : 1;
    setTeacherList(prev => 
      prev.map(teacher => 
        teacher.id === teacherId ? { ...teacher, status: newStatus } : teacher
      )
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  return (
    <AdminLTELayout>
      <div className="admn">
        <h3 className="page-title">Teacher Users</h3>
        
        <p>
          <a href="/admin/teachers/create" className="btn btn-success" style={{ display: 'none' }}>Add New</a>
        </p>
      </div>

      <div className="panel panel-default">
        <div style={{ fontSize: '20px' }} className="panel-heading">
          List
        </div>

        <div className="panel-body scroll-table table-responsive">
          <table className={`table table-bordered table-striped ${teacherList.length > 0 ? 'datatable1' : ''} dt-select`}>
            <thead>
              <tr>
                <th style={{ fontSize: '15px' }}>Sr.No</th>
                <th style={{ fontSize: '15px' }}>Name</th>
                <th style={{ fontSize: '15px' }}>Email</th>
                <th style={{ fontSize: '15px' }}>Phone</th>
                <th style={{ fontSize: '15px' }}>Subject</th>
                <th style={{ fontSize: '15px' }}>Experience</th>
                <th style={{ fontSize: '15px' }}>Qualification</th>
                <th style={{ fontSize: '15px' }}>Status</th>
                <th style={{ fontSize: '15px' }}>Created At</th>
                <th style={{ fontSize: '15px' }}>Action</th>
              </tr>
            </thead>
            
            <tbody>
              {teacherList.length > 0 ? (
                teacherList.map((teacher, index) => (
                  <tr key={teacher.id} data-entry-id={teacher.id}>
                    <td>{index + 1}</td>
                    <td>{teacher.name}</td>
                    <td>{teacher.email}</td>
                    <td>{teacher.phone}</td>
                    <td>{teacher.subject}</td>
                    <td>{teacher.experience} years</td>
                    <td>{teacher.qualification}</td>
                    <td>
                      {teacher.status === 1 ? (
                        <a 
                          href="#" 
                          className="label label-success"
                          onClick={(e) => {
                            e.preventDefault();
                            handleStatusToggle(teacher.id, teacher.status);
                          }}
                        >
                          Active
                        </a>
                      ) : (
                        <a 
                          href="#" 
                          className="label label-danger"
                          onClick={(e) => {
                            e.preventDefault();
                            handleStatusToggle(teacher.id, teacher.status);
                          }}
                        >
                          Inactive
                        </a>
                      )}
                    </td>
                    <td>{formatDate(teacher.created_at)}</td>
                    <td>
                      <a href={`/admin/teachers/edit/${teacher.id}`} className="btn btn-xs btn-info" style={{ display: 'none' }}>
                        Edit
                      </a>
                      <a href={`/admin/teachers/view/${teacher.id}`} className="btn btn-xs btn-primary">
                        View
                      </a>
                      {teacher.role_id !== 1 && (
                        <>
                          {' '}
                          <button 
                            className="btn btn-xs btn-danger"
                            onClick={() => {
                              if (confirm('Are you sure you want to delete this teacher?')) {
                                setTeacherList(prev => prev.filter(t => t.id !== teacher.id));
                              }
                            }}
                          >
                            Delete
                          </button>
                        </>
                      )}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={10}>No entries in table</td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </AdminLTELayout>
  );
}
