'use client';

import React from 'react';
import AdminLTELayout from '@/components/layout/AdminLTELayout';
import { dashboardStats } from '@/data/dummyData';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Bar, Line, Doughnut } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

export default function MainDashboardPage() {
  // Chart data
  const coursesChartData = {
    labels: ['Mathematics', 'Science', 'English', 'Computer Science', 'History'],
    datasets: [
      {
        label: 'Courses by Category',
        data: [12, 8, 6, 10, 4],
        backgroundColor: [
          'rgba(255, 99, 132, 0.8)',
          'rgba(54, 162, 235, 0.8)',
          'rgba(255, 205, 86, 0.8)',
          'rgba(75, 192, 192, 0.8)',
          'rgba(153, 102, 255, 0.8)',
        ],
        borderColor: [
          'rgba(255, 99, 132, 1)',
          'rgba(54, 162, 235, 1)',
          'rgba(255, 205, 86, 1)',
          'rgba(75, 192, 192, 1)',
          'rgba(153, 102, 255, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  const studentsChartData = {
    labels: ['Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12'],
    datasets: [
      {
        label: 'Students by Grade',
        data: [120, 150, 180, 200, 220, 190, 160],
        backgroundColor: 'rgba(54, 162, 235, 0.8)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 2,
        fill: false,
      },
    ],
  };

  const assignmentChartData = {
    labels: ['Completed', 'Pending', 'Overdue'],
    datasets: [
      {
        data: [105, 23, 8],
        backgroundColor: [
          'rgba(75, 192, 192, 0.8)',
          'rgba(255, 205, 86, 0.8)',
          'rgba(255, 99, 132, 0.8)',
        ],
        borderColor: [
          'rgba(75, 192, 192, 1)',
          'rgba(255, 205, 86, 1)',
          'rgba(255, 99, 132, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  const revenueChartData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'Monthly Revenue ($)',
        data: [35000, 42000, 38000, 45000, 48000, 52000],
        backgroundColor: 'rgba(153, 102, 255, 0.8)',
        borderColor: 'rgba(153, 102, 255, 1)',
        borderWidth: 2,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
    },
  };

  return (
    <AdminLTELayout>
      <div className="panel panel-default">
        <div style={{ fontSize: '20px' }} className="panel-heading">
          Dashboard
        </div>
        <div className="panel-body">
          <div className="row text-center">
            {/* Teachers Card */}
            <div className="col-md-4">
              <a href="/admin/teachers">
                <div className="bg-info dashboard-bx-pm">
                  <h2><i className="fa fa-users"></i> {dashboardStats.total_teachers}</h2>
                  <span style={{ fontSize: '16px' }}>Total Active Teachers <i className="fa fa-arrow-circle-right"></i></span>
                </div>
              </a>
            </div>

            {/* Students Card */}
            <div className="col-md-4">
              <a href="/admin/students">
                <div className="bg-primary dashboard-bx-pm">
                  <h2><i className="fa fa-users"></i> {dashboardStats.total_students}</h2>
                  <span style={{ fontSize: '16px' }}>Total Active Students <i className="fa fa-arrow-circle-right"></i></span>
                </div>
              </a>
            </div>

            {/* Courses Card */}
            <div className="col-md-4">
              <a href="/admin/courses">
                <div className="bg-success dashboard-bx-pm">
                  <h2><i className="fa fa-book"></i> {dashboardStats.total_courses}</h2>
                  <span style={{ fontSize: '16px' }}>Total Active Courses <i className="fa fa-arrow-circle-right"></i></span>
                </div>
              </a>
            </div>

            {/* Assignments Card */}
            <div className="col-md-4">
              <a href="/admin/assignments">
                <div className="bg-warning dashboard-bx-pm">
                  <h2><i className="fa fa-tasks"></i> {dashboardStats.total_assignments}</h2>
                  <span style={{ fontSize: '16px' }}>Total Assignments <i className="fa fa-arrow-circle-right"></i></span>
                </div>
              </a>
            </div>

            {/* Live Classes Card */}
            <div className="col-md-4">
              <a href="/admin/live-classes">
                <div className="bg-danger dashboard-bx-pm">
                  <h2><i className="fa fa-video-camera"></i> {dashboardStats.live_classes_today}</h2>
                  <span style={{ fontSize: '16px' }}>Live Classes Today <i className="fa fa-arrow-circle-right"></i></span>
                </div>
              </a>
            </div>

            {/* Demo Requests Card */}
            <div className="col-md-4">
              <a href="/admin/demo-requests">
                <div className="bg-info dashboard-bx-pm">
                  <h2><i className="fa fa-calendar"></i> {dashboardStats.new_registrations_today}</h2>
                  <span style={{ fontSize: '16px' }}>New Registrations Today <i className="fa fa-arrow-circle-right"></i></span>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="panel panel-default">
        <div className="panel-body">
          <div className="col-md-12">
            <div className="col-md-6">
              <h2 className="text-capitalize">
                Courses by Category: <span style={{ fontSize: '30px', fontWeight: 'bold', color: '#ec3535' }}>CHART</span>
              </h2>
              <div style={{ height: '300px' }}>
                <Doughnut data={coursesChartData} options={chartOptions} />
              </div>
            </div>
            <div className="col-md-6">
              <h2 className="text-capitalize">
                Students by Grade: <span style={{ fontSize: '30px', fontWeight: 'bold', color: '#ec3535' }}>CHART</span>
              </h2>
              <div style={{ height: '300px' }}>
                <Line data={studentsChartData} options={chartOptions} />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Analytics */}
      <div className="panel panel-default">
        <div className="panel-body">
          <div className="col-md-12">
            <div className="col-md-6">
              <h2 className="text-capitalize">
                Assignment Status: <span style={{ fontSize: '30px', fontWeight: 'bold', color: '#ec3535' }}>CHART</span>
              </h2>
              <div style={{ height: '300px' }}>
                <Doughnut data={assignmentChartData} options={chartOptions} />
              </div>
            </div>
            <div className="col-md-6">
              <h2 className="text-capitalize">
                Monthly Revenue: <span style={{ fontSize: '30px', fontWeight: 'bold', color: '#ec3535' }}>CHART</span>
              </h2>
              <div style={{ height: '300px' }}>
                <Bar data={revenueChartData} options={chartOptions} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLTELayout>
  );
}
