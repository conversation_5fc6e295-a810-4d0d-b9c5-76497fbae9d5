'use client';

import React, { useState } from 'react';
import AdminLTELayout from '@/components/layout/AdminLTELayout';
import { adminUsers } from '@/data/dummyData';

// Extended dummy users data
const users = [
  {
    id: 1,
    name: 'Super Admin',
    email: '<EMAIL>',
    role: 'Admin',
    status: 1,
    created_at: '2024-01-01T10:00:00Z',
    role_id: 1
  },
  {
    id: 2,
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Admin',
    status: 1,
    created_at: '2024-01-15T10:00:00Z',
    role_id: 1
  },
  {
    id: 3,
    name: 'Sarah Manager',
    email: '<EMAIL>',
    role: 'Director',
    status: 1,
    created_at: '2024-02-01T10:00:00Z',
    role_id: 5
  },
  {
    id: 4,
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Course Builder',
    status: 0,
    created_at: '2024-02-10T10:00:00Z',
    role_id: 4
  }
];

export default function UsersPage() {
  const [userList, setUserList] = useState(users);

  const handleStatusToggle = (userId: number, currentStatus: number) => {
    const newStatus = currentStatus === 1 ? 0 : 1;
    setUserList(prev => 
      prev.map(user => 
        user.id === userId ? { ...user, status: newStatus } : user
      )
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  return (
    <AdminLTELayout>
      <div className="admn">
        <h3 className="page-title">Administrator Users</h3>
        
        <p>
          <a href="/admin/users/create" className="btn btn-success">Add New</a>
        </p>
      </div>

      <div className="panel panel-default">
        <div style={{ fontSize: '20px' }} className="panel-heading">
          List
        </div>

        <div className="panel-body scroll-table table-responsive">
          <table className={`table table-bordered table-striped ${userList.length > 0 ? 'datatable1' : ''} dt-select`}>
            <thead>
              <tr>
                <th style={{ fontSize: '15px' }}>Sr.No</th>
                <th style={{ fontSize: '15px' }}>Name</th>
                <th style={{ fontSize: '15px' }}>Email</th>
                <th style={{ fontSize: '15px' }}>Role</th>
                <th style={{ fontSize: '15px' }}>Status</th>
                <th style={{ fontSize: '15px' }}>Created At</th>
                <th style={{ fontSize: '15px' }}>Action</th>
              </tr>
            </thead>
            
            <tbody>
              {userList.length > 0 ? (
                userList.map((user, index) => (
                  <tr key={user.id} data-entry-id={user.id}>
                    <td>{index + 1}</td>
                    <td>{user.name}</td>
                    <td>{user.email}</td>
                    <td>{user.role}</td>
                    <td>
                      {user.status === 1 ? (
                        <a 
                          href="#" 
                          className="label label-success"
                          onClick={(e) => {
                            e.preventDefault();
                            handleStatusToggle(user.id, user.status);
                          }}
                        >
                          Active
                        </a>
                      ) : (
                        <a 
                          href="#" 
                          className="label label-danger"
                          onClick={(e) => {
                            e.preventDefault();
                            handleStatusToggle(user.id, user.status);
                          }}
                        >
                          Inactive
                        </a>
                      )}
                    </td>
                    <td>{formatDate(user.created_at)}</td>
                    <td>
                      <a href={`/admin/users/edit/${user.id}`} className="btn btn-xs btn-info">
                        Edit
                      </a>
                      {' '}
                      <a href={`/admin/users/view/${user.id}`} className="btn btn-xs btn-primary">
                        View
                      </a>
                      {user.role_id !== 1 && (
                        <>
                          {' '}
                          <button 
                            className="btn btn-xs btn-danger"
                            onClick={() => {
                              if (confirm('Are you sure you want to delete this user?')) {
                                setUserList(prev => prev.filter(u => u.id !== user.id));
                              }
                            }}
                          >
                            Delete
                          </button>
                        </>
                      )}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={7}>No entries in table</td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </AdminLTELayout>
  );
}
