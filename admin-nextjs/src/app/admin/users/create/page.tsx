'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import AdminLTELayout from '@/components/layout/AdminLTELayout';

interface UserFormData {
  name: string;
  email: string;
  phone: string;
  password: string;
  roles: string[];
}

const availableRoles = [
  { value: 'Admin', label: 'Admin' },
  { value: 'Director', label: 'Director' },
  { value: 'Course Builder', label: 'Course Builder' },
];

export default function CreateUserPage() {
  const router = useRouter();
  const [formData, setFormData] = useState<UserFormData>({
    name: '',
    email: '',
    phone: '',
    password: '',
    roles: []
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleRoleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedOptions = Array.from(e.target.selectedOptions, option => option.value);
    setFormData(prev => ({
      ...prev,
      roles: selectedOptions
    }));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Mobile is required';
    } else if (formData.phone.length !== 10) {
      newErrors.phone = 'Mobile must be 10 digits';
    }

    if (!formData.password.trim()) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    if (formData.roles.length === 0) {
      newErrors.roles = 'Please select at least one role';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // In a real app, you would make an API call here
      console.log('Creating user:', formData);
      
      // Redirect to users list
      router.push('/admin/users');
    } catch (error) {
      console.error('Error creating user:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <AdminLTELayout>
      <h3 className="page-title">Create User</h3>

      <div className="panel panel-default">
        <div className="panel-heading">
          Create new user
        </div>

        <div className="panel-body">
          <form onSubmit={handleSubmit}>
            <div className="row">
              <div className="col-xs-12 form-group">
                <label htmlFor="name" className="control-label">Name*</label>
                <input
                  type="text"
                  name="name"
                  className="form-control"
                  placeholder=""
                  value={formData.name}
                  onChange={handleChange}
                  required
                />
                {errors.name && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.name}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-xs-12 form-group">
                <label htmlFor="email" className="control-label">Email*</label>
                <input
                  type="email"
                  name="email"
                  className="form-control"
                  placeholder=""
                  value={formData.email}
                  onChange={handleChange}
                  required
                />
                {errors.email && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.email}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-xs-12 form-group">
                <label htmlFor="phone" className="control-label">Mobile*</label>
                <input
                  type="text"
                  name="phone"
                  className="form-control"
                  placeholder=""
                  maxLength={10}
                  value={formData.phone}
                  onChange={handleChange}
                  required
                />
                {errors.phone && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.phone}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-xs-12 form-group">
                <label htmlFor="password" className="control-label">Password*</label>
                <div style={{ position: 'relative' }}>
                  <input
                    type={showPassword ? 'text' : 'password'}
                    name="password"
                    className="form-control"
                    id="password-field"
                    placeholder=""
                    value={formData.password}
                    onChange={handleChange}
                    required
                  />
                  <span 
                    className={`fa fa-fw ${showPassword ? 'fa-eye-slash' : 'fa-eye'} field-icon toggle-password`}
                    style={{
                      position: 'absolute',
                      right: '10px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      cursor: 'pointer',
                      color: '#999'
                    }}
                    onClick={() => setShowPassword(!showPassword)}
                  ></span>
                </div>
                {errors.password && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.password}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-xs-12 form-group">
                <label htmlFor="roles" className="control-label">Roles*</label>
                <select
                  name="roles"
                  className="form-control select2"
                  multiple
                  value={formData.roles}
                  onChange={handleRoleChange}
                  style={{ height: 'auto', minHeight: '100px' }}
                >
                  {availableRoles.map(role => (
                    <option key={role.value} value={role.value}>
                      {role.label}
                    </option>
                  ))}
                </select>
                {errors.roles && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.roles}
                  </p>
                )}
              </div>
            </div>

            <div>
              <button 
                type="submit" 
                className="btn btn-danger"
                disabled={loading}
                style={{
                  backgroundColor: '#375dbc',
                  borderColor: '#375dbc'
                }}
              >
                {loading ? 'Creating...' : 'Save'}
              </button>
              {' '}
              <a href="/admin/users" className="btn btn-default">
                Cancel
              </a>
            </div>
          </form>
        </div>
      </div>
    </AdminLTELayout>
  );
}
