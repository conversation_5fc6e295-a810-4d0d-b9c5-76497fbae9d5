'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import AdminLTELayout from '@/components/layout/AdminLTELayout';
import { users } from '@/data/dummyData';

interface UserFormData {
  name: string;
  email: string;
  phone: string;
  roles: string[];
}

const availableRoles = [
  { value: 'Admin', label: 'Admin' },
  { value: 'Director', label: 'Director' },
  { value: 'Course Builder', label: 'Course Builder' },
];

export default function EditUserPage() {
  const router = useRouter();
  const params = useParams();
  const userId = params.id as string;
  
  const [formData, setFormData] = useState<UserFormData>({
    name: '',
    email: '',
    phone: '',
    roles: []
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);

  useEffect(() => {
    // Load user data
    const loadUser = async () => {
      try {
        // In a real app, you would fetch from API
        const user = users.find(u => u.id === parseInt(userId));
        
        if (user) {
          setFormData({
            name: user.name,
            email: user.email,
            phone: user.phone || '',
            roles: user.roles || []
          });
        } else {
          alert('User not found');
          router.push('/admin/users');
        }
      } catch (error) {
        console.error('Error loading user:', error);
        alert('Error loading user');
      } finally {
        setInitialLoading(false);
      }
    };

    if (userId) {
      loadUser();
    }
  }, [userId, router]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleRoleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedOptions = Array.from(e.target.selectedOptions, option => option.value);
    setFormData(prev => ({
      ...prev,
      roles: selectedOptions
    }));
    
    if (errors.roles) {
      setErrors(prev => ({
        ...prev,
        roles: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    } else if (formData.name.length < 2) {
      newErrors.name = 'Name must be at least 2 characters';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (formData.phone && !/^\+?[\d\s\-\(\)]+$/.test(formData.phone)) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    if (formData.roles.length === 0) {
      newErrors.roles = 'At least one role is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // In a real app, you would make an API call here
      console.log('Updating user:', { id: userId, ...formData });
      
      // Show success message
      alert('User updated successfully!');
      
      // Redirect to users list
      router.push('/admin/users');
    } catch (error) {
      console.error('Error updating user:', error);
      alert('Error updating user. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <AdminLTELayout>
        <div className="text-center">
          <p>Loading user...</p>
        </div>
      </AdminLTELayout>
    );
  }

  return (
    <AdminLTELayout>
      <h3 className="page-title">Edit User</h3>

      <div className="panel panel-default">
        <div className="panel-heading">
          Edit user
        </div>

        <div className="panel-body">
          <form onSubmit={handleSubmit}>
            <div className="row">
              <div className="col-xs-12 form-group">
                <label htmlFor="name" className="control-label">Name*</label>
                <input
                  type="text"
                  name="name"
                  className="form-control"
                  placeholder=""
                  value={formData.name}
                  onChange={handleChange}
                  required
                />
                {errors.name && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.name}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-xs-12 form-group">
                <label htmlFor="email" className="control-label">Email*</label>
                <input
                  type="email"
                  name="email"
                  className="form-control"
                  placeholder=""
                  value={formData.email}
                  onChange={handleChange}
                  required
                />
                {errors.email && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.email}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-xs-12 form-group">
                <label htmlFor="phone" className="control-label">Phone</label>
                <input
                  type="tel"
                  name="phone"
                  className="form-control"
                  placeholder=""
                  value={formData.phone}
                  onChange={handleChange}
                />
                {errors.phone && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.phone}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-xs-12 form-group">
                <label htmlFor="roles" className="control-label">Roles*</label>
                <select
                  name="roles"
                  className="form-control"
                  multiple
                  value={formData.roles}
                  onChange={handleRoleChange}
                  required
                  style={{ height: '120px' }}
                >
                  {availableRoles.map(role => (
                    <option key={role.value} value={role.value}>
                      {role.label}
                    </option>
                  ))}
                </select>
                <p className="help-block">Hold Ctrl (Cmd on Mac) to select multiple roles</p>
                {errors.roles && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.roles}
                  </p>
                )}
              </div>
            </div>

            <div>
              <button 
                type="submit" 
                className="btn btn-danger"
                disabled={loading}
                style={{
                  backgroundColor: '#375dbc',
                  borderColor: '#375dbc'
                }}
              >
                {loading ? 'Updating...' : 'Update'}
              </button>
              {' '}
              <a href="/admin/users" className="btn btn-default">
                Cancel
              </a>
            </div>
          </form>
        </div>
      </div>
    </AdminLTELayout>
  );
}
