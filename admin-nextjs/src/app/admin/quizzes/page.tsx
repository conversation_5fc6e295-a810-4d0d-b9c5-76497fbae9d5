'use client';

import React, { useState } from 'react';
import AdminLTELayout from '@/components/layout/AdminLTELayout';
import { courses } from '@/data/dummyData';

// Quiz data
const quizzesData = [
  {
    id: 1,
    name: 'Calculus Quiz 1',
    class_type: 'Regular',
    class_name: 'Grade 12',
    course_name: 'Advanced Calculus',
    lesson_name: 'Derivatives',
    topic_name: 'Basic Derivatives',
    time_limit: 60,
    total_questions: 20,
    total_marks: 100,
    status: 1,
    created_at: '2024-02-01T10:00:00Z'
  },
  {
    id: 2,
    name: 'Physics Quiz - Quantum Mechanics',
    class_type: 'Advanced',
    class_name: 'Grade 11',
    course_name: 'Quantum Physics',
    lesson_name: 'Wave Functions',
    topic_name: 'Schrödinger Equation',
    time_limit: 45,
    total_questions: 15,
    total_marks: 75,
    status: 1,
    created_at: '2024-02-02T10:00:00Z'
  },
  {
    id: 3,
    name: 'English Literature Quiz',
    class_type: 'Regular',
    class_name: 'Grade 10',
    course_name: 'Creative Writing',
    lesson_name: 'Poetry Analysis',
    topic_name: 'Metaphors and Similes',
    time_limit: 30,
    total_questions: 10,
    total_marks: 50,
    status: 0,
    created_at: '2024-02-03T10:00:00Z'
  },
  {
    id: 4,
    name: 'Data Structures Quiz',
    class_type: 'Advanced',
    class_name: 'Grade 12',
    course_name: 'Data Structures',
    lesson_name: 'Arrays and Lists',
    topic_name: 'Dynamic Arrays',
    time_limit: 90,
    total_questions: 25,
    total_marks: 125,
    status: 1,
    created_at: '2024-02-04T10:00:00Z'
  }
];

export default function QuizzesPage() {
  const [quizList, setQuizList] = useState(quizzesData);
  const [filters, setFilters] = useState({
    class_type: '',
    class_id: '',
    course_id: ''
  });

  const handleStatusToggle = (quizId: number, currentStatus: number) => {
    const newStatus = currentStatus === 1 ? 0 : 1;
    setQuizList(prev => 
      prev.map(quiz => 
        quiz.id === quizId ? { ...quiz, status: newStatus } : quiz
      )
    );
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: '2-digit'
    });
  };

  return (
    <AdminLTELayout>
      <div className="row">
        <div className="col-md-12">
          <div className="panel panel-default">
            <div className="panel-heading">All Quiz</div>
            <div className="panel-body">
              <div id="message"></div>
              
              {/* Filters */}
              <form name="filterfrm" method="GET">
                <div className="row">
                  <div className="form-group col-md-2">
                    <select name="class_type" className="form-control" value={filters.class_type} onChange={handleFilterChange}>
                      <option value="">--Select Class Type--</option>
                      <option value="Regular">Regular</option>
                      <option value="Advanced">Advanced</option>
                    </select>
                  </div>
                  
                  <div className="form-group col-md-2">
                    <select name="class_id" className="form-control select2" value={filters.class_id} onChange={handleFilterChange}>
                      <option value="">--select class--</option>
                      <option value="10">Grade 10</option>
                      <option value="11">Grade 11</option>
                      <option value="12">Grade 12</option>
                    </select>
                  </div>
                  
                  <div className="form-group col-md-2">
                    <select name="course_id" className="form-control select2" value={filters.course_id} onChange={handleFilterChange}>
                      <option value="">--select course--</option>
                      {courses.map(course => (
                        <option key={course.id} value={course.id}>{course.name}</option>
                      ))}
                    </select>
                  </div>
                  
                  <div className="form-group col-md-2">
                    <button type="submit" className="btn btn-primary">Filter</button>
                  </div>
                </div>
              </form>
              
              <div className="row">
                <div className="col-md-12">
                  <div className="col-md-6 text-right">
                    
                  </div>
                  <div className="col-md-6">
                    <div className="box-default text-right">
                      <a className="btn btn-bitbucket float-right" href="/admin/quizzes/import">Import Quiz Questions & Options</a>
                      <a className="btn btn-bitbucket float-right" href="/admin/quizzes/create" style={{ marginRight: '10px' }}>Add New Quiz</a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="panel-body table-responsive">
              <table className={`table table-bordered table-striped dt-select ${quizList.length > 0 ? 'datatable1' : ''}`} id="courses1">
                <thead>
                  <tr>
                    <th>#</th>
                    <th>Class Type</th>
                    <th>Class</th>
                    <th>Course Name</th>
                    <th>Lesson Name</th>
                    <th>Topic Name</th>
                    <th>Quiz Title</th>
                    <th>Created At</th>
                    <th>Status</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  {quizList.length > 0 ? (
                    quizList.map((quiz, index) => (
                      <tr key={quiz.id}>
                        <td>{index + 1}</td>
                        <td>{quiz.class_type}</td>
                        <td>{quiz.class_name}</td>
                        <td>{quiz.course_name}</td>
                        <td>{quiz.lesson_name}</td>
                        <td>{quiz.topic_name}</td>
                        <td>{quiz.name}</td>
                        <td>{formatDate(quiz.created_at)}</td>
                        <td>
                          {quiz.status === 1 ? (
                            <a 
                              href="#" 
                              className="label label-success"
                              onClick={(e) => {
                                e.preventDefault();
                                handleStatusToggle(quiz.id, quiz.status);
                              }}
                            >
                              Active
                            </a>
                          ) : (
                            <a 
                              href="#" 
                              className="label label-danger"
                              onClick={(e) => {
                                e.preventDefault();
                                handleStatusToggle(quiz.id, quiz.status);
                              }}
                            >
                              Inactive
                            </a>
                          )}
                        </td>
                        <td>
                          <a href={`/admin/quizzes/edit/${quiz.id}`} className="btn btn-primary btn-sm">Edit</a>
                          {' '}
                          <a href={`/admin/quizzes/view/${quiz.id}`} className="btn btn-info btn-sm">View</a>
                          {' '}
                          <a href={`/admin/quizzes/questions/${quiz.id}`} className="btn btn-warning btn-sm">Questions</a>
                          {' '}
                          <button 
                            className="btn btn-danger btn-sm"
                            onClick={() => {
                              if (confirm('Are you sure you want to delete this quiz?')) {
                                setQuizList(prev => prev.filter(q => q.id !== quiz.id));
                              }
                            }}
                          >
                            Delete
                          </button>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={10}>No entries in table</td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </AdminLTELayout>
  );
}
