'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import AdminLTELayout from '@/components/layout/AdminLTELayout';
import { quizzes, subjects } from '@/data/dummyData';

interface QuizFormData {
  title: string;
  description: string;
  subject_id: string;
  duration: string;
  total_marks: string;
  passing_marks: string;
  instructions: string;
  status: string;
}

export default function EditQuizPage() {
  const router = useRouter();
  const params = useParams();
  const quizId = params.id as string;
  
  const [formData, setFormData] = useState<QuizFormData>({
    title: '',
    description: '',
    subject_id: '',
    duration: '',
    total_marks: '',
    passing_marks: '',
    instructions: '',
    status: '1'
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);

  useEffect(() => {
    // Load quiz data
    const loadQuiz = async () => {
      try {
        // In a real app, you would fetch from API
        const quiz = quizzes.find(q => q.id === parseInt(quizId));
        
        if (quiz) {
          setFormData({
            title: quiz.title,
            description: quiz.description || '',
            subject_id: quiz.subject_id.toString(),
            duration: quiz.duration?.toString() || '',
            total_marks: quiz.total_marks?.toString() || '',
            passing_marks: quiz.passing_marks?.toString() || '',
            instructions: quiz.instructions || '',
            status: quiz.status.toString()
          });
        } else {
          alert('Quiz not found');
          router.push('/admin/quizzes');
        }
      } catch (error) {
        console.error('Error loading quiz:', error);
        alert('Error loading quiz');
      } finally {
        setInitialLoading(false);
      }
    };

    if (quizId) {
      loadQuiz();
    }
  }, [quizId, router]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Quiz title is required';
    } else if (formData.title.length < 3) {
      newErrors.title = 'Quiz title must be at least 3 characters';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    } else if (formData.description.length < 10) {
      newErrors.description = 'Description must be at least 10 characters';
    }

    if (!formData.subject_id) {
      newErrors.subject_id = 'Subject is required';
    }

    if (!formData.duration.trim()) {
      newErrors.duration = 'Duration is required';
    } else if (isNaN(Number(formData.duration)) || Number(formData.duration) < 1) {
      newErrors.duration = 'Duration must be a valid number in minutes';
    }

    if (!formData.total_marks.trim()) {
      newErrors.total_marks = 'Total marks is required';
    } else if (isNaN(Number(formData.total_marks)) || Number(formData.total_marks) < 1) {
      newErrors.total_marks = 'Total marks must be a valid number';
    }

    if (!formData.passing_marks.trim()) {
      newErrors.passing_marks = 'Passing marks is required';
    } else if (isNaN(Number(formData.passing_marks)) || Number(formData.passing_marks) < 0) {
      newErrors.passing_marks = 'Passing marks must be a valid number';
    } else if (Number(formData.passing_marks) > Number(formData.total_marks)) {
      newErrors.passing_marks = 'Passing marks cannot be greater than total marks';
    }

    if (!formData.instructions.trim()) {
      newErrors.instructions = 'Instructions are required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // In a real app, you would make an API call here
      console.log('Updating quiz:', { id: quizId, ...formData });
      
      // Show success message
      alert('Quiz updated successfully!');
      
      // Redirect to quizzes list
      router.push('/admin/quizzes');
    } catch (error) {
      console.error('Error updating quiz:', error);
      alert('Error updating quiz. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <AdminLTELayout>
        <div className="text-center">
          <p>Loading quiz...</p>
        </div>
      </AdminLTELayout>
    );
  }

  return (
    <AdminLTELayout>
      <h3 className="page-title">Edit Quiz</h3>

      <div className="panel panel-default">
        <div className="panel-heading">
          Edit quiz
        </div>

        <div className="panel-body">
          <form onSubmit={handleSubmit}>
            <div className="row">
              <div className="col-xs-12 form-group">
                <label htmlFor="title" className="control-label">Quiz Title*</label>
                <input
                  type="text"
                  name="title"
                  className="form-control"
                  placeholder="Enter quiz title"
                  value={formData.title}
                  onChange={handleChange}
                  required
                />
                {errors.title && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.title}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-xs-12 form-group">
                <label htmlFor="description" className="control-label">Description*</label>
                <textarea
                  name="description"
                  className="form-control"
                  placeholder="Enter quiz description"
                  rows={4}
                  value={formData.description}
                  onChange={handleChange}
                  required
                />
                {errors.description && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.description}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-md-6 form-group">
                <label htmlFor="subject_id" className="control-label">Subject*</label>
                <select
                  name="subject_id"
                  className="form-control"
                  value={formData.subject_id}
                  onChange={handleChange}
                  required
                >
                  <option value="">Select Subject</option>
                  {subjects.map(subject => (
                    <option key={subject.id} value={subject.id}>
                      {subject.name}
                    </option>
                  ))}
                </select>
                {errors.subject_id && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.subject_id}
                  </p>
                )}
              </div>

              <div className="col-md-6 form-group">
                <label htmlFor="duration" className="control-label">Duration (minutes)*</label>
                <input
                  type="number"
                  name="duration"
                  className="form-control"
                  placeholder="Enter duration in minutes"
                  value={formData.duration}
                  onChange={handleChange}
                  min="1"
                  required
                />
                {errors.duration && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.duration}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-md-6 form-group">
                <label htmlFor="total_marks" className="control-label">Total Marks*</label>
                <input
                  type="number"
                  name="total_marks"
                  className="form-control"
                  placeholder="Enter total marks"
                  value={formData.total_marks}
                  onChange={handleChange}
                  min="1"
                  required
                />
                {errors.total_marks && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.total_marks}
                  </p>
                )}
              </div>

              <div className="col-md-6 form-group">
                <label htmlFor="passing_marks" className="control-label">Passing Marks*</label>
                <input
                  type="number"
                  name="passing_marks"
                  className="form-control"
                  placeholder="Enter passing marks"
                  value={formData.passing_marks}
                  onChange={handleChange}
                  min="0"
                  required
                />
                {errors.passing_marks && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.passing_marks}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-xs-12 form-group">
                <label htmlFor="instructions" className="control-label">Instructions*</label>
                <textarea
                  name="instructions"
                  className="form-control"
                  placeholder="Enter quiz instructions"
                  rows={5}
                  value={formData.instructions}
                  onChange={handleChange}
                  required
                />
                {errors.instructions && (
                  <p className="help-block" style={{ color: '#a94442' }}>
                    {errors.instructions}
                  </p>
                )}
              </div>
            </div>

            <div className="row">
              <div className="col-md-6 form-group">
                <label htmlFor="status" className="control-label">Status*</label>
                <select
                  name="status"
                  className="form-control"
                  value={formData.status}
                  onChange={handleChange}
                  required
                >
                  <option value="1">Active</option>
                  <option value="0">Inactive</option>
                </select>
              </div>
            </div>

            <div>
              <button 
                type="submit" 
                className="btn btn-danger"
                disabled={loading}
                style={{
                  backgroundColor: '#375dbc',
                  borderColor: '#375dbc'
                }}
              >
                {loading ? 'Updating...' : 'Update'}
              </button>
              {' '}
              <a href="/admin/quizzes" className="btn btn-default">
                Cancel
              </a>
            </div>
          </form>
        </div>
      </div>
    </AdminLTELayout>
  );
}
