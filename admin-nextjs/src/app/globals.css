@import url(https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,600,700,300italic,400italic,600italic);

/* AdminLTE Base Styles */
* {
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  font-family: "Source Sans Pro", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-weight: 400;
  overflow-x: hidden;
  overflow-y: auto;
  background-color: #ecf0f5;
  color: #444;
  font-size: 14px;
  line-height: 1.42857143;
}

/* AdminLTE Wrapper */
.wrapper {
  height: 100%;
  position: relative;
  overflow: hidden;
}

/* Main Header */
.main-header {
  position: relative;
  max-height: 100px;
  z-index: 1030;
}

.main-header .navbar {
  transition: margin-left 0.3s ease-in-out;
  margin-bottom: 0;
  margin-left: 230px;
  border: none;
  min-height: 50px;
  border-radius: 0;
}

.main-header .navbar-custom-menu > .navbar-nav > li {
  position: relative;
}

.main-header .navbar-custom-menu > .navbar-nav > li > .dropdown-menu {
  position: absolute;
  right: 0;
  left: auto;
}

.main-header .logo {
  transition: width 0.3s ease-in-out;
  display: block;
  float: left;
  height: 50px;
  font-size: 20px;
  line-height: 50px;
  text-align: center;
  width: 230px;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  padding: 0 15px;
  font-weight: 300;
  overflow: hidden;
}

.main-header .logo .logo-lg {
  display: block;
}

.main-header .logo .logo-mini {
  display: none;
}

.main-header .navbar .sidebar-toggle {
  float: left;
  background-color: transparent;
  background-image: none;
  padding: 15px 15px;
  font-family: fontAwesome;
}

.main-header .navbar .sidebar-toggle:before {
  content: "\f0c9";
}

/* Sidebar */
.main-sidebar,
.left-side {
  position: absolute;
  top: 0;
  left: 0;
  padding-top: 50px;
  min-height: 100%;
  width: 230px;
  z-index: 810;
  transition: transform 0.3s ease-in-out, width 0.3s ease-in-out;
}

.sidebar {
  padding-bottom: 10px;
}

.sidebar-menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

.sidebar-menu > li {
  position: relative;
  margin: 0;
  padding: 0;
}

.sidebar-menu > li > a {
  padding: 12px 5px 12px 15px;
  display: block;
  text-decoration: none;
}

.sidebar-menu > li.header {
  padding: 10px 25px 10px 15px;
  background: #1a2226;
  color: #4b646f;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.sidebar-menu > li > a > .fa,
.sidebar-menu > li > a > .glyphicon,
.sidebar-menu > li > a > .ion {
  width: 20px;
}

.sidebar-menu > li .label,
.sidebar-menu > li .badge {
  margin-right: 5px;
}

.sidebar-menu > li .badge {
  margin-top: 3px;
}

.sidebar-menu li.active > a,
.sidebar-menu li:hover > a {
  color: #fff;
  background: #1e282c;
  border-left-color: #375dbc;
}

.sidebar-menu > li > .treeview-menu {
  margin: 0 1px;
  background: #2c3b41;
}

.treeview-menu {
  display: none;
  list-style: none;
  padding: 0;
  margin: 0;
  padding-left: 5px;
}

.treeview-menu > li {
  margin: 0;
}

.treeview-menu > li > a {
  padding: 5px 5px 5px 15px;
  display: block;
  font-size: 14px;
  text-decoration: none;
}

.treeview-menu > li > a > .fa,
.treeview-menu > li > a > .glyphicon,
.treeview-menu > li > a > .ion {
  width: 20px;
}

/* Content Wrapper */
.content-wrapper,
.right-side {
  min-height: 100%;
  background-color: #ecf0f5;
  z-index: 800;
  transition: transform 0.3s ease-in-out, margin 0.3s ease-in-out;
  margin-left: 230px;
}

.content {
  min-height: 250px;
  padding: 0;
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px;
}

/* Responsive */
@media (max-width: 767px) {
  .content-wrapper,
  .right-side,
  .main-footer {
    margin-left: 0;
  }

  .main-sidebar,
  .left-side {
    padding-top: 100px;
    transform: translate(-230px, 0);
  }

  .sidebar-open .content-wrapper,
  .sidebar-open .right-side,
  .sidebar-open .main-footer {
    transform: translate(230px, 0);
  }

  .sidebar-open .main-sidebar,
  .sidebar-open .left-side {
    transform: translate(0, 0);
  }
}

@media (min-width: 768px) {
  .sidebar-collapse .content-wrapper,
  .sidebar-collapse .right-side,
  .sidebar-collapse .main-footer {
    margin-left: 0;
  }

  .sidebar-collapse .main-sidebar,
  .sidebar-collapse .left-side {
    transform: translate(-230px, 0);
  }
}

/* Skin Blue Styles - Exact AdminLTE */
.skin-blue .main-header .navbar {
  background-color: #375dbc;
}

.skin-blue .main-header .navbar .nav > li > a {
  color: #fff;
}

.skin-blue .main-header .navbar .nav > li > a:hover,
.skin-blue .main-header .navbar .nav > li > a:active,
.skin-blue .main-header .navbar .nav > li > a:focus,
.skin-blue .main-header .navbar .nav .open > a,
.skin-blue .main-header .navbar .nav .open > a:hover,
.skin-blue .main-header .navbar .nav .open > a:focus,
.skin-blue .main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.1);
  color: #f6f6f6;
}

.skin-blue .main-header .navbar .sidebar-toggle {
  color: #fff;
}

.skin-blue .main-header .navbar .sidebar-toggle:hover {
  color: #f6f6f6;
  background: rgba(0, 0, 0, 0.1);
}

.skin-blue .main-header .logo {
  background-color: #375dbc;
  color: #fff;
  border-bottom: 0 solid transparent;
}

.skin-blue .main-header .logo:hover {
  background-color: #375dbc;
}

.skin-blue .wrapper,
.skin-blue .main-sidebar,
.skin-blue .left-side {
  background-color: #375dbc;
}

.skin-blue .sidebar-menu > li.header {
  color: #4b646f;
  background: #1a2226;
}

.skin-blue .sidebar-menu > li > a {
  border-left: 3px solid transparent;
}

.skin-blue .sidebar-menu > li:hover > a,
.skin-blue .sidebar-menu > li.active > a {
  color: #fff;
  background: #09336f;
  border-left-color: #09336f;
}

.skin-blue .sidebar-menu > li > .treeview-menu {
  margin: 0 1px;
  background: #ffffff;
}

.skin-blue .sidebar a {
  color: #ffffff;
}

.skin-blue .sidebar a:hover {
  text-decoration: none;
}

.skin-blue .treeview-menu > li > a {
  color: #8aa4af;
}

.skin-blue .treeview-menu > li.active > a,
.skin-blue .treeview-menu > li > a:hover {
  color: #fff;
  background: #09336f;
}

.skin-blue li.treeview.open {
  background: #09336f;
}

/* Scroll Table Styles */
.scroll-table {
  overflow: hidden;
  height: calc(80vh - 68px);
  overflow-x: hidden;
  overflow-y: auto;
}

.scroll-table.scroll-table-two {
  height: calc(100vh - 68px) !important;
}

.scroll-table:hover {
  overflow: auto !important;
}

.scroll-table::-webkit-scrollbar {
  width: 8px;
}

.scroll-table::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px grey;
  border-radius: 6px;
}

.scroll-table::-webkit-scrollbar-thumb {
  background: #ededed;
  border-radius: 10px;
}

.scroll-table::-webkit-scrollbar-thumb:hover {
  background: #ededed;
}

/* Button Styles */
.btn-success {
  background-color: #375dbc;
  border-color: #375dbc;
}

.btn-success:hover {
  color: #fff;
  background-color: #375dbc !important;
  border-color: #375dbc !important;
}

.btn-warning {
  background-color: #09336f;
  border-color: #09336f;
}

/* Panel Styles */
.panel {
  margin-bottom: 20px;
  background-color: #fff;
  border: 1px solid transparent;
  border-radius: 4px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}

.panel-default {
  border-color: #ddd;
}

.panel-default > .panel-heading {
  color: #333;
  background-color: #f5f5f5;
  border-color: #ddd;
}

.panel-heading {
  padding: 10px 15px;
  border-bottom: 1px solid transparent;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}

.panel-body {
  padding: 15px;
}

/* Table Styles */
.table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 20px;
  background-color: transparent;
}

.table > thead > tr > th,
.table > tbody > tr > th,
.table > tfoot > tr > th,
.table > thead > tr > td,
.table > tbody > tr > td,
.table > tfoot > tr > td {
  padding: 8px;
  line-height: 1.42857143;
  vertical-align: top;
  border-top: 1px solid #ddd;
}

.table > thead > tr > th {
  vertical-align: bottom;
  border-bottom: 2px solid #ddd;
  background-color: #f4f4f4;
}

.table-bordered {
  border: 1px solid #ddd;
}

.table-bordered > thead > tr > th,
.table-bordered > tbody > tr > th,
.table-bordered > tfoot > tr > th,
.table-bordered > thead > tr > td,
.table-bordered > tbody > tr > td,
.table-bordered > tfoot > tr > td {
  border: 1px solid #ddd;
}

/* Treeview Menu Styles */
.sidebar-menu .treeview-menu {
  display: none;
  list-style: none;
  padding: 0;
  margin: 0;
  padding-left: 5px;
}

.sidebar-menu .treeview.menu-open > .treeview-menu {
  display: block;
}

.sidebar-menu .treeview > a > .fa-angle-left,
.sidebar-menu .treeview > a > .pull-right-container > .fa-angle-left {
  transition: transform 0.3s;
}

.sidebar-menu .treeview.menu-open > a > .fa-angle-left,
.sidebar-menu .treeview.menu-open > a > .pull-right-container > .fa-angle-left {
  transform: rotate(-90deg);
}

.sidebar-menu .treeview-menu > li > a {
  padding: 5px 5px 5px 15px;
  display: block;
  font-size: 14px;
  text-decoration: none;
  margin: 0 1px;
}

.sidebar-menu .treeview-menu > li > a > .fa,
.sidebar-menu .treeview-menu > li > a > .glyphicon,
.sidebar-menu .treeview-menu > li > a > .ion {
  width: 20px;
}

/* Active states for treeview */
.skin-blue .sidebar-menu .treeview-menu > li.active > a,
.skin-blue .sidebar-menu .treeview-menu > li > a:hover {
  color: #fff;
  background: #09336f;
}

.skin-blue .sidebar-menu .treeview.active > a {
  color: #fff;
  background: #09336f;
}

/* Bootstrap Grid System */
.row {
  margin-right: -15px;
  margin-left: -15px;
}

.col-md-1,
.col-md-2,
.col-md-3,
.col-md-4,
.col-md-5,
.col-md-6,
.col-md-7,
.col-md-8,
.col-md-9,
.col-md-10,
.col-md-11,
.col-md-12 {
  position: relative;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
}

@media (min-width: 992px) {
  .col-md-1,
  .col-md-2,
  .col-md-3,
  .col-md-4,
  .col-md-5,
  .col-md-6,
  .col-md-7,
  .col-md-8,
  .col-md-9,
  .col-md-10,
  .col-md-11,
  .col-md-12 {
    float: left;
  }

  .col-md-12 {
    width: 100%;
  }

  .col-md-11 {
    width: 91.66666667%;
  }

  .col-md-10 {
    width: 83.33333333%;
  }

  .col-md-9 {
    width: 75%;
  }

  .col-md-8 {
    width: 66.66666667%;
  }

  .col-md-7 {
    width: 58.33333333%;
  }

  .col-md-6 {
    width: 50%;
  }

  .col-md-5 {
    width: 41.66666667%;
  }

  .col-md-4 {
    width: 33.33333333%;
  }

  .col-md-3 {
    width: 25%;
  }

  .col-md-2 {
    width: 16.66666667%;
  }

  .col-md-1 {
    width: 8.33333333%;
  }
}

/* Additional AdminLTE Styles */
.info {
  color: #31708f;
  background-color: #d9edf7;
  border-color: #bce8f1;
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid transparent;
  border-radius: 4px;
}

.text-capitalize {
  text-transform: capitalize;
}
