"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { LoginForm } from "@/types";

export default function LoginPage() {
  const router = useRouter();
  const [formData, setFormData] = useState<LoginForm>({
    email: "",
    password: "",
    remember: false,
  });
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked, type } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      // Simulate login API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // For demo purposes, accept any email/password
      if (formData.email && formData.password) {
        // Store auth token (dummy)
        localStorage.setItem("auth_token", "dummy_token");
        localStorage.setItem("user_role", "admin");

        router.push("/admin/dashboard");
      } else {
        setError("Please enter both email and password");
      }
    } catch (err) {
      setError("Login failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      className="login-page"
      style={{
        background: "linear-gradient(135deg, #375dbc 0%, #2a6496 100%)",
        minHeight: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        fontFamily:
          '"Source Sans Pro", "Helvetica Neue", Helvetica, Arial, sans-serif',
      }}
    >
      <div
        className="login-box"
        style={{
          width: "360px",
          margin: "7% auto",
        }}
      >
        <div
          className="login-logo"
          style={{
            fontSize: "35px",
            textAlign: "center",
            marginBottom: "25px",
          }}
        >
          <a
            href="#"
            style={{
              color: "#fff",
              textDecoration: "none",
              fontWeight: "300",
            }}
          >
            <b>Admin</b>LTE
          </a>
        </div>

        <div
          className="login-box-body"
          style={{
            background: "#fff",
            padding: "20px",
            borderRadius: "4px",
            boxShadow: "0 2px 3px rgba(0,0,0,0.125)",
          }}
        >
          <p
            className="login-box-msg"
            style={{
              margin: "0 0 20px 0",
              textAlign: "center",
              fontSize: "14px",
              color: "#666",
            }}
          >
            Sign in to start your session
          </p>

          {error && (
            <div
              className="alert alert-danger"
              style={{
                color: "#a94442",
                backgroundColor: "#f2dede",
                borderColor: "#ebccd1",
                padding: "15px",
                marginBottom: "20px",
                border: "1px solid transparent",
                borderRadius: "4px",
              }}
            >
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div
              className="form-group has-feedback"
              style={{ marginBottom: "15px" }}
            >
              <input
                type="email"
                name="email"
                className="form-control"
                placeholder="Email"
                value={formData.email}
                onChange={handleChange}
                required
                autoFocus
                style={{
                  display: "block",
                  width: "100%",
                  height: "34px",
                  padding: "6px 12px",
                  fontSize: "14px",
                  lineHeight: "1.42857143",
                  color: "#555",
                  backgroundColor: "#fff",
                  backgroundImage: "none",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                  boxShadow: "inset 0 1px 1px rgba(0,0,0,.075)",
                  transition:
                    "border-color ease-in-out .15s,box-shadow ease-in-out .15s",
                  boxSizing: "border-box",
                }}
              />
              <span
                className="glyphicon glyphicon-envelope form-control-feedback"
                style={{
                  position: "absolute",
                  top: "0",
                  right: "0",
                  zIndex: 2,
                  display: "block",
                  width: "34px",
                  height: "34px",
                  lineHeight: "34px",
                  textAlign: "center",
                  pointerEvents: "none",
                }}
              ></span>
            </div>

            <div
              className="form-group has-feedback"
              style={{ marginBottom: "15px" }}
            >
              <input
                type="password"
                name="password"
                className="form-control"
                placeholder="Password"
                value={formData.password}
                onChange={handleChange}
                required
                style={{
                  display: "block",
                  width: "100%",
                  height: "34px",
                  padding: "6px 12px",
                  fontSize: "14px",
                  lineHeight: "1.42857143",
                  color: "#555",
                  backgroundColor: "#fff",
                  backgroundImage: "none",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                  boxShadow: "inset 0 1px 1px rgba(0,0,0,.075)",
                  transition:
                    "border-color ease-in-out .15s,box-shadow ease-in-out .15s",
                  boxSizing: "border-box",
                }}
              />
              <span
                className="glyphicon glyphicon-lock form-control-feedback"
                style={{
                  position: "absolute",
                  top: "0",
                  right: "0",
                  zIndex: 2,
                  display: "block",
                  width: "34px",
                  height: "34px",
                  lineHeight: "34px",
                  textAlign: "center",
                  pointerEvents: "none",
                }}
              ></span>
            </div>

            <div className="row" style={{ margin: "0 -15px" }}>
              <div
                className="col-xs-8"
                style={{
                  position: "relative",
                  minHeight: "1px",
                  paddingLeft: "15px",
                  paddingRight: "15px",
                  width: "66.66666667%",
                  float: "left",
                }}
              >
                <div
                  className="checkbox icheck"
                  style={{ marginTop: "5px", marginBottom: "10px" }}
                >
                  <label style={{ fontWeight: "normal", cursor: "pointer" }}>
                    <input
                      type="checkbox"
                      name="remember"
                      checked={formData.remember}
                      onChange={handleChange}
                      style={{ marginRight: "5px" }}
                    />
                    Remember Me
                  </label>
                </div>
              </div>

              <div
                className="col-xs-4"
                style={{
                  position: "relative",
                  minHeight: "1px",
                  paddingLeft: "15px",
                  paddingRight: "15px",
                  width: "33.33333333%",
                  float: "left",
                }}
              >
                <button
                  type="submit"
                  className="btn btn-primary btn-block btn-flat"
                  disabled={loading}
                  style={{
                    color: "#fff",
                    backgroundColor: "#375dbc",
                    borderColor: "#375dbc",
                    display: "block",
                    width: "100%",
                    padding: "6px 12px",
                    marginBottom: "0",
                    fontSize: "14px",
                    fontWeight: "normal",
                    lineHeight: "1.42857143",
                    textAlign: "center",
                    whiteSpace: "nowrap",
                    verticalAlign: "middle",
                    touchAction: "manipulation",
                    cursor: "pointer",
                    userSelect: "none",
                    backgroundImage: "none",
                    border: "1px solid transparent",
                    borderRadius: "0",
                    textDecoration: "none",
                  }}
                >
                  {loading ? "Signing in..." : "Sign In"}
                </button>
              </div>
            </div>
          </form>

          <div style={{ marginTop: "20px", textAlign: "center" }}>
            <small style={{ color: "#666", fontSize: "12px" }}>
              Demo: Use any email and password to login
            </small>
          </div>
        </div>
      </div>
    </div>
  );
}
