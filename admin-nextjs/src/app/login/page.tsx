"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { LoginForm } from "@/types";

export default function LoginPage() {
  const router = useRouter();
  const [formData, setFormData] = useState<LoginForm>({
    email: "",
    password: "",
    remember: false,
  });
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked, type } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      // Simulate login API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // For demo purposes, accept any email/password
      if (formData.email && formData.password) {
        // Store auth token (dummy)
        localStorage.setItem("auth_token", "dummy_token");
        localStorage.setItem("user_role", "admin");

        router.push("/admin/dashboard");
      } else {
        setError("Please enter both email and password");
      }
    } catch (err) {
      setError("Login failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <link
        href="https://fonts.googleapis.com/css?family=Karla:400,700&display=swap"
        rel="stylesheet"
      />
      <link
        rel="stylesheet"
        href="https://cdn.materialdesignicons.com/4.8.95/css/materialdesignicons.min.css"
      />
      <link
        rel="stylesheet"
        href="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css"
      />
      <link
        rel="stylesheet"
        href="https://www.bootstrapdash.com/demo/login-template-free-2/assets/css/login.css"
      />

      <style jsx>{`
        body {
          font-family: "Karla", sans-serif;
        }
        .login-card .login-btn {
          padding: 13px 20px 12px;
          background-color: #375dbc;
          border-radius: 4px;
          font-size: 17px;
          font-weight: bold;
          line-height: 20px;
          color: #fff;
          margin-bottom: 24px;
          border: none;
          width: 100%;
        }
        .login-card .login-btn:hover {
          border: 1px solid #375dbc;
          background-color: transparent;
          color: #375dbc;
        }
        a {
          color: #375dbc;
          text-decoration: none;
          background-color: transparent;
        }
        .login-card-img {
          object-fit: contain;
          width: 100%;
          height: 100%;
        }
        .login-card-description {
          font-size: 16px;
          color: #666;
          margin-bottom: 30px;
        }
        .form-control {
          border-radius: 4px;
          border: 1px solid #ddd;
          padding: 12px 15px;
          font-size: 14px;
          margin-bottom: 15px;
        }
        .form-control:focus {
          border-color: #375dbc;
          box-shadow: 0 0 0 0.2rem rgba(55, 93, 188, 0.25);
        }
        .alert {
          border-radius: 4px;
          padding: 15px;
          margin-bottom: 20px;
        }
        .alert-danger {
          color: #721c24;
          background-color: #f8d7da;
          border-color: #f5c6cb;
        }
        .close {
          float: right;
          font-size: 21px;
          font-weight: bold;
          line-height: 1;
          color: #000;
          text-shadow: 0 1px 0 #fff;
          opacity: 0.5;
          background: none;
          border: none;
          cursor: pointer;
        }
        .close:hover {
          opacity: 0.75;
        }
      `}</style>

      <main className="d-flex align-items-center min-vh-100 py-3 py-md-0">
        <div className="container">
          <div className="card login-card">
            <div className="row no-gutters">
              <div className="col-md-5">
                <img
                  src="/img/bnr-imge.svg"
                  alt="login"
                  className="login-card-img"
                  style={{
                    width: "100%",
                    height: "100%",
                    objectFit: "contain",
                  }}
                />
              </div>
              <div className="col-md-7">
                <div className="card-body">
                  <div className="brand-wrapper">
                    <img
                      style={{ display: "none" }}
                      src="/img/web-logo.png"
                      alt="logo"
                      className="logo"
                    />
                  </div>

                  <p className="login-card-description">
                    Sign into your account
                  </p>

                  {error && (
                    <div className="alert alert-danger alert-block">
                      <button
                        type="button"
                        className="close"
                        onClick={() => setError("")}
                      >
                        ×
                      </button>
                      <strong>{error}</strong>
                    </div>
                  )}

                  <form
                    className="form-horizontal"
                    role="form"
                    onSubmit={handleSubmit}
                  >
                    <div className="form-group">
                      <label htmlFor="email" className="sr-only">
                        Email
                      </label>
                      <input
                        type="email"
                        value={formData.email}
                        name="email"
                        id="email"
                        className="form-control"
                        required
                        placeholder="Email address"
                        onChange={handleChange}
                      />
                    </div>
                    <div className="form-group mb-4">
                      <label htmlFor="password" className="sr-only">
                        Password
                      </label>
                      <input
                        required
                        type="password"
                        name="password"
                        id="password"
                        className="form-control"
                        placeholder="***********"
                        value={formData.password}
                        onChange={handleChange}
                      />
                    </div>
                    <input
                      type="submit"
                      name="login"
                      id="login"
                      className="btn btn-block login-btn mb-4"
                      value={loading ? "Logging in..." : "Login"}
                      disabled={loading}
                    />
                  </form>
                  <a href="/password/reset">Forget Password</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </>
  );
}
