// User Types
export interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
  avatar?: string;
}

export interface Teacher extends User {
  subject: string;
  experience: number;
  qualification: string;
  phone: string;
  address: string;
  fees_per_hour: number;
}

export interface Student extends User {
  class: string;
  parent_name: string;
  parent_phone: string;
  date_of_birth: string;
  address: string;
}

// Course Types
export interface Category {
  id: number;
  name: string;
  description: string;
  image?: string;
  status: 'active' | 'inactive';
  sort_order: number;
  created_at: string;
}

export interface Course {
  id: number;
  name: string;
  description: string;
  category_id: number;
  category: Category;
  image?: string;
  video?: string;
  price: number;
  duration: string;
  status: 'active' | 'inactive';
  is_featured: boolean;
  created_at: string;
}

export interface Lesson {
  id: number;
  title: string;
  description: string;
  course_id: number;
  course: Course;
  video?: string;
  pdf?: string;
  duration: string;
  sort_order: number;
  status: 'active' | 'inactive';
  created_at: string;
}

// Assignment Types
export interface Assignment {
  id: number;
  title: string;
  description: string;
  course_id: number;
  course: Course;
  due_date: string;
  max_marks: number;
  status: 'active' | 'inactive';
  created_at: string;
}

export interface UserAssignment {
  id: number;
  assignment_id: number;
  assignment: Assignment;
  user_id: number;
  user: Student;
  submitted_at?: string;
  marks?: number;
  feedback?: string;
  status: 'pending' | 'submitted' | 'graded';
}

// Quiz Types
export interface Quiz {
  id: number;
  title: string;
  description: string;
  course_id: number;
  course: Course;
  time_limit: number; // in minutes
  total_questions: number;
  total_marks: number;
  status: 'active' | 'inactive';
  created_at: string;
}

export interface QuizQuestion {
  id: number;
  quiz_id: number;
  question: string;
  options: string[];
  correct_answer: number;
  marks: number;
}

// Live Class Types
export interface LiveClass {
  id: number;
  title: string;
  description: string;
  course_id: number;
  course: Course;
  teacher_id: number;
  teacher: Teacher;
  scheduled_at: string;
  duration: number; // in minutes
  meeting_link?: string;
  status: 'scheduled' | 'live' | 'completed' | 'cancelled';
  created_at: string;
}

// Community Post Types
export interface CommunityPost {
  id: number;
  title: string;
  content: string;
  user_id: number;
  user: User;
  category: string;
  likes_count: number;
  comments_count: number;
  status: 'active' | 'inactive';
  created_at: string;
}

// Notification Types
export interface Notification {
  id: number;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  user_id?: number;
  is_read: boolean;
  created_at: string;
}

// Dashboard Stats
export interface DashboardStats {
  total_students: number;
  total_teachers: number;
  total_courses: number;
  total_assignments: number;
  pending_assignments: number;
  live_classes_today: number;
  new_registrations_today: number;
  revenue_this_month: number;
}

// Course Request Types
export interface CourseRequest {
  id: number;
  course_id: number;
  course: Course;
  instructor_id: number;
  instructor: Teacher;
  builder_id?: number;
  builder?: User;
  status: 'pending' | 'assigned' | 'in_progress' | 'completed';
  created_at: string;
  waiting_time: string;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  status: 'success' | 'error';
}

export interface PaginatedResponse<T> {
  data: T[];
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
  remember?: boolean;
}

export interface UserForm {
  name: string;
  email: string;
  password?: string;
  role: string;
  status: 'active' | 'inactive';
}

export interface CourseForm {
  name: string;
  description: string;
  category_id: number;
  price: number;
  duration: string;
  status: 'active' | 'inactive';
  is_featured: boolean;
}
