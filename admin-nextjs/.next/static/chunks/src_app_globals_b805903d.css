/* [project]/src/app/globals.css [app-client] (css) */
* {
  box-sizing: border-box;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  color: #444;
  background-color: #ecf0f5;
  font-family: Source Sans Pro, Helvetica Neue, Helvetica, Arial, sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.42857;
  overflow: hidden auto;
}

.wrapper {
  height: 100%;
  position: relative;
  overflow: hidden;
}

.main-header {
  z-index: 1030;
  max-height: 100px;
  position: relative;
}

.main-header .navbar {
  border: none;
  border-radius: 0;
  min-height: 50px;
  margin-bottom: 0;
  margin-left: 230px;
  transition: margin-left .3s ease-in-out;
}

.main-header .navbar-custom-menu > .navbar-nav > li {
  position: relative;
}

.main-header .navbar-custom-menu > .navbar-nav > li > .dropdown-menu {
  position: absolute;
  left: auto;
  right: 0;
}

.main-header .logo {
  float: left;
  text-align: center;
  width: 230px;
  height: 50px;
  padding: 0 15px;
  font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
  font-size: 20px;
  font-weight: 300;
  line-height: 50px;
  transition: width .3s ease-in-out;
  display: block;
  overflow: hidden;
}

.main-header .logo .logo-lg {
  display: block;
}

.main-header .logo .logo-mini {
  display: none;
}

.main-header .navbar .sidebar-toggle {
  float: left;
  background-color: #0000;
  background-image: none;
  padding: 15px;
  font-family: fontAwesome;
}

.main-header .navbar .sidebar-toggle:before {
  content: "";
}

.main-sidebar, .left-side {
  z-index: 810;
  width: 230px;
  min-height: 100%;
  padding-top: 50px;
  transition: transform .3s ease-in-out, width .3s ease-in-out;
  position: absolute;
  top: 0;
  left: 0;
}

.sidebar {
  padding-bottom: 10px;
}

.sidebar-menu {
  margin: 0;
  padding: 0;
  list-style: none;
}

.sidebar-menu > li {
  margin: 0;
  padding: 0;
  position: relative;
}

.sidebar-menu > li > a {
  padding: 12px 5px 12px 15px;
  text-decoration: none;
  display: block;
}

.sidebar-menu > li.header {
  color: #4b646f;
  text-transform: uppercase;
  background: #1a2226;
  padding: 10px 25px 10px 15px;
  font-size: 12px;
  font-weight: 600;
}

.sidebar-menu > li > a > .fa, .sidebar-menu > li > a > .glyphicon, .sidebar-menu > li > a > .ion {
  width: 20px;
}

.sidebar-menu > li .label, .sidebar-menu > li .badge {
  margin-right: 5px;
}

.sidebar-menu > li .badge {
  margin-top: 3px;
}

.sidebar-menu li.active > a, .sidebar-menu li:hover > a {
  color: #fff;
  background: #1e282c;
  border-left-color: #375dbc;
}

.sidebar-menu > li > .treeview-menu {
  background: #2c3b41;
  margin: 0 1px;
}

.treeview-menu {
  margin: 0;
  padding: 0 0 0 5px;
  list-style: none;
  display: none;
}

.treeview-menu > li {
  margin: 0;
}

.treeview-menu > li > a {
  padding: 5px 5px 5px 15px;
  font-size: 14px;
  text-decoration: none;
  display: block;
}

.treeview-menu > li > a > .fa, .treeview-menu > li > a > .glyphicon, .treeview-menu > li > a > .ion {
  width: 20px;
}

.content-wrapper, .right-side {
  z-index: 800;
  background-color: #ecf0f5;
  min-height: 100%;
  margin-left: 230px;
  transition: transform .3s ease-in-out, margin .3s ease-in-out;
}

.content {
  min-height: 250px;
  margin-left: auto;
  margin-right: auto;
  padding: 0 15px;
}

@media (width <= 767px) {
  .content-wrapper, .right-side, .main-footer {
    margin-left: 0;
  }

  .main-sidebar, .left-side {
    padding-top: 100px;
    transform: translate(-230px);
  }

  .sidebar-open .content-wrapper, .sidebar-open .right-side, .sidebar-open .main-footer {
    transform: translate(230px);
  }

  .sidebar-open .main-sidebar, .sidebar-open .left-side {
    transform: translate(0);
  }
}

@media (width >= 768px) {
  .sidebar-collapse .content-wrapper, .sidebar-collapse .right-side, .sidebar-collapse .main-footer {
    margin-left: 0;
  }

  .sidebar-collapse .main-sidebar, .sidebar-collapse .left-side {
    transform: translate(-230px);
  }
}

.skin-blue .main-header .navbar {
  background-color: #375dbc;
}

.skin-blue .main-header .navbar .nav > li > a {
  color: #fff;
}

.skin-blue .main-header .navbar .nav > li > a:hover, .skin-blue .main-header .navbar .nav > li > a:active, .skin-blue .main-header .navbar .nav > li > a:focus, .skin-blue .main-header .navbar .nav .open > a, .skin-blue .main-header .navbar .nav .open > a:hover, .skin-blue .main-header .navbar .nav .open > a:focus, .skin-blue .main-header .navbar .nav > .active > a {
  color: #f6f6f6;
  background: #0000001a;
}

.skin-blue .main-header .navbar .sidebar-toggle {
  color: #fff;
}

.skin-blue .main-header .navbar .sidebar-toggle:hover {
  color: #f6f6f6;
  background: #0000001a;
}

.skin-blue .main-header .logo {
  color: #fff;
  background-color: #375dbc;
  border-bottom: 0 solid #0000;
}

.skin-blue .main-header .logo:hover, .skin-blue .wrapper, .skin-blue .main-sidebar, .skin-blue .left-side {
  background-color: #375dbc;
}

.skin-blue .sidebar-menu > li.header {
  color: #4b646f;
  background: #1a2226;
}

.skin-blue .sidebar-menu > li > a {
  border-left: 3px solid #0000;
}

.skin-blue .sidebar-menu > li:hover > a, .skin-blue .sidebar-menu > li.active > a {
  color: #fff;
  background: #09336f;
  border-left-color: #09336f;
}

.skin-blue .sidebar-menu > li > .treeview-menu {
  background: #fff;
  margin: 0 1px;
}

.skin-blue .sidebar a {
  color: #fff;
}

.skin-blue .sidebar a:hover {
  text-decoration: none;
}

.skin-blue .treeview-menu > li > a {
  color: #8aa4af;
}

.skin-blue .treeview-menu > li.active > a, .skin-blue .treeview-menu > li > a:hover {
  color: #fff;
  background: #09336f;
}

.skin-blue li.treeview.open {
  background: #09336f;
}

.btn-success {
  background-color: #375dbc;
  border-color: #375dbc;
}

.btn-success:hover {
  color: #fff;
  background-color: #375dbc !important;
  border-color: #375dbc !important;
}

.btn-warning {
  background-color: #09336f;
  border-color: #09336f;
}

.panel {
  background-color: #fff;
  border: 1px solid #0000;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 1px 1px #0000000d;
}

.panel-default {
  border-color: #ddd;
}

.panel-default > .panel-heading {
  color: #333;
  background-color: #f5f5f5;
  border-color: #ddd;
}

.panel-heading {
  border-bottom: 1px solid #0000;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  padding: 10px 15px;
}

.panel-body {
  padding: 15px;
}

.table {
  background-color: #0000;
  width: 100%;
  max-width: 100%;
  margin-bottom: 20px;
}

.table > thead > tr > th, .table > tbody > tr > th, .table > tfoot > tr > th, .table > thead > tr > td, .table > tbody > tr > td, .table > tfoot > tr > td {
  vertical-align: top;
  border-top: 1px solid #ddd;
  padding: 8px;
  line-height: 1.42857;
}

.table > thead > tr > th {
  vertical-align: bottom;
  background-color: #f4f4f4;
  border-bottom: 2px solid #ddd;
}

.table-bordered, .table-bordered > thead > tr > th, .table-bordered > tbody > tr > th, .table-bordered > tfoot > tr > th, .table-bordered > thead > tr > td, .table-bordered > tbody > tr > td, .table-bordered > tfoot > tr > td {
  border: 1px solid #ddd;
}

.sidebar-menu .treeview-menu {
  margin: 0;
  padding: 0 0 0 5px;
  list-style: none;
  display: none;
}

.sidebar-menu .treeview.menu-open > .treeview-menu {
  display: block;
}

.sidebar-menu .treeview > a > .fa-angle-left, .sidebar-menu .treeview > a > .pull-right-container > .fa-angle-left {
  transition: transform .3s;
}

.sidebar-menu .treeview.menu-open > a > .fa-angle-left, .sidebar-menu .treeview.menu-open > a > .pull-right-container > .fa-angle-left {
  transform: rotate(-90deg);
}

.sidebar-menu .treeview-menu > li > a {
  margin: 0 1px;
  padding: 5px 5px 5px 15px;
  font-size: 14px;
  text-decoration: none;
  display: block;
}

.sidebar-menu .treeview-menu > li > a > .fa, .sidebar-menu .treeview-menu > li > a > .glyphicon, .sidebar-menu .treeview-menu > li > a > .ion {
  width: 20px;
}

.skin-blue .sidebar-menu .treeview-menu > li.active > a, .skin-blue .sidebar-menu .treeview-menu > li > a:hover, .skin-blue .sidebar-menu .treeview.active > a {
  color: #fff;
  background: #09336f;
}

.row {
  margin-left: -15px;
  margin-right: -15px;
}

.col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12 {
  min-height: 1px;
  padding-left: 15px;
  padding-right: 15px;
  position: relative;
}

@media (width >= 992px) {
  .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12 {
    float: left;
  }

  .col-md-12 {
    width: 100%;
  }

  .col-md-11 {
    width: 91.6667%;
  }

  .col-md-10 {
    width: 83.3333%;
  }

  .col-md-9 {
    width: 75%;
  }

  .col-md-8 {
    width: 66.6667%;
  }

  .col-md-7 {
    width: 58.3333%;
  }

  .col-md-6 {
    width: 50%;
  }

  .col-md-5 {
    width: 41.6667%;
  }

  .col-md-4 {
    width: 33.3333%;
  }

  .col-md-3 {
    width: 25%;
  }

  .col-md-2 {
    width: 16.6667%;
  }

  .col-md-1 {
    width: 8.33333%;
  }
}

.info {
  color: #31708f;
  background-color: #d9edf7;
  border: 1px solid #0000;
  border-radius: 4px;
  margin-bottom: 20px;
  padding: 15px;
}

.text-capitalize {
  text-transform: capitalize;
}

.label {
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: .25em;
  padding: .2em .6em .3em;
  font-size: 75%;
  font-weight: bold;
  line-height: 1;
  display: inline;
}

.label-success {
  background-color: #5cb85c;
}

.label-danger {
  background-color: #d9534f;
}

.label-info {
  background-color: #5bc0de;
}

.label-many {
  margin-bottom: 2px;
  margin-right: 5px;
  display: inline-block;
}

.form-group {
  margin-bottom: 15px;
}

.control-label {
  text-align: right;
  margin-bottom: 0;
  padding-top: 7px;
  font-weight: bold;
}

.form-control {
  color: #555;
  box-sizing: border-box;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ccc;
  border-radius: 4px;
  width: 100%;
  height: 34px;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.42857;
  transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
  display: block;
  box-shadow: inset 0 1px 1px #00000013;
}

.form-control:focus {
  border-color: #66afe9;
  outline: 0;
  box-shadow: inset 0 1px 1px #00000013, 0 0 8px #66afe999;
}

.form-control[disabled], .form-control[readonly] {
  opacity: 1;
  background-color: #eee;
}

select.form-control {
  height: 34px;
  line-height: 34px;
}

select[multiple].form-control {
  height: auto;
}

.help-block {
  color: #737373;
  margin-top: 5px;
  margin-bottom: 10px;
  display: block;
}

.btn {
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  touch-action: manipulation;
  cursor: pointer;
  user-select: none;
  background-image: none;
  border: 1px solid #0000;
  border-radius: 4px;
  margin-bottom: 0;
  padding: 6px 12px;
  font-size: 14px;
  font-weight: normal;
  line-height: 1.42857;
  text-decoration: none;
  display: inline-block;
}

.btn:focus, .btn:active:focus {
  outline: thin dotted;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}

.btn:hover, .btn:focus {
  color: #333;
  text-decoration: none;
}

.btn-primary {
  color: #fff;
  background-color: #375dbc;
  border-color: #375dbc;
}

.btn-primary:hover, .btn-primary:focus {
  color: #fff;
  background-color: #2a6496;
  border-color: #2a6496;
}

.btn-success {
  color: #fff;
  background-color: #5cb85c;
  border-color: #4cae4c;
}

.btn-success:hover, .btn-success:focus {
  color: #fff;
  background-color: #449d44;
  border-color: #398439;
}

.btn-info {
  color: #fff;
  background-color: #5bc0de;
  border-color: #46b8da;
}

.btn-info:hover, .btn-info:focus {
  color: #fff;
  background-color: #31b0d5;
  border-color: #269abc;
}

.btn-warning {
  color: #fff;
  background-color: #f0ad4e;
  border-color: #eea236;
}

.btn-warning:hover, .btn-warning:focus {
  color: #fff;
  background-color: #ec971f;
  border-color: #d58512;
}

.btn-danger {
  color: #fff;
  background-color: #d9534f;
  border-color: #d43f3a;
}

.btn-danger:hover, .btn-danger:focus {
  color: #fff;
  background-color: #c9302c;
  border-color: #ac2925;
}

.btn-default {
  color: #333;
  background-color: #fff;
  border-color: #ccc;
}

.btn-default:hover, .btn-default:focus {
  color: #333;
  background-color: #e6e6e6;
  border-color: #adadad;
}

.btn-xs {
  border-radius: 3px;
  padding: 1px 5px;
  font-size: 12px;
  line-height: 1.5;
}

.btn-sm {
  border-radius: 3px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
}

.datatable1 {
  width: 100% !important;
}

.page-title {
  color: #444;
  margin: 20px 0;
  font-size: 24px;
  font-weight: 400;
}

.admn {
  margin-bottom: 20px;
}

.btn-bitbucket {
  color: #fff;
  background-color: #205081;
  border-color: #205081;
}

.btn-bitbucket:hover, .btn-bitbucket:focus {
  color: #fff;
  background-color: #163758;
  border-color: #163758;
}

.float-right {
  float: right;
}

.box-default {
  margin-bottom: 15px;
}

textarea.form-control {
  resize: vertical;
  height: auto;
}

textarea[readonly] {
  cursor: not-allowed;
  background-color: #f5f5f5;
}

.img-responsive {
  max-width: 100%;
  height: auto;
}

.scroll-table {
  height: calc(80vh - 68px);
  overflow: hidden auto;
}

.scroll-table.scroll-table-two {
  height: calc(100vh - 68px) !important;
}

.scroll-table:hover {
  overflow: auto !important;
}

.scroll-table::-webkit-scrollbar {
  width: 8px;
}

.scroll-table::-webkit-scrollbar-track {
  border-radius: 6px;
  box-shadow: inset 0 0 5px gray;
}

.scroll-table::-webkit-scrollbar-thumb {
  background: #ededed;
  border-radius: 10px;
}

.scroll-table::-webkit-scrollbar-thumb:hover {
  background: #ededed;
}


/*# sourceMappingURL=src_app_globals_b805903d.css.map*/