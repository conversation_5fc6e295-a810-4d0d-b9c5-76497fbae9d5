{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/src/components/layout/AdminLTELayout.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { useRouter, usePathname } from \"next/navigation\";\n\ninterface AdminLTELayoutProps {\n  children: React.ReactNode;\n  title?: string;\n}\n\nexport default function AdminLTELayout({\n  children,\n  title,\n}: AdminLTELayoutProps) {\n  const router = useRouter();\n  const pathname = usePathname();\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const [openTreeviews, setOpenTreeviews] = useState<string[]>([]);\n\n  const handleSidebarToggle = () => {\n    setSidebarCollapsed(!sidebarCollapsed);\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem(\"auth_token\");\n    localStorage.removeItem(\"user_role\");\n    router.push(\"/login\");\n  };\n\n  const isActive = (path: string) => {\n    return pathname === path;\n  };\n\n  const toggleTreeview = (id: string) => {\n    setOpenTreeviews((prev) =>\n      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]\n    );\n  };\n\n  const isTreeviewOpen = (id: string) => {\n    return openTreeviews.includes(id);\n  };\n\n  // Initialize treeview states based on current path\n  useEffect(() => {\n    if (\n      pathname.includes(\"/admin/permissions\") ||\n      pathname.includes(\"/admin/roles\") ||\n      pathname.includes(\"/admin/users\")\n    ) {\n      setOpenTreeviews((prev) => [...prev, \"user-management\"]);\n    }\n    if (\n      pathname.includes(\"/admin/classes\") ||\n      pathname.includes(\"/admin/subjects\")\n    ) {\n      setOpenTreeviews((prev) => [...prev, \"course-management\"]);\n    }\n    if (\n      pathname.includes(\"/admin/categories\") ||\n      pathname.includes(\"/admin/courses\")\n    ) {\n      setOpenTreeviews((prev) => [...prev, \"course-category\"]);\n    }\n    if (\n      pathname.includes(\"/admin/about-us\") ||\n      pathname.includes(\"/admin/privacy-policy\") ||\n      pathname.includes(\"/admin/terms-condition\")\n    ) {\n      setOpenTreeviews((prev) => [...prev, \"cms\"]);\n    }\n  }, [pathname]);\n\n  return (\n    <div\n      className={`wrapper skin-blue ${\n        sidebarCollapsed ? \"sidebar-collapse\" : \"\"\n      }`}\n    >\n      {/* Main Header */}\n      <header className=\"main-header\">\n        {/* Logo */}\n        <a\n          href=\"#\"\n          className=\"logo\"\n          style={{ fontSize: \"16px\", color: \"#fff\", textDecoration: \"none\" }}\n        >\n          <b>Admin</b>LTE\n        </a>\n\n        {/* Header Navbar */}\n        <nav\n          className=\"navbar navbar-static-top\"\n          style={{ display: \"block\", padding: 0 }}\n        >\n          {/* Sidebar toggle button */}\n          <a\n            href=\"#\"\n            className=\"sidebar-toggle\"\n            onClick={(e) => {\n              e.preventDefault();\n              handleSidebarToggle();\n            }}\n            role=\"button\"\n          >\n            <span className=\"sr-only\">Toggle navigation</span>\n            <span className=\"icon-bar\"></span>\n            <span className=\"icon-bar\"></span>\n            <span className=\"icon-bar\"></span>\n          </a>\n        </nav>\n      </header>\n\n      {/* Left side column. contains the sidebar */}\n      <aside className=\"main-sidebar\">\n        {/* sidebar: style can be found in sidebar.less */}\n        <section className=\"sidebar\">\n          <ul className=\"sidebar-menu scroll-table scroll-table-two\">\n            {/* Dashboard */}\n            <li className={isActive(\"/admin/dashboard\") ? \"active\" : \"\"}>\n              <a href=\"/admin/dashboard\">\n                <i className=\"fa fa-dashboard\"></i>\n                <span className=\"title\">Dashboard</span>\n              </a>\n            </li>\n\n            {/* User Management */}\n            <li\n              className={`treeview ${\n                isTreeviewOpen(\"user-management\") ? \"active menu-open\" : \"\"\n              }`}\n            >\n              <a\n                href=\"#\"\n                onClick={(e) => {\n                  e.preventDefault();\n                  toggleTreeview(\"user-management\");\n                }}\n              >\n                <i className=\"fa fa-users\"></i>\n                <span className=\"title\">User Management</span>\n                <span className=\"pull-right-container\">\n                  <i\n                    className={`fa fa-angle-left pull-right ${\n                      isTreeviewOpen(\"user-management\") ? \"fa-angle-down\" : \"\"\n                    }`}\n                  ></i>\n                </span>\n              </a>\n              <ul\n                className=\"treeview-menu\"\n                style={{\n                  display: isTreeviewOpen(\"user-management\") ? \"block\" : \"none\",\n                }}\n              >\n                <li\n                  className={\n                    isActive(\"/admin/permissions\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/permissions\">\n                    <i className=\"fa fa-briefcase\"></i>\n                    <span className=\"title\">Permissions</span>\n                  </a>\n                </li>\n                <li\n                  className={\n                    isActive(\"/admin/roles\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/roles\">\n                    <i className=\"fa fa-briefcase\"></i>\n                    <span className=\"title\">Roles</span>\n                  </a>\n                </li>\n                <li\n                  className={\n                    isActive(\"/admin/users\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/users\">\n                    <i className=\"fa fa-user\"></i>\n                    <span className=\"title\">Administrator Users</span>\n                  </a>\n                </li>\n              </ul>\n            </li>\n\n            {/* Fees Update */}\n            <li className={isActive(\"/admin/fees\") ? \"active active-sub\" : \"\"}>\n              <a href=\"/admin/fees\">\n                <i className=\"fa fa-briefcase\"></i>\n                <span className=\"title\">Fees Update</span>\n              </a>\n            </li>\n\n            {/* Teacher Management */}\n            <li\n              className={isActive(\"/admin/teachers\") ? \"active active-sub\" : \"\"}\n            >\n              <a href=\"/admin/teachers\">\n                <i className=\"fa fa-user\"></i>\n                <span className=\"title\">Teacher Management</span>\n              </a>\n            </li>\n\n            {/* Student Management */}\n            <li\n              className={isActive(\"/admin/students\") ? \"active active-sub\" : \"\"}\n            >\n              <a href=\"/admin/students\">\n                <i className=\"fa fa-user\"></i>\n                <span className=\"title\">Student Management</span>\n              </a>\n            </li>\n\n            {/* Course Management */}\n            <li className=\"treeview\">\n              <a href=\"#\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Course Management</span>\n                <span className=\"pull-right-container\">\n                  <i className=\"fa fa-angle-left pull-right\"></i>\n                </span>\n              </a>\n              <ul className=\"treeview-menu\">\n                <li\n                  className={\n                    isActive(\"/admin/classes\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/classes\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">Class Details</span>\n                  </a>\n                </li>\n                <li\n                  className={\n                    isActive(\"/admin/subjects\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/subjects\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">Subjects Details</span>\n                  </a>\n                </li>\n              </ul>\n            </li>\n\n            {/* Course & Category */}\n            <li className=\"treeview\">\n              <a href=\"#\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Course & Category</span>\n                <span className=\"pull-right-container\">\n                  <i className=\"fa fa-angle-left pull-right\"></i>\n                </span>\n              </a>\n              <ul className=\"treeview-menu\">\n                <li\n                  className={\n                    isActive(\"/admin/categories\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/categories\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">Category Details</span>\n                  </a>\n                </li>\n                <li\n                  className={\n                    isActive(\"/admin/courses\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/courses\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">Course Details</span>\n                  </a>\n                </li>\n              </ul>\n            </li>\n\n            {/* Demo Request */}\n            <li\n              className={\n                isActive(\"/admin/demo-requests\") ? \"active active-sub\" : \"\"\n              }\n            >\n              <a href=\"/admin/demo-requests\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Demo Request</span>\n              </a>\n            </li>\n\n            {/* Withdrawal */}\n            <li\n              className={\n                isActive(\"/admin/withdrawal\") ? \"active active-sub\" : \"\"\n              }\n            >\n              <a href=\"/admin/withdrawal\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Withdrawal</span>\n              </a>\n            </li>\n\n            {/* Incomplete Classes */}\n            <li\n              className={\n                isActive(\"/admin/incomplete-classes\") ? \"active active-sub\" : \"\"\n              }\n            >\n              <a href=\"/admin/incomplete-classes\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Incomplete Classes</span>\n              </a>\n            </li>\n\n            {/* Student Purchase Class */}\n            <li\n              className={\n                isActive(\"/admin/student-purchase-class\")\n                  ? \"active active-sub\"\n                  : \"\"\n              }\n            >\n              <a href=\"/admin/student-purchase-class\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Student Purchase Class</span>\n              </a>\n            </li>\n\n            {/* Offers */}\n            <li\n              className={isActive(\"/admin/offers\") ? \"active active-sub\" : \"\"}\n            >\n              <a href=\"/admin/offers\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Offers Details</span>\n              </a>\n            </li>\n\n            {/* CMS */}\n            <li className=\"treeview\">\n              <a href=\"#\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">CMS</span>\n                <span className=\"pull-right-container\">\n                  <i className=\"fa fa-angle-left pull-right\"></i>\n                </span>\n              </a>\n              <ul className=\"treeview-menu\">\n                <li\n                  className={\n                    isActive(\"/admin/about-us\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/about-us\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">About Us</span>\n                  </a>\n                </li>\n                <li\n                  className={\n                    isActive(\"/admin/privacy-policy\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/privacy-policy\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">Privacy Policy</span>\n                  </a>\n                </li>\n                <li\n                  className={\n                    isActive(\"/admin/terms-condition\")\n                      ? \"active active-sub\"\n                      : \"\"\n                  }\n                >\n                  <a href=\"/admin/terms-condition\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">Terms & Condition</span>\n                  </a>\n                </li>\n              </ul>\n            </li>\n          </ul>\n        </section>\n      </aside>\n\n      {/* Content Wrapper. Contains page content */}\n      <div className=\"content-wrapper\">\n        {/* Main content */}\n        <section className=\"content\">\n          {title && (\n            <h3\n              className=\"page-title\"\n              style={{\n                margin: \"20px 0\",\n                color: \"#444444\",\n                fontSize: \"24px\",\n                fontWeight: 400,\n              }}\n            >\n              {title}\n            </h3>\n          )}\n\n          <div className=\"row\">\n            <div className=\"col-md-12\">{children}</div>\n          </div>\n        </section>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAUe,SAAS,eAAe,EACrC,QAAQ,EACR,KAAK,EACe;;IACpB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,MAAM,sBAAsB;QAC1B,oBAAoB,CAAC;IACvB;IAEA,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,WAAW,CAAC;QAChB,OAAO,aAAa;IACtB;IAEA,MAAM,iBAAiB,CAAC;QACtB,iBAAiB,CAAC,OAChB,KAAK,QAAQ,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,OAAS,SAAS,MAAM;mBAAI;gBAAM;aAAG;IAE1E;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,cAAc,QAAQ,CAAC;IAChC;IAEA,mDAAmD;IACnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IACE,SAAS,QAAQ,CAAC,yBAClB,SAAS,QAAQ,CAAC,mBAClB,SAAS,QAAQ,CAAC,iBAClB;gBACA;gDAAiB,CAAC,OAAS;+BAAI;4BAAM;yBAAkB;;YACzD;YACA,IACE,SAAS,QAAQ,CAAC,qBAClB,SAAS,QAAQ,CAAC,oBAClB;gBACA;gDAAiB,CAAC,OAAS;+BAAI;4BAAM;yBAAoB;;YAC3D;YACA,IACE,SAAS,QAAQ,CAAC,wBAClB,SAAS,QAAQ,CAAC,mBAClB;gBACA;gDAAiB,CAAC,OAAS;+BAAI;4BAAM;yBAAkB;;YACzD;YACA,IACE,SAAS,QAAQ,CAAC,sBAClB,SAAS,QAAQ,CAAC,4BAClB,SAAS,QAAQ,CAAC,2BAClB;gBACA;gDAAiB,CAAC,OAAS;+BAAI;4BAAM;yBAAM;;YAC7C;QACF;mCAAG;QAAC;KAAS;IAEb,qBACE,6LAAC;QACC,WAAW,CAAC,kBAAkB,EAC5B,mBAAmB,qBAAqB,IACxC;;0BAGF,6LAAC;gBAAO,WAAU;;kCAEhB,6LAAC;wBACC,MAAK;wBACL,WAAU;wBACV,OAAO;4BAAE,UAAU;4BAAQ,OAAO;4BAAQ,gBAAgB;wBAAO;;0CAEjE,6LAAC;0CAAE;;;;;;4BAAS;;;;;;;kCAId,6LAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,SAAS;4BAAS,SAAS;wBAAE;kCAGtC,cAAA,6LAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS,CAAC;gCACR,EAAE,cAAc;gCAChB;4BACF;4BACA,MAAK;;8CAEL,6LAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMtB,6LAAC;gBAAM,WAAU;0BAEf,cAAA,6LAAC;oBAAQ,WAAU;8BACjB,cAAA,6LAAC;wBAAG,WAAU;;0CAEZ,6LAAC;gCAAG,WAAW,SAAS,sBAAsB,WAAW;0CACvD,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCACC,WAAW,CAAC,SAAS,EACnB,eAAe,qBAAqB,qBAAqB,IACzD;;kDAEF,6LAAC;wCACC,MAAK;wCACL,SAAS,CAAC;4CACR,EAAE,cAAc;4CAChB,eAAe;wCACjB;;0DAEA,6LAAC;gDAAE,WAAU;;;;;;0DACb,6LAAC;gDAAK,WAAU;0DAAQ;;;;;;0DACxB,6LAAC;gDAAK,WAAU;0DACd,cAAA,6LAAC;oDACC,WAAW,CAAC,4BAA4B,EACtC,eAAe,qBAAqB,kBAAkB,IACtD;;;;;;;;;;;;;;;;;kDAIR,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,SAAS,eAAe,qBAAqB,UAAU;wCACzD;;0DAEA,6LAAC;gDACC,WACE,SAAS,wBAAwB,sBAAsB;0DAGzD,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;0DAG5B,6LAAC;gDACC,WACE,SAAS,kBAAkB,sBAAsB;0DAGnD,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;0DAG5B,6LAAC;gDACC,WACE,SAAS,kBAAkB,sBAAsB;0DAGnD,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhC,6LAAC;gCAAG,WAAW,SAAS,iBAAiB,sBAAsB;0CAC7D,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCACC,WAAW,SAAS,qBAAqB,sBAAsB;0CAE/D,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCACC,WAAW,SAAS,qBAAqB,sBAAsB;0CAE/D,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAE,MAAK;;0DACN,6LAAC;gDAAE,WAAU;;;;;;0DACb,6LAAC;gDAAK,WAAU;0DAAQ;;;;;;0DACxB,6LAAC;gDAAK,WAAU;0DACd,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;;;;;;;kDAGjB,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDACC,WACE,SAAS,oBAAoB,sBAAsB;0DAGrD,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;0DAG5B,6LAAC;gDACC,WACE,SAAS,qBAAqB,sBAAsB;0DAGtD,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhC,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAE,MAAK;;0DACN,6LAAC;gDAAE,WAAU;;;;;;0DACb,6LAAC;gDAAK,WAAU;0DAAQ;;;;;;0DACxB,6LAAC;gDAAK,WAAU;0DACd,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;;;;;;;kDAGjB,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDACC,WACE,SAAS,uBAAuB,sBAAsB;0DAGxD,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;0DAG5B,6LAAC;gDACC,WACE,SAAS,oBAAoB,sBAAsB;0DAGrD,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhC,6LAAC;gCACC,WACE,SAAS,0BAA0B,sBAAsB;0CAG3D,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCACC,WACE,SAAS,uBAAuB,sBAAsB;0CAGxD,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCACC,WACE,SAAS,+BAA+B,sBAAsB;0CAGhE,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCACC,WACE,SAAS,mCACL,sBACA;0CAGN,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCACC,WAAW,SAAS,mBAAmB,sBAAsB;0CAE7D,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAE,MAAK;;0DACN,6LAAC;gDAAE,WAAU;;;;;;0DACb,6LAAC;gDAAK,WAAU;0DAAQ;;;;;;0DACxB,6LAAC;gDAAK,WAAU;0DACd,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;;;;;;;kDAGjB,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDACC,WACE,SAAS,qBAAqB,sBAAsB;0DAGtD,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;0DAG5B,6LAAC;gDACC,WACE,SAAS,2BAA2B,sBAAsB;0DAG5D,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;0DAG5B,6LAAC;gDACC,WACE,SAAS,4BACL,sBACA;0DAGN,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUtC,6LAAC;gBAAI,WAAU;0BAEb,cAAA,6LAAC;oBAAQ,WAAU;;wBAChB,uBACC,6LAAC;4BACC,WAAU;4BACV,OAAO;gCACL,QAAQ;gCACR,OAAO;gCACP,UAAU;gCACV,YAAY;4BACd;sCAEC;;;;;;sCAIL,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxC;GArZwB;;QAIP,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KALN", "debugId": null}}, {"offset": {"line": 1086, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/src/app/admin/categories/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport AdminLTELayout from '@/components/layout/AdminLTELayout';\nimport { categories } from '@/data/dummyData';\n\n// Extended categories data with class names\nconst categoriesData = [\n  {\n    id: 1,\n    name: 'Mathematics',\n    description: 'Advanced mathematics courses',\n    class_name: 'Grade 10-12',\n    image: null,\n    status: 1,\n    created_at: '2024-01-15T10:00:00Z',\n    sort_id: 1\n  },\n  {\n    id: 2,\n    name: 'Science',\n    description: 'Physics, Chemistry, Biology courses',\n    class_name: 'Grade 9-12',\n    image: null,\n    status: 1,\n    created_at: '2024-01-16T10:00:00Z',\n    sort_id: 2\n  },\n  {\n    id: 3,\n    name: 'English',\n    description: 'English language and literature',\n    class_name: 'Grade 6-12',\n    image: null,\n    status: 1,\n    created_at: '2024-01-17T10:00:00Z',\n    sort_id: 3\n  },\n  {\n    id: 4,\n    name: 'Computer Science',\n    description: 'Programming and computer science',\n    class_name: 'Grade 11-12',\n    image: null,\n    status: 0,\n    created_at: '2024-01-18T10:00:00Z',\n    sort_id: 4\n  },\n  {\n    id: 5,\n    name: 'History',\n    description: 'World and local history courses',\n    class_name: 'Grade 8-12',\n    image: null,\n    status: 1,\n    created_at: '2024-01-19T10:00:00Z',\n    sort_id: 5\n  }\n];\n\nexport default function CategoriesPage() {\n  const [categoryList, setCategoryList] = useState(categoriesData);\n\n  const handleStatusToggle = (categoryId: number, currentStatus: number) => {\n    const newStatus = currentStatus === 1 ? 2 : 1;\n    setCategoryList(prev => \n      prev.map(category => \n        category.id === categoryId ? { ...category, status: newStatus } : category\n      )\n    );\n  };\n\n  const handleDelete = (categoryId: number) => {\n    if (confirm('Are you sure you want to delete this?')) {\n      setCategoryList(prev => prev.filter(category => category.id !== categoryId));\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-GB', {\n      day: '2-digit',\n      month: 'short',\n      year: '2-digit'\n    });\n  };\n\n  return (\n    <AdminLTELayout>\n      <div className=\"row\">\n        <div className=\"col-md-12\">\n          <div className=\"panel panel-default\">\n            <div style={{ fontSize: '20px' }} className=\"panel-heading\">All Category</div>\n            <div className=\"panel-body\">\n              <div id=\"message\"></div>\n              \n              <div className=\"row\">\n                <div className=\"col-md-12\">\n                  <div className=\"col-md-6 text-right\">\n                    \n                  </div>\n                  <div className=\"col-md-6\">\n                    <div className=\"box-default text-right\">\n                      <a className=\"btn btn-bitbucket float-right\" href=\"/admin/categories/create\">Add New Category</a>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            \n            <div className=\"panel-body table-responsive scroll-table\">\n              <table className={`table table-bordered table-striped dt-select ${categoryList.length > 0 ? 'datatable1' : ''}`} id=\"category1\">\n                <thead>\n                  <tr>\n                    <th>Sr.No</th>\n                    <th>Class Name</th>\n                    <th>Name</th>\n                    <th>Category Image</th>\n                    <th>Status</th>\n                    <th>Created At</th>\n                    <th>Action</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {categoryList.length > 0 ? (\n                    categoryList.map((category, index) => (\n                      <tr key={category.id}>\n                        <td>{index + 1}</td>\n                        <td>{category.class_name}</td>\n                        <td>{category.name}</td>\n                        <td>\n                          {category.image ? (\n                            <img \n                              className=\"img-responsive\" \n                              src={`/public/category/${category.image}`} \n                              width=\"80\" \n                              height=\"50\"\n                              style={{ height: '90px', objectFit: 'cover' }}\n                            />\n                          ) : (\n                            'No image found'\n                          )}\n                        </td>\n                        <td>\n                          {category.status === 1 ? (\n                            <a \n                              href=\"#\" \n                              className=\"label label-success\"\n                              onClick={(e) => {\n                                e.preventDefault();\n                                handleStatusToggle(category.id, category.status);\n                              }}\n                            >\n                              Active\n                            </a>\n                          ) : (\n                            <a \n                              href=\"#\" \n                              className=\"label label-danger\"\n                              onClick={(e) => {\n                                e.preventDefault();\n                                handleStatusToggle(category.id, category.status);\n                              }}\n                            >\n                              Inactive\n                            </a>\n                          )}\n                        </td>\n                        <td>{formatDate(category.created_at)}</td>\n                        <td>\n                          <a href={`/admin/categories/edit/${category.id}`} className=\"btn btn-primary btn-sm\">\n                            Edit\n                          </a>\n                          {' '}\n                          <button \n                            className=\"btn btn-primary btn-sm\"\n                            onClick={() => handleDelete(category.id)}\n                          >\n                            Delete\n                          </button>\n                        </td>\n                      </tr>\n                    ))\n                  ) : (\n                    <tr>\n                      <td colSpan={7}>No entries in table</td>\n                    </tr>\n                  )}\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </AdminLTELayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAMA,4CAA4C;AAC5C,MAAM,iBAAiB;IACrB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,YAAY;QACZ,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,YAAY;QACZ,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,YAAY;QACZ,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,YAAY;QACZ,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,YAAY;QACZ,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,SAAS;IACX;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,qBAAqB,CAAC,YAAoB;QAC9C,MAAM,YAAY,kBAAkB,IAAI,IAAI;QAC5C,gBAAgB,CAAA,OACd,KAAK,GAAG,CAAC,CAAA,WACP,SAAS,EAAE,KAAK,aAAa;oBAAE,GAAG,QAAQ;oBAAE,QAAQ;gBAAU,IAAI;IAGxE;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,QAAQ,0CAA0C;YACpD,gBAAgB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;QAClE;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,KAAK;YACL,OAAO;YACP,MAAM;QACR;IACF;IAEA,qBACE,6LAAC,iJAAA,CAAA,UAAc;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,OAAO;gCAAE,UAAU;4BAAO;4BAAG,WAAU;sCAAgB;;;;;;sCAC5D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,IAAG;;;;;;8CAER,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DAGf,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;wDAAgC,MAAK;kEAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOvF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAM,WAAW,CAAC,6CAA6C,EAAE,aAAa,MAAM,GAAG,IAAI,eAAe,IAAI;gCAAE,IAAG;;kDAClH,6LAAC;kDACC,cAAA,6LAAC;;8DACC,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;;;;;;kDAGR,6LAAC;kDACE,aAAa,MAAM,GAAG,IACrB,aAAa,GAAG,CAAC,CAAC,UAAU,sBAC1B,6LAAC;;kEACC,6LAAC;kEAAI,QAAQ;;;;;;kEACb,6LAAC;kEAAI,SAAS,UAAU;;;;;;kEACxB,6LAAC;kEAAI,SAAS,IAAI;;;;;;kEAClB,6LAAC;kEACE,SAAS,KAAK,iBACb,6LAAC;4DACC,WAAU;4DACV,KAAK,CAAC,iBAAiB,EAAE,SAAS,KAAK,EAAE;4DACzC,OAAM;4DACN,QAAO;4DACP,OAAO;gEAAE,QAAQ;gEAAQ,WAAW;4DAAQ;;;;;mEAG9C;;;;;;kEAGJ,6LAAC;kEACE,SAAS,MAAM,KAAK,kBACnB,6LAAC;4DACC,MAAK;4DACL,WAAU;4DACV,SAAS,CAAC;gEACR,EAAE,cAAc;gEAChB,mBAAmB,SAAS,EAAE,EAAE,SAAS,MAAM;4DACjD;sEACD;;;;;iFAID,6LAAC;4DACC,MAAK;4DACL,WAAU;4DACV,SAAS,CAAC;gEACR,EAAE,cAAc;gEAChB,mBAAmB,SAAS,EAAE,EAAE,SAAS,MAAM;4DACjD;sEACD;;;;;;;;;;;kEAKL,6LAAC;kEAAI,WAAW,SAAS,UAAU;;;;;;kEACnC,6LAAC;;0EACC,6LAAC;gEAAE,MAAM,CAAC,uBAAuB,EAAE,SAAS,EAAE,EAAE;gEAAE,WAAU;0EAAyB;;;;;;4DAGpF;0EACD,6LAAC;gEACC,WAAU;gEACV,SAAS,IAAM,aAAa,SAAS,EAAE;0EACxC;;;;;;;;;;;;;+CAnDI,SAAS,EAAE;;;;sEA0DtB,6LAAC;sDACC,cAAA,6LAAC;gDAAG,SAAS;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtC;GAxIwB;KAAA", "debugId": null}}, {"offset": {"line": 1501, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}