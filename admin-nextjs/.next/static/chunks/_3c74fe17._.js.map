{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/src/components/layout/AdminLTELayout.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { useRouter, usePathname } from \"next/navigation\";\n\ninterface AdminLTELayoutProps {\n  children: React.ReactNode;\n  title?: string;\n}\n\nexport default function AdminLTELayout({\n  children,\n  title,\n}: AdminLTELayoutProps) {\n  const router = useRouter();\n  const pathname = usePathname();\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const [openTreeviews, setOpenTreeviews] = useState<string[]>([]);\n\n  const handleSidebarToggle = () => {\n    setSidebarCollapsed(!sidebarCollapsed);\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem(\"auth_token\");\n    localStorage.removeItem(\"user_role\");\n    router.push(\"/login\");\n  };\n\n  const isActive = (path: string) => {\n    return pathname === path;\n  };\n\n  const toggleTreeview = (id: string) => {\n    setOpenTreeviews((prev) =>\n      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]\n    );\n  };\n\n  const isTreeviewOpen = (id: string) => {\n    return openTreeviews.includes(id);\n  };\n\n  // Initialize treeview states based on current path\n  useEffect(() => {\n    if (\n      pathname.includes(\"/admin/permissions\") ||\n      pathname.includes(\"/admin/roles\") ||\n      pathname.includes(\"/admin/users\")\n    ) {\n      setOpenTreeviews((prev) => [...prev, \"user-management\"]);\n    }\n    if (\n      pathname.includes(\"/admin/classes\") ||\n      pathname.includes(\"/admin/subjects\")\n    ) {\n      setOpenTreeviews((prev) => [...prev, \"course-management\"]);\n    }\n    if (\n      pathname.includes(\"/admin/categories\") ||\n      pathname.includes(\"/admin/courses\")\n    ) {\n      setOpenTreeviews((prev) => [...prev, \"course-category\"]);\n    }\n    if (\n      pathname.includes(\"/admin/about-us\") ||\n      pathname.includes(\"/admin/privacy-policy\") ||\n      pathname.includes(\"/admin/terms-condition\")\n    ) {\n      setOpenTreeviews((prev) => [...prev, \"cms\"]);\n    }\n  }, [pathname]);\n\n  return (\n    <div\n      className={`wrapper skin-blue ${\n        sidebarCollapsed ? \"sidebar-collapse\" : \"\"\n      }`}\n    >\n      {/* Main Header */}\n      <header className=\"main-header\">\n        {/* Logo */}\n        <a\n          href=\"#\"\n          className=\"logo\"\n          style={{ fontSize: \"16px\", color: \"#fff\", textDecoration: \"none\" }}\n        >\n          <b>Admin</b>LTE\n        </a>\n\n        {/* Header Navbar */}\n        <nav\n          className=\"navbar navbar-static-top\"\n          style={{ display: \"block\", padding: 0 }}\n        >\n          {/* Sidebar toggle button */}\n          <a\n            href=\"#\"\n            className=\"sidebar-toggle\"\n            onClick={(e) => {\n              e.preventDefault();\n              handleSidebarToggle();\n            }}\n            role=\"button\"\n          >\n            <span className=\"sr-only\">Toggle navigation</span>\n            <span className=\"icon-bar\"></span>\n            <span className=\"icon-bar\"></span>\n            <span className=\"icon-bar\"></span>\n          </a>\n        </nav>\n      </header>\n\n      {/* Left side column. contains the sidebar */}\n      <aside className=\"main-sidebar\">\n        {/* sidebar: style can be found in sidebar.less */}\n        <section className=\"sidebar\">\n          <ul className=\"sidebar-menu scroll-table scroll-table-two\">\n            {/* Dashboard */}\n            <li className={isActive(\"/admin/dashboard\") ? \"active\" : \"\"}>\n              <a href=\"/admin/dashboard\">\n                <i className=\"fa fa-dashboard\"></i>\n                <span className=\"title\">Dashboard</span>\n              </a>\n            </li>\n\n            {/* User Management */}\n            <li\n              className={`treeview ${\n                isTreeviewOpen(\"user-management\") ? \"active menu-open\" : \"\"\n              }`}\n            >\n              <a\n                href=\"#\"\n                onClick={(e) => {\n                  e.preventDefault();\n                  toggleTreeview(\"user-management\");\n                }}\n              >\n                <i className=\"fa fa-users\"></i>\n                <span className=\"title\">User Management</span>\n                <span className=\"pull-right-container\">\n                  <i\n                    className={`fa fa-angle-left pull-right ${\n                      isTreeviewOpen(\"user-management\") ? \"fa-angle-down\" : \"\"\n                    }`}\n                  ></i>\n                </span>\n              </a>\n              <ul\n                className=\"treeview-menu\"\n                style={{\n                  display: isTreeviewOpen(\"user-management\") ? \"block\" : \"none\",\n                }}\n              >\n                <li\n                  className={\n                    isActive(\"/admin/permissions\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/permissions\">\n                    <i className=\"fa fa-briefcase\"></i>\n                    <span className=\"title\">Permissions</span>\n                  </a>\n                </li>\n                <li\n                  className={\n                    isActive(\"/admin/roles\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/roles\">\n                    <i className=\"fa fa-briefcase\"></i>\n                    <span className=\"title\">Roles</span>\n                  </a>\n                </li>\n                <li\n                  className={\n                    isActive(\"/admin/users\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/users\">\n                    <i className=\"fa fa-user\"></i>\n                    <span className=\"title\">Administrator Users</span>\n                  </a>\n                </li>\n              </ul>\n            </li>\n\n            {/* Fees Update */}\n            <li className={isActive(\"/admin/fees\") ? \"active active-sub\" : \"\"}>\n              <a href=\"/admin/fees\">\n                <i className=\"fa fa-briefcase\"></i>\n                <span className=\"title\">Fees Update</span>\n              </a>\n            </li>\n\n            {/* Teacher Management */}\n            <li\n              className={isActive(\"/admin/teachers\") ? \"active active-sub\" : \"\"}\n            >\n              <a href=\"/admin/teachers\">\n                <i className=\"fa fa-user\"></i>\n                <span className=\"title\">Teacher Management</span>\n              </a>\n            </li>\n\n            {/* Student Management */}\n            <li\n              className={isActive(\"/admin/students\") ? \"active active-sub\" : \"\"}\n            >\n              <a href=\"/admin/students\">\n                <i className=\"fa fa-user\"></i>\n                <span className=\"title\">Student Management</span>\n              </a>\n            </li>\n\n            {/* Course Management */}\n            <li className=\"treeview\">\n              <a href=\"#\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Course Management</span>\n                <span className=\"pull-right-container\">\n                  <i className=\"fa fa-angle-left pull-right\"></i>\n                </span>\n              </a>\n              <ul className=\"treeview-menu\">\n                <li\n                  className={\n                    isActive(\"/admin/classes\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/classes\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">Class Details</span>\n                  </a>\n                </li>\n                <li\n                  className={\n                    isActive(\"/admin/subjects\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/subjects\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">Subjects Details</span>\n                  </a>\n                </li>\n              </ul>\n            </li>\n\n            {/* Course & Category */}\n            <li className=\"treeview\">\n              <a href=\"#\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Course & Category</span>\n                <span className=\"pull-right-container\">\n                  <i className=\"fa fa-angle-left pull-right\"></i>\n                </span>\n              </a>\n              <ul className=\"treeview-menu\">\n                <li\n                  className={\n                    isActive(\"/admin/categories\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/categories\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">Category Details</span>\n                  </a>\n                </li>\n                <li\n                  className={\n                    isActive(\"/admin/courses\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/courses\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">Course Details</span>\n                  </a>\n                </li>\n              </ul>\n            </li>\n\n            {/* Demo Request */}\n            <li\n              className={\n                isActive(\"/admin/demo-requests\") ? \"active active-sub\" : \"\"\n              }\n            >\n              <a href=\"/admin/demo-requests\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Demo Request</span>\n              </a>\n            </li>\n\n            {/* Withdrawal */}\n            <li\n              className={\n                isActive(\"/admin/withdrawal\") ? \"active active-sub\" : \"\"\n              }\n            >\n              <a href=\"/admin/withdrawal\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Withdrawal</span>\n              </a>\n            </li>\n\n            {/* Incomplete Classes */}\n            <li\n              className={\n                isActive(\"/admin/incomplete-classes\") ? \"active active-sub\" : \"\"\n              }\n            >\n              <a href=\"/admin/incomplete-classes\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Incomplete Classes</span>\n              </a>\n            </li>\n\n            {/* Student Purchase Class */}\n            <li\n              className={\n                isActive(\"/admin/student-purchase-class\")\n                  ? \"active active-sub\"\n                  : \"\"\n              }\n            >\n              <a href=\"/admin/student-purchase-class\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Student Purchase Class</span>\n              </a>\n            </li>\n\n            {/* Offers */}\n            <li\n              className={isActive(\"/admin/offers\") ? \"active active-sub\" : \"\"}\n            >\n              <a href=\"/admin/offers\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Offers Details</span>\n              </a>\n            </li>\n\n            {/* CMS */}\n            <li className=\"treeview\">\n              <a href=\"#\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">CMS</span>\n                <span className=\"pull-right-container\">\n                  <i className=\"fa fa-angle-left pull-right\"></i>\n                </span>\n              </a>\n              <ul className=\"treeview-menu\">\n                <li\n                  className={\n                    isActive(\"/admin/about-us\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/about-us\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">About Us</span>\n                  </a>\n                </li>\n                <li\n                  className={\n                    isActive(\"/admin/privacy-policy\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/privacy-policy\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">Privacy Policy</span>\n                  </a>\n                </li>\n                <li\n                  className={\n                    isActive(\"/admin/terms-condition\")\n                      ? \"active active-sub\"\n                      : \"\"\n                  }\n                >\n                  <a href=\"/admin/terms-condition\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">Terms & Condition</span>\n                  </a>\n                </li>\n              </ul>\n            </li>\n          </ul>\n        </section>\n      </aside>\n\n      {/* Content Wrapper. Contains page content */}\n      <div className=\"content-wrapper\">\n        {/* Main content */}\n        <section className=\"content\">\n          {title && (\n            <h3\n              className=\"page-title\"\n              style={{\n                margin: \"20px 0\",\n                color: \"#444444\",\n                fontSize: \"24px\",\n                fontWeight: 400,\n              }}\n            >\n              {title}\n            </h3>\n          )}\n\n          <div className=\"row\">\n            <div className=\"col-md-12\">{children}</div>\n          </div>\n        </section>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAUe,SAAS,eAAe,EACrC,QAAQ,EACR,KAAK,EACe;;IACpB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,MAAM,sBAAsB;QAC1B,oBAAoB,CAAC;IACvB;IAEA,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,WAAW,CAAC;QAChB,OAAO,aAAa;IACtB;IAEA,MAAM,iBAAiB,CAAC;QACtB,iBAAiB,CAAC,OAChB,KAAK,QAAQ,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,OAAS,SAAS,MAAM;mBAAI;gBAAM;aAAG;IAE1E;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,cAAc,QAAQ,CAAC;IAChC;IAEA,mDAAmD;IACnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IACE,SAAS,QAAQ,CAAC,yBAClB,SAAS,QAAQ,CAAC,mBAClB,SAAS,QAAQ,CAAC,iBAClB;gBACA;gDAAiB,CAAC,OAAS;+BAAI;4BAAM;yBAAkB;;YACzD;YACA,IACE,SAAS,QAAQ,CAAC,qBAClB,SAAS,QAAQ,CAAC,oBAClB;gBACA;gDAAiB,CAAC,OAAS;+BAAI;4BAAM;yBAAoB;;YAC3D;YACA,IACE,SAAS,QAAQ,CAAC,wBAClB,SAAS,QAAQ,CAAC,mBAClB;gBACA;gDAAiB,CAAC,OAAS;+BAAI;4BAAM;yBAAkB;;YACzD;YACA,IACE,SAAS,QAAQ,CAAC,sBAClB,SAAS,QAAQ,CAAC,4BAClB,SAAS,QAAQ,CAAC,2BAClB;gBACA;gDAAiB,CAAC,OAAS;+BAAI;4BAAM;yBAAM;;YAC7C;QACF;mCAAG;QAAC;KAAS;IAEb,qBACE,6LAAC;QACC,WAAW,CAAC,kBAAkB,EAC5B,mBAAmB,qBAAqB,IACxC;;0BAGF,6LAAC;gBAAO,WAAU;;kCAEhB,6LAAC;wBACC,MAAK;wBACL,WAAU;wBACV,OAAO;4BAAE,UAAU;4BAAQ,OAAO;4BAAQ,gBAAgB;wBAAO;;0CAEjE,6LAAC;0CAAE;;;;;;4BAAS;;;;;;;kCAId,6LAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,SAAS;4BAAS,SAAS;wBAAE;kCAGtC,cAAA,6LAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS,CAAC;gCACR,EAAE,cAAc;gCAChB;4BACF;4BACA,MAAK;;8CAEL,6LAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMtB,6LAAC;gBAAM,WAAU;0BAEf,cAAA,6LAAC;oBAAQ,WAAU;8BACjB,cAAA,6LAAC;wBAAG,WAAU;;0CAEZ,6LAAC;gCAAG,WAAW,SAAS,sBAAsB,WAAW;0CACvD,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCACC,WAAW,CAAC,SAAS,EACnB,eAAe,qBAAqB,qBAAqB,IACzD;;kDAEF,6LAAC;wCACC,MAAK;wCACL,SAAS,CAAC;4CACR,EAAE,cAAc;4CAChB,eAAe;wCACjB;;0DAEA,6LAAC;gDAAE,WAAU;;;;;;0DACb,6LAAC;gDAAK,WAAU;0DAAQ;;;;;;0DACxB,6LAAC;gDAAK,WAAU;0DACd,cAAA,6LAAC;oDACC,WAAW,CAAC,4BAA4B,EACtC,eAAe,qBAAqB,kBAAkB,IACtD;;;;;;;;;;;;;;;;;kDAIR,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,SAAS,eAAe,qBAAqB,UAAU;wCACzD;;0DAEA,6LAAC;gDACC,WACE,SAAS,wBAAwB,sBAAsB;0DAGzD,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;0DAG5B,6LAAC;gDACC,WACE,SAAS,kBAAkB,sBAAsB;0DAGnD,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;0DAG5B,6LAAC;gDACC,WACE,SAAS,kBAAkB,sBAAsB;0DAGnD,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhC,6LAAC;gCAAG,WAAW,SAAS,iBAAiB,sBAAsB;0CAC7D,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCACC,WAAW,SAAS,qBAAqB,sBAAsB;0CAE/D,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCACC,WAAW,SAAS,qBAAqB,sBAAsB;0CAE/D,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAE,MAAK;;0DACN,6LAAC;gDAAE,WAAU;;;;;;0DACb,6LAAC;gDAAK,WAAU;0DAAQ;;;;;;0DACxB,6LAAC;gDAAK,WAAU;0DACd,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;;;;;;;kDAGjB,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDACC,WACE,SAAS,oBAAoB,sBAAsB;0DAGrD,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;0DAG5B,6LAAC;gDACC,WACE,SAAS,qBAAqB,sBAAsB;0DAGtD,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhC,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAE,MAAK;;0DACN,6LAAC;gDAAE,WAAU;;;;;;0DACb,6LAAC;gDAAK,WAAU;0DAAQ;;;;;;0DACxB,6LAAC;gDAAK,WAAU;0DACd,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;;;;;;;kDAGjB,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDACC,WACE,SAAS,uBAAuB,sBAAsB;0DAGxD,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;0DAG5B,6LAAC;gDACC,WACE,SAAS,oBAAoB,sBAAsB;0DAGrD,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhC,6LAAC;gCACC,WACE,SAAS,0BAA0B,sBAAsB;0CAG3D,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCACC,WACE,SAAS,uBAAuB,sBAAsB;0CAGxD,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCACC,WACE,SAAS,+BAA+B,sBAAsB;0CAGhE,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCACC,WACE,SAAS,mCACL,sBACA;0CAGN,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCACC,WAAW,SAAS,mBAAmB,sBAAsB;0CAE7D,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAE,MAAK;;0DACN,6LAAC;gDAAE,WAAU;;;;;;0DACb,6LAAC;gDAAK,WAAU;0DAAQ;;;;;;0DACxB,6LAAC;gDAAK,WAAU;0DACd,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;;;;;;;kDAGjB,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDACC,WACE,SAAS,qBAAqB,sBAAsB;0DAGtD,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;0DAG5B,6LAAC;gDACC,WACE,SAAS,2BAA2B,sBAAsB;0DAG5D,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;0DAG5B,6LAAC;gDACC,WACE,SAAS,4BACL,sBACA;0DAGN,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUtC,6LAAC;gBAAI,WAAU;0BAEb,cAAA,6LAAC;oBAAQ,WAAU;;wBAChB,uBACC,6LAAC;4BACC,WAAU;4BACV,OAAO;gCACL,QAAQ;gCACR,OAAO;gCACP,UAAU;gCACV,YAAY;4BACd;sCAEC;;;;;;sCAIL,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxC;GArZwB;;QAIP,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KALN", "debugId": null}}, {"offset": {"line": 1086, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/src/app/admin/users/create/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport AdminLTELayout from '@/components/layout/AdminLTELayout';\n\ninterface UserFormData {\n  name: string;\n  email: string;\n  phone: string;\n  password: string;\n  roles: string[];\n}\n\nconst availableRoles = [\n  { value: 'Admin', label: 'Admin' },\n  { value: 'Director', label: 'Director' },\n  { value: 'Course Builder', label: 'Course Builder' },\n];\n\nexport default function CreateUserPage() {\n  const router = useRouter();\n  const [formData, setFormData] = useState<UserFormData>({\n    name: '',\n    email: '',\n    phone: '',\n    password: '',\n    roles: []\n  });\n  const [errors, setErrors] = useState<Record<string, string>>({});\n  const [loading, setLoading] = useState(false);\n  const [showPassword, setShowPassword] = useState(false);\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    \n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n\n  const handleRoleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    const selectedOptions = Array.from(e.target.selectedOptions, option => option.value);\n    setFormData(prev => ({\n      ...prev,\n      roles: selectedOptions\n    }));\n  };\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.name.trim()) {\n      newErrors.name = 'Name is required';\n    }\n\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n\n    if (!formData.phone.trim()) {\n      newErrors.phone = 'Mobile is required';\n    } else if (formData.phone.length !== 10) {\n      newErrors.phone = 'Mobile must be 10 digits';\n    }\n\n    if (!formData.password.trim()) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n\n    if (formData.roles.length === 0) {\n      newErrors.roles = 'Please select at least one role';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    setLoading(true);\n\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      // In a real app, you would make an API call here\n      console.log('Creating user:', formData);\n      \n      // Redirect to users list\n      router.push('/admin/users');\n    } catch (error) {\n      console.error('Error creating user:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <AdminLTELayout>\n      <h3 className=\"page-title\">Create User</h3>\n\n      <div className=\"panel panel-default\">\n        <div className=\"panel-heading\">\n          Create new user\n        </div>\n\n        <div className=\"panel-body\">\n          <form onSubmit={handleSubmit}>\n            <div className=\"row\">\n              <div className=\"col-xs-12 form-group\">\n                <label htmlFor=\"name\" className=\"control-label\">Name*</label>\n                <input\n                  type=\"text\"\n                  name=\"name\"\n                  className=\"form-control\"\n                  placeholder=\"\"\n                  value={formData.name}\n                  onChange={handleChange}\n                  required\n                />\n                {errors.name && (\n                  <p className=\"help-block\" style={{ color: '#a94442' }}>\n                    {errors.name}\n                  </p>\n                )}\n              </div>\n            </div>\n\n            <div className=\"row\">\n              <div className=\"col-xs-12 form-group\">\n                <label htmlFor=\"email\" className=\"control-label\">Email*</label>\n                <input\n                  type=\"email\"\n                  name=\"email\"\n                  className=\"form-control\"\n                  placeholder=\"\"\n                  value={formData.email}\n                  onChange={handleChange}\n                  required\n                />\n                {errors.email && (\n                  <p className=\"help-block\" style={{ color: '#a94442' }}>\n                    {errors.email}\n                  </p>\n                )}\n              </div>\n            </div>\n\n            <div className=\"row\">\n              <div className=\"col-xs-12 form-group\">\n                <label htmlFor=\"phone\" className=\"control-label\">Mobile*</label>\n                <input\n                  type=\"text\"\n                  name=\"phone\"\n                  className=\"form-control\"\n                  placeholder=\"\"\n                  maxLength={10}\n                  value={formData.phone}\n                  onChange={handleChange}\n                  required\n                />\n                {errors.phone && (\n                  <p className=\"help-block\" style={{ color: '#a94442' }}>\n                    {errors.phone}\n                  </p>\n                )}\n              </div>\n            </div>\n\n            <div className=\"row\">\n              <div className=\"col-xs-12 form-group\">\n                <label htmlFor=\"password\" className=\"control-label\">Password*</label>\n                <div style={{ position: 'relative' }}>\n                  <input\n                    type={showPassword ? 'text' : 'password'}\n                    name=\"password\"\n                    className=\"form-control\"\n                    id=\"password-field\"\n                    placeholder=\"\"\n                    value={formData.password}\n                    onChange={handleChange}\n                    required\n                  />\n                  <span \n                    className={`fa fa-fw ${showPassword ? 'fa-eye-slash' : 'fa-eye'} field-icon toggle-password`}\n                    style={{\n                      position: 'absolute',\n                      right: '10px',\n                      top: '50%',\n                      transform: 'translateY(-50%)',\n                      cursor: 'pointer',\n                      color: '#999'\n                    }}\n                    onClick={() => setShowPassword(!showPassword)}\n                  ></span>\n                </div>\n                {errors.password && (\n                  <p className=\"help-block\" style={{ color: '#a94442' }}>\n                    {errors.password}\n                  </p>\n                )}\n              </div>\n            </div>\n\n            <div className=\"row\">\n              <div className=\"col-xs-12 form-group\">\n                <label htmlFor=\"roles\" className=\"control-label\">Roles*</label>\n                <select\n                  name=\"roles\"\n                  className=\"form-control select2\"\n                  multiple\n                  value={formData.roles}\n                  onChange={handleRoleChange}\n                  style={{ height: 'auto', minHeight: '100px' }}\n                >\n                  {availableRoles.map(role => (\n                    <option key={role.value} value={role.value}>\n                      {role.label}\n                    </option>\n                  ))}\n                </select>\n                {errors.roles && (\n                  <p className=\"help-block\" style={{ color: '#a94442' }}>\n                    {errors.roles}\n                  </p>\n                )}\n              </div>\n            </div>\n\n            <div>\n              <button \n                type=\"submit\" \n                className=\"btn btn-danger\"\n                disabled={loading}\n                style={{\n                  backgroundColor: '#375dbc',\n                  borderColor: '#375dbc'\n                }}\n              >\n                {loading ? 'Creating...' : 'Save'}\n              </button>\n              {' '}\n              <a href=\"/admin/users\" className=\"btn btn-default\">\n                Cancel\n              </a>\n            </div>\n          </form>\n        </div>\n      </div>\n    </AdminLTELayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAcA,MAAM,iBAAiB;IACrB;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAkB,OAAO;IAAiB;CACpD;AAEc,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;QACrD,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO,EAAE;IACX;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;QAED,sCAAsC;QACtC,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,UAAU,CAAA,OAAQ,CAAC;oBACjB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE;gBACV,CAAC;QACH;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,kBAAkB,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,eAAe,EAAE,CAAA,SAAU,OAAO,KAAK;QACnF,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,OAAO;YACT,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB;QAEA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,CAAC,eAAe,IAAI,CAAC,SAAS,KAAK,GAAG;YAC/C,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,SAAS,KAAK,CAAC,MAAM,KAAK,IAAI;YACvC,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI;YAC7B,UAAU,QAAQ,GAAG;QACvB,OAAO,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;YACvC,UAAU,QAAQ,GAAG;QACvB;QAEA,IAAI,SAAS,KAAK,CAAC,MAAM,KAAK,GAAG;YAC/B,UAAU,KAAK,GAAG;QACpB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,WAAW;QAEX,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,iDAAiD;YACjD,QAAQ,GAAG,CAAC,kBAAkB;YAE9B,yBAAyB;YACzB,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC,iJAAA,CAAA,UAAc;;0BACb,6LAAC;gBAAG,WAAU;0BAAa;;;;;;0BAE3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAI/B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,UAAU;;8CACd,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,SAAQ;gDAAO,WAAU;0DAAgB;;;;;;0DAChD,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,WAAU;gDACV,aAAY;gDACZ,OAAO,SAAS,IAAI;gDACpB,UAAU;gDACV,QAAQ;;;;;;4CAET,OAAO,IAAI,kBACV,6LAAC;gDAAE,WAAU;gDAAa,OAAO;oDAAE,OAAO;gDAAU;0DACjD,OAAO,IAAI;;;;;;;;;;;;;;;;;8CAMpB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,SAAQ;gDAAQ,WAAU;0DAAgB;;;;;;0DACjD,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,WAAU;gDACV,aAAY;gDACZ,OAAO,SAAS,KAAK;gDACrB,UAAU;gDACV,QAAQ;;;;;;4CAET,OAAO,KAAK,kBACX,6LAAC;gDAAE,WAAU;gDAAa,OAAO;oDAAE,OAAO;gDAAU;0DACjD,OAAO,KAAK;;;;;;;;;;;;;;;;;8CAMrB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,SAAQ;gDAAQ,WAAU;0DAAgB;;;;;;0DACjD,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,WAAU;gDACV,aAAY;gDACZ,WAAW;gDACX,OAAO,SAAS,KAAK;gDACrB,UAAU;gDACV,QAAQ;;;;;;4CAET,OAAO,KAAK,kBACX,6LAAC;gDAAE,WAAU;gDAAa,OAAO;oDAAE,OAAO;gDAAU;0DACjD,OAAO,KAAK;;;;;;;;;;;;;;;;;8CAMrB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,SAAQ;gDAAW,WAAU;0DAAgB;;;;;;0DACpD,6LAAC;gDAAI,OAAO;oDAAE,UAAU;gDAAW;;kEACjC,6LAAC;wDACC,MAAM,eAAe,SAAS;wDAC9B,MAAK;wDACL,WAAU;wDACV,IAAG;wDACH,aAAY;wDACZ,OAAO,SAAS,QAAQ;wDACxB,UAAU;wDACV,QAAQ;;;;;;kEAEV,6LAAC;wDACC,WAAW,CAAC,SAAS,EAAE,eAAe,iBAAiB,SAAS,2BAA2B,CAAC;wDAC5F,OAAO;4DACL,UAAU;4DACV,OAAO;4DACP,KAAK;4DACL,WAAW;4DACX,QAAQ;4DACR,OAAO;wDACT;wDACA,SAAS,IAAM,gBAAgB,CAAC;;;;;;;;;;;;4CAGnC,OAAO,QAAQ,kBACd,6LAAC;gDAAE,WAAU;gDAAa,OAAO;oDAAE,OAAO;gDAAU;0DACjD,OAAO,QAAQ;;;;;;;;;;;;;;;;;8CAMxB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,SAAQ;gDAAQ,WAAU;0DAAgB;;;;;;0DACjD,6LAAC;gDACC,MAAK;gDACL,WAAU;gDACV,QAAQ;gDACR,OAAO,SAAS,KAAK;gDACrB,UAAU;gDACV,OAAO;oDAAE,QAAQ;oDAAQ,WAAW;gDAAQ;0DAE3C,eAAe,GAAG,CAAC,CAAA,qBAClB,6LAAC;wDAAwB,OAAO,KAAK,KAAK;kEACvC,KAAK,KAAK;uDADA,KAAK,KAAK;;;;;;;;;;4CAK1B,OAAO,KAAK,kBACX,6LAAC;gDAAE,WAAU;gDAAa,OAAO;oDAAE,OAAO;gDAAU;0DACjD,OAAO,KAAK;;;;;;;;;;;;;;;;;8CAMrB,6LAAC;;sDACC,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,UAAU;4CACV,OAAO;gDACL,iBAAiB;gDACjB,aAAa;4CACf;sDAEC,UAAU,gBAAgB;;;;;;wCAE5B;sDACD,6LAAC;4CAAE,MAAK;4CAAe,WAAU;sDAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjE;GAzPwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}, {"offset": {"line": 1574, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}