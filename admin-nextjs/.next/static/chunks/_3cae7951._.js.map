{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/src/app/login/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { LoginForm } from \"@/types\";\n\nexport default function LoginPage() {\n  const router = useRouter();\n  const [formData, setFormData] = useState<LoginForm>({\n    email: \"\",\n    password: \"\",\n    remember: false,\n  });\n  const [error, setError] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value, checked, type } = e.target;\n    setFormData((prev) => ({\n      ...prev,\n      [name]: type === \"checkbox\" ? checked : value,\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError(\"\");\n\n    try {\n      // Simulate login API call\n      await new Promise((resolve) => setTimeout(resolve, 1000));\n\n      // For demo purposes, accept any email/password\n      if (formData.email && formData.password) {\n        // Store auth token (dummy)\n        localStorage.setItem(\"auth_token\", \"dummy_token\");\n        localStorage.setItem(\"user_role\", \"admin\");\n\n        router.push(\"/admin/dashboard\");\n      } else {\n        setError(\"Please enter both email and password\");\n      }\n    } catch (err) {\n      setError(\"Login failed. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div\n      className=\"login-page\"\n      style={{\n        background: \"linear-gradient(135deg, #375dbc 0%, #2a6496 100%)\",\n        minHeight: \"100vh\",\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"center\",\n        fontFamily:\n          '\"Source Sans Pro\", \"Helvetica Neue\", Helvetica, Arial, sans-serif',\n      }}\n    >\n      <div\n        className=\"login-box\"\n        style={{\n          width: \"360px\",\n          margin: \"7% auto\",\n        }}\n      >\n        <div\n          className=\"login-logo\"\n          style={{\n            fontSize: \"35px\",\n            textAlign: \"center\",\n            marginBottom: \"25px\",\n          }}\n        >\n          <a\n            href=\"#\"\n            style={{\n              color: \"#fff\",\n              textDecoration: \"none\",\n              fontWeight: \"300\",\n            }}\n          >\n            <b>Admin</b>LTE\n          </a>\n        </div>\n\n        <div\n          className=\"login-box-body\"\n          style={{\n            background: \"#fff\",\n            padding: \"20px\",\n            borderRadius: \"4px\",\n            boxShadow: \"0 2px 3px rgba(0,0,0,0.125)\",\n          }}\n        >\n          <p\n            className=\"login-box-msg\"\n            style={{\n              margin: \"0 0 20px 0\",\n              textAlign: \"center\",\n              fontSize: \"14px\",\n              color: \"#666\",\n            }}\n          >\n            Sign in to start your session\n          </p>\n\n          {error && (\n            <div\n              className=\"alert alert-danger\"\n              style={{\n                color: \"#a94442\",\n                backgroundColor: \"#f2dede\",\n                borderColor: \"#ebccd1\",\n                padding: \"15px\",\n                marginBottom: \"20px\",\n                border: \"1px solid transparent\",\n                borderRadius: \"4px\",\n              }}\n            >\n              {error}\n            </div>\n          )}\n\n          <form onSubmit={handleSubmit}>\n            <div\n              className=\"form-group has-feedback\"\n              style={{ marginBottom: \"15px\" }}\n            >\n              <input\n                type=\"email\"\n                name=\"email\"\n                className=\"form-control\"\n                placeholder=\"Email\"\n                value={formData.email}\n                onChange={handleChange}\n                required\n                autoFocus\n                style={{\n                  display: \"block\",\n                  width: \"100%\",\n                  height: \"34px\",\n                  padding: \"6px 12px\",\n                  fontSize: \"14px\",\n                  lineHeight: \"1.42857143\",\n                  color: \"#555\",\n                  backgroundColor: \"#fff\",\n                  backgroundImage: \"none\",\n                  border: \"1px solid #ccc\",\n                  borderRadius: \"4px\",\n                  boxShadow: \"inset 0 1px 1px rgba(0,0,0,.075)\",\n                  transition:\n                    \"border-color ease-in-out .15s,box-shadow ease-in-out .15s\",\n                  boxSizing: \"border-box\",\n                }}\n              />\n              <span\n                className=\"glyphicon glyphicon-envelope form-control-feedback\"\n                style={{\n                  position: \"absolute\",\n                  top: \"0\",\n                  right: \"0\",\n                  zIndex: 2,\n                  display: \"block\",\n                  width: \"34px\",\n                  height: \"34px\",\n                  lineHeight: \"34px\",\n                  textAlign: \"center\",\n                  pointerEvents: \"none\",\n                }}\n              ></span>\n            </div>\n\n            <div\n              className=\"form-group has-feedback\"\n              style={{ marginBottom: \"15px\" }}\n            >\n              <input\n                type=\"password\"\n                name=\"password\"\n                className=\"form-control\"\n                placeholder=\"Password\"\n                value={formData.password}\n                onChange={handleChange}\n                required\n                style={{\n                  display: \"block\",\n                  width: \"100%\",\n                  height: \"34px\",\n                  padding: \"6px 12px\",\n                  fontSize: \"14px\",\n                  lineHeight: \"1.42857143\",\n                  color: \"#555\",\n                  backgroundColor: \"#fff\",\n                  backgroundImage: \"none\",\n                  border: \"1px solid #ccc\",\n                  borderRadius: \"4px\",\n                  boxShadow: \"inset 0 1px 1px rgba(0,0,0,.075)\",\n                  transition:\n                    \"border-color ease-in-out .15s,box-shadow ease-in-out .15s\",\n                  boxSizing: \"border-box\",\n                }}\n              />\n              <span\n                className=\"glyphicon glyphicon-lock form-control-feedback\"\n                style={{\n                  position: \"absolute\",\n                  top: \"0\",\n                  right: \"0\",\n                  zIndex: 2,\n                  display: \"block\",\n                  width: \"34px\",\n                  height: \"34px\",\n                  lineHeight: \"34px\",\n                  textAlign: \"center\",\n                  pointerEvents: \"none\",\n                }}\n              ></span>\n            </div>\n\n            <div className=\"row\" style={{ margin: \"0 -15px\" }}>\n              <div\n                className=\"col-xs-8\"\n                style={{\n                  position: \"relative\",\n                  minHeight: \"1px\",\n                  paddingLeft: \"15px\",\n                  paddingRight: \"15px\",\n                  width: \"66.66666667%\",\n                  float: \"left\",\n                }}\n              >\n                <div\n                  className=\"checkbox icheck\"\n                  style={{ marginTop: \"5px\", marginBottom: \"10px\" }}\n                >\n                  <label style={{ fontWeight: \"normal\", cursor: \"pointer\" }}>\n                    <input\n                      type=\"checkbox\"\n                      name=\"remember\"\n                      checked={formData.remember}\n                      onChange={handleChange}\n                      style={{ marginRight: \"5px\" }}\n                    />\n                    Remember Me\n                  </label>\n                </div>\n              </div>\n\n              <div\n                className=\"col-xs-4\"\n                style={{\n                  position: \"relative\",\n                  minHeight: \"1px\",\n                  paddingLeft: \"15px\",\n                  paddingRight: \"15px\",\n                  width: \"33.33333333%\",\n                  float: \"left\",\n                }}\n              >\n                <button\n                  type=\"submit\"\n                  className=\"btn btn-primary btn-block btn-flat\"\n                  disabled={loading}\n                  style={{\n                    color: \"#fff\",\n                    backgroundColor: \"#375dbc\",\n                    borderColor: \"#375dbc\",\n                    display: \"block\",\n                    width: \"100%\",\n                    padding: \"6px 12px\",\n                    marginBottom: \"0\",\n                    fontSize: \"14px\",\n                    fontWeight: \"normal\",\n                    lineHeight: \"1.42857143\",\n                    textAlign: \"center\",\n                    whiteSpace: \"nowrap\",\n                    verticalAlign: \"middle\",\n                    touchAction: \"manipulation\",\n                    cursor: \"pointer\",\n                    userSelect: \"none\",\n                    backgroundImage: \"none\",\n                    border: \"1px solid transparent\",\n                    borderRadius: \"0\",\n                    textDecoration: \"none\",\n                  }}\n                >\n                  {loading ? \"Signing in...\" : \"Sign In\"}\n                </button>\n              </div>\n            </div>\n          </form>\n\n          <div style={{ marginTop: \"20px\", textAlign: \"center\" }}>\n            <small style={{ color: \"#666\", fontSize: \"12px\" }}>\n              Demo: Use any email and password to login\n            </small>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAMe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QAClD,OAAO;QACP,UAAU;QACV,UAAU;IACZ;IACA,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QAC/C,YAAY,CAAC,OAAS,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;YAC1C,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,0BAA0B;YAC1B,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;YAEnD,+CAA+C;YAC/C,IAAI,SAAS,KAAK,IAAI,SAAS,QAAQ,EAAE;gBACvC,2BAA2B;gBAC3B,aAAa,OAAO,CAAC,cAAc;gBACnC,aAAa,OAAO,CAAC,aAAa;gBAElC,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QACC,WAAU;QACV,OAAO;YACL,YAAY;YACZ,WAAW;YACX,SAAS;YACT,YAAY;YACZ,gBAAgB;YAChB,YACE;QACJ;kBAEA,cAAA,6LAAC;YACC,WAAU;YACV,OAAO;gBACL,OAAO;gBACP,QAAQ;YACV;;8BAEA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBACL,UAAU;wBACV,WAAW;wBACX,cAAc;oBAChB;8BAEA,cAAA,6LAAC;wBACC,MAAK;wBACL,OAAO;4BACL,OAAO;4BACP,gBAAgB;4BAChB,YAAY;wBACd;;0CAEA,6LAAC;0CAAE;;;;;;4BAAS;;;;;;;;;;;;8BAIhB,6LAAC;oBACC,WAAU;oBACV,OAAO;wBACL,YAAY;wBACZ,SAAS;wBACT,cAAc;wBACd,WAAW;oBACb;;sCAEA,6LAAC;4BACC,WAAU;4BACV,OAAO;gCACL,QAAQ;gCACR,WAAW;gCACX,UAAU;gCACV,OAAO;4BACT;sCACD;;;;;;wBAIA,uBACC,6LAAC;4BACC,WAAU;4BACV,OAAO;gCACL,OAAO;gCACP,iBAAiB;gCACjB,aAAa;gCACb,SAAS;gCACT,cAAc;gCACd,QAAQ;gCACR,cAAc;4BAChB;sCAEC;;;;;;sCAIL,6LAAC;4BAAK,UAAU;;8CACd,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,cAAc;oCAAO;;sDAE9B,6LAAC;4CACC,MAAK;4CACL,MAAK;4CACL,WAAU;4CACV,aAAY;4CACZ,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,QAAQ;4CACR,SAAS;4CACT,OAAO;gDACL,SAAS;gDACT,OAAO;gDACP,QAAQ;gDACR,SAAS;gDACT,UAAU;gDACV,YAAY;gDACZ,OAAO;gDACP,iBAAiB;gDACjB,iBAAiB;gDACjB,QAAQ;gDACR,cAAc;gDACd,WAAW;gDACX,YACE;gDACF,WAAW;4CACb;;;;;;sDAEF,6LAAC;4CACC,WAAU;4CACV,OAAO;gDACL,UAAU;gDACV,KAAK;gDACL,OAAO;gDACP,QAAQ;gDACR,SAAS;gDACT,OAAO;gDACP,QAAQ;gDACR,YAAY;gDACZ,WAAW;gDACX,eAAe;4CACjB;;;;;;;;;;;;8CAIJ,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,cAAc;oCAAO;;sDAE9B,6LAAC;4CACC,MAAK;4CACL,MAAK;4CACL,WAAU;4CACV,aAAY;4CACZ,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,QAAQ;4CACR,OAAO;gDACL,SAAS;gDACT,OAAO;gDACP,QAAQ;gDACR,SAAS;gDACT,UAAU;gDACV,YAAY;gDACZ,OAAO;gDACP,iBAAiB;gDACjB,iBAAiB;gDACjB,QAAQ;gDACR,cAAc;gDACd,WAAW;gDACX,YACE;gDACF,WAAW;4CACb;;;;;;sDAEF,6LAAC;4CACC,WAAU;4CACV,OAAO;gDACL,UAAU;gDACV,KAAK;gDACL,OAAO;gDACP,QAAQ;gDACR,SAAS;gDACT,OAAO;gDACP,QAAQ;gDACR,YAAY;gDACZ,WAAW;gDACX,eAAe;4CACjB;;;;;;;;;;;;8CAIJ,6LAAC;oCAAI,WAAU;oCAAM,OAAO;wCAAE,QAAQ;oCAAU;;sDAC9C,6LAAC;4CACC,WAAU;4CACV,OAAO;gDACL,UAAU;gDACV,WAAW;gDACX,aAAa;gDACb,cAAc;gDACd,OAAO;gDACP,OAAO;4CACT;sDAEA,cAAA,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,WAAW;oDAAO,cAAc;gDAAO;0DAEhD,cAAA,6LAAC;oDAAM,OAAO;wDAAE,YAAY;wDAAU,QAAQ;oDAAU;;sEACtD,6LAAC;4DACC,MAAK;4DACL,MAAK;4DACL,SAAS,SAAS,QAAQ;4DAC1B,UAAU;4DACV,OAAO;gEAAE,aAAa;4DAAM;;;;;;wDAC5B;;;;;;;;;;;;;;;;;sDAMR,6LAAC;4CACC,WAAU;4CACV,OAAO;gDACL,UAAU;gDACV,WAAW;gDACX,aAAa;gDACb,cAAc;gDACd,OAAO;gDACP,OAAO;4CACT;sDAEA,cAAA,6LAAC;gDACC,MAAK;gDACL,WAAU;gDACV,UAAU;gDACV,OAAO;oDACL,OAAO;oDACP,iBAAiB;oDACjB,aAAa;oDACb,SAAS;oDACT,OAAO;oDACP,SAAS;oDACT,cAAc;oDACd,UAAU;oDACV,YAAY;oDACZ,YAAY;oDACZ,WAAW;oDACX,YAAY;oDACZ,eAAe;oDACf,aAAa;oDACb,QAAQ;oDACR,YAAY;oDACZ,iBAAiB;oDACjB,QAAQ;oDACR,cAAc;oDACd,gBAAgB;gDAClB;0DAEC,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;sCAMrC,6LAAC;4BAAI,OAAO;gCAAE,WAAW;gCAAQ,WAAW;4BAAS;sCACnD,cAAA,6LAAC;gCAAM,OAAO;oCAAE,OAAO;oCAAQ,UAAU;gCAAO;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/D;GA5SwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}, {"offset": {"line": 444, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}