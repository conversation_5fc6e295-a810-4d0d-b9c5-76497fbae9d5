{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/src/app/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  TextField,\n  Button,\n  Typography,\n  Container,\n  Alert,\n  Checkbox,\n  FormControlLabel,\n} from '@mui/material';\nimport { useRouter } from 'next/navigation';\nimport { LoginForm } from '@/types';\n\nexport default function LoginPage() {\n  const router = useRouter();\n  const [formData, setFormData] = useState<LoginForm>({\n    email: '',\n    password: '',\n    remember: false,\n  });\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value, checked, type } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value,\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      // Simulate login API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      // For demo purposes, accept any email/password\n      if (formData.email && formData.password) {\n        // Store auth token (dummy)\n        localStorage.setItem('auth_token', 'dummy_token');\n        localStorage.setItem('user_role', 'admin');\n        \n        router.push('/admin/dashboard');\n      } else {\n        setError('Please enter both email and password');\n      }\n    } catch (err) {\n      setError('Login failed. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Box\n      sx={{\n        minHeight: '100vh',\n        backgroundColor: '#3c8dbc',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundImage: 'linear-gradient(135deg, #3c8dbc 0%, #2a6496 100%)',\n      }}\n    >\n      <Container maxWidth=\"sm\">\n        <Card sx={{ boxShadow: '0 10px 25px rgba(0,0,0,0.1)' }}>\n          <CardContent sx={{ p: 4 }}>\n            <Box sx={{ textAlign: 'center', mb: 4 }}>\n              <Typography variant=\"h4\" component=\"h1\" gutterBottom sx={{ color: '#444444', fontWeight: 'bold' }}>\n                Admin Login\n              </Typography>\n              <Typography variant=\"body1\" color=\"textSecondary\">\n                Sign in to your admin account\n              </Typography>\n            </Box>\n\n            {error && (\n              <Alert severity=\"error\" sx={{ mb: 3 }}>\n                {error}\n              </Alert>\n            )}\n\n            <form onSubmit={handleSubmit}>\n              <TextField\n                fullWidth\n                label=\"Email Address\"\n                name=\"email\"\n                type=\"email\"\n                value={formData.email}\n                onChange={handleChange}\n                margin=\"normal\"\n                required\n                autoComplete=\"email\"\n                autoFocus\n                sx={{ mb: 2 }}\n              />\n\n              <TextField\n                fullWidth\n                label=\"Password\"\n                name=\"password\"\n                type=\"password\"\n                value={formData.password}\n                onChange={handleChange}\n                margin=\"normal\"\n                required\n                autoComplete=\"current-password\"\n                sx={{ mb: 2 }}\n              />\n\n              <FormControlLabel\n                control={\n                  <Checkbox\n                    name=\"remember\"\n                    checked={formData.remember}\n                    onChange={handleChange}\n                    color=\"primary\"\n                  />\n                }\n                label=\"Remember me\"\n                sx={{ mb: 3 }}\n              />\n\n              <Button\n                type=\"submit\"\n                fullWidth\n                variant=\"contained\"\n                size=\"large\"\n                disabled={loading}\n                sx={{\n                  py: 1.5,\n                  backgroundColor: '#3c8dbc',\n                  '&:hover': {\n                    backgroundColor: '#2a6496',\n                  },\n                }}\n              >\n                {loading ? 'Signing in...' : 'Sign In'}\n              </Button>\n            </form>\n\n            <Box sx={{ mt: 3, textAlign: 'center' }}>\n              <Typography variant=\"body2\" color=\"textSecondary\">\n                Demo credentials: Any email and password will work\n              </Typography>\n            </Box>\n          </CardContent>\n        </Card>\n      </Container>\n    </Box>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;;;AAfA;;;;AAkBe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QAClD,OAAO;QACP,UAAU;QACV,UAAU;IACZ;IACA,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QAC/C,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;YAC1C,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,0BAA0B;YAC1B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,+CAA+C;YAC/C,IAAI,SAAS,KAAK,IAAI,SAAS,QAAQ,EAAE;gBACvC,2BAA2B;gBAC3B,aAAa,OAAO,CAAC,cAAc;gBACnC,aAAa,OAAO,CAAC,aAAa;gBAElC,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC,2LAAA,CAAA,MAAG;QACF,IAAI;YACF,WAAW;YACX,iBAAiB;YACjB,SAAS;YACT,YAAY;YACZ,gBAAgB;YAChB,iBAAiB;QACnB;kBAEA,cAAA,6LAAC,6MAAA,CAAA,YAAS;YAAC,UAAS;sBAClB,cAAA,6LAAC,8LAAA,CAAA,OAAI;gBAAC,IAAI;oBAAE,WAAW;gBAA8B;0BACnD,cAAA,6LAAC,mNAAA,CAAA,cAAW;oBAAC,IAAI;wBAAE,GAAG;oBAAE;;sCACtB,6LAAC,2LAAA,CAAA,MAAG;4BAAC,IAAI;gCAAE,WAAW;gCAAU,IAAI;4BAAE;;8CACpC,6LAAC,gNAAA,CAAA,aAAU;oCAAC,SAAQ;oCAAK,WAAU;oCAAK,YAAY;oCAAC,IAAI;wCAAE,OAAO;wCAAW,YAAY;oCAAO;8CAAG;;;;;;8CAGnG,6LAAC,gNAAA,CAAA,aAAU;oCAAC,SAAQ;oCAAQ,OAAM;8CAAgB;;;;;;;;;;;;wBAKnD,uBACC,6LAAC,iMAAA,CAAA,QAAK;4BAAC,UAAS;4BAAQ,IAAI;gCAAE,IAAI;4BAAE;sCACjC;;;;;;sCAIL,6LAAC;4BAAK,UAAU;;8CACd,6LAAC,6MAAA,CAAA,YAAS;oCACR,SAAS;oCACT,OAAM;oCACN,MAAK;oCACL,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU;oCACV,QAAO;oCACP,QAAQ;oCACR,cAAa;oCACb,SAAS;oCACT,IAAI;wCAAE,IAAI;oCAAE;;;;;;8CAGd,6LAAC,6MAAA,CAAA,YAAS;oCACR,SAAS;oCACT,OAAM;oCACN,MAAK;oCACL,MAAK;oCACL,OAAO,SAAS,QAAQ;oCACxB,UAAU;oCACV,QAAO;oCACP,QAAQ;oCACR,cAAa;oCACb,IAAI;wCAAE,IAAI;oCAAE;;;;;;8CAGd,6LAAC,kOAAA,CAAA,mBAAgB;oCACf,uBACE,6LAAC,0MAAA,CAAA,WAAQ;wCACP,MAAK;wCACL,SAAS,SAAS,QAAQ;wCAC1B,UAAU;wCACV,OAAM;;;;;;oCAGV,OAAM;oCACN,IAAI;wCAAE,IAAI;oCAAE;;;;;;8CAGd,6LAAC,oMAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS;oCACT,SAAQ;oCACR,MAAK;oCACL,UAAU;oCACV,IAAI;wCACF,IAAI;wCACJ,iBAAiB;wCACjB,WAAW;4CACT,iBAAiB;wCACnB;oCACF;8CAEC,UAAU,kBAAkB;;;;;;;;;;;;sCAIjC,6LAAC,2LAAA,CAAA,MAAG;4BAAC,IAAI;gCAAE,IAAI;gCAAG,WAAW;4BAAS;sCACpC,cAAA,6LAAC,gNAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAQ,OAAM;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShE;GA9IwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}