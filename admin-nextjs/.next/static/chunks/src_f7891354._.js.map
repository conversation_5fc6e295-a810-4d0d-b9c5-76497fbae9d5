{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/src/components/layout/AdminLTELayout.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { useRouter, usePathname } from \"next/navigation\";\nimport Image from \"next/image\";\n\ninterface AdminLTELayoutProps {\n  children: React.ReactNode;\n  title?: string;\n}\n\nexport default function AdminLTELayout({\n  children,\n  title,\n}: AdminLTELayoutProps) {\n  const router = useRouter();\n  const pathname = usePathname();\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const [openTreeview, setOpenTreeview] = useState<string | null>(null);\n\n  const handleSidebarToggle = () => {\n    setSidebarCollapsed(!sidebarCollapsed);\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem(\"auth_token\");\n    localStorage.removeItem(\"user_role\");\n    router.push(\"/login\");\n  };\n\n  const isActive = (path: string) => {\n    return pathname === path;\n  };\n\n  const toggleTreeview = (id: string) => {\n    // Laravel behavior: only one treeview can be open at a time\n    setOpenTreeview((prev) => (prev === id ? null : id));\n  };\n\n  const isTreeviewOpen = (id: string) => {\n    return openTreeview === id;\n  };\n\n  // Initialize treeview state based on current path (Laravel behavior: only one open)\n  useEffect(() => {\n    if (\n      pathname.includes(\"/admin/permissions\") ||\n      pathname.includes(\"/admin/roles\") ||\n      pathname.includes(\"/admin/users\")\n    ) {\n      setOpenTreeview(\"user-management\");\n    } else if (\n      pathname.includes(\"/admin/classes\") ||\n      pathname.includes(\"/admin/subjects\")\n    ) {\n      setOpenTreeview(\"course-management\");\n    } else if (\n      pathname.includes(\"/admin/categories\") ||\n      pathname.includes(\"/admin/courses\")\n    ) {\n      setOpenTreeview(\"course-category\");\n    } else if (pathname.includes(\"/admin/quizzes\")) {\n      setOpenTreeview(\"quizzes\");\n    } else if (pathname.includes(\"/admin/assignments\")) {\n      setOpenTreeview(\"assignments\");\n    } else if (pathname.includes(\"/admin/live-classes\")) {\n      setOpenTreeview(\"live-classes\");\n    } else if (pathname.includes(\"/admin/notifications\")) {\n      setOpenTreeview(\"notifications\");\n    } else if (\n      pathname.includes(\"/admin/about-us\") ||\n      pathname.includes(\"/admin/privacy-policy\") ||\n      pathname.includes(\"/admin/terms-condition\")\n    ) {\n      setOpenTreeview(\"cms\");\n    } else {\n      // Close all treeviews if not on a treeview page\n      setOpenTreeview(null);\n    }\n  }, [pathname]);\n\n  return (\n    <>\n      <style jsx>{`\n        .treeview-menu {\n          max-height: ${openTreeview ? \"1000px\" : \"0\"};\n          transition: max-height 0.3s ease-in-out;\n        }\n        .treeview-menu li {\n          opacity: ${openTreeview ? \"1\" : \"0\"};\n          transition: opacity 0.2s ease-in-out;\n        }\n      `}</style>\n      <div\n        className={`wrapper skin-blue sidebar-mini ${\n          sidebarCollapsed ? \"sidebar-collapse\" : \"\"\n        }`}\n      >\n        {/* Main Header */}\n        <header className=\"main-header\">\n          {/* Logo */}\n          <a\n            href=\"#\"\n            className=\"logo\"\n            style={{ color: \"#fff\", textDecoration: \"none\" }}\n          >\n            <span className=\"logo-lg\">\n              <Image\n                src=\"/img/web-logo.png\"\n                alt=\"Logo\"\n                width={170}\n                height={35}\n                style={{ alignContent: \"right\" }}\n              />\n            </span>\n          </a>\n\n          {/* Header Navbar */}\n          <nav\n            className=\"navbar navbar-static-top\"\n            style={{ display: \"block\", padding: 0 }}\n          >\n            {/* Sidebar toggle button */}\n            <a\n              href=\"#\"\n              className=\"sidebar-toggle\"\n              onClick={(e) => {\n                e.preventDefault();\n                handleSidebarToggle();\n              }}\n              role=\"button\"\n            >\n              <span className=\"sr-only\">Toggle navigation</span>\n              <span className=\"icon-bar\"></span>\n              <span className=\"icon-bar\"></span>\n              <span className=\"icon-bar\"></span>\n            </a>\n          </nav>\n        </header>\n\n        {/* Left side column. contains the sidebar */}\n        <aside className=\"main-sidebar\">\n          {/* sidebar: style can be found in sidebar.less */}\n          <section className=\"sidebar\">\n            <ul className=\"sidebar-menu scroll-table scroll-table-two\">\n              {/* Dashboard */}\n              <li className={isActive(\"/admin/dashboard\") ? \"active\" : \"\"}>\n                <a href=\"/admin/dashboard\" title=\"Dashboard\">\n                  <i className=\"fa fa-dashboard\"></i>\n                  <span className=\"title\">Dashboard</span>\n                </a>\n              </li>\n\n              {/* User Management */}\n              <li\n                className={`treeview ${\n                  isTreeviewOpen(\"user-management\") ? \"active menu-open\" : \"\"\n                }`}\n              >\n                <a\n                  href=\"#\"\n                  title=\"User Management\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    toggleTreeview(\"user-management\");\n                  }}\n                >\n                  <i className=\"fa fa-users\"></i>\n                  <span className=\"title\">User Management</span>\n                  <span className=\"pull-right-container\">\n                    <i\n                      className={`fa fa-angle-left pull-right ${\n                        isTreeviewOpen(\"user-management\") ? \"fa-angle-down\" : \"\"\n                      }`}\n                    ></i>\n                  </span>\n                </a>\n                <ul\n                  className=\"treeview-menu\"\n                  style={{\n                    display: isTreeviewOpen(\"user-management\")\n                      ? \"block\"\n                      : \"none\",\n                    transition: \"all 0.3s ease-in-out\",\n                    overflow: \"hidden\",\n                  }}\n                >\n                  <li\n                    className={\n                      isActive(\"/admin/permissions\") ? \"active active-sub\" : \"\"\n                    }\n                  >\n                    <a href=\"/admin/permissions\">\n                      <i className=\"fa fa-briefcase\"></i>\n                      <span className=\"title\">Permissions</span>\n                    </a>\n                  </li>\n                  <li\n                    className={\n                      isActive(\"/admin/roles\") ? \"active active-sub\" : \"\"\n                    }\n                  >\n                    <a href=\"/admin/roles\">\n                      <i className=\"fa fa-briefcase\"></i>\n                      <span className=\"title\">Roles</span>\n                    </a>\n                  </li>\n                  <li\n                    className={\n                      isActive(\"/admin/users\") ? \"active active-sub\" : \"\"\n                    }\n                  >\n                    <a href=\"/admin/users\">\n                      <i className=\"fa fa-user\"></i>\n                      <span className=\"title\">Administrator Users</span>\n                    </a>\n                  </li>\n                </ul>\n              </li>\n\n              {/* Fees Update */}\n              <li\n                className={isActive(\"/admin/fees\") ? \"active active-sub\" : \"\"}\n              >\n                <a href=\"/admin/fees\">\n                  <i className=\"fa fa-briefcase\"></i>\n                  <span className=\"title\">Fees Update</span>\n                </a>\n              </li>\n\n              {/* Teacher Management */}\n              <li\n                className={\n                  isActive(\"/admin/teachers\") ? \"active active-sub\" : \"\"\n                }\n              >\n                <a href=\"/admin/teachers\">\n                  <i className=\"fa fa-user\"></i>\n                  <span className=\"title\">Teacher Management</span>\n                </a>\n              </li>\n\n              {/* Student Management */}\n              <li\n                className={\n                  isActive(\"/admin/students\") ? \"active active-sub\" : \"\"\n                }\n              >\n                <a href=\"/admin/students\">\n                  <i className=\"fa fa-user\"></i>\n                  <span className=\"title\">Student Management</span>\n                </a>\n              </li>\n\n              {/* Course Management */}\n              <li\n                className={`treeview ${\n                  isTreeviewOpen(\"course-management\") ? \"active menu-open\" : \"\"\n                }`}\n              >\n                <a\n                  href=\"#\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    toggleTreeview(\"course-management\");\n                  }}\n                >\n                  <i className=\"fa fa-graduation-cap\"></i>\n                  <span className=\"title\">Course Management</span>\n                  <span className=\"pull-right-container\">\n                    <i\n                      className={`fa fa-angle-left pull-right ${\n                        isTreeviewOpen(\"course-management\")\n                          ? \"fa-angle-down\"\n                          : \"\"\n                      }`}\n                    ></i>\n                  </span>\n                </a>\n                <ul\n                  className=\"treeview-menu\"\n                  style={{\n                    display: isTreeviewOpen(\"course-management\")\n                      ? \"block\"\n                      : \"none\",\n                    transition: \"all 0.3s ease-in-out\",\n                    overflow: \"hidden\",\n                  }}\n                >\n                  <li\n                    className={\n                      isActive(\"/admin/classes\") ? \"active active-sub\" : \"\"\n                    }\n                  >\n                    <a href=\"/admin/classes\">\n                      <i className=\"fa fa-graduation-cap\"></i>\n                      <span className=\"title\">Class Details</span>\n                    </a>\n                  </li>\n                  <li\n                    className={\n                      isActive(\"/admin/subjects\") ? \"active active-sub\" : \"\"\n                    }\n                  >\n                    <a href=\"/admin/subjects\">\n                      <i className=\"fa fa-graduation-cap\"></i>\n                      <span className=\"title\">Subjects Details</span>\n                    </a>\n                  </li>\n                </ul>\n              </li>\n\n              {/* Course & Category */}\n              <li\n                className={`treeview ${\n                  isTreeviewOpen(\"course-category\") ? \"active menu-open\" : \"\"\n                }`}\n              >\n                <a\n                  href=\"#\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    toggleTreeview(\"course-category\");\n                  }}\n                >\n                  <i className=\"fa fa-graduation-cap\"></i>\n                  <span className=\"title\">Course & Category</span>\n                  <span className=\"pull-right-container\">\n                    <i\n                      className={`fa fa-angle-left pull-right ${\n                        isTreeviewOpen(\"course-category\") ? \"fa-angle-down\" : \"\"\n                      }`}\n                    ></i>\n                  </span>\n                </a>\n                <ul\n                  className=\"treeview-menu\"\n                  style={{\n                    display: isTreeviewOpen(\"course-category\")\n                      ? \"block\"\n                      : \"none\",\n                    transition: \"all 0.3s ease-in-out\",\n                    overflow: \"hidden\",\n                  }}\n                >\n                  <li\n                    className={\n                      isActive(\"/admin/categories\") ? \"active active-sub\" : \"\"\n                    }\n                  >\n                    <a href=\"/admin/categories\">\n                      <i className=\"fa fa-graduation-cap\"></i>\n                      <span className=\"title\">Category Details</span>\n                    </a>\n                  </li>\n                  <li\n                    className={\n                      isActive(\"/admin/courses\") ? \"active active-sub\" : \"\"\n                    }\n                  >\n                    <a href=\"/admin/courses\">\n                      <i className=\"fa fa-graduation-cap\"></i>\n                      <span className=\"title\">Course Details</span>\n                    </a>\n                  </li>\n                </ul>\n              </li>\n\n              {/* Demo Request */}\n              <li\n                className={\n                  isActive(\"/admin/demo-requests\") ? \"active active-sub\" : \"\"\n                }\n              >\n                <a href=\"/admin/demo-requests\">\n                  <i className=\"fa fa-graduation-cap\"></i>\n                  <span className=\"title\">Demo Request</span>\n                </a>\n              </li>\n\n              {/* Withdrawal */}\n              <li\n                className={\n                  isActive(\"/admin/withdrawal\") ? \"active active-sub\" : \"\"\n                }\n              >\n                <a href=\"/admin/withdrawal\">\n                  <i className=\"fa fa-graduation-cap\"></i>\n                  <span className=\"title\">Withdrawal</span>\n                </a>\n              </li>\n\n              {/* Incomplete Classes */}\n              <li\n                className={\n                  isActive(\"/admin/incomplete-classes\")\n                    ? \"active active-sub\"\n                    : \"\"\n                }\n              >\n                <a href=\"/admin/incomplete-classes\">\n                  <i className=\"fa fa-graduation-cap\"></i>\n                  <span className=\"title\">Incomplete Classes</span>\n                </a>\n              </li>\n\n              {/* Student Purchase Class */}\n              <li\n                className={\n                  isActive(\"/admin/student-purchase-class\")\n                    ? \"active active-sub\"\n                    : \"\"\n                }\n              >\n                <a href=\"/admin/student-purchase-class\">\n                  <i className=\"fa fa-graduation-cap\"></i>\n                  <span className=\"title\">Student Purchase Class</span>\n                </a>\n              </li>\n\n              {/* Offers */}\n              <li\n                className={isActive(\"/admin/offers\") ? \"active active-sub\" : \"\"}\n              >\n                <a href=\"/admin/offers\">\n                  <i className=\"fa fa-graduation-cap\"></i>\n                  <span className=\"title\">Offers Details</span>\n                </a>\n              </li>\n\n              {/* Student Invoice */}\n              <li\n                className={\n                  isActive(\"/admin/student-invoice\") ? \"active active-sub\" : \"\"\n                }\n              >\n                <a href=\"/admin/student-invoice\">\n                  <i className=\"fa fa-graduation-cap\"></i>\n                  <span className=\"title\">Student Invoice</span>\n                </a>\n              </li>\n\n              {/* Quizzes */}\n              <li\n                className={`treeview ${\n                  isTreeviewOpen(\"quizzes\") ? \"active menu-open\" : \"\"\n                }`}\n              >\n                <a\n                  href=\"#\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    toggleTreeview(\"quizzes\");\n                  }}\n                >\n                  <i className=\"fa fa-quora\"></i>\n                  <span className=\"title\">Quizes</span>\n                  <span className=\"pull-right-container\">\n                    <i\n                      className={`fa fa-angle-left pull-right ${\n                        isTreeviewOpen(\"quizzes\") ? \"fa-angle-down\" : \"\"\n                      }`}\n                    ></i>\n                  </span>\n                </a>\n                <ul\n                  className=\"treeview-menu\"\n                  style={{\n                    display: isTreeviewOpen(\"quizzes\") ? \"block\" : \"none\",\n                    transition: \"all 0.3s ease-in-out\",\n                    overflow: \"hidden\",\n                  }}\n                >\n                  <li\n                    className={\n                      isActive(\"/admin/quizzes\") ? \"active active-sub\" : \"\"\n                    }\n                  >\n                    <a href=\"/admin/quizzes\">\n                      <i className=\"fa fa-quora\"></i>\n                      <span className=\"title\">Quizes</span>\n                    </a>\n                  </li>\n                </ul>\n              </li>\n\n              {/* Assignment */}\n              <li\n                className={`treeview ${\n                  isTreeviewOpen(\"assignments\") ? \"active menu-open\" : \"\"\n                }`}\n              >\n                <a\n                  href=\"#\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    toggleTreeview(\"assignments\");\n                  }}\n                >\n                  <i className=\"fa fa-list\"></i>\n                  <span className=\"title\">Assignment</span>\n                  <span className=\"pull-right-container\">\n                    <i\n                      className={`fa fa-angle-left pull-right ${\n                        isTreeviewOpen(\"assignments\") ? \"fa-angle-down\" : \"\"\n                      }`}\n                    ></i>\n                  </span>\n                </a>\n                <ul\n                  className=\"treeview-menu\"\n                  style={{\n                    display: isTreeviewOpen(\"assignments\") ? \"block\" : \"none\",\n                    transition: \"all 0.3s ease-in-out\",\n                    overflow: \"hidden\",\n                  }}\n                >\n                  <li\n                    className={\n                      isActive(\"/admin/assignments\") ? \"active active-sub\" : \"\"\n                    }\n                  >\n                    <a href=\"/admin/assignments\">\n                      <i className=\"fa fa-list\"></i>\n                      <span className=\"title\">Assignment Details</span>\n                    </a>\n                  </li>\n                </ul>\n              </li>\n\n              {/* Live Classes */}\n              <li\n                className={`treeview ${\n                  isTreeviewOpen(\"live-classes\") ? \"active menu-open\" : \"\"\n                }`}\n              >\n                <a\n                  href=\"#\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    toggleTreeview(\"live-classes\");\n                  }}\n                >\n                  <i className=\"fa fa-video-camera\"></i>\n                  <span className=\"title\">Live Classes</span>\n                  <span className=\"pull-right-container\">\n                    <i\n                      className={`fa fa-angle-left pull-right ${\n                        isTreeviewOpen(\"live-classes\") ? \"fa-angle-down\" : \"\"\n                      }`}\n                    ></i>\n                  </span>\n                </a>\n                <ul\n                  className=\"treeview-menu\"\n                  style={{\n                    display: isTreeviewOpen(\"live-classes\") ? \"block\" : \"none\",\n                    transition: \"all 0.3s ease-in-out\",\n                    overflow: \"hidden\",\n                  }}\n                >\n                  <li\n                    className={\n                      isActive(\"/admin/live-classes\") ? \"active active-sub\" : \"\"\n                    }\n                  >\n                    <a href=\"/admin/live-classes\">\n                      <i className=\"fa fa-graduation-cap\"></i>\n                      <span className=\"title\">Live Classes Details</span>\n                    </a>\n                  </li>\n                </ul>\n              </li>\n\n              {/* Banners */}\n              <li\n                className={\n                  isActive(\"/admin/banners\") ? \"active active-sub\" : \"\"\n                }\n              >\n                <a href=\"/admin/banners\">\n                  <i className=\"fa fa-picture-o\"></i>\n                  <span className=\"title\">Banners</span>\n                </a>\n              </li>\n\n              {/* Referral */}\n              <li\n                className={\n                  isActive(\"/admin/referral\") ? \"active active-sub\" : \"\"\n                }\n              >\n                <a href=\"/admin/referral\">\n                  <i className=\"fa fa-picture-o\"></i>\n                  <span className=\"title\">Referral</span>\n                </a>\n              </li>\n\n              {/* Contact Us */}\n              <li className={isActive(\"/admin/contact-us\") ? \"active\" : \"\"}>\n                <a href=\"/admin/contact-us\">\n                  <i className=\"fa fa-envelope-open-o\"></i>\n                  <span className=\"title\">Contact Us</span>\n                </a>\n              </li>\n\n              {/* Notification */}\n              <li\n                className={`treeview ${\n                  isTreeviewOpen(\"notifications\") ? \"active menu-open\" : \"\"\n                }`}\n              >\n                <a\n                  href=\"#\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    toggleTreeview(\"notifications\");\n                  }}\n                >\n                  <i className=\"fa fa-bell\"></i>\n                  <span className=\"title\">Notification</span>\n                  <span className=\"pull-right-container\">\n                    <i\n                      className={`fa fa-angle-left pull-right ${\n                        isTreeviewOpen(\"notifications\") ? \"fa-angle-down\" : \"\"\n                      }`}\n                    ></i>\n                  </span>\n                </a>\n                <ul\n                  className=\"treeview-menu\"\n                  style={{\n                    display: isTreeviewOpen(\"notifications\") ? \"block\" : \"none\",\n                    transition: \"all 0.3s ease-in-out\",\n                    overflow: \"hidden\",\n                  }}\n                >\n                  <li\n                    className={\n                      isActive(\"/admin/notifications\")\n                        ? \"active active-sub\"\n                        : \"\"\n                    }\n                  >\n                    <a href=\"/admin/notifications\">\n                      <i className=\"fa fa-bell\"></i>\n                      <span className=\"title\">Notifications</span>\n                    </a>\n                  </li>\n                </ul>\n              </li>\n\n              {/* Reports */}\n              <li className={isActive(\"/admin/reports\") ? \"active\" : \"\"}>\n                <a href=\"/admin/reports\">\n                  <i className=\"fa fa-bar-chart\"></i>\n                  <span className=\"title\">Reports</span>\n                </a>\n              </li>\n\n              {/* CMS */}\n              <li\n                className={`treeview ${\n                  isTreeviewOpen(\"cms\") ? \"active menu-open\" : \"\"\n                }`}\n              >\n                <a\n                  href=\"#\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    toggleTreeview(\"cms\");\n                  }}\n                >\n                  <i className=\"fa fa-graduation-cap\"></i>\n                  <span className=\"title\">CMS</span>\n                  <span className=\"pull-right-container\">\n                    <i\n                      className={`fa fa-angle-left pull-right ${\n                        isTreeviewOpen(\"cms\") ? \"fa-angle-down\" : \"\"\n                      }`}\n                    ></i>\n                  </span>\n                </a>\n                <ul\n                  className=\"treeview-menu\"\n                  style={{\n                    display: isTreeviewOpen(\"cms\") ? \"block\" : \"none\",\n                    transition: \"all 0.3s ease-in-out\",\n                    overflow: \"hidden\",\n                  }}\n                >\n                  <li\n                    className={\n                      isActive(\"/admin/about-us\") ? \"active active-sub\" : \"\"\n                    }\n                  >\n                    <a href=\"/admin/about-us\">\n                      <i className=\"fa fa-graduation-cap\"></i>\n                      <span className=\"title\">About Us</span>\n                    </a>\n                  </li>\n                  <li\n                    className={\n                      isActive(\"/admin/privacy-policy\")\n                        ? \"active active-sub\"\n                        : \"\"\n                    }\n                  >\n                    <a href=\"/admin/privacy-policy\">\n                      <i className=\"fa fa-graduation-cap\"></i>\n                      <span className=\"title\">Privacy Policy</span>\n                    </a>\n                  </li>\n                  <li\n                    className={\n                      isActive(\"/admin/terms-condition\")\n                        ? \"active active-sub\"\n                        : \"\"\n                    }\n                  >\n                    <a href=\"/admin/terms-condition\">\n                      <i className=\"fa fa-graduation-cap\"></i>\n                      <span className=\"title\">Terms & Condition</span>\n                    </a>\n                  </li>\n                </ul>\n              </li>\n\n              {/* Logout */}\n              <li>\n                <a\n                  href=\"#\"\n                  title=\"Logout\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    handleLogout();\n                  }}\n                >\n                  <i className=\"fa fa-arrow-left\"></i>\n                  <span className=\"title\">Logout</span>\n                </a>\n              </li>\n            </ul>\n          </section>\n        </aside>\n\n        {/* Content Wrapper. Contains page content */}\n        <div className=\"content-wrapper\">\n          {/* Main content */}\n          <section className=\"content\">\n            {title && (\n              <h3\n                className=\"page-title\"\n                style={{\n                  margin: \"20px 0\",\n                  color: \"#444444\",\n                  fontSize: \"24px\",\n                  fontWeight: 400,\n                }}\n              >\n                {title}\n              </h3>\n            )}\n\n            <div className=\"row\">\n              <div className=\"col-md-12\">{children}</div>\n            </div>\n          </section>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;;AAWe,SAAS,eAAe,EACrC,QAAQ,EACR,KAAK,EACe;;IACpB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,MAAM,sBAAsB;QAC1B,oBAAoB,CAAC;IACvB;IAEA,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,WAAW,CAAC;QAChB,OAAO,aAAa;IACtB;IAEA,MAAM,iBAAiB,CAAC;QACtB,4DAA4D;QAC5D,gBAAgB,CAAC,OAAU,SAAS,KAAK,OAAO;IAClD;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,iBAAiB;IAC1B;IAEA,oFAAoF;IACpF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IACE,SAAS,QAAQ,CAAC,yBAClB,SAAS,QAAQ,CAAC,mBAClB,SAAS,QAAQ,CAAC,iBAClB;gBACA,gBAAgB;YAClB,OAAO,IACL,SAAS,QAAQ,CAAC,qBAClB,SAAS,QAAQ,CAAC,oBAClB;gBACA,gBAAgB;YAClB,OAAO,IACL,SAAS,QAAQ,CAAC,wBAClB,SAAS,QAAQ,CAAC,mBAClB;gBACA,gBAAgB;YAClB,OAAO,IAAI,SAAS,QAAQ,CAAC,mBAAmB;gBAC9C,gBAAgB;YAClB,OAAO,IAAI,SAAS,QAAQ,CAAC,uBAAuB;gBAClD,gBAAgB;YAClB,OAAO,IAAI,SAAS,QAAQ,CAAC,wBAAwB;gBACnD,gBAAgB;YAClB,OAAO,IAAI,SAAS,QAAQ,CAAC,yBAAyB;gBACpD,gBAAgB;YAClB,OAAO,IACL,SAAS,QAAQ,CAAC,sBAClB,SAAS,QAAQ,CAAC,4BAClB,SAAS,QAAQ,CAAC,2BAClB;gBACA,gBAAgB;YAClB,OAAO;gBACL,gDAAgD;gBAChD,gBAAgB;YAClB;QACF;mCAAG;QAAC;KAAS;IAEb,qBACE;;;;;oBAGoB,eAAe,WAAW;oBAI7B,eAAe,MAAM;;oFAJlB,eAAe,WAAW,iIAI7B,eAAe,MAAM;;0BAIpC,6LAAC;;;;;4BARiB,eAAe,WAAW;4BAI7B,eAAe,MAAM;;;2BAKvB,CAAC,+BAA+B,EACzC,mBAAmB,qBAAqB,IACxC;;kCAGF,6LAAC;;;;;oCAde,eAAe,WAAW;oCAI7B,eAAe,MAAM;;;mCAUhB;;0CAEhB,6LAAC;gCACC,MAAK;gCAEL,OAAO;oCAAE,OAAO;oCAAQ,gBAAgB;gCAAO;;;;;4CAnBnC,eAAe,WAAW;4CAI7B,eAAe,MAAM;;;2CAcpB;0CAGV,cAAA,6LAAC;;;;;gDArBW,eAAe,WAAW;gDAI7B,eAAe,MAAM;;;+CAiBd;8CACd,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,OAAO;4CAAE,cAAc;wCAAQ;;;;;;;;;;;;;;;;0CAMrC,6LAAC;gCAEC,OAAO;oCAAE,SAAS;oCAAS,SAAS;gCAAE;;;;;4CAnC1B,eAAe,WAAW;4CAI7B,eAAe,MAAM;;;2CA8BpB;0CAIV,cAAA,6LAAC;oCACC,MAAK;oCAEL,SAAS,CAAC;wCACR,EAAE,cAAc;wCAChB;oCACF;oCACA,MAAK;;;;;gDA7CK,eAAe,WAAW;gDAI7B,eAAe,MAAM;;;+CAoClB;;sDAOV,6LAAC;;;;;wDA/CS,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;uDA2CZ;sDAAU;;;;;;sDAC1B,6LAAC;;;;;wDAhDS,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;uDA4CZ;;;;;;sDAChB,6LAAC;;;;;wDAjDS,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;uDA6CZ;;;;;;sDAChB,6LAAC;;;;;wDAlDS,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;uDA8CZ;;;;;;;;;;;;;;;;;;;;;;;kCAMtB,6LAAC;;;;;oCAxDe,eAAe,WAAW;oCAI7B,eAAe,MAAM;;;mCAoDjB;kCAEf,cAAA,6LAAC;;;;;wCA1Da,eAAe,WAAW;wCAI7B,eAAe,MAAM;;;uCAsDb;sCACjB,cAAA,6LAAC;;;;;4CA3DW,eAAe,WAAW;4CAI7B,eAAe,MAAM;;;2CAuDhB;;kDAEZ,6LAAC;;;;;oDA7DS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDAyDb,CAAA,SAAS,sBAAsB,WAAW,EAAC;kDACxD,cAAA,6LAAC;4CAAE,MAAK;4CAAmB,OAAM;;;;;wDA9DzB,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DA2DxB,6LAAC;;;;;gEA/DK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DA2DX;;;;;;8DACb,6LAAC;;;;;gEAhEK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DA4DR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,6LAAC;;;;;oDArES,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;mDAkEf,CAAC,SAAS,EACnB,eAAe,qBAAqB,qBAAqB,IACzD;;0DAEF,6LAAC;gDACC,MAAK;gDACL,OAAM;gDACN,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,eAAe;gDACjB;;;;;4DAhFM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;;;kEA8ExB,6LAAC;;;;;oEAlFK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEA8EX;;;;;;kEACb,6LAAC;;;;;oEAnFK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEA+ER;kEAAQ;;;;;;kEACxB,6LAAC;;;;;oEApFK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAgFR;kEACd,cAAA,6LAAC;;;;;wEArFG,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;uEAkFT,CAAC,4BAA4B,EACtC,eAAe,qBAAqB,kBAAkB,IACtD;;;;;;;;;;;;;;;;;0DAIR,6LAAC;gDAEC,OAAO;oDACL,SAAS,eAAe,qBACpB,UACA;oDACJ,YAAY;oDACZ,UAAU;gDACZ;;;;;4DApGM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;2DAyFd;;kEASV,6LAAC;;;;;oEAtGK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;oEAoGpB,CAAA,SAAS,wBAAwB,sBAAsB,EAAC;kEAG1D,cAAA,6LAAC;4DAAE,MAAK;;;;;wEA3GJ,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;;;8EAwGpB,6LAAC;;;;;gFA5GC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAwGP;;;;;;8EACb,6LAAC;;;;;gFA7GC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAyGJ;8EAAQ;;;;;;;;;;;;;;;;;kEAG5B,6LAAC;;;;;oEAhHK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;oEA8GpB,CAAA,SAAS,kBAAkB,sBAAsB,EAAC;kEAGpD,cAAA,6LAAC;4DAAE,MAAK;;;;;wEArHJ,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;;;8EAkHpB,6LAAC;;;;;gFAtHC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAkHP;;;;;;8EACb,6LAAC;;;;;gFAvHC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAmHJ;8EAAQ;;;;;;;;;;;;;;;;;kEAG5B,6LAAC;;;;;oEA1HK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;oEAwHpB,CAAA,SAAS,kBAAkB,sBAAsB,EAAC;kEAGpD,cAAA,6LAAC;4DAAE,MAAK;;;;;wEA/HJ,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;;;8EA4HpB,6LAAC;;;;;gFAhIC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EA4HP;;;;;;8EACb,6LAAC;;;;;gFAjIC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EA6HJ;8EAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOhC,6LAAC;;;;;oDAxIS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDAqIf,CAAA,SAAS,iBAAiB,sBAAsB,EAAC;kDAE5D,cAAA,6LAAC;4CAAE,MAAK;;;;;wDA3IA,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DAwIxB,6LAAC;;;;;gEA5IK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAwIX;;;;;;8DACb,6LAAC;;;;;gEA7IK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAyIR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,6LAAC;;;;;oDAlJS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDAgJxB,CAAA,SAAS,qBAAqB,sBAAsB,EAAC;kDAGvD,cAAA,6LAAC;4CAAE,MAAK;;;;;wDAvJA,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DAoJxB,6LAAC;;;;;gEAxJK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAoJX;;;;;;8DACb,6LAAC;;;;;gEAzJK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAqJR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,6LAAC;;;;;oDA9JS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDA4JxB,CAAA,SAAS,qBAAqB,sBAAsB,EAAC;kDAGvD,cAAA,6LAAC;4CAAE,MAAK;;;;;wDAnKA,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DAgKxB,6LAAC;;;;;gEApKK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAgKX;;;;;;8DACb,6LAAC;;;;;gEArKK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAiKR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,6LAAC;;;;;oDA1KS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;mDAuKf,CAAC,SAAS,EACnB,eAAe,uBAAuB,qBAAqB,IAC3D;;0DAEF,6LAAC;gDACC,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,eAAe;gDACjB;;;;;4DApLM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;;;kEAkLxB,6LAAC;;;;;oEAtLK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAkLX;;;;;;kEACb,6LAAC;;;;;oEAvLK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAmLR;kEAAQ;;;;;;kEACxB,6LAAC;;;;;oEAxLK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAoLR;kEACd,cAAA,6LAAC;;;;;wEAzLG,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;uEAsLT,CAAC,4BAA4B,EACtC,eAAe,uBACX,kBACA,IACJ;;;;;;;;;;;;;;;;;0DAIR,6LAAC;gDAEC,OAAO;oDACL,SAAS,eAAe,uBACpB,UACA;oDACJ,YAAY;oDACZ,UAAU;gDACZ;;;;;4DA1MM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;2DA+Ld;;kEASV,6LAAC;;;;;oEA5MK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;oEA0MpB,CAAA,SAAS,oBAAoB,sBAAsB,EAAC;kEAGtD,cAAA,6LAAC;4DAAE,MAAK;;;;;wEAjNJ,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;;;8EA8MpB,6LAAC;;;;;gFAlNC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EA8MP;;;;;;8EACb,6LAAC;;;;;gFAnNC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EA+MJ;8EAAQ;;;;;;;;;;;;;;;;;kEAG5B,6LAAC;;;;;oEAtNK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;oEAoNpB,CAAA,SAAS,qBAAqB,sBAAsB,EAAC;kEAGvD,cAAA,6LAAC;4DAAE,MAAK;;;;;wEA3NJ,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;;;8EAwNpB,6LAAC;;;;;gFA5NC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAwNP;;;;;;8EACb,6LAAC;;;;;gFA7NC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAyNJ;8EAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOhC,6LAAC;;;;;oDApOS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;mDAiOf,CAAC,SAAS,EACnB,eAAe,qBAAqB,qBAAqB,IACzD;;0DAEF,6LAAC;gDACC,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,eAAe;gDACjB;;;;;4DA9OM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;;;kEA4OxB,6LAAC;;;;;oEAhPK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEA4OX;;;;;;kEACb,6LAAC;;;;;oEAjPK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEA6OR;kEAAQ;;;;;;kEACxB,6LAAC;;;;;oEAlPK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEA8OR;kEACd,cAAA,6LAAC;;;;;wEAnPG,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;uEAgPT,CAAC,4BAA4B,EACtC,eAAe,qBAAqB,kBAAkB,IACtD;;;;;;;;;;;;;;;;;0DAIR,6LAAC;gDAEC,OAAO;oDACL,SAAS,eAAe,qBACpB,UACA;oDACJ,YAAY;oDACZ,UAAU;gDACZ;;;;;4DAlQM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;2DAuPd;;kEASV,6LAAC;;;;;oEApQK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;oEAkQpB,CAAA,SAAS,uBAAuB,sBAAsB,EAAC;kEAGzD,cAAA,6LAAC;4DAAE,MAAK;;;;;wEAzQJ,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;;;8EAsQpB,6LAAC;;;;;gFA1QC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAsQP;;;;;;8EACb,6LAAC;;;;;gFA3QC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAuQJ;8EAAQ;;;;;;;;;;;;;;;;;kEAG5B,6LAAC;;;;;oEA9QK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;oEA4QpB,CAAA,SAAS,oBAAoB,sBAAsB,EAAC;kEAGtD,cAAA,6LAAC;4DAAE,MAAK;;;;;wEAnRJ,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;;;8EAgRpB,6LAAC;;;;;gFApRC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAgRP;;;;;;8EACb,6LAAC;;;;;gFArRC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAiRJ;8EAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOhC,6LAAC;;;;;oDA5RS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDA0RxB,CAAA,SAAS,0BAA0B,sBAAsB,EAAC;kDAG5D,cAAA,6LAAC;4CAAE,MAAK;;;;;wDAjSA,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DA8RxB,6LAAC;;;;;gEAlSK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DA8RX;;;;;;8DACb,6LAAC;;;;;gEAnSK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DA+RR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,6LAAC;;;;;oDAxSS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDAsSxB,CAAA,SAAS,uBAAuB,sBAAsB,EAAC;kDAGzD,cAAA,6LAAC;4CAAE,MAAK;;;;;wDA7SA,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DA0SxB,6LAAC;;;;;gEA9SK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DA0SX;;;;;;8DACb,6LAAC;;;;;gEA/SK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DA2SR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,6LAAC;;;;;oDApTS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDAkTxB,CAAA,SAAS,+BACL,sBACA,EAAC;kDAGP,cAAA,6LAAC;4CAAE,MAAK;;;;;wDA3TA,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DAwTxB,6LAAC;;;;;gEA5TK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAwTX;;;;;;8DACb,6LAAC;;;;;gEA7TK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAyTR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,6LAAC;;;;;oDAlUS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDAgUxB,CAAA,SAAS,mCACL,sBACA,EAAC;kDAGP,cAAA,6LAAC;4CAAE,MAAK;;;;;wDAzUA,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DAsUxB,6LAAC;;;;;gEA1UK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAsUX;;;;;;8DACb,6LAAC;;;;;gEA3UK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAuUR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,6LAAC;;;;;oDAhVS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDA6Uf,CAAA,SAAS,mBAAmB,sBAAsB,EAAC;kDAE9D,cAAA,6LAAC;4CAAE,MAAK;;;;;wDAnVA,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DAgVxB,6LAAC;;;;;gEApVK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAgVX;;;;;;8DACb,6LAAC;;;;;gEArVK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAiVR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,6LAAC;;;;;oDA1VS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDAwVxB,CAAA,SAAS,4BAA4B,sBAAsB,EAAC;kDAG9D,cAAA,6LAAC;4CAAE,MAAK;;;;;wDA/VA,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DA4VxB,6LAAC;;;;;gEAhWK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DA4VX;;;;;;8DACb,6LAAC;;;;;gEAjWK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DA6VR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,6LAAC;;;;;oDAtWS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;mDAmWf,CAAC,SAAS,EACnB,eAAe,aAAa,qBAAqB,IACjD;;0DAEF,6LAAC;gDACC,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,eAAe;gDACjB;;;;;4DAhXM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;;;kEA8WxB,6LAAC;;;;;oEAlXK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEA8WX;;;;;;kEACb,6LAAC;;;;;oEAnXK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEA+WR;kEAAQ;;;;;;kEACxB,6LAAC;;;;;oEApXK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAgXR;kEACd,cAAA,6LAAC;;;;;wEArXG,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;uEAkXT,CAAC,4BAA4B,EACtC,eAAe,aAAa,kBAAkB,IAC9C;;;;;;;;;;;;;;;;;0DAIR,6LAAC;gDAEC,OAAO;oDACL,SAAS,eAAe,aAAa,UAAU;oDAC/C,YAAY;oDACZ,UAAU;gDACZ;;;;;4DAlYM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;2DAyXd;0DAOV,cAAA,6LAAC;;;;;gEApYK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;gEAkYpB,CAAA,SAAS,oBAAoB,sBAAsB,EAAC;8DAGtD,cAAA,6LAAC;wDAAE,MAAK;;;;;oEAzYJ,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;;;0EAsYpB,6LAAC;;;;;4EA1YC,eAAe,WAAW;4EAI7B,eAAe,MAAM;;;2EAsYP;;;;;;0EACb,6LAAC;;;;;4EA3YC,eAAe,WAAW;4EAI7B,eAAe,MAAM;;;2EAuYJ;0EAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOhC,6LAAC;;;;;oDAlZS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;mDA+Yf,CAAC,SAAS,EACnB,eAAe,iBAAiB,qBAAqB,IACrD;;0DAEF,6LAAC;gDACC,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,eAAe;gDACjB;;;;;4DA5ZM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;;;kEA0ZxB,6LAAC;;;;;oEA9ZK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEA0ZX;;;;;;kEACb,6LAAC;;;;;oEA/ZK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEA2ZR;kEAAQ;;;;;;kEACxB,6LAAC;;;;;oEAhaK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEA4ZR;kEACd,cAAA,6LAAC;;;;;wEAjaG,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;uEA8ZT,CAAC,4BAA4B,EACtC,eAAe,iBAAiB,kBAAkB,IAClD;;;;;;;;;;;;;;;;;0DAIR,6LAAC;gDAEC,OAAO;oDACL,SAAS,eAAe,iBAAiB,UAAU;oDACnD,YAAY;oDACZ,UAAU;gDACZ;;;;;4DA9aM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;2DAqad;0DAOV,cAAA,6LAAC;;;;;gEAhbK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;gEA8apB,CAAA,SAAS,wBAAwB,sBAAsB,EAAC;8DAG1D,cAAA,6LAAC;wDAAE,MAAK;;;;;oEArbJ,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;;;0EAkbpB,6LAAC;;;;;4EAtbC,eAAe,WAAW;4EAI7B,eAAe,MAAM;;;2EAkbP;;;;;;0EACb,6LAAC;;;;;4EAvbC,eAAe,WAAW;4EAI7B,eAAe,MAAM;;;2EAmbJ;0EAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOhC,6LAAC;;;;;oDA9bS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;mDA2bf,CAAC,SAAS,EACnB,eAAe,kBAAkB,qBAAqB,IACtD;;0DAEF,6LAAC;gDACC,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,eAAe;gDACjB;;;;;4DAxcM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;;;kEAscxB,6LAAC;;;;;oEA1cK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAscX;;;;;;kEACb,6LAAC;;;;;oEA3cK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAucR;kEAAQ;;;;;;kEACxB,6LAAC;;;;;oEA5cK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAwcR;kEACd,cAAA,6LAAC;;;;;wEA7cG,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;uEA0cT,CAAC,4BAA4B,EACtC,eAAe,kBAAkB,kBAAkB,IACnD;;;;;;;;;;;;;;;;;0DAIR,6LAAC;gDAEC,OAAO;oDACL,SAAS,eAAe,kBAAkB,UAAU;oDACpD,YAAY;oDACZ,UAAU;gDACZ;;;;;4DA1dM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;2DAidd;0DAOV,cAAA,6LAAC;;;;;gEA5dK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;gEA0dpB,CAAA,SAAS,yBAAyB,sBAAsB,EAAC;8DAG3D,cAAA,6LAAC;wDAAE,MAAK;;;;;oEAjeJ,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;;;0EA8dpB,6LAAC;;;;;4EAleC,eAAe,WAAW;4EAI7B,eAAe,MAAM;;;2EA8dP;;;;;;0EACb,6LAAC;;;;;4EAneC,eAAe,WAAW;4EAI7B,eAAe,MAAM;;;2EA+dJ;0EAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOhC,6LAAC;;;;;oDA1eS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDAwexB,CAAA,SAAS,oBAAoB,sBAAsB,EAAC;kDAGtD,cAAA,6LAAC;4CAAE,MAAK;;;;;wDA/eA,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DA4exB,6LAAC;;;;;gEAhfK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DA4eX;;;;;;8DACb,6LAAC;;;;;gEAjfK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DA6eR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,6LAAC;;;;;oDAtfS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDAofxB,CAAA,SAAS,qBAAqB,sBAAsB,EAAC;kDAGvD,cAAA,6LAAC;4CAAE,MAAK;;;;;wDA3fA,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DAwfxB,6LAAC;;;;;gEA5fK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAwfX;;;;;;8DACb,6LAAC;;;;;gEA7fK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAyfR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,6LAAC;;;;;oDAlgBS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDA8fb,CAAA,SAAS,uBAAuB,WAAW,EAAC;kDACzD,cAAA,6LAAC;4CAAE,MAAK;;;;;wDAngBA,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DAggBxB,6LAAC;;;;;gEApgBK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAggBX;;;;;;8DACb,6LAAC;;;;;gEArgBK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAigBR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,6LAAC;;;;;oDA1gBS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;mDAugBf,CAAC,SAAS,EACnB,eAAe,mBAAmB,qBAAqB,IACvD;;0DAEF,6LAAC;gDACC,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,eAAe;gDACjB;;;;;4DAphBM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;;;kEAkhBxB,6LAAC;;;;;oEAthBK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAkhBX;;;;;;kEACb,6LAAC;;;;;oEAvhBK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAmhBR;kEAAQ;;;;;;kEACxB,6LAAC;;;;;oEAxhBK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAohBR;kEACd,cAAA,6LAAC;;;;;wEAzhBG,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;uEAshBT,CAAC,4BAA4B,EACtC,eAAe,mBAAmB,kBAAkB,IACpD;;;;;;;;;;;;;;;;;0DAIR,6LAAC;gDAEC,OAAO;oDACL,SAAS,eAAe,mBAAmB,UAAU;oDACrD,YAAY;oDACZ,UAAU;gDACZ;;;;;4DAtiBM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;2DA6hBd;0DAOV,cAAA,6LAAC;;;;;gEAxiBK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;gEAsiBpB,CAAA,SAAS,0BACL,sBACA,EAAC;8DAGP,cAAA,6LAAC;wDAAE,MAAK;;;;;oEA/iBJ,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;;;0EA4iBpB,6LAAC;;;;;4EAhjBC,eAAe,WAAW;4EAI7B,eAAe,MAAM;;;2EA4iBP;;;;;;0EACb,6LAAC;;;;;4EAjjBC,eAAe,WAAW;4EAI7B,eAAe,MAAM;;;2EA6iBJ;0EAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOhC,6LAAC;;;;;oDAxjBS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDAojBb,CAAA,SAAS,oBAAoB,WAAW,EAAC;kDACtD,cAAA,6LAAC;4CAAE,MAAK;;;;;wDAzjBA,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DAsjBxB,6LAAC;;;;;gEA1jBK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAsjBX;;;;;;8DACb,6LAAC;;;;;gEA3jBK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAujBR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,6LAAC;;;;;oDAhkBS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;mDA6jBf,CAAC,SAAS,EACnB,eAAe,SAAS,qBAAqB,IAC7C;;0DAEF,6LAAC;gDACC,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,eAAe;gDACjB;;;;;4DA1kBM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;;;kEAwkBxB,6LAAC;;;;;oEA5kBK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAwkBX;;;;;;kEACb,6LAAC;;;;;oEA7kBK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAykBR;kEAAQ;;;;;;kEACxB,6LAAC;;;;;oEA9kBK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEA0kBR;kEACd,cAAA,6LAAC;;;;;wEA/kBG,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;uEA4kBT,CAAC,4BAA4B,EACtC,eAAe,SAAS,kBAAkB,IAC1C;;;;;;;;;;;;;;;;;0DAIR,6LAAC;gDAEC,OAAO;oDACL,SAAS,eAAe,SAAS,UAAU;oDAC3C,YAAY;oDACZ,UAAU;gDACZ;;;;;4DA5lBM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;2DAmlBd;;kEAOV,6LAAC;;;;;oEA9lBK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;oEA4lBpB,CAAA,SAAS,qBAAqB,sBAAsB,EAAC;kEAGvD,cAAA,6LAAC;4DAAE,MAAK;;;;;wEAnmBJ,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;;;8EAgmBpB,6LAAC;;;;;gFApmBC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAgmBP;;;;;;8EACb,6LAAC;;;;;gFArmBC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAimBJ;8EAAQ;;;;;;;;;;;;;;;;;kEAG5B,6LAAC;;;;;oEAxmBK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;oEAsmBpB,CAAA,SAAS,2BACL,sBACA,EAAC;kEAGP,cAAA,6LAAC;4DAAE,MAAK;;;;;wEA/mBJ,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;;;8EA4mBpB,6LAAC;;;;;gFAhnBC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EA4mBP;;;;;;8EACb,6LAAC;;;;;gFAjnBC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EA6mBJ;8EAAQ;;;;;;;;;;;;;;;;;kEAG5B,6LAAC;;;;;oEApnBK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;oEAknBpB,CAAA,SAAS,4BACL,sBACA,EAAC;kEAGP,cAAA,6LAAC;4DAAE,MAAK;;;;;wEA3nBJ,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;;;8EAwnBpB,6LAAC;;;;;gFA5nBC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAwnBP;;;;;;8EACb,6LAAC;;;;;gFA7nBC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAynBJ;8EAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOhC,6LAAC;;;;;oDApoBS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;;kDAioB1B,cAAA,6LAAC;4CACC,MAAK;4CACL,OAAM;4CACN,SAAS,CAAC;gDACR,EAAE,cAAc;gDAChB;4CACF;;;;;wDA3oBM,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DAyoBxB,6LAAC;;;;;gEA7oBK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAyoBX;;;;;;8DACb,6LAAC;;;;;gEA9oBK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DA0oBR;8DAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQlC,6LAAC;;;;;oCAtpBe,eAAe,WAAW;oCAI7B,eAAe,MAAM;;;mCAkpBnB;kCAEb,cAAA,6LAAC;;;;;wCAxpBa,eAAe,WAAW;wCAI7B,eAAe,MAAM;;;uCAopBb;;gCAChB,uBACC,6LAAC;oCAEC,OAAO;wCACL,QAAQ;wCACR,OAAO;wCACP,UAAU;wCACV,YAAY;oCACd;;;;;gDAjqBQ,eAAe,WAAW;gDAI7B,eAAe,MAAM;;;+CAupBhB;8CAQT;;;;;;8CAIL,6LAAC;;;;;gDAvqBW,eAAe,WAAW;gDAI7B,eAAe,MAAM;;;+CAmqBf;8CACb,cAAA,6LAAC;;;;;oDAxqBS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;mDAoqBb;kDAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1C;GAzvBwB;;QAIP,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KALN", "debugId": null}}, {"offset": {"line": 3252, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/src/app/admin/permissions/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport AdminLTELayout from '@/components/layout/AdminLTELayout';\n\n// Dummy permissions data that matches Laravel structure\nconst permissions = [\n  { id: 1, name: 'users_manage', display_name: 'User Management' },\n  { id: 2, name: 'Permission', display_name: 'Permission' },\n  { id: 3, name: 'role', display_name: 'Role' },\n  { id: 4, name: 'teacher_manage', display_name: 'Teacher Management' },\n  { id: 5, name: 'student_management', display_name: 'Student Management' },\n  { id: 6, name: 'student_class', display_name: 'Student Class' },\n  { id: 7, name: 'category', display_name: 'Category' },\n  { id: 8, name: 'student_apply_for_demo', display_name: 'Student Apply For Demo' },\n  { id: 9, name: 'assignments', display_name: 'Assignments' },\n  { id: 10, name: 'quiz', display_name: 'Quiz' },\n  { id: 11, name: 'liveclasses', display_name: 'Live Classes' },\n  { id: 12, name: 'community_posts', display_name: 'Community Posts' },\n  { id: 13, name: 'notifications', display_name: 'Notifications' },\n  { id: 14, name: 'reports', display_name: 'Reports' },\n  { id: 15, name: 'dashboard', display_name: 'Dashboard' },\n];\n\nexport default function PermissionsPage() {\n  return (\n    <AdminLTELayout>\n      <h3 className=\"page-title\">Permissions</h3>\n      <p style={{ display: 'none' }}>\n        <a href=\"/admin/permissions/create\" className=\"btn btn-success\">Add New</a>\n      </p>\n\n      <div className=\"panel panel-default\">\n        <div style={{ fontSize: '20px' }} className=\"panel-heading\">\n          List\n        </div>\n\n        <div className=\"panel-body scroll-table table-responsive\">\n          <table className={`table table-bordered table-striped ${permissions.length > 0 ? 'datatable1' : ''} dt-select`}>\n            <thead>\n              <tr>\n                <th style={{ fontSize: '15px' }}>Sr.No</th>\n                <th style={{ fontSize: '15px' }}>Name</th>\n                <th style={{ fontSize: '15px' }}>Display Name</th>\n                <th style={{ display: 'none' }}>&nbsp;</th>\n              </tr>\n            </thead>\n            \n            <tbody>\n              {permissions.length > 0 ? (\n                permissions.map((permission, index) => (\n                  <tr key={permission.id} data-entry-id={permission.id}>\n                    <td>{index + 1}</td>\n                    <td>{permission.name}</td>\n                    <td>{permission.display_name}</td>\n                    \n                    <td style={{ display: 'none' }}>\n                      <a href={`/admin/permissions/edit/${permission.id}`} className=\"btn btn-xs btn-info\">\n                        Edit\n                      </a>\n                    </td>\n                  </tr>\n                ))\n              ) : (\n                <tr>\n                  <td colSpan={3}>No entries in table</td>\n                </tr>\n              )}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </AdminLTELayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,wDAAwD;AACxD,MAAM,cAAc;IAClB;QAAE,IAAI;QAAG,MAAM;QAAgB,cAAc;IAAkB;IAC/D;QAAE,IAAI;QAAG,MAAM;QAAc,cAAc;IAAa;IACxD;QAAE,IAAI;QAAG,MAAM;QAAQ,cAAc;IAAO;IAC5C;QAAE,IAAI;QAAG,MAAM;QAAkB,cAAc;IAAqB;IACpE;QAAE,IAAI;QAAG,MAAM;QAAsB,cAAc;IAAqB;IACxE;QAAE,IAAI;QAAG,MAAM;QAAiB,cAAc;IAAgB;IAC9D;QAAE,IAAI;QAAG,MAAM;QAAY,cAAc;IAAW;IACpD;QAAE,IAAI;QAAG,MAAM;QAA0B,cAAc;IAAyB;IAChF;QAAE,IAAI;QAAG,MAAM;QAAe,cAAc;IAAc;IAC1D;QAAE,IAAI;QAAI,MAAM;QAAQ,cAAc;IAAO;IAC7C;QAAE,IAAI;QAAI,MAAM;QAAe,cAAc;IAAe;IAC5D;QAAE,IAAI;QAAI,MAAM;QAAmB,cAAc;IAAkB;IACnE;QAAE,IAAI;QAAI,MAAM;QAAiB,cAAc;IAAgB;IAC/D;QAAE,IAAI;QAAI,MAAM;QAAW,cAAc;IAAU;IACnD;QAAE,IAAI;QAAI,MAAM;QAAa,cAAc;IAAY;CACxD;AAEc,SAAS;IACtB,qBACE,6LAAC,iJAAA,CAAA,UAAc;;0BACb,6LAAC;gBAAG,WAAU;0BAAa;;;;;;0BAC3B,6LAAC;gBAAE,OAAO;oBAAE,SAAS;gBAAO;0BAC1B,cAAA,6LAAC;oBAAE,MAAK;oBAA4B,WAAU;8BAAkB;;;;;;;;;;;0BAGlE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,OAAO;4BAAE,UAAU;wBAAO;wBAAG,WAAU;kCAAgB;;;;;;kCAI5D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAW,CAAC,mCAAmC,EAAE,YAAY,MAAM,GAAG,IAAI,eAAe,GAAG,UAAU,CAAC;;8CAC5G,6LAAC;8CACC,cAAA,6LAAC;;0DACC,6LAAC;gDAAG,OAAO;oDAAE,UAAU;gDAAO;0DAAG;;;;;;0DACjC,6LAAC;gDAAG,OAAO;oDAAE,UAAU;gDAAO;0DAAG;;;;;;0DACjC,6LAAC;gDAAG,OAAO;oDAAE,UAAU;gDAAO;0DAAG;;;;;;0DACjC,6LAAC;gDAAG,OAAO;oDAAE,SAAS;gDAAO;0DAAG;;;;;;;;;;;;;;;;;8CAIpC,6LAAC;8CACE,YAAY,MAAM,GAAG,IACpB,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC3B,6LAAC;4CAAuB,iBAAe,WAAW,EAAE;;8DAClD,6LAAC;8DAAI,QAAQ;;;;;;8DACb,6LAAC;8DAAI,WAAW,IAAI;;;;;;8DACpB,6LAAC;8DAAI,WAAW,YAAY;;;;;;8DAE5B,6LAAC;oDAAG,OAAO;wDAAE,SAAS;oDAAO;8DAC3B,cAAA,6LAAC;wDAAE,MAAM,CAAC,wBAAwB,EAAE,WAAW,EAAE,EAAE;wDAAE,WAAU;kEAAsB;;;;;;;;;;;;2CANhF,WAAW,EAAE;;;;kEAaxB,6LAAC;kDACC,cAAA,6LAAC;4CAAG,SAAS;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlC;KAlDwB", "debugId": null}}]}