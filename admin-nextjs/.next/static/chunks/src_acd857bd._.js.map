{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/src/theme/theme.ts"], "sourcesContent": ["'use client';\n\nimport { createTheme } from '@mui/material/styles';\n\n// Create a theme that matches the Laravel admin design\nconst theme = createTheme({\n  palette: {\n    mode: 'light',\n    primary: {\n      main: '#3c8dbc', // AdminLTE blue color\n      light: '#5ba7d6',\n      dark: '#2a6496',\n      contrastText: '#ffffff',\n    },\n    secondary: {\n      main: '#f39c12', // AdminLTE orange\n      light: '#f5b041',\n      dark: '#d68910',\n      contrastText: '#ffffff',\n    },\n    background: {\n      default: '#ecf0f5', // AdminLTE background\n      paper: '#ffffff',\n    },\n    text: {\n      primary: '#444444',\n      secondary: '#666666',\n    },\n    error: {\n      main: '#dd4b39', // AdminLTE red\n    },\n    warning: {\n      main: '#f39c12', // AdminLTE orange\n    },\n    info: {\n      main: '#00c0ef', // AdminLTE light blue\n    },\n    success: {\n      main: '#00a65a', // AdminLTE green\n    },\n  },\n  typography: {\n    fontFamily: '\"Source Sans Pro\", \"Helvetica Neue\", Helvetica, Arial, sans-serif',\n    h1: {\n      fontSize: '2.125rem',\n      fontWeight: 400,\n      lineHeight: 1.2,\n    },\n    h2: {\n      fontSize: '1.875rem',\n      fontWeight: 400,\n      lineHeight: 1.2,\n    },\n    h3: {\n      fontSize: '1.5rem',\n      fontWeight: 400,\n      lineHeight: 1.2,\n    },\n    h4: {\n      fontSize: '1.25rem',\n      fontWeight: 400,\n      lineHeight: 1.2,\n    },\n    h5: {\n      fontSize: '1.125rem',\n      fontWeight: 400,\n      lineHeight: 1.2,\n    },\n    h6: {\n      fontSize: '1rem',\n      fontWeight: 400,\n      lineHeight: 1.2,\n    },\n    body1: {\n      fontSize: '14px',\n      lineHeight: 1.42857143,\n    },\n    body2: {\n      fontSize: '12px',\n      lineHeight: 1.42857143,\n    },\n  },\n  components: {\n    MuiCssBaseline: {\n      styleOverrides: {\n        body: {\n          backgroundColor: '#ecf0f5',\n          fontFamily: '\"Source Sans Pro\", \"Helvetica Neue\", Helvetica, Arial, sans-serif',\n          fontSize: '14px',\n          lineHeight: 1.42857143,\n          color: '#444444',\n        },\n      },\n    },\n    MuiAppBar: {\n      styleOverrides: {\n        root: {\n          backgroundColor: '#3c8dbc',\n          boxShadow: 'none',\n          borderBottom: '1px solid #d2d6de',\n        },\n      },\n    },\n    MuiDrawer: {\n      styleOverrides: {\n        paper: {\n          backgroundColor: '#222d32',\n          color: '#b8c7ce',\n          width: 230,\n        },\n      },\n    },\n    MuiListItem: {\n      styleOverrides: {\n        root: {\n          '&:hover': {\n            backgroundColor: '#1e282c',\n          },\n          '&.Mui-selected': {\n            backgroundColor: '#1e282c',\n            borderLeft: '3px solid #3c8dbc',\n          },\n        },\n      },\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          borderRadius: '3px',\n        },\n        containedPrimary: {\n          backgroundColor: '#3c8dbc',\n          '&:hover': {\n            backgroundColor: '#2a6496',\n          },\n        },\n      },\n    },\n    MuiPaper: {\n      styleOverrides: {\n        root: {\n          borderRadius: '3px',\n          boxShadow: '0 1px 1px rgba(0,0,0,0.1)',\n        },\n      },\n    },\n    MuiTableHead: {\n      styleOverrides: {\n        root: {\n          backgroundColor: '#f4f4f4',\n        },\n      },\n    },\n  },\n});\n\nexport default theme;\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAIA,uDAAuD;AACvD,MAAM,QAAQ,CAAA,GAAA,8MAAA,CAAA,cAAW,AAAD,EAAE;IACxB,SAAS;QACP,MAAM;QACN,SAAS;YACP,MAAM;YACN,OAAO;YACP,MAAM;YACN,cAAc;QAChB;QACA,WAAW;YACT,MAAM;YACN,OAAO;YACP,MAAM;YACN,cAAc;QAChB;QACA,YAAY;YACV,SAAS;YACT,OAAO;QACT;QACA,MAAM;YACJ,SAAS;YACT,WAAW;QACb;QACA,OAAO;YACL,MAAM;QACR;QACA,SAAS;YACP,MAAM;QACR;QACA,MAAM;YACJ,MAAM;QACR;QACA,SAAS;YACP,MAAM;QACR;IACF;IACA,YAAY;QACV,YAAY;QACZ,IAAI;YACF,UAAU;YACV,YAAY;YACZ,YAAY;QACd;QACA,IAAI;YACF,UAAU;YACV,YAAY;YACZ,YAAY;QACd;QACA,IAAI;YACF,UAAU;YACV,YAAY;YACZ,YAAY;QACd;QACA,IAAI;YACF,UAAU;YACV,YAAY;YACZ,YAAY;QACd;QACA,IAAI;YACF,UAAU;YACV,YAAY;YACZ,YAAY;QACd;QACA,IAAI;YACF,UAAU;YACV,YAAY;YACZ,YAAY;QACd;QACA,OAAO;YACL,UAAU;YACV,YAAY;QACd;QACA,OAAO;YACL,UAAU;YACV,YAAY;QACd;IACF;IACA,YAAY;QACV,gBAAgB;YACd,gBAAgB;gBACd,MAAM;oBACJ,iBAAiB;oBACjB,YAAY;oBACZ,UAAU;oBACV,YAAY;oBACZ,OAAO;gBACT;YACF;QACF;QACA,WAAW;YACT,gBAAgB;gBACd,MAAM;oBACJ,iBAAiB;oBACjB,WAAW;oBACX,cAAc;gBAChB;YACF;QACF;QACA,WAAW;YACT,gBAAgB;gBACd,OAAO;oBACL,iBAAiB;oBACjB,OAAO;oBACP,OAAO;gBACT;YACF;QACF;QACA,aAAa;YACX,gBAAgB;gBACd,MAAM;oBACJ,WAAW;wBACT,iBAAiB;oBACnB;oBACA,kBAAkB;wBAChB,iBAAiB;wBACjB,YAAY;oBACd;gBACF;YACF;QACF;QACA,WAAW;YACT,gBAAgB;gBACd,MAAM;oBACJ,eAAe;oBACf,cAAc;gBAChB;gBACA,kBAAkB;oBAChB,iBAAiB;oBACjB,WAAW;wBACT,iBAAiB;oBACnB;gBACF;YACF;QACF;QACA,UAAU;YACR,gBAAgB;gBACd,MAAM;oBACJ,cAAc;oBACd,WAAW;gBACb;YACF;QACF;QACA,cAAc;YACZ,gBAAgB;gBACd,MAAM;oBACJ,iBAAiB;gBACnB;YACF;QACF;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/src/components/ThemeProvider.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { ThemeProvider as MuiThemeProvider } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport theme from '@/theme/theme';\n\ninterface ThemeProviderProps {\n  children: React.ReactNode;\n}\n\nexport default function ThemeProvider({ children }: ThemeProviderProps) {\n  return (\n    <MuiThemeProvider theme={theme}>\n      <CssBaseline />\n      {children}\n    </MuiThemeProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAWe,SAAS,cAAc,EAAE,QAAQ,EAAsB;IACpE,qBACE,6LAAC,kNAAA,CAAA,gBAAgB;QAAC,OAAO,wHAAA,CAAA,UAAK;;0BAC5B,6LAAC,yKAAA,CAAA,UAAW;;;;;YACX;;;;;;;AAGP;KAPwB", "debugId": null}}]}