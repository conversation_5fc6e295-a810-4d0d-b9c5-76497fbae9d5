{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\n\nexport default function Home() {\n  const router = useRouter();\n\n  useEffect(() => {\n    // Check if user is authenticated\n    const token = localStorage.getItem(\"auth_token\");\n\n    if (token) {\n      // Redirect to admin dashboard if authenticated\n      router.push(\"/admin/dashboard\");\n    } else {\n      // Redirect to login if not authenticated\n      router.push(\"/login\");\n    }\n  }, [router]);\n\n  return (\n    <div\n      style={{\n        display: \"flex\",\n        justifyContent: \"center\",\n        alignItems: \"center\",\n        height: \"100vh\",\n        backgroundColor: \"#ecf0f5\",\n      }}\n    >\n      <div>Loading...</div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,iCAAiC;YACjC,MAAM,QAAQ,aAAa,OAAO,CAAC;YAEnC,IAAI,OAAO;gBACT,+CAA+C;gBAC/C,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,yCAAyC;gBACzC,OAAO,IAAI,CAAC;YACd;QACF;yBAAG;QAAC;KAAO;IAEX,qBACE,6LAAC;QACC,OAAO;YACL,SAAS;YACT,gBAAgB;YAChB,YAAY;YACZ,QAAQ;YACR,iBAAiB;QACnB;kBAEA,cAAA,6LAAC;sBAAI;;;;;;;;;;;AAGX;GA7BwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}