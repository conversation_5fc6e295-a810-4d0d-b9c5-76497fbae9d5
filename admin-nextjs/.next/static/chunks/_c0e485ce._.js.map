{"version": 3, "sources": [], "sections": [{"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/src/app/admin/users/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport AdminLTELayout from '@/components/layout/AdminLTELayout';\nimport { adminUsers } from '@/data/dummyData';\n\n// Extended dummy users data\nconst users = [\n  {\n    id: 1,\n    name: 'Super Admin',\n    email: '<EMAIL>',\n    role: 'Admin',\n    status: 1,\n    created_at: '2024-01-01T10:00:00Z',\n    role_id: 1\n  },\n  {\n    id: 2,\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    role: 'Admin',\n    status: 1,\n    created_at: '2024-01-15T10:00:00Z',\n    role_id: 1\n  },\n  {\n    id: 3,\n    name: 'Sarah Manager',\n    email: '<EMAIL>',\n    role: 'Director',\n    status: 1,\n    created_at: '2024-02-01T10:00:00Z',\n    role_id: 5\n  },\n  {\n    id: 4,\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    role: 'Course Builder',\n    status: 0,\n    created_at: '2024-02-10T10:00:00Z',\n    role_id: 4\n  }\n];\n\nexport default function UsersPage() {\n  const [userList, setUserList] = useState(users);\n\n  const handleStatusToggle = (userId: number, currentStatus: number) => {\n    const newStatus = currentStatus === 1 ? 0 : 1;\n    setUserList(prev => \n      prev.map(user => \n        user.id === userId ? { ...user, status: newStatus } : user\n      )\n    );\n  };\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-GB', {\n      day: '2-digit',\n      month: 'short',\n      year: '2-digit',\n      hour: '2-digit',\n      minute: '2-digit',\n      hour12: true\n    });\n  };\n\n  return (\n    <AdminLTELayout>\n      <div className=\"admn\">\n        <h3 className=\"page-title\">Administrator Users</h3>\n        \n        <p>\n          <a href=\"/admin/users/create\" className=\"btn btn-success\">Add New</a>\n        </p>\n      </div>\n\n      <div className=\"panel panel-default\">\n        <div style={{ fontSize: '20px' }} className=\"panel-heading\">\n          List\n        </div>\n\n        <div className=\"panel-body scroll-table table-responsive\">\n          <table className={`table table-bordered table-striped ${userList.length > 0 ? 'datatable1' : ''} dt-select`}>\n            <thead>\n              <tr>\n                <th style={{ fontSize: '15px' }}>Sr.No</th>\n                <th style={{ fontSize: '15px' }}>Name</th>\n                <th style={{ fontSize: '15px' }}>Email</th>\n                <th style={{ fontSize: '15px' }}>Role</th>\n                <th style={{ fontSize: '15px' }}>Status</th>\n                <th style={{ fontSize: '15px' }}>Created At</th>\n                <th style={{ fontSize: '15px' }}>Action</th>\n              </tr>\n            </thead>\n            \n            <tbody>\n              {userList.length > 0 ? (\n                userList.map((user, index) => (\n                  <tr key={user.id} data-entry-id={user.id}>\n                    <td>{index + 1}</td>\n                    <td>{user.name}</td>\n                    <td>{user.email}</td>\n                    <td>{user.role}</td>\n                    <td>\n                      {user.status === 1 ? (\n                        <a \n                          href=\"#\" \n                          className=\"label label-success\"\n                          onClick={(e) => {\n                            e.preventDefault();\n                            handleStatusToggle(user.id, user.status);\n                          }}\n                        >\n                          Active\n                        </a>\n                      ) : (\n                        <a \n                          href=\"#\" \n                          className=\"label label-danger\"\n                          onClick={(e) => {\n                            e.preventDefault();\n                            handleStatusToggle(user.id, user.status);\n                          }}\n                        >\n                          Inactive\n                        </a>\n                      )}\n                    </td>\n                    <td>{formatDate(user.created_at)}</td>\n                    <td>\n                      <a href={`/admin/users/edit/${user.id}`} className=\"btn btn-xs btn-info\">\n                        Edit\n                      </a>\n                      {' '}\n                      <a href={`/admin/users/view/${user.id}`} className=\"btn btn-xs btn-primary\">\n                        View\n                      </a>\n                      {user.role_id !== 1 && (\n                        <>\n                          {' '}\n                          <button \n                            className=\"btn btn-xs btn-danger\"\n                            onClick={() => {\n                              if (confirm('Are you sure you want to delete this user?')) {\n                                setUserList(prev => prev.filter(u => u.id !== user.id));\n                              }\n                            }}\n                          >\n                            Delete\n                          </button>\n                        </>\n                      )}\n                    </td>\n                  </tr>\n                ))\n              ) : (\n                <tr>\n                  <td colSpan={7}>No entries in table</td>\n                </tr>\n              )}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </AdminLTELayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAMA,4BAA4B;AAC5B,MAAM,QAAQ;IACZ;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,SAAS;IACX;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,qBAAqB,CAAC,QAAgB;QAC1C,MAAM,YAAY,kBAAkB,IAAI,IAAI;QAC5C,YAAY,CAAA,OACV,KAAK,GAAG,CAAC,CAAA,OACP,KAAK,EAAE,KAAK,SAAS;oBAAE,GAAG,IAAI;oBAAE,QAAQ;gBAAU,IAAI;IAG5D;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,KAAK;YACL,OAAO;YACP,MAAM;YACN,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,qBACE,6LAAC,iJAAA,CAAA,UAAc;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAa;;;;;;kCAE3B,6LAAC;kCACC,cAAA,6LAAC;4BAAE,MAAK;4BAAsB,WAAU;sCAAkB;;;;;;;;;;;;;;;;;0BAI9D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,OAAO;4BAAE,UAAU;wBAAO;wBAAG,WAAU;kCAAgB;;;;;;kCAI5D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAW,CAAC,mCAAmC,EAAE,SAAS,MAAM,GAAG,IAAI,eAAe,GAAG,UAAU,CAAC;;8CACzG,6LAAC;8CACC,cAAA,6LAAC;;0DACC,6LAAC;gDAAG,OAAO;oDAAE,UAAU;gDAAO;0DAAG;;;;;;0DACjC,6LAAC;gDAAG,OAAO;oDAAE,UAAU;gDAAO;0DAAG;;;;;;0DACjC,6LAAC;gDAAG,OAAO;oDAAE,UAAU;gDAAO;0DAAG;;;;;;0DACjC,6LAAC;gDAAG,OAAO;oDAAE,UAAU;gDAAO;0DAAG;;;;;;0DACjC,6LAAC;gDAAG,OAAO;oDAAE,UAAU;gDAAO;0DAAG;;;;;;0DACjC,6LAAC;gDAAG,OAAO;oDAAE,UAAU;gDAAO;0DAAG;;;;;;0DACjC,6LAAC;gDAAG,OAAO;oDAAE,UAAU;gDAAO;0DAAG;;;;;;;;;;;;;;;;;8CAIrC,6LAAC;8CACE,SAAS,MAAM,GAAG,IACjB,SAAS,GAAG,CAAC,CAAC,MAAM,sBAClB,6LAAC;4CAAiB,iBAAe,KAAK,EAAE;;8DACtC,6LAAC;8DAAI,QAAQ;;;;;;8DACb,6LAAC;8DAAI,KAAK,IAAI;;;;;;8DACd,6LAAC;8DAAI,KAAK,KAAK;;;;;;8DACf,6LAAC;8DAAI,KAAK,IAAI;;;;;;8DACd,6LAAC;8DACE,KAAK,MAAM,KAAK,kBACf,6LAAC;wDACC,MAAK;wDACL,WAAU;wDACV,SAAS,CAAC;4DACR,EAAE,cAAc;4DAChB,mBAAmB,KAAK,EAAE,EAAE,KAAK,MAAM;wDACzC;kEACD;;;;;6EAID,6LAAC;wDACC,MAAK;wDACL,WAAU;wDACV,SAAS,CAAC;4DACR,EAAE,cAAc;4DAChB,mBAAmB,KAAK,EAAE,EAAE,KAAK,MAAM;wDACzC;kEACD;;;;;;;;;;;8DAKL,6LAAC;8DAAI,WAAW,KAAK,UAAU;;;;;;8DAC/B,6LAAC;;sEACC,6LAAC;4DAAE,MAAM,CAAC,kBAAkB,EAAE,KAAK,EAAE,EAAE;4DAAE,WAAU;sEAAsB;;;;;;wDAGxE;sEACD,6LAAC;4DAAE,MAAM,CAAC,kBAAkB,EAAE,KAAK,EAAE,EAAE;4DAAE,WAAU;sEAAyB;;;;;;wDAG3E,KAAK,OAAO,KAAK,mBAChB;;gEACG;8EACD,6LAAC;oEACC,WAAU;oEACV,SAAS;wEACP,IAAI,QAAQ,+CAA+C;4EACzD,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;wEACvD;oEACF;8EACD;;;;;;;;;;;;;;;2CAjDA,KAAK,EAAE;;;;kEA0DlB,6LAAC;kDACC,cAAA,6LAAC;4CAAG,SAAS;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlC;GA5HwB;KAAA", "debugId": null}}, {"offset": {"line": 399, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}