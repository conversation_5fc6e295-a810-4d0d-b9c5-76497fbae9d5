{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/src/components/layout/AdminLTELayout.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { useRouter, usePathname } from \"next/navigation\";\n\ninterface AdminLTELayoutProps {\n  children: React.ReactNode;\n  title?: string;\n}\n\nexport default function AdminLTELayout({\n  children,\n  title,\n}: AdminLTELayoutProps) {\n  const router = useRouter();\n  const pathname = usePathname();\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const [openTreeviews, setOpenTreeviews] = useState<string[]>([]);\n\n  const handleSidebarToggle = () => {\n    setSidebarCollapsed(!sidebarCollapsed);\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem(\"auth_token\");\n    localStorage.removeItem(\"user_role\");\n    router.push(\"/login\");\n  };\n\n  const isActive = (path: string) => {\n    return pathname === path;\n  };\n\n  const toggleTreeview = (id: string) => {\n    setOpenTreeviews((prev) =>\n      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]\n    );\n  };\n\n  const isTreeviewOpen = (id: string) => {\n    return openTreeviews.includes(id);\n  };\n\n  // Initialize treeview states based on current path\n  useEffect(() => {\n    if (\n      pathname.includes(\"/admin/permissions\") ||\n      pathname.includes(\"/admin/roles\") ||\n      pathname.includes(\"/admin/users\")\n    ) {\n      setOpenTreeviews((prev) => [...prev, \"user-management\"]);\n    }\n    if (\n      pathname.includes(\"/admin/classes\") ||\n      pathname.includes(\"/admin/subjects\")\n    ) {\n      setOpenTreeviews((prev) => [...prev, \"course-management\"]);\n    }\n    if (\n      pathname.includes(\"/admin/categories\") ||\n      pathname.includes(\"/admin/courses\")\n    ) {\n      setOpenTreeviews((prev) => [...prev, \"course-category\"]);\n    }\n    if (\n      pathname.includes(\"/admin/about-us\") ||\n      pathname.includes(\"/admin/privacy-policy\") ||\n      pathname.includes(\"/admin/terms-condition\")\n    ) {\n      setOpenTreeviews((prev) => [...prev, \"cms\"]);\n    }\n  }, [pathname]);\n\n  return (\n    <div\n      className={`wrapper skin-blue ${\n        sidebarCollapsed ? \"sidebar-collapse\" : \"\"\n      }`}\n    >\n      {/* Main Header */}\n      <header className=\"main-header\">\n        {/* Logo */}\n        <a\n          href=\"#\"\n          className=\"logo\"\n          style={{ fontSize: \"16px\", color: \"#fff\", textDecoration: \"none\" }}\n        >\n          <b>Admin</b>LTE\n        </a>\n\n        {/* Header Navbar */}\n        <nav\n          className=\"navbar navbar-static-top\"\n          style={{ display: \"block\", padding: 0 }}\n        >\n          {/* Sidebar toggle button */}\n          <a\n            href=\"#\"\n            className=\"sidebar-toggle\"\n            onClick={(e) => {\n              e.preventDefault();\n              handleSidebarToggle();\n            }}\n            role=\"button\"\n          >\n            <span className=\"sr-only\">Toggle navigation</span>\n            <span className=\"icon-bar\"></span>\n            <span className=\"icon-bar\"></span>\n            <span className=\"icon-bar\"></span>\n          </a>\n        </nav>\n      </header>\n\n      {/* Left side column. contains the sidebar */}\n      <aside className=\"main-sidebar\">\n        {/* sidebar: style can be found in sidebar.less */}\n        <section className=\"sidebar\">\n          <ul className=\"sidebar-menu scroll-table scroll-table-two\">\n            {/* Dashboard */}\n            <li className={isActive(\"/admin/dashboard\") ? \"active\" : \"\"}>\n              <a href=\"/admin/dashboard\">\n                <i className=\"fa fa-dashboard\"></i>\n                <span className=\"title\">Dashboard</span>\n              </a>\n            </li>\n\n            {/* User Management */}\n            <li\n              className={`treeview ${\n                isTreeviewOpen(\"user-management\") ? \"active menu-open\" : \"\"\n              }`}\n            >\n              <a\n                href=\"#\"\n                onClick={(e) => {\n                  e.preventDefault();\n                  toggleTreeview(\"user-management\");\n                }}\n              >\n                <i className=\"fa fa-users\"></i>\n                <span className=\"title\">User Management</span>\n                <span className=\"pull-right-container\">\n                  <i\n                    className={`fa fa-angle-left pull-right ${\n                      isTreeviewOpen(\"user-management\") ? \"fa-angle-down\" : \"\"\n                    }`}\n                  ></i>\n                </span>\n              </a>\n              <ul\n                className=\"treeview-menu\"\n                style={{\n                  display: isTreeviewOpen(\"user-management\") ? \"block\" : \"none\",\n                }}\n              >\n                <li\n                  className={\n                    isActive(\"/admin/permissions\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/permissions\">\n                    <i className=\"fa fa-briefcase\"></i>\n                    <span className=\"title\">Permissions</span>\n                  </a>\n                </li>\n                <li\n                  className={\n                    isActive(\"/admin/roles\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/roles\">\n                    <i className=\"fa fa-briefcase\"></i>\n                    <span className=\"title\">Roles</span>\n                  </a>\n                </li>\n                <li\n                  className={\n                    isActive(\"/admin/users\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/users\">\n                    <i className=\"fa fa-user\"></i>\n                    <span className=\"title\">Administrator Users</span>\n                  </a>\n                </li>\n              </ul>\n            </li>\n\n            {/* Fees Update */}\n            <li className={isActive(\"/admin/fees\") ? \"active active-sub\" : \"\"}>\n              <a href=\"/admin/fees\">\n                <i className=\"fa fa-briefcase\"></i>\n                <span className=\"title\">Fees Update</span>\n              </a>\n            </li>\n\n            {/* Teacher Management */}\n            <li\n              className={isActive(\"/admin/teachers\") ? \"active active-sub\" : \"\"}\n            >\n              <a href=\"/admin/teachers\">\n                <i className=\"fa fa-user\"></i>\n                <span className=\"title\">Teacher Management</span>\n              </a>\n            </li>\n\n            {/* Student Management */}\n            <li\n              className={isActive(\"/admin/students\") ? \"active active-sub\" : \"\"}\n            >\n              <a href=\"/admin/students\">\n                <i className=\"fa fa-user\"></i>\n                <span className=\"title\">Student Management</span>\n              </a>\n            </li>\n\n            {/* Course Management */}\n            <li className=\"treeview\">\n              <a href=\"#\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Course Management</span>\n                <span className=\"pull-right-container\">\n                  <i className=\"fa fa-angle-left pull-right\"></i>\n                </span>\n              </a>\n              <ul className=\"treeview-menu\">\n                <li\n                  className={\n                    isActive(\"/admin/classes\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/classes\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">Class Details</span>\n                  </a>\n                </li>\n                <li\n                  className={\n                    isActive(\"/admin/subjects\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/subjects\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">Subjects Details</span>\n                  </a>\n                </li>\n              </ul>\n            </li>\n\n            {/* Course & Category */}\n            <li className=\"treeview\">\n              <a href=\"#\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Course & Category</span>\n                <span className=\"pull-right-container\">\n                  <i className=\"fa fa-angle-left pull-right\"></i>\n                </span>\n              </a>\n              <ul className=\"treeview-menu\">\n                <li\n                  className={\n                    isActive(\"/admin/categories\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/categories\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">Category Details</span>\n                  </a>\n                </li>\n                <li\n                  className={\n                    isActive(\"/admin/courses\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/courses\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">Course Details</span>\n                  </a>\n                </li>\n              </ul>\n            </li>\n\n            {/* Demo Request */}\n            <li\n              className={\n                isActive(\"/admin/demo-requests\") ? \"active active-sub\" : \"\"\n              }\n            >\n              <a href=\"/admin/demo-requests\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Demo Request</span>\n              </a>\n            </li>\n\n            {/* Withdrawal */}\n            <li\n              className={\n                isActive(\"/admin/withdrawal\") ? \"active active-sub\" : \"\"\n              }\n            >\n              <a href=\"/admin/withdrawal\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Withdrawal</span>\n              </a>\n            </li>\n\n            {/* Incomplete Classes */}\n            <li\n              className={\n                isActive(\"/admin/incomplete-classes\") ? \"active active-sub\" : \"\"\n              }\n            >\n              <a href=\"/admin/incomplete-classes\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Incomplete Classes</span>\n              </a>\n            </li>\n\n            {/* Student Purchase Class */}\n            <li\n              className={\n                isActive(\"/admin/student-purchase-class\")\n                  ? \"active active-sub\"\n                  : \"\"\n              }\n            >\n              <a href=\"/admin/student-purchase-class\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Student Purchase Class</span>\n              </a>\n            </li>\n\n            {/* Offers */}\n            <li\n              className={isActive(\"/admin/offers\") ? \"active active-sub\" : \"\"}\n            >\n              <a href=\"/admin/offers\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Offers Details</span>\n              </a>\n            </li>\n\n            {/* CMS */}\n            <li className=\"treeview\">\n              <a href=\"#\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">CMS</span>\n                <span className=\"pull-right-container\">\n                  <i className=\"fa fa-angle-left pull-right\"></i>\n                </span>\n              </a>\n              <ul className=\"treeview-menu\">\n                <li\n                  className={\n                    isActive(\"/admin/about-us\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/about-us\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">About Us</span>\n                  </a>\n                </li>\n                <li\n                  className={\n                    isActive(\"/admin/privacy-policy\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/privacy-policy\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">Privacy Policy</span>\n                  </a>\n                </li>\n                <li\n                  className={\n                    isActive(\"/admin/terms-condition\")\n                      ? \"active active-sub\"\n                      : \"\"\n                  }\n                >\n                  <a href=\"/admin/terms-condition\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">Terms & Condition</span>\n                  </a>\n                </li>\n              </ul>\n            </li>\n          </ul>\n        </section>\n      </aside>\n\n      {/* Content Wrapper. Contains page content */}\n      <div className=\"content-wrapper\">\n        {/* Main content */}\n        <section className=\"content\">\n          {title && (\n            <h3\n              className=\"page-title\"\n              style={{\n                margin: \"20px 0\",\n                color: \"#444444\",\n                fontSize: \"24px\",\n                fontWeight: 400,\n              }}\n            >\n              {title}\n            </h3>\n          )}\n\n          <div className=\"row\">\n            <div className=\"col-md-12\">{children}</div>\n          </div>\n        </section>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAUe,SAAS,eAAe,EACrC,QAAQ,EACR,KAAK,EACe;;IACpB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,MAAM,sBAAsB;QAC1B,oBAAoB,CAAC;IACvB;IAEA,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,WAAW,CAAC;QAChB,OAAO,aAAa;IACtB;IAEA,MAAM,iBAAiB,CAAC;QACtB,iBAAiB,CAAC,OAChB,KAAK,QAAQ,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,OAAS,SAAS,MAAM;mBAAI;gBAAM;aAAG;IAE1E;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,cAAc,QAAQ,CAAC;IAChC;IAEA,mDAAmD;IACnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IACE,SAAS,QAAQ,CAAC,yBAClB,SAAS,QAAQ,CAAC,mBAClB,SAAS,QAAQ,CAAC,iBAClB;gBACA;gDAAiB,CAAC,OAAS;+BAAI;4BAAM;yBAAkB;;YACzD;YACA,IACE,SAAS,QAAQ,CAAC,qBAClB,SAAS,QAAQ,CAAC,oBAClB;gBACA;gDAAiB,CAAC,OAAS;+BAAI;4BAAM;yBAAoB;;YAC3D;YACA,IACE,SAAS,QAAQ,CAAC,wBAClB,SAAS,QAAQ,CAAC,mBAClB;gBACA;gDAAiB,CAAC,OAAS;+BAAI;4BAAM;yBAAkB;;YACzD;YACA,IACE,SAAS,QAAQ,CAAC,sBAClB,SAAS,QAAQ,CAAC,4BAClB,SAAS,QAAQ,CAAC,2BAClB;gBACA;gDAAiB,CAAC,OAAS;+BAAI;4BAAM;yBAAM;;YAC7C;QACF;mCAAG;QAAC;KAAS;IAEb,qBACE,6LAAC;QACC,WAAW,CAAC,kBAAkB,EAC5B,mBAAmB,qBAAqB,IACxC;;0BAGF,6LAAC;gBAAO,WAAU;;kCAEhB,6LAAC;wBACC,MAAK;wBACL,WAAU;wBACV,OAAO;4BAAE,UAAU;4BAAQ,OAAO;4BAAQ,gBAAgB;wBAAO;;0CAEjE,6LAAC;0CAAE;;;;;;4BAAS;;;;;;;kCAId,6LAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,SAAS;4BAAS,SAAS;wBAAE;kCAGtC,cAAA,6LAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS,CAAC;gCACR,EAAE,cAAc;gCAChB;4BACF;4BACA,MAAK;;8CAEL,6LAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMtB,6LAAC;gBAAM,WAAU;0BAEf,cAAA,6LAAC;oBAAQ,WAAU;8BACjB,cAAA,6LAAC;wBAAG,WAAU;;0CAEZ,6LAAC;gCAAG,WAAW,SAAS,sBAAsB,WAAW;0CACvD,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCACC,WAAW,CAAC,SAAS,EACnB,eAAe,qBAAqB,qBAAqB,IACzD;;kDAEF,6LAAC;wCACC,MAAK;wCACL,SAAS,CAAC;4CACR,EAAE,cAAc;4CAChB,eAAe;wCACjB;;0DAEA,6LAAC;gDAAE,WAAU;;;;;;0DACb,6LAAC;gDAAK,WAAU;0DAAQ;;;;;;0DACxB,6LAAC;gDAAK,WAAU;0DACd,cAAA,6LAAC;oDACC,WAAW,CAAC,4BAA4B,EACtC,eAAe,qBAAqB,kBAAkB,IACtD;;;;;;;;;;;;;;;;;kDAIR,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,SAAS,eAAe,qBAAqB,UAAU;wCACzD;;0DAEA,6LAAC;gDACC,WACE,SAAS,wBAAwB,sBAAsB;0DAGzD,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;0DAG5B,6LAAC;gDACC,WACE,SAAS,kBAAkB,sBAAsB;0DAGnD,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;0DAG5B,6LAAC;gDACC,WACE,SAAS,kBAAkB,sBAAsB;0DAGnD,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhC,6LAAC;gCAAG,WAAW,SAAS,iBAAiB,sBAAsB;0CAC7D,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCACC,WAAW,SAAS,qBAAqB,sBAAsB;0CAE/D,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCACC,WAAW,SAAS,qBAAqB,sBAAsB;0CAE/D,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAE,MAAK;;0DACN,6LAAC;gDAAE,WAAU;;;;;;0DACb,6LAAC;gDAAK,WAAU;0DAAQ;;;;;;0DACxB,6LAAC;gDAAK,WAAU;0DACd,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;;;;;;;kDAGjB,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDACC,WACE,SAAS,oBAAoB,sBAAsB;0DAGrD,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;0DAG5B,6LAAC;gDACC,WACE,SAAS,qBAAqB,sBAAsB;0DAGtD,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhC,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAE,MAAK;;0DACN,6LAAC;gDAAE,WAAU;;;;;;0DACb,6LAAC;gDAAK,WAAU;0DAAQ;;;;;;0DACxB,6LAAC;gDAAK,WAAU;0DACd,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;;;;;;;kDAGjB,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDACC,WACE,SAAS,uBAAuB,sBAAsB;0DAGxD,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;0DAG5B,6LAAC;gDACC,WACE,SAAS,oBAAoB,sBAAsB;0DAGrD,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhC,6LAAC;gCACC,WACE,SAAS,0BAA0B,sBAAsB;0CAG3D,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCACC,WACE,SAAS,uBAAuB,sBAAsB;0CAGxD,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCACC,WACE,SAAS,+BAA+B,sBAAsB;0CAGhE,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCACC,WACE,SAAS,mCACL,sBACA;0CAGN,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCACC,WAAW,SAAS,mBAAmB,sBAAsB;0CAE7D,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAE,MAAK;;0DACN,6LAAC;gDAAE,WAAU;;;;;;0DACb,6LAAC;gDAAK,WAAU;0DAAQ;;;;;;0DACxB,6LAAC;gDAAK,WAAU;0DACd,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;;;;;;;kDAGjB,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDACC,WACE,SAAS,qBAAqB,sBAAsB;0DAGtD,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;0DAG5B,6LAAC;gDACC,WACE,SAAS,2BAA2B,sBAAsB;0DAG5D,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;0DAG5B,6LAAC;gDACC,WACE,SAAS,4BACL,sBACA;0DAGN,cAAA,6LAAC;oDAAE,MAAK;;sEACN,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUtC,6LAAC;gBAAI,WAAU;0BAEb,cAAA,6LAAC;oBAAQ,WAAU;;wBAChB,uBACC,6LAAC;4BACC,WAAU;4BACV,OAAO;gCACL,QAAQ;gCACR,OAAO;gCACP,UAAU;gCACV,YAAY;4BACd;sCAEC;;;;;;sCAIL,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxC;GArZwB;;QAIP,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KALN", "debugId": null}}, {"offset": {"line": 1086, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/src/app/admin/users/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport AdminLTELayout from '@/components/layout/AdminLTELayout';\nimport { adminUsers } from '@/data/dummyData';\n\n// Extended dummy users data\nconst users = [\n  {\n    id: 1,\n    name: 'Super Admin',\n    email: '<EMAIL>',\n    role: 'Admin',\n    status: 1,\n    created_at: '2024-01-01T10:00:00Z',\n    role_id: 1\n  },\n  {\n    id: 2,\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    role: 'Admin',\n    status: 1,\n    created_at: '2024-01-15T10:00:00Z',\n    role_id: 1\n  },\n  {\n    id: 3,\n    name: 'Sarah Manager',\n    email: '<EMAIL>',\n    role: 'Director',\n    status: 1,\n    created_at: '2024-02-01T10:00:00Z',\n    role_id: 5\n  },\n  {\n    id: 4,\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    role: 'Course Builder',\n    status: 0,\n    created_at: '2024-02-10T10:00:00Z',\n    role_id: 4\n  }\n];\n\nexport default function UsersPage() {\n  const [userList, setUserList] = useState(users);\n\n  const handleStatusToggle = (userId: number, currentStatus: number) => {\n    const newStatus = currentStatus === 1 ? 0 : 1;\n    setUserList(prev => \n      prev.map(user => \n        user.id === userId ? { ...user, status: newStatus } : user\n      )\n    );\n  };\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-GB', {\n      day: '2-digit',\n      month: 'short',\n      year: '2-digit',\n      hour: '2-digit',\n      minute: '2-digit',\n      hour12: true\n    });\n  };\n\n  return (\n    <AdminLTELayout>\n      <div className=\"admn\">\n        <h3 className=\"page-title\">Administrator Users</h3>\n        \n        <p>\n          <a href=\"/admin/users/create\" className=\"btn btn-success\">Add New</a>\n        </p>\n      </div>\n\n      <div className=\"panel panel-default\">\n        <div style={{ fontSize: '20px' }} className=\"panel-heading\">\n          List\n        </div>\n\n        <div className=\"panel-body scroll-table table-responsive\">\n          <table className={`table table-bordered table-striped ${userList.length > 0 ? 'datatable1' : ''} dt-select`}>\n            <thead>\n              <tr>\n                <th style={{ fontSize: '15px' }}>Sr.No</th>\n                <th style={{ fontSize: '15px' }}>Name</th>\n                <th style={{ fontSize: '15px' }}>Email</th>\n                <th style={{ fontSize: '15px' }}>Role</th>\n                <th style={{ fontSize: '15px' }}>Status</th>\n                <th style={{ fontSize: '15px' }}>Created At</th>\n                <th style={{ fontSize: '15px' }}>Action</th>\n              </tr>\n            </thead>\n            \n            <tbody>\n              {userList.length > 0 ? (\n                userList.map((user, index) => (\n                  <tr key={user.id} data-entry-id={user.id}>\n                    <td>{index + 1}</td>\n                    <td>{user.name}</td>\n                    <td>{user.email}</td>\n                    <td>{user.role}</td>\n                    <td>\n                      {user.status === 1 ? (\n                        <a \n                          href=\"#\" \n                          className=\"label label-success\"\n                          onClick={(e) => {\n                            e.preventDefault();\n                            handleStatusToggle(user.id, user.status);\n                          }}\n                        >\n                          Active\n                        </a>\n                      ) : (\n                        <a \n                          href=\"#\" \n                          className=\"label label-danger\"\n                          onClick={(e) => {\n                            e.preventDefault();\n                            handleStatusToggle(user.id, user.status);\n                          }}\n                        >\n                          Inactive\n                        </a>\n                      )}\n                    </td>\n                    <td>{formatDate(user.created_at)}</td>\n                    <td>\n                      <a href={`/admin/users/edit/${user.id}`} className=\"btn btn-xs btn-info\">\n                        Edit\n                      </a>\n                      {' '}\n                      <a href={`/admin/users/view/${user.id}`} className=\"btn btn-xs btn-primary\">\n                        View\n                      </a>\n                      {user.role_id !== 1 && (\n                        <>\n                          {' '}\n                          <button \n                            className=\"btn btn-xs btn-danger\"\n                            onClick={() => {\n                              if (confirm('Are you sure you want to delete this user?')) {\n                                setUserList(prev => prev.filter(u => u.id !== user.id));\n                              }\n                            }}\n                          >\n                            Delete\n                          </button>\n                        </>\n                      )}\n                    </td>\n                  </tr>\n                ))\n              ) : (\n                <tr>\n                  <td colSpan={7}>No entries in table</td>\n                </tr>\n              )}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </AdminLTELayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAMA,4BAA4B;AAC5B,MAAM,QAAQ;IACZ;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,SAAS;IACX;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,qBAAqB,CAAC,QAAgB;QAC1C,MAAM,YAAY,kBAAkB,IAAI,IAAI;QAC5C,YAAY,CAAA,OACV,KAAK,GAAG,CAAC,CAAA,OACP,KAAK,EAAE,KAAK,SAAS;oBAAE,GAAG,IAAI;oBAAE,QAAQ;gBAAU,IAAI;IAG5D;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,KAAK;YACL,OAAO;YACP,MAAM;YACN,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,qBACE,6LAAC,iJAAA,CAAA,UAAc;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAa;;;;;;kCAE3B,6LAAC;kCACC,cAAA,6LAAC;4BAAE,MAAK;4BAAsB,WAAU;sCAAkB;;;;;;;;;;;;;;;;;0BAI9D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,OAAO;4BAAE,UAAU;wBAAO;wBAAG,WAAU;kCAAgB;;;;;;kCAI5D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAW,CAAC,mCAAmC,EAAE,SAAS,MAAM,GAAG,IAAI,eAAe,GAAG,UAAU,CAAC;;8CACzG,6LAAC;8CACC,cAAA,6LAAC;;0DACC,6LAAC;gDAAG,OAAO;oDAAE,UAAU;gDAAO;0DAAG;;;;;;0DACjC,6LAAC;gDAAG,OAAO;oDAAE,UAAU;gDAAO;0DAAG;;;;;;0DACjC,6LAAC;gDAAG,OAAO;oDAAE,UAAU;gDAAO;0DAAG;;;;;;0DACjC,6LAAC;gDAAG,OAAO;oDAAE,UAAU;gDAAO;0DAAG;;;;;;0DACjC,6LAAC;gDAAG,OAAO;oDAAE,UAAU;gDAAO;0DAAG;;;;;;0DACjC,6LAAC;gDAAG,OAAO;oDAAE,UAAU;gDAAO;0DAAG;;;;;;0DACjC,6LAAC;gDAAG,OAAO;oDAAE,UAAU;gDAAO;0DAAG;;;;;;;;;;;;;;;;;8CAIrC,6LAAC;8CACE,SAAS,MAAM,GAAG,IACjB,SAAS,GAAG,CAAC,CAAC,MAAM,sBAClB,6LAAC;4CAAiB,iBAAe,KAAK,EAAE;;8DACtC,6LAAC;8DAAI,QAAQ;;;;;;8DACb,6LAAC;8DAAI,KAAK,IAAI;;;;;;8DACd,6LAAC;8DAAI,KAAK,KAAK;;;;;;8DACf,6LAAC;8DAAI,KAAK,IAAI;;;;;;8DACd,6LAAC;8DACE,KAAK,MAAM,KAAK,kBACf,6LAAC;wDACC,MAAK;wDACL,WAAU;wDACV,SAAS,CAAC;4DACR,EAAE,cAAc;4DAChB,mBAAmB,KAAK,EAAE,EAAE,KAAK,MAAM;wDACzC;kEACD;;;;;6EAID,6LAAC;wDACC,MAAK;wDACL,WAAU;wDACV,SAAS,CAAC;4DACR,EAAE,cAAc;4DAChB,mBAAmB,KAAK,EAAE,EAAE,KAAK,MAAM;wDACzC;kEACD;;;;;;;;;;;8DAKL,6LAAC;8DAAI,WAAW,KAAK,UAAU;;;;;;8DAC/B,6LAAC;;sEACC,6LAAC;4DAAE,MAAM,CAAC,kBAAkB,EAAE,KAAK,EAAE,EAAE;4DAAE,WAAU;sEAAsB;;;;;;wDAGxE;sEACD,6LAAC;4DAAE,MAAM,CAAC,kBAAkB,EAAE,KAAK,EAAE,EAAE;4DAAE,WAAU;sEAAyB;;;;;;wDAG3E,KAAK,OAAO,KAAK,mBAChB;;gEACG;8EACD,6LAAC;oEACC,WAAU;oEACV,SAAS;wEACP,IAAI,QAAQ,+CAA+C;4EACzD,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;wEACvD;oEACF;8EACD;;;;;;;;;;;;;;;2CAjDA,KAAK,EAAE;;;;kEA0DlB,6LAAC;kDACC,cAAA,6LAAC;4CAAG,SAAS;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlC;GA5HwB;KAAA", "debugId": null}}, {"offset": {"line": 1469, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}