{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/src/components/layout/TopBar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport {\n  App<PERSON>ar,\n  Toolbar,\n  IconButton,\n  Typography,\n  Menu,\n  MenuItem,\n  Avatar,\n  Box,\n  Badge,\n  Tooltip,\n} from '@mui/material';\nimport {\n  Menu as MenuIcon,\n  AccountCircle,\n  Notifications,\n  Settings,\n  Logout,\n  Person,\n} from '@mui/icons-material';\nimport { useRouter } from 'next/navigation';\n\ninterface TopBarProps {\n  onMenuClick: () => void;\n  sidebarOpen: boolean;\n}\n\nexport default function TopBar({ onMenuClick, sidebarOpen }: TopBarProps) {\n  const router = useRouter();\n  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);\n  const [notificationAnchor, setNotificationAnchor] = useState<null | HTMLElement>(null);\n\n  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleProfileMenuClose = () => {\n    setAnchorEl(null);\n  };\n\n  const handleNotificationMenuOpen = (event: React.MouseEvent<HTMLElement>) => {\n    setNotificationAnchor(event.currentTarget);\n  };\n\n  const handleNotificationMenuClose = () => {\n    setNotificationAnchor(null);\n  };\n\n  const handleLogout = () => {\n    // Handle logout logic here\n    handleProfileMenuClose();\n    router.push('/login');\n  };\n\n  const handleProfile = () => {\n    handleProfileMenuClose();\n    router.push('/admin/profile');\n  };\n\n  const isMenuOpen = Boolean(anchorEl);\n  const isNotificationOpen = Boolean(notificationAnchor);\n\n  return (\n    <AppBar\n      position=\"fixed\"\n      sx={{\n        zIndex: (theme) => theme.zIndex.drawer + 1,\n        backgroundColor: '#3c8dbc',\n        boxShadow: 'none',\n        borderBottom: '1px solid #d2d6de',\n      }}\n    >\n      <Toolbar>\n        <IconButton\n          color=\"inherit\"\n          aria-label=\"open drawer\"\n          onClick={onMenuClick}\n          edge=\"start\"\n          sx={{ mr: 2 }}\n        >\n          <MenuIcon />\n        </IconButton>\n        \n        <Typography variant=\"h6\" noWrap component=\"div\" sx={{ flexGrow: 1 }}>\n          Admin Dashboard\n        </Typography>\n\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n          {/* Notifications */}\n          <Tooltip title=\"Notifications\">\n            <IconButton\n              color=\"inherit\"\n              onClick={handleNotificationMenuOpen}\n            >\n              <Badge badgeContent={4} color=\"error\">\n                <Notifications />\n              </Badge>\n            </IconButton>\n          </Tooltip>\n\n          {/* Profile Menu */}\n          <Tooltip title=\"Account settings\">\n            <IconButton\n              onClick={handleProfileMenuOpen}\n              color=\"inherit\"\n            >\n              <Avatar\n                sx={{ \n                  width: 32, \n                  height: 32, \n                  backgroundColor: '#2a6496',\n                  fontSize: '14px'\n                }}\n              >\n                A\n              </Avatar>\n            </IconButton>\n          </Tooltip>\n        </Box>\n\n        {/* Profile Menu */}\n        <Menu\n          anchorEl={anchorEl}\n          anchorOrigin={{\n            vertical: 'bottom',\n            horizontal: 'right',\n          }}\n          keepMounted\n          transformOrigin={{\n            vertical: 'top',\n            horizontal: 'right',\n          }}\n          open={isMenuOpen}\n          onClose={handleProfileMenuClose}\n        >\n          <MenuItem onClick={handleProfile}>\n            <Person sx={{ mr: 1 }} />\n            Profile\n          </MenuItem>\n          <MenuItem onClick={handleProfileMenuClose}>\n            <Settings sx={{ mr: 1 }} />\n            Settings\n          </MenuItem>\n          <MenuItem onClick={handleLogout}>\n            <Logout sx={{ mr: 1 }} />\n            Logout\n          </MenuItem>\n        </Menu>\n\n        {/* Notifications Menu */}\n        <Menu\n          anchorEl={notificationAnchor}\n          anchorOrigin={{\n            vertical: 'bottom',\n            horizontal: 'right',\n          }}\n          keepMounted\n          transformOrigin={{\n            vertical: 'top',\n            horizontal: 'right',\n          }}\n          open={isNotificationOpen}\n          onClose={handleNotificationMenuClose}\n        >\n          <MenuItem onClick={handleNotificationMenuClose}>\n            <Typography variant=\"body2\">New user registered</Typography>\n          </MenuItem>\n          <MenuItem onClick={handleNotificationMenuClose}>\n            <Typography variant=\"body2\">Course request pending</Typography>\n          </MenuItem>\n          <MenuItem onClick={handleNotificationMenuClose}>\n            <Typography variant=\"body2\">Assignment submitted</Typography>\n          </MenuItem>\n          <MenuItem onClick={handleNotificationMenuClose}>\n            <Typography variant=\"body2\">Live class starting soon</Typography>\n          </MenuItem>\n        </Menu>\n      </Toolbar>\n    </AppBar>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAAA;AAAA;AAAA;AAAA;AAQA;AAvBA;;;;;;;;;;AA8Be,SAAS,OAAO,EAAE,WAAW,EAAE,WAAW,EAAe;IACtE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAC7D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAEjF,MAAM,wBAAwB,CAAC;QAC7B,YAAY,MAAM,aAAa;IACjC;IAEA,MAAM,yBAAyB;QAC7B,YAAY;IACd;IAEA,MAAM,6BAA6B,CAAC;QAClC,sBAAsB,MAAM,aAAa;IAC3C;IAEA,MAAM,8BAA8B;QAClC,sBAAsB;IACxB;IAEA,MAAM,eAAe;QACnB,2BAA2B;QAC3B;QACA,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,gBAAgB;QACpB;QACA,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,aAAa,QAAQ;IAC3B,MAAM,qBAAqB,QAAQ;IAEnC,qBACE,8OAAC,iMAAA,CAAA,SAAM;QACL,UAAS;QACT,IAAI;YACF,QAAQ,CAAC,QAAU,MAAM,MAAM,CAAC,MAAM,GAAG;YACzC,iBAAiB;YACjB,WAAW;YACX,cAAc;QAChB;kBAEA,cAAA,8OAAC,oMAAA,CAAA,UAAO;;8BACN,8OAAC,6MAAA,CAAA,aAAU;oBACT,OAAM;oBACN,cAAW;oBACX,SAAS;oBACT,MAAK;oBACL,IAAI;wBAAE,IAAI;oBAAE;8BAEZ,cAAA,8OAAC,yJAAA,CAAA,UAAQ;;;;;;;;;;8BAGX,8OAAC,6MAAA,CAAA,aAAU;oBAAC,SAAQ;oBAAK,MAAM;oBAAC,WAAU;oBAAM,IAAI;wBAAE,UAAU;oBAAE;8BAAG;;;;;;8BAIrE,8OAAC,wLAAA,CAAA,MAAG;oBAAC,IAAI;wBAAE,SAAS;wBAAQ,YAAY;wBAAU,KAAK;oBAAE;;sCAEvD,8OAAC,oMAAA,CAAA,UAAO;4BAAC,OAAM;sCACb,cAAA,8OAAC,6MAAA,CAAA,aAAU;gCACT,OAAM;gCACN,SAAS;0CAET,cAAA,8OAAC,8LAAA,CAAA,QAAK;oCAAC,cAAc;oCAAG,OAAM;8CAC5B,cAAA,8OAAC,kKAAA,CAAA,UAAa;;;;;;;;;;;;;;;;;;;;sCAMpB,8OAAC,oMAAA,CAAA,UAAO;4BAAC,OAAM;sCACb,cAAA,8OAAC,6MAAA,CAAA,aAAU;gCACT,SAAS;gCACT,OAAM;0CAEN,cAAA,8OAAC,iMAAA,CAAA,SAAM;oCACL,IAAI;wCACF,OAAO;wCACP,QAAQ;wCACR,iBAAiB;wCACjB,UAAU;oCACZ;8CACD;;;;;;;;;;;;;;;;;;;;;;8BAQP,8OAAC,2LAAA,CAAA,OAAI;oBACH,UAAU;oBACV,cAAc;wBACZ,UAAU;wBACV,YAAY;oBACd;oBACA,WAAW;oBACX,iBAAiB;wBACf,UAAU;wBACV,YAAY;oBACd;oBACA,MAAM;oBACN,SAAS;;sCAET,8OAAC,uMAAA,CAAA,WAAQ;4BAAC,SAAS;;8CACjB,8OAAC,2JAAA,CAAA,UAAM;oCAAC,IAAI;wCAAE,IAAI;oCAAE;;;;;;gCAAK;;;;;;;sCAG3B,8OAAC,uMAAA,CAAA,WAAQ;4BAAC,SAAS;;8CACjB,8OAAC,6JAAA,CAAA,UAAQ;oCAAC,IAAI;wCAAE,IAAI;oCAAE;;;;;;gCAAK;;;;;;;sCAG7B,8OAAC,uMAAA,CAAA,WAAQ;4BAAC,SAAS;;8CACjB,8OAAC,2JAAA,CAAA,UAAM;oCAAC,IAAI;wCAAE,IAAI;oCAAE;;;;;;gCAAK;;;;;;;;;;;;;8BAM7B,8OAAC,2LAAA,CAAA,OAAI;oBACH,UAAU;oBACV,cAAc;wBACZ,UAAU;wBACV,YAAY;oBACd;oBACA,WAAW;oBACX,iBAAiB;wBACf,UAAU;wBACV,YAAY;oBACd;oBACA,MAAM;oBACN,SAAS;;sCAET,8OAAC,uMAAA,CAAA,WAAQ;4BAAC,SAAS;sCACjB,cAAA,8OAAC,6MAAA,CAAA,aAAU;gCAAC,SAAQ;0CAAQ;;;;;;;;;;;sCAE9B,8OAAC,uMAAA,CAAA,WAAQ;4BAAC,SAAS;sCACjB,cAAA,8OAAC,6MAAA,CAAA,aAAU;gCAAC,SAAQ;0CAAQ;;;;;;;;;;;sCAE9B,8OAAC,uMAAA,CAAA,WAAQ;4BAAC,SAAS;sCACjB,cAAA,8OAAC,6MAAA,CAAA,aAAU;gCAAC,SAAQ;0CAAQ;;;;;;;;;;;sCAE9B,8OAAC,uMAAA,CAAA,WAAQ;4BAAC,SAAS;sCACjB,cAAA,8OAAC,6MAAA,CAAA,aAAU;gCAAC,SAAQ;0CAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxC", "debugId": null}}, {"offset": {"line": 374, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport {\n  Drawer,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemIcon,\n  ListItemText,\n  Collapse,\n  Box,\n  Typography,\n  Divider,\n} from '@mui/material';\nimport {\n  Dashboard,\n  People,\n  School,\n  Category,\n  Assignment,\n  Quiz,\n  VideoLibrary,\n  LiveTv,\n  Forum,\n  Settings,\n  ExpandLess,\n  ExpandMore,\n  Person,\n  SupervisorAccount,\n  Class,\n  Subject,\n  Notifications,\n  ContactSupport,\n  Description,\n  Payment,\n  TrendingUp,\n} from '@mui/icons-material';\nimport { useRouter, usePathname } from 'next/navigation';\n\nconst drawerWidth = 230;\n\ninterface MenuItem {\n  id: string;\n  label: string;\n  icon: React.ReactNode;\n  path?: string;\n  children?: MenuItem[];\n  permission?: string;\n}\n\nconst menuItems: MenuItem[] = [\n  {\n    id: 'dashboard',\n    label: 'Dashboard',\n    icon: <Dashboard />,\n    path: '/admin/dashboard',\n    permission: 'dashboard',\n  },\n  {\n    id: 'user-management',\n    label: 'User Management',\n    icon: <People />,\n    permission: 'users_manage',\n    children: [\n      {\n        id: 'permissions',\n        label: 'Permissions',\n        icon: <Settings />,\n        path: '/admin/permissions',\n        permission: 'Permission',\n      },\n      {\n        id: 'roles',\n        label: 'Roles',\n        icon: <SupervisorAccount />,\n        path: '/admin/roles',\n        permission: 'role',\n      },\n      {\n        id: 'users',\n        label: 'Administrator Users',\n        icon: <Person />,\n        path: '/admin/users',\n      },\n    ],\n  },\n  {\n    id: 'teacher-management',\n    label: 'Teacher Management',\n    icon: <School />,\n    path: '/admin/teachers',\n    permission: 'teacher_manage',\n  },\n  {\n    id: 'student-management',\n    label: 'Student Management',\n    icon: <People />,\n    path: '/admin/students',\n    permission: 'student_management',\n  },\n  {\n    id: 'course-management',\n    label: 'Course Management',\n    icon: <School />,\n    permission: 'student_class',\n    children: [\n      {\n        id: 'classes',\n        label: 'Class Details',\n        icon: <Class />,\n        path: '/admin/classes',\n      },\n      {\n        id: 'subjects',\n        label: 'Subjects Details',\n        icon: <Subject />,\n        path: '/admin/subjects',\n      },\n    ],\n  },\n  {\n    id: 'course-category',\n    label: 'Course & Category',\n    icon: <Category />,\n    permission: 'category',\n    children: [\n      {\n        id: 'categories',\n        label: 'Category Details',\n        icon: <Category />,\n        path: '/admin/categories',\n      },\n      {\n        id: 'courses',\n        label: 'Course Details',\n        icon: <School />,\n        path: '/admin/courses',\n      },\n    ],\n  },\n  {\n    id: 'demo-requests',\n    label: 'Demo Request',\n    icon: <Assignment />,\n    path: '/admin/demo-requests',\n    permission: 'student_apply_for_demo',\n  },\n  {\n    id: 'assignments',\n    label: 'Assignments',\n    icon: <Assignment />,\n    path: '/admin/assignments',\n    permission: 'assignments',\n  },\n  {\n    id: 'quizzes',\n    label: 'Quizzes',\n    icon: <Quiz />,\n    path: '/admin/quizzes',\n    permission: 'quiz',\n  },\n  {\n    id: 'live-classes',\n    label: 'Live Classes',\n    icon: <LiveTv />,\n    path: '/admin/live-classes',\n    permission: 'liveclasses',\n  },\n  {\n    id: 'community-posts',\n    label: 'Community Posts',\n    icon: <Forum />,\n    path: '/admin/community-posts',\n    permission: 'community_posts',\n  },\n  {\n    id: 'notifications',\n    label: 'Notifications',\n    icon: <Notifications />,\n    path: '/admin/notifications',\n    permission: 'notifications',\n  },\n  {\n    id: 'reports',\n    label: 'Reports',\n    icon: <TrendingUp />,\n    path: '/admin/reports',\n    permission: 'reports',\n  },\n];\n\ninterface SidebarProps {\n  open: boolean;\n  onClose: () => void;\n}\n\nexport default function Sidebar({ open, onClose }: SidebarProps) {\n  const router = useRouter();\n  const pathname = usePathname();\n  const [expandedItems, setExpandedItems] = useState<string[]>([]);\n\n  const handleItemClick = (item: MenuItem) => {\n    if (item.children) {\n      const isExpanded = expandedItems.includes(item.id);\n      if (isExpanded) {\n        setExpandedItems(expandedItems.filter(id => id !== item.id));\n      } else {\n        setExpandedItems([...expandedItems, item.id]);\n      }\n    } else if (item.path) {\n      router.push(item.path);\n    }\n  };\n\n  const isItemSelected = (path: string) => {\n    return pathname === path;\n  };\n\n  const renderMenuItem = (item: MenuItem, level = 0) => {\n    const hasChildren = item.children && item.children.length > 0;\n    const isExpanded = expandedItems.includes(item.id);\n    const isSelected = item.path ? isItemSelected(item.path) : false;\n\n    return (\n      <React.Fragment key={item.id}>\n        <ListItem disablePadding sx={{ pl: level * 2 }}>\n          <ListItemButton\n            onClick={() => handleItemClick(item)}\n            selected={isSelected}\n            sx={{\n              minHeight: 48,\n              color: '#b8c7ce',\n              '&:hover': {\n                backgroundColor: '#1e282c',\n              },\n              '&.Mui-selected': {\n                backgroundColor: '#1e282c',\n                borderLeft: '3px solid #3c8dbc',\n                color: '#ffffff',\n              },\n            }}\n          >\n            <ListItemIcon sx={{ color: 'inherit', minWidth: 40 }}>\n              {item.icon}\n            </ListItemIcon>\n            <ListItemText \n              primary={item.label} \n              primaryTypographyProps={{\n                fontSize: '14px',\n                fontWeight: isSelected ? 600 : 400,\n              }}\n            />\n            {hasChildren && (\n              isExpanded ? <ExpandLess /> : <ExpandMore />\n            )}\n          </ListItemButton>\n        </ListItem>\n        {hasChildren && (\n          <Collapse in={isExpanded} timeout=\"auto\" unmountOnExit>\n            <List component=\"div\" disablePadding>\n              {item.children!.map(child => renderMenuItem(child, level + 1))}\n            </List>\n          </Collapse>\n        )}\n      </React.Fragment>\n    );\n  };\n\n  return (\n    <Drawer\n      variant=\"persistent\"\n      anchor=\"left\"\n      open={open}\n      sx={{\n        width: drawerWidth,\n        flexShrink: 0,\n        '& .MuiDrawer-paper': {\n          width: drawerWidth,\n          boxSizing: 'border-box',\n          backgroundColor: '#222d32',\n          color: '#b8c7ce',\n          borderRight: 'none',\n        },\n      }}\n    >\n      <Box sx={{ p: 2, backgroundColor: '#367fa9' }}>\n        <Typography variant=\"h6\" sx={{ color: 'white', fontWeight: 'bold' }}>\n          Admin Panel\n        </Typography>\n      </Box>\n      <Divider sx={{ backgroundColor: '#1a252f' }} />\n      <Box sx={{ overflow: 'auto', height: '100%' }}>\n        <List>\n          {menuItems.map(item => renderMenuItem(item))}\n        </List>\n      </Box>\n    </Drawer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuBA;AAtCA;;;;;;;;;;;;;;;;;;;;;;AAwCA,MAAM,cAAc;AAWpB,MAAM,YAAwB;IAC5B;QACE,IAAI;QACJ,OAAO;QACP,oBAAM,8OAAC,8JAAA,CAAA,UAAS;;;;;QAChB,MAAM;QACN,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,oBAAM,8OAAC,2JAAA,CAAA,UAAM;;;;;QACb,YAAY;QACZ,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,oBAAM,8OAAC,6JAAA,CAAA,UAAQ;;;;;gBACf,MAAM;gBACN,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,oBAAM,8OAAC,sKAAA,CAAA,UAAiB;;;;;gBACxB,MAAM;gBACN,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,oBAAM,8OAAC,2JAAA,CAAA,UAAM;;;;;gBACb,MAAM;YACR;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,oBAAM,8OAAC,2JAAA,CAAA,UAAM;;;;;QACb,MAAM;QACN,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,oBAAM,8OAAC,2JAAA,CAAA,UAAM;;;;;QACb,MAAM;QACN,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,oBAAM,8OAAC,2JAAA,CAAA,UAAM;;;;;QACb,YAAY;QACZ,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,oBAAM,8OAAC,0JAAA,CAAA,UAAK;;;;;gBACZ,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,oBAAM,8OAAC,4JAAA,CAAA,UAAO;;;;;gBACd,MAAM;YACR;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,oBAAM,8OAAC,6JAAA,CAAA,UAAQ;;;;;QACf,YAAY;QACZ,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,oBAAM,8OAAC,6JAAA,CAAA,UAAQ;;;;;gBACf,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,oBAAM,8OAAC,2JAAA,CAAA,UAAM;;;;;gBACb,MAAM;YACR;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,oBAAM,8OAAC,+JAAA,CAAA,UAAU;;;;;QACjB,MAAM;QACN,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,oBAAM,8OAAC,+JAAA,CAAA,UAAU;;;;;QACjB,MAAM;QACN,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,oBAAM,8OAAC,yJAAA,CAAA,UAAI;;;;;QACX,MAAM;QACN,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,oBAAM,8OAAC,2JAAA,CAAA,UAAM;;;;;QACb,MAAM;QACN,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,oBAAM,8OAAC,0JAAA,CAAA,UAAK;;;;;QACZ,MAAM;QACN,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,oBAAM,8OAAC,kKAAA,CAAA,UAAa;;;;;QACpB,MAAM;QACN,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,oBAAM,8OAAC,+JAAA,CAAA,UAAU;;;;;QACjB,MAAM;QACN,YAAY;IACd;CACD;AAOc,SAAS,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAgB;IAC7D,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,MAAM,kBAAkB,CAAC;QACvB,IAAI,KAAK,QAAQ,EAAE;YACjB,MAAM,aAAa,cAAc,QAAQ,CAAC,KAAK,EAAE;YACjD,IAAI,YAAY;gBACd,iBAAiB,cAAc,MAAM,CAAC,CAAA,KAAM,OAAO,KAAK,EAAE;YAC5D,OAAO;gBACL,iBAAiB;uBAAI;oBAAe,KAAK,EAAE;iBAAC;YAC9C;QACF,OAAO,IAAI,KAAK,IAAI,EAAE;YACpB,OAAO,IAAI,CAAC,KAAK,IAAI;QACvB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,aAAa;IACtB;IAEA,MAAM,iBAAiB,CAAC,MAAgB,QAAQ,CAAC;QAC/C,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;QAC5D,MAAM,aAAa,cAAc,QAAQ,CAAC,KAAK,EAAE;QACjD,MAAM,aAAa,KAAK,IAAI,GAAG,eAAe,KAAK,IAAI,IAAI;QAE3D,qBACE,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;8BACb,8OAAC,uMAAA,CAAA,WAAQ;oBAAC,cAAc;oBAAC,IAAI;wBAAE,IAAI,QAAQ;oBAAE;8BAC3C,cAAA,8OAAC,yNAAA,CAAA,iBAAc;wBACb,SAAS,IAAM,gBAAgB;wBAC/B,UAAU;wBACV,IAAI;4BACF,WAAW;4BACX,OAAO;4BACP,WAAW;gCACT,iBAAiB;4BACnB;4BACA,kBAAkB;gCAChB,iBAAiB;gCACjB,YAAY;gCACZ,OAAO;4BACT;wBACF;;0CAEA,8OAAC,mNAAA,CAAA,eAAY;gCAAC,IAAI;oCAAE,OAAO;oCAAW,UAAU;gCAAG;0CAChD,KAAK,IAAI;;;;;;0CAEZ,8OAAC,mNAAA,CAAA,eAAY;gCACX,SAAS,KAAK,KAAK;gCACnB,wBAAwB;oCACtB,UAAU;oCACV,YAAY,aAAa,MAAM;gCACjC;;;;;;4BAED,eAAe,CACd,2BAAa,8OAAC,+JAAA,CAAA,UAAU;;;;qDAAM,8OAAC,+JAAA,CAAA,UAAU;;;;oCAC3C;;;;;;;;;;;;gBAGH,6BACC,8OAAC,uMAAA,CAAA,WAAQ;oBAAC,IAAI;oBAAY,SAAQ;oBAAO,aAAa;8BACpD,cAAA,8OAAC,2LAAA,CAAA,OAAI;wBAAC,WAAU;wBAAM,cAAc;kCACjC,KAAK,QAAQ,CAAE,GAAG,CAAC,CAAA,QAAS,eAAe,OAAO,QAAQ;;;;;;;;;;;;WApC9C,KAAK,EAAE;;;;;IA0ChC;IAEA,qBACE,8OAAC,iMAAA,CAAA,SAAM;QACL,SAAQ;QACR,QAAO;QACP,MAAM;QACN,IAAI;YACF,OAAO;YACP,YAAY;YACZ,sBAAsB;gBACpB,OAAO;gBACP,WAAW;gBACX,iBAAiB;gBACjB,OAAO;gBACP,aAAa;YACf;QACF;;0BAEA,8OAAC,wLAAA,CAAA,MAAG;gBAAC,IAAI;oBAAE,GAAG;oBAAG,iBAAiB;gBAAU;0BAC1C,cAAA,8OAAC,6MAAA,CAAA,aAAU;oBAAC,SAAQ;oBAAK,IAAI;wBAAE,OAAO;wBAAS,YAAY;oBAAO;8BAAG;;;;;;;;;;;0BAIvE,8OAAC,oMAAA,CAAA,UAAO;gBAAC,IAAI;oBAAE,iBAAiB;gBAAU;;;;;;0BAC1C,8OAAC,wLAAA,CAAA,MAAG;gBAAC,IAAI;oBAAE,UAAU;oBAAQ,QAAQ;gBAAO;0BAC1C,cAAA,8OAAC,2LAAA,CAAA,OAAI;8BACF,UAAU,GAAG,CAAC,CAAA,OAAQ,eAAe;;;;;;;;;;;;;;;;;AAKhD", "debugId": null}}, {"offset": {"line": 843, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/src/components/layout/AdminLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Box, Toolbar, Container } from '@mui/material';\nimport TopBar from './TopBar';\nimport Sidebar from './Sidebar';\n\ninterface AdminLayoutProps {\n  children: React.ReactNode;\n  title?: string;\n}\n\nconst drawerWidth = 230;\n\nexport default function AdminLayout({ children, title }: AdminLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(true);\n\n  const handleDrawerToggle = () => {\n    setSidebarOpen(!sidebarOpen);\n  };\n\n  return (\n    <Box sx={{ display: 'flex', minHeight: '100vh' }}>\n      <TopBar onMenuClick={handleDrawerToggle} sidebarOpen={sidebarOpen} />\n      \n      <Sidebar open={sidebarOpen} onClose={() => setSidebarOpen(false)} />\n      \n      <Box\n        component=\"main\"\n        sx={{\n          flexGrow: 1,\n          backgroundColor: '#ecf0f5',\n          minHeight: '100vh',\n          transition: (theme) =>\n            theme.transitions.create(['margin', 'width'], {\n              easing: theme.transitions.easing.sharp,\n              duration: theme.transitions.duration.leavingScreen,\n            }),\n          marginLeft: sidebarOpen ? 0 : `-${drawerWidth}px`,\n          width: sidebarOpen ? `calc(100% - ${drawerWidth}px)` : '100%',\n        }}\n      >\n        <Toolbar />\n        \n        <Container \n          maxWidth={false} \n          sx={{ \n            mt: 3, \n            mb: 3,\n            px: 3,\n          }}\n        >\n          {title && (\n            <Box sx={{ mb: 3 }}>\n              <h3 style={{ \n                margin: 0, \n                color: '#444444', \n                fontSize: '24px', \n                fontWeight: 400 \n              }}>\n                {title}\n              </h3>\n            </Box>\n          )}\n          \n          {children}\n        </Container>\n      </Box>\n    </Box>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AAYA,MAAM,cAAc;AAEL,SAAS,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAoB;IACvE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,qBAAqB;QACzB,eAAe,CAAC;IAClB;IAEA,qBACE,8OAAC,wLAAA,CAAA,MAAG;QAAC,IAAI;YAAE,SAAS;YAAQ,WAAW;QAAQ;;0BAC7C,8OAAC,sIAAA,CAAA,UAAM;gBAAC,aAAa;gBAAoB,aAAa;;;;;;0BAEtD,8OAAC,uIAAA,CAAA,UAAO;gBAAC,MAAM;gBAAa,SAAS,IAAM,eAAe;;;;;;0BAE1D,8OAAC,wLAAA,CAAA,MAAG;gBACF,WAAU;gBACV,IAAI;oBACF,UAAU;oBACV,iBAAiB;oBACjB,WAAW;oBACX,YAAY,CAAC,QACX,MAAM,WAAW,CAAC,MAAM,CAAC;4BAAC;4BAAU;yBAAQ,EAAE;4BAC5C,QAAQ,MAAM,WAAW,CAAC,MAAM,CAAC,KAAK;4BACtC,UAAU,MAAM,WAAW,CAAC,QAAQ,CAAC,aAAa;wBACpD;oBACF,YAAY,cAAc,IAAI,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC;oBACjD,OAAO,cAAc,CAAC,YAAY,EAAE,YAAY,GAAG,CAAC,GAAG;gBACzD;;kCAEA,8OAAC,oMAAA,CAAA,UAAO;;;;;kCAER,8OAAC,0MAAA,CAAA,YAAS;wBACR,UAAU;wBACV,IAAI;4BACF,IAAI;4BACJ,IAAI;4BACJ,IAAI;wBACN;;4BAEC,uBACC,8OAAC,wLAAA,CAAA,MAAG;gCAAC,IAAI;oCAAE,IAAI;gCAAE;0CACf,cAAA,8OAAC;oCAAG,OAAO;wCACT,QAAQ;wCACR,OAAO;wCACP,UAAU;wCACV,YAAY;oCACd;8CACG;;;;;;;;;;;4BAKN;;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 965, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/src/data/dummyData.ts"], "sourcesContent": ["import {\n  User,\n  Teacher,\n  Student,\n  Category,\n  Course,\n  Assignment,\n  Quiz,\n  LiveClass,\n  CommunityPost,\n  Notification,\n  DashboardStats,\n  CourseRequest,\n} from '@/types';\n\n// Dashboard Statistics\nexport const dashboardStats: DashboardStats = {\n  total_students: 1250,\n  total_teachers: 85,\n  total_courses: 45,\n  total_assignments: 128,\n  pending_assignments: 23,\n  live_classes_today: 8,\n  new_registrations_today: 12,\n  revenue_this_month: 45000,\n};\n\n// Categories\nexport const categories: Category[] = [\n  {\n    id: 1,\n    name: 'Mathematics',\n    description: 'Advanced mathematics courses',\n    status: 'active',\n    sort_order: 1,\n    created_at: '2024-01-15T10:00:00Z',\n  },\n  {\n    id: 2,\n    name: 'Science',\n    description: 'Physics, Chemistry, Biology courses',\n    status: 'active',\n    sort_order: 2,\n    created_at: '2024-01-16T10:00:00Z',\n  },\n  {\n    id: 3,\n    name: 'English',\n    description: 'English language and literature',\n    status: 'active',\n    sort_order: 3,\n    created_at: '2024-01-17T10:00:00Z',\n  },\n  {\n    id: 4,\n    name: 'Computer Science',\n    description: 'Programming and computer science',\n    status: 'active',\n    sort_order: 4,\n    created_at: '2024-01-18T10:00:00Z',\n  },\n];\n\n// Teachers\nexport const teachers: Teacher[] = [\n  {\n    id: 1,\n    name: 'Dr. <PERSON>',\n    email: '<EMAIL>',\n    role: 'teacher',\n    status: 'active',\n    subject: 'Mathematics',\n    experience: 8,\n    qualification: 'PhD in Mathematics',\n    phone: '+1234567890',\n    address: '123 Main St, City',\n    fees_per_hour: 50,\n    created_at: '2024-01-10T10:00:00Z',\n    updated_at: '2024-01-10T10:00:00Z',\n  },\n  {\n    id: 2,\n    name: 'Prof. Michael Chen',\n    email: '<EMAIL>',\n    role: 'teacher',\n    status: 'active',\n    subject: 'Physics',\n    experience: 12,\n    qualification: 'PhD in Physics',\n    phone: '+1234567891',\n    address: '456 Oak Ave, City',\n    fees_per_hour: 60,\n    created_at: '2024-01-11T10:00:00Z',\n    updated_at: '2024-01-11T10:00:00Z',\n  },\n  {\n    id: 3,\n    name: 'Ms. Emily Davis',\n    email: '<EMAIL>',\n    role: 'teacher',\n    status: 'active',\n    subject: 'English',\n    experience: 6,\n    qualification: 'MA in English Literature',\n    phone: '+1234567892',\n    address: '789 Pine St, City',\n    fees_per_hour: 45,\n    created_at: '2024-01-12T10:00:00Z',\n    updated_at: '2024-01-12T10:00:00Z',\n  },\n];\n\n// Students\nexport const students: Student[] = [\n  {\n    id: 4,\n    name: 'John Smith',\n    email: '<EMAIL>',\n    role: 'student',\n    status: 'active',\n    class: 'Grade 10',\n    parent_name: 'Robert Smith',\n    parent_phone: '+1234567893',\n    date_of_birth: '2008-05-15',\n    address: '321 Elm St, City',\n    created_at: '2024-01-20T10:00:00Z',\n    updated_at: '2024-01-20T10:00:00Z',\n  },\n  {\n    id: 5,\n    name: 'Emma Wilson',\n    email: '<EMAIL>',\n    role: 'student',\n    status: 'active',\n    class: 'Grade 11',\n    parent_name: 'Lisa Wilson',\n    parent_phone: '+1234567894',\n    date_of_birth: '2007-08-22',\n    address: '654 Maple Ave, City',\n    created_at: '2024-01-21T10:00:00Z',\n    updated_at: '2024-01-21T10:00:00Z',\n  },\n];\n\n// Courses\nexport const courses: Course[] = [\n  {\n    id: 1,\n    name: 'Advanced Calculus',\n    description: 'Comprehensive calculus course for advanced students',\n    category_id: 1,\n    category: categories[0],\n    price: 299,\n    duration: '12 weeks',\n    status: 'active',\n    is_featured: true,\n    created_at: '2024-01-25T10:00:00Z',\n  },\n  {\n    id: 2,\n    name: 'Quantum Physics',\n    description: 'Introduction to quantum mechanics and physics',\n    category_id: 2,\n    category: categories[1],\n    price: 399,\n    duration: '16 weeks',\n    status: 'active',\n    is_featured: false,\n    created_at: '2024-01-26T10:00:00Z',\n  },\n  {\n    id: 3,\n    name: 'Creative Writing',\n    description: 'Develop your creative writing skills',\n    category_id: 3,\n    category: categories[2],\n    price: 199,\n    duration: '8 weeks',\n    status: 'active',\n    is_featured: true,\n    created_at: '2024-01-27T10:00:00Z',\n  },\n];\n\n// Course Requests\nexport const courseRequests: CourseRequest[] = [\n  {\n    id: 1,\n    course_id: 1,\n    course: courses[0],\n    instructor_id: 1,\n    instructor: teachers[0],\n    status: 'pending',\n    created_at: '2024-02-01T10:00:00Z',\n    waiting_time: '2 hours ago',\n  },\n  {\n    id: 2,\n    course_id: 2,\n    course: courses[1],\n    instructor_id: 2,\n    instructor: teachers[1],\n    builder_id: 3,\n    builder: {\n      id: 3,\n      name: 'Course Builder',\n      email: '<EMAIL>',\n      role: 'course_builder',\n      status: 'active',\n      created_at: '2024-01-01T10:00:00Z',\n      updated_at: '2024-01-01T10:00:00Z',\n    },\n    status: 'assigned',\n    created_at: '2024-01-30T10:00:00Z',\n    waiting_time: '2 days ago',\n  },\n];\n\n// Assignments\nexport const assignments: Assignment[] = [\n  {\n    id: 1,\n    title: 'Calculus Problem Set 1',\n    description: 'Solve the given calculus problems',\n    course_id: 1,\n    course: courses[0],\n    due_date: '2024-02-15T23:59:59Z',\n    max_marks: 100,\n    status: 'active',\n    created_at: '2024-02-01T10:00:00Z',\n  },\n  {\n    id: 2,\n    title: 'Physics Lab Report',\n    description: 'Submit your quantum physics lab report',\n    course_id: 2,\n    course: courses[1],\n    due_date: '2024-02-20T23:59:59Z',\n    max_marks: 50,\n    status: 'active',\n    created_at: '2024-02-02T10:00:00Z',\n  },\n];\n\n// Live Classes\nexport const liveClasses: LiveClass[] = [\n  {\n    id: 1,\n    title: 'Advanced Calculus - Derivatives',\n    description: 'Live session on derivatives and applications',\n    course_id: 1,\n    course: courses[0],\n    teacher_id: 1,\n    teacher: teachers[0],\n    scheduled_at: '2024-02-10T14:00:00Z',\n    duration: 60,\n    meeting_link: 'https://zoom.us/j/123456789',\n    status: 'scheduled',\n    created_at: '2024-02-01T10:00:00Z',\n  },\n  {\n    id: 2,\n    title: 'Quantum Physics - Wave Functions',\n    description: 'Understanding wave functions in quantum mechanics',\n    course_id: 2,\n    course: courses[1],\n    teacher_id: 2,\n    teacher: teachers[1],\n    scheduled_at: '2024-02-11T15:00:00Z',\n    duration: 90,\n    meeting_link: 'https://zoom.us/j/987654321',\n    status: 'scheduled',\n    created_at: '2024-02-02T10:00:00Z',\n  },\n];\n\n// Notifications\nexport const notifications: Notification[] = [\n  {\n    id: 1,\n    title: 'New Student Registration',\n    message: 'A new student has registered for the platform',\n    type: 'info',\n    is_read: false,\n    created_at: '2024-02-05T10:00:00Z',\n  },\n  {\n    id: 2,\n    title: 'Assignment Submitted',\n    message: 'John Smith submitted Calculus Problem Set 1',\n    type: 'success',\n    is_read: false,\n    created_at: '2024-02-05T11:00:00Z',\n  },\n  {\n    id: 3,\n    title: 'Live Class Starting Soon',\n    message: 'Advanced Calculus class starts in 30 minutes',\n    type: 'warning',\n    is_read: true,\n    created_at: '2024-02-05T13:30:00Z',\n  },\n];\n\n// Admin Users\nexport const adminUsers: User[] = [\n  {\n    id: 1,\n    name: 'Admin User',\n    email: '<EMAIL>',\n    role: 'admin',\n    status: 'active',\n    created_at: '2024-01-01T10:00:00Z',\n    updated_at: '2024-01-01T10:00:00Z',\n  },\n  {\n    id: 2,\n    name: 'Super Admin',\n    email: '<EMAIL>',\n    role: 'super_admin',\n    status: 'active',\n    created_at: '2024-01-01T10:00:00Z',\n    updated_at: '2024-01-01T10:00:00Z',\n  },\n];\n"], "names": [], "mappings": ";;;;;;;;;;;;AAgBO,MAAM,iBAAiC;IAC5C,gBAAgB;IAChB,gBAAgB;IAChB,eAAe;IACf,mBAAmB;IACnB,qBAAqB;IACrB,oBAAoB;IACpB,yBAAyB;IACzB,oBAAoB;AACtB;AAGO,MAAM,aAAyB;IACpC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,YAAY;QACZ,YAAY;IACd;CACD;AAGM,MAAM,WAAsB;IACjC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,SAAS;QACT,YAAY;QACZ,eAAe;QACf,OAAO;QACP,SAAS;QACT,eAAe;QACf,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,SAAS;QACT,YAAY;QACZ,eAAe;QACf,OAAO;QACP,SAAS;QACT,eAAe;QACf,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,SAAS;QACT,YAAY;QACZ,eAAe;QACf,OAAO;QACP,SAAS;QACT,eAAe;QACf,YAAY;QACZ,YAAY;IACd;CACD;AAGM,MAAM,WAAsB;IACjC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,OAAO;QACP,aAAa;QACb,cAAc;QACd,eAAe;QACf,SAAS;QACT,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,OAAO;QACP,aAAa;QACb,cAAc;QACd,eAAe;QACf,SAAS;QACT,YAAY;QACZ,YAAY;IACd;CACD;AAGM,MAAM,UAAoB;IAC/B;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,UAAU,UAAU,CAAC,EAAE;QACvB,OAAO;QACP,UAAU;QACV,QAAQ;QACR,aAAa;QACb,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,UAAU,UAAU,CAAC,EAAE;QACvB,OAAO;QACP,UAAU;QACV,QAAQ;QACR,aAAa;QACb,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,UAAU,UAAU,CAAC,EAAE;QACvB,OAAO;QACP,UAAU;QACV,QAAQ;QACR,aAAa;QACb,YAAY;IACd;CACD;AAGM,MAAM,iBAAkC;IAC7C;QACE,IAAI;QACJ,WAAW;QACX,QAAQ,OAAO,CAAC,EAAE;QAClB,eAAe;QACf,YAAY,QAAQ,CAAC,EAAE;QACvB,QAAQ;QACR,YAAY;QACZ,cAAc;IAChB;IACA;QACE,IAAI;QACJ,WAAW;QACX,QAAQ,OAAO,CAAC,EAAE;QAClB,eAAe;QACf,YAAY,QAAQ,CAAC,EAAE;QACvB,YAAY;QACZ,SAAS;YACP,IAAI;YACJ,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ;YACR,YAAY;YACZ,YAAY;QACd;QACA,QAAQ;QACR,YAAY;QACZ,cAAc;IAChB;CACD;AAGM,MAAM,cAA4B;IACvC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,WAAW;QACX,QAAQ,OAAO,CAAC,EAAE;QAClB,UAAU;QACV,WAAW;QACX,QAAQ;QACR,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,WAAW;QACX,QAAQ,OAAO,CAAC,EAAE;QAClB,UAAU;QACV,WAAW;QACX,QAAQ;QACR,YAAY;IACd;CACD;AAGM,MAAM,cAA2B;IACtC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,WAAW;QACX,QAAQ,OAAO,CAAC,EAAE;QAClB,YAAY;QACZ,SAAS,QAAQ,CAAC,EAAE;QACpB,cAAc;QACd,UAAU;QACV,cAAc;QACd,QAAQ;QACR,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,WAAW;QACX,QAAQ,OAAO,CAAC,EAAE;QAClB,YAAY;QACZ,SAAS,QAAQ,CAAC,EAAE;QACpB,cAAc;QACd,UAAU;QACV,cAAc;QACd,QAAQ;QACR,YAAY;IACd;CACD;AAGM,MAAM,gBAAgC;IAC3C;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,MAAM;QACN,SAAS;QACT,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,MAAM;QACN,SAAS;QACT,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,MAAM;QACN,SAAS;QACT,YAAY;IACd;CACD;AAGM,MAAM,aAAqB;IAChC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,YAAY;IACd;CACD", "debugId": null}}, {"offset": {"line": 1274, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/src/app/admin/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport {\n  Grid,\n  Paper,\n  Typography,\n  Box,\n  Card,\n  CardContent,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Button,\n  Chip,\n} from '@mui/material';\nimport {\n  People,\n  School,\n  Assignment,\n  TrendingUp,\n  Schedule,\n  AttachMoney,\n} from '@mui/icons-material';\nimport AdminLayout from '@/components/layout/AdminLayout';\nimport { dashboardStats, courseRequests } from '@/data/dummyData';\n\n// Stats Card Component\ninterface StatsCardProps {\n  title: string;\n  value: number | string;\n  icon: React.ReactNode;\n  color: string;\n}\n\nfunction StatsCard({ title, value, icon, color }: StatsCardProps) {\n  return (\n    <Card sx={{ height: '100%' }}>\n      <CardContent>\n        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n          <Box>\n            <Typography color=\"textSecondary\" gutterBottom variant=\"body2\">\n              {title}\n            </Typography>\n            <Typography variant=\"h4\" component=\"div\" sx={{ fontWeight: 'bold', color }}>\n              {value}\n            </Typography>\n          </Box>\n          <Box sx={{ color, fontSize: '3rem' }}>\n            {icon}\n          </Box>\n        </Box>\n      </CardContent>\n    </Card>\n  );\n}\n\nexport default function DashboardPage() {\n  const handleAssignCourseBuilder = (requestId: number) => {\n    console.log('Assigning course builder for request:', requestId);\n    // Handle assignment logic here\n  };\n\n  return (\n    <AdminLayout title=\"Dashboard\">\n      <Grid container spacing={3}>\n        {/* Stats Cards */}\n        <Grid item xs={12} sm={6} md={3}>\n          <StatsCard\n            title=\"Total Students\"\n            value={dashboardStats.total_students}\n            icon={<People />}\n            color=\"#00a65a\"\n          />\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <StatsCard\n            title=\"Total Teachers\"\n            value={dashboardStats.total_teachers}\n            icon={<School />}\n            color=\"#3c8dbc\"\n          />\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <StatsCard\n            title=\"Total Courses\"\n            value={dashboardStats.total_courses}\n            icon={<Assignment />}\n            color=\"#f39c12\"\n          />\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <StatsCard\n            title=\"Revenue This Month\"\n            value={`$${dashboardStats.revenue_this_month.toLocaleString()}`}\n            icon={<AttachMoney />}\n            color=\"#dd4b39\"\n          />\n        </Grid>\n\n        {/* Additional Stats */}\n        <Grid item xs={12} sm={6} md={3}>\n          <StatsCard\n            title=\"Pending Assignments\"\n            value={dashboardStats.pending_assignments}\n            icon={<Assignment />}\n            color=\"#f39c12\"\n          />\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <StatsCard\n            title=\"Live Classes Today\"\n            value={dashboardStats.live_classes_today}\n            icon={<Schedule />}\n            color=\"#00c0ef\"\n          />\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <StatsCard\n            title=\"New Registrations\"\n            value={dashboardStats.new_registrations_today}\n            icon={<People />}\n            color=\"#00a65a\"\n          />\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <StatsCard\n            title=\"Total Assignments\"\n            value={dashboardStats.total_assignments}\n            icon={<Assignment />}\n            color=\"#3c8dbc\"\n          />\n        </Grid>\n\n        {/* New eCourse Request Table */}\n        <Grid item xs={12} lg={8}>\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ color: '#444444', mb: 2 }}>\n              New eCourse Request\n            </Typography>\n            <TableContainer>\n              <Table>\n                <TableHead sx={{ backgroundColor: '#f4f4f4' }}>\n                  <TableRow>\n                    <TableCell>#</TableCell>\n                    <TableCell>Course Id</TableCell>\n                    <TableCell>Course Name</TableCell>\n                    <TableCell>Instructor</TableCell>\n                    <TableCell>Waiting time</TableCell>\n                    <TableCell>Action</TableCell>\n                  </TableRow>\n                </TableHead>\n                <TableBody>\n                  {courseRequests\n                    .filter(request => request.status === 'pending')\n                    .map((request, index) => (\n                    <TableRow key={request.id}>\n                      <TableCell>{index + 1}</TableCell>\n                      <TableCell>{request.id}</TableCell>\n                      <TableCell>{request.course.name}</TableCell>\n                      <TableCell>{request.instructor.name}</TableCell>\n                      <TableCell>{request.waiting_time}</TableCell>\n                      <TableCell>\n                        <Button\n                          variant=\"contained\"\n                          color=\"primary\"\n                          size=\"small\"\n                          onClick={() => handleAssignCourseBuilder(request.id)}\n                        >\n                          Assign Course Builder\n                        </Button>\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                  {courseRequests.filter(request => request.status === 'pending').length === 0 && (\n                    <TableRow>\n                      <TableCell colSpan={6} align=\"center\">\n                        <Typography variant=\"h6\" color=\"textSecondary\">\n                          No record found\n                        </Typography>\n                      </TableCell>\n                    </TableRow>\n                  )}\n                </TableBody>\n              </Table>\n            </TableContainer>\n          </Paper>\n        </Grid>\n\n        {/* List of eCourses */}\n        <Grid item xs={12} lg={4}>\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ color: '#444444', mb: 2 }}>\n              List of eCourses\n            </Typography>\n            <TableContainer>\n              <Table size=\"small\">\n                <TableHead sx={{ backgroundColor: '#f4f4f4' }}>\n                  <TableRow>\n                    <TableCell>Course Id</TableCell>\n                    <TableCell>Course Name</TableCell>\n                    <TableCell>Status</TableCell>\n                  </TableRow>\n                </TableHead>\n                <TableBody>\n                  {courseRequests.map((request) => (\n                    <TableRow key={request.id}>\n                      <TableCell>{request.id}</TableCell>\n                      <TableCell>{request.course.name}</TableCell>\n                      <TableCell>\n                        <Chip\n                          label={request.status}\n                          color={\n                            request.status === 'completed' ? 'success' :\n                            request.status === 'assigned' ? 'primary' :\n                            request.status === 'in_progress' ? 'warning' : 'default'\n                          }\n                          size=\"small\"\n                        />\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </TableContainer>\n          </Paper>\n        </Grid>\n\n        {/* Chart Placeholder */}\n        <Grid item xs={12}>\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ color: '#444444', mb: 2 }}>\n              Courses by College: <span style={{ fontSize: '30px', fontWeight: 'bold', color: '#ec3535' }}>CHART</span>\n            </Typography>\n            <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', backgroundColor: '#f9f9f9' }}>\n              <Typography variant=\"h6\" color=\"textSecondary\">\n                Chart will be implemented here\n              </Typography>\n            </Box>\n          </Paper>\n        </Grid>\n      </Grid>\n    </AdminLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AA5BA;;;;;;;;;;AAsCA,SAAS,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAkB;IAC9D,qBACE,8OAAC,2LAAA,CAAA,OAAI;QAAC,IAAI;YAAE,QAAQ;QAAO;kBACzB,cAAA,8OAAC,gNAAA,CAAA,cAAW;sBACV,cAAA,8OAAC,wLAAA,CAAA,MAAG;gBAAC,IAAI;oBAAE,SAAS;oBAAQ,YAAY;oBAAU,gBAAgB;gBAAgB;;kCAChF,8OAAC,wLAAA,CAAA,MAAG;;0CACF,8OAAC,6MAAA,CAAA,aAAU;gCAAC,OAAM;gCAAgB,YAAY;gCAAC,SAAQ;0CACpD;;;;;;0CAEH,8OAAC,6MAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAK,WAAU;gCAAM,IAAI;oCAAE,YAAY;oCAAQ;gCAAM;0CACtE;;;;;;;;;;;;kCAGL,8OAAC,wLAAA,CAAA,MAAG;wBAAC,IAAI;4BAAE;4BAAO,UAAU;wBAAO;kCAChC;;;;;;;;;;;;;;;;;;;;;;AAMb;AAEe,SAAS;IACtB,MAAM,4BAA4B,CAAC;QACjC,QAAQ,GAAG,CAAC,yCAAyC;IACrD,+BAA+B;IACjC;IAEA,qBACE,8OAAC,2IAAA,CAAA,UAAW;QAAC,OAAM;kBACjB,cAAA,8OAAC,2LAAA,CAAA,OAAI;YAAC,SAAS;YAAC,SAAS;;8BAEvB,8OAAC,2LAAA,CAAA,OAAI;oBAAC,IAAI;oBAAC,IAAI;oBAAI,IAAI;oBAAG,IAAI;8BAC5B,cAAA,8OAAC;wBACC,OAAM;wBACN,OAAO,wHAAA,CAAA,iBAAc,CAAC,cAAc;wBACpC,oBAAM,8OAAC,2JAAA,CAAA,UAAM;;;;;wBACb,OAAM;;;;;;;;;;;8BAGV,8OAAC,2LAAA,CAAA,OAAI;oBAAC,IAAI;oBAAC,IAAI;oBAAI,IAAI;oBAAG,IAAI;8BAC5B,cAAA,8OAAC;wBACC,OAAM;wBACN,OAAO,wHAAA,CAAA,iBAAc,CAAC,cAAc;wBACpC,oBAAM,8OAAC,2JAAA,CAAA,UAAM;;;;;wBACb,OAAM;;;;;;;;;;;8BAGV,8OAAC,2LAAA,CAAA,OAAI;oBAAC,IAAI;oBAAC,IAAI;oBAAI,IAAI;oBAAG,IAAI;8BAC5B,cAAA,8OAAC;wBACC,OAAM;wBACN,OAAO,wHAAA,CAAA,iBAAc,CAAC,aAAa;wBACnC,oBAAM,8OAAC,+JAAA,CAAA,UAAU;;;;;wBACjB,OAAM;;;;;;;;;;;8BAGV,8OAAC,2LAAA,CAAA,OAAI;oBAAC,IAAI;oBAAC,IAAI;oBAAI,IAAI;oBAAG,IAAI;8BAC5B,cAAA,8OAAC;wBACC,OAAM;wBACN,OAAO,CAAC,CAAC,EAAE,wHAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,cAAc,IAAI;wBAC/D,oBAAM,8OAAC,gKAAA,CAAA,UAAW;;;;;wBAClB,OAAM;;;;;;;;;;;8BAKV,8OAAC,2LAAA,CAAA,OAAI;oBAAC,IAAI;oBAAC,IAAI;oBAAI,IAAI;oBAAG,IAAI;8BAC5B,cAAA,8OAAC;wBACC,OAAM;wBACN,OAAO,wHAAA,CAAA,iBAAc,CAAC,mBAAmB;wBACzC,oBAAM,8OAAC,+JAAA,CAAA,UAAU;;;;;wBACjB,OAAM;;;;;;;;;;;8BAGV,8OAAC,2LAAA,CAAA,OAAI;oBAAC,IAAI;oBAAC,IAAI;oBAAI,IAAI;oBAAG,IAAI;8BAC5B,cAAA,8OAAC;wBACC,OAAM;wBACN,OAAO,wHAAA,CAAA,iBAAc,CAAC,kBAAkB;wBACxC,oBAAM,8OAAC,6JAAA,CAAA,UAAQ;;;;;wBACf,OAAM;;;;;;;;;;;8BAGV,8OAAC,2LAAA,CAAA,OAAI;oBAAC,IAAI;oBAAC,IAAI;oBAAI,IAAI;oBAAG,IAAI;8BAC5B,cAAA,8OAAC;wBACC,OAAM;wBACN,OAAO,wHAAA,CAAA,iBAAc,CAAC,uBAAuB;wBAC7C,oBAAM,8OAAC,2JAAA,CAAA,UAAM;;;;;wBACb,OAAM;;;;;;;;;;;8BAGV,8OAAC,2LAAA,CAAA,OAAI;oBAAC,IAAI;oBAAC,IAAI;oBAAI,IAAI;oBAAG,IAAI;8BAC5B,cAAA,8OAAC;wBACC,OAAM;wBACN,OAAO,wHAAA,CAAA,iBAAc,CAAC,iBAAiB;wBACvC,oBAAM,8OAAC,+JAAA,CAAA,UAAU;;;;;wBACjB,OAAM;;;;;;;;;;;8BAKV,8OAAC,2LAAA,CAAA,OAAI;oBAAC,IAAI;oBAAC,IAAI;oBAAI,IAAI;8BACrB,cAAA,8OAAC,8LAAA,CAAA,QAAK;wBAAC,IAAI;4BAAE,GAAG;wBAAE;;0CAChB,8OAAC,6MAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAK,YAAY;gCAAC,IAAI;oCAAE,OAAO;oCAAW,IAAI;gCAAE;0CAAG;;;;;;0CAGvE,8OAAC,yNAAA,CAAA,iBAAc;0CACb,cAAA,8OAAC,8LAAA,CAAA,QAAK;;sDACJ,8OAAC,0MAAA,CAAA,YAAS;4CAAC,IAAI;gDAAE,iBAAiB;4CAAU;sDAC1C,cAAA,8OAAC,uMAAA,CAAA,WAAQ;;kEACP,8OAAC,0MAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,0MAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,0MAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,0MAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,0MAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,0MAAA,CAAA,YAAS;kEAAC;;;;;;;;;;;;;;;;;sDAGf,8OAAC,0MAAA,CAAA,YAAS;;gDACP,wHAAA,CAAA,iBAAc,CACZ,MAAM,CAAC,CAAA,UAAW,QAAQ,MAAM,KAAK,WACrC,GAAG,CAAC,CAAC,SAAS,sBACf,8OAAC,uMAAA,CAAA,WAAQ;;0EACP,8OAAC,0MAAA,CAAA,YAAS;0EAAE,QAAQ;;;;;;0EACpB,8OAAC,0MAAA,CAAA,YAAS;0EAAE,QAAQ,EAAE;;;;;;0EACtB,8OAAC,0MAAA,CAAA,YAAS;0EAAE,QAAQ,MAAM,CAAC,IAAI;;;;;;0EAC/B,8OAAC,0MAAA,CAAA,YAAS;0EAAE,QAAQ,UAAU,CAAC,IAAI;;;;;;0EACnC,8OAAC,0MAAA,CAAA,YAAS;0EAAE,QAAQ,YAAY;;;;;;0EAChC,8OAAC,0MAAA,CAAA,YAAS;0EACR,cAAA,8OAAC,iMAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,OAAM;oEACN,MAAK;oEACL,SAAS,IAAM,0BAA0B,QAAQ,EAAE;8EACpD;;;;;;;;;;;;uDAZU,QAAQ,EAAE;;;;;gDAkB1B,wHAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,MAAM,KAAK,WAAW,MAAM,KAAK,mBACzE,8OAAC,uMAAA,CAAA,WAAQ;8DACP,cAAA,8OAAC,0MAAA,CAAA,YAAS;wDAAC,SAAS;wDAAG,OAAM;kEAC3B,cAAA,8OAAC,6MAAA,CAAA,aAAU;4DAAC,SAAQ;4DAAK,OAAM;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAa/D,8OAAC,2LAAA,CAAA,OAAI;oBAAC,IAAI;oBAAC,IAAI;oBAAI,IAAI;8BACrB,cAAA,8OAAC,8LAAA,CAAA,QAAK;wBAAC,IAAI;4BAAE,GAAG;wBAAE;;0CAChB,8OAAC,6MAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAK,YAAY;gCAAC,IAAI;oCAAE,OAAO;oCAAW,IAAI;gCAAE;0CAAG;;;;;;0CAGvE,8OAAC,yNAAA,CAAA,iBAAc;0CACb,cAAA,8OAAC,8LAAA,CAAA,QAAK;oCAAC,MAAK;;sDACV,8OAAC,0MAAA,CAAA,YAAS;4CAAC,IAAI;gDAAE,iBAAiB;4CAAU;sDAC1C,cAAA,8OAAC,uMAAA,CAAA,WAAQ;;kEACP,8OAAC,0MAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,0MAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,0MAAA,CAAA,YAAS;kEAAC;;;;;;;;;;;;;;;;;sDAGf,8OAAC,0MAAA,CAAA,YAAS;sDACP,wHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,CAAC,wBACnB,8OAAC,uMAAA,CAAA,WAAQ;;sEACP,8OAAC,0MAAA,CAAA,YAAS;sEAAE,QAAQ,EAAE;;;;;;sEACtB,8OAAC,0MAAA,CAAA,YAAS;sEAAE,QAAQ,MAAM,CAAC,IAAI;;;;;;sEAC/B,8OAAC,0MAAA,CAAA,YAAS;sEACR,cAAA,8OAAC,2LAAA,CAAA,OAAI;gEACH,OAAO,QAAQ,MAAM;gEACrB,OACE,QAAQ,MAAM,KAAK,cAAc,YACjC,QAAQ,MAAM,KAAK,aAAa,YAChC,QAAQ,MAAM,KAAK,gBAAgB,YAAY;gEAEjD,MAAK;;;;;;;;;;;;mDAXI,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAuBrC,8OAAC,2LAAA,CAAA,OAAI;oBAAC,IAAI;oBAAC,IAAI;8BACb,cAAA,8OAAC,8LAAA,CAAA,QAAK;wBAAC,IAAI;4BAAE,GAAG;wBAAE;;0CAChB,8OAAC,6MAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAK,YAAY;gCAAC,IAAI;oCAAE,OAAO;oCAAW,IAAI;gCAAE;;oCAAG;kDACjD,8OAAC;wCAAK,OAAO;4CAAE,UAAU;4CAAQ,YAAY;4CAAQ,OAAO;wCAAU;kDAAG;;;;;;;;;;;;0CAE/F,8OAAC,wLAAA,CAAA,MAAG;gCAAC,IAAI;oCAAE,QAAQ;oCAAK,SAAS;oCAAQ,YAAY;oCAAU,gBAAgB;oCAAU,iBAAiB;gCAAU;0CAClH,cAAA,8OAAC,6MAAA,CAAA,aAAU;oCAAC,SAAQ;oCAAK,OAAM;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7D", "debugId": null}}]}