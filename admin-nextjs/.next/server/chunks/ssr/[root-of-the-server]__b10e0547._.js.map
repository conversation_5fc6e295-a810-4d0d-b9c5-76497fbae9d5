{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/src/components/layout/AdminLTELayout.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { useRouter, usePathname } from \"next/navigation\";\n\ninterface AdminLTELayoutProps {\n  children: React.ReactNode;\n  title?: string;\n}\n\nexport default function AdminLTELayout({\n  children,\n  title,\n}: AdminLTELayoutProps) {\n  const router = useRouter();\n  const pathname = usePathname();\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const [openTreeview, setOpenTreeview] = useState<string | null>(null);\n\n  const handleSidebarToggle = () => {\n    setSidebarCollapsed(!sidebarCollapsed);\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem(\"auth_token\");\n    localStorage.removeItem(\"user_role\");\n    router.push(\"/login\");\n  };\n\n  const isActive = (path: string) => {\n    return pathname === path;\n  };\n\n  const toggleTreeview = (id: string) => {\n    // Laravel behavior: only one treeview can be open at a time\n    setOpenTreeview((prev) => (prev === id ? null : id));\n  };\n\n  const isTreeviewOpen = (id: string) => {\n    return openTreeview === id;\n  };\n\n  // Initialize treeview state based on current path (Laravel behavior: only one open)\n  useEffect(() => {\n    if (\n      pathname.includes(\"/admin/permissions\") ||\n      pathname.includes(\"/admin/roles\") ||\n      pathname.includes(\"/admin/users\")\n    ) {\n      setOpenTreeview(\"user-management\");\n    } else if (\n      pathname.includes(\"/admin/classes\") ||\n      pathname.includes(\"/admin/subjects\")\n    ) {\n      setOpenTreeview(\"course-management\");\n    } else if (\n      pathname.includes(\"/admin/categories\") ||\n      pathname.includes(\"/admin/courses\")\n    ) {\n      setOpenTreeview(\"course-category\");\n    } else if (pathname.includes(\"/admin/quizzes\")) {\n      setOpenTreeview(\"quizzes\");\n    } else if (pathname.includes(\"/admin/assignments\")) {\n      setOpenTreeview(\"assignments\");\n    } else if (pathname.includes(\"/admin/live-classes\")) {\n      setOpenTreeview(\"live-classes\");\n    } else if (pathname.includes(\"/admin/notifications\")) {\n      setOpenTreeview(\"notifications\");\n    } else if (\n      pathname.includes(\"/admin/about-us\") ||\n      pathname.includes(\"/admin/privacy-policy\") ||\n      pathname.includes(\"/admin/terms-condition\")\n    ) {\n      setOpenTreeview(\"cms\");\n    } else {\n      // Close all treeviews if not on a treeview page\n      setOpenTreeview(null);\n    }\n  }, [pathname]);\n\n  return (\n    <>\n      <style jsx>{`\n        .treeview-menu {\n          max-height: ${openTreeview ? \"1000px\" : \"0\"};\n          transition: max-height 0.3s ease-in-out;\n        }\n        .treeview-menu li {\n          opacity: ${openTreeview ? \"1\" : \"0\"};\n          transition: opacity 0.2s ease-in-out;\n        }\n      `}</style>\n      <div\n        className={`wrapper skin-blue ${\n          sidebarCollapsed ? \"sidebar-collapse\" : \"\"\n        }`}\n      >\n        {/* Main Header */}\n        <header className=\"main-header\">\n          {/* Logo */}\n          <a\n            href=\"#\"\n            className=\"logo\"\n            style={{ fontSize: \"16px\", color: \"#fff\", textDecoration: \"none\" }}\n          >\n            <b>Admin</b>LTE\n          </a>\n\n          {/* Header Navbar */}\n          <nav\n            className=\"navbar navbar-static-top\"\n            style={{ display: \"block\", padding: 0 }}\n          >\n            {/* Sidebar toggle button */}\n            <a\n              href=\"#\"\n              className=\"sidebar-toggle\"\n              onClick={(e) => {\n                e.preventDefault();\n                handleSidebarToggle();\n              }}\n              role=\"button\"\n            >\n              <span className=\"sr-only\">Toggle navigation</span>\n              <span className=\"icon-bar\"></span>\n              <span className=\"icon-bar\"></span>\n              <span className=\"icon-bar\"></span>\n            </a>\n          </nav>\n        </header>\n\n        {/* Left side column. contains the sidebar */}\n        <aside className=\"main-sidebar\">\n          {/* sidebar: style can be found in sidebar.less */}\n          <section className=\"sidebar\">\n            <ul className=\"sidebar-menu scroll-table scroll-table-two\">\n              {/* Dashboard */}\n              <li className={isActive(\"/admin/dashboard\") ? \"active\" : \"\"}>\n                <a href=\"/admin/dashboard\">\n                  <i className=\"fa fa-dashboard\"></i>\n                  <span className=\"title\">Dashboard</span>\n                </a>\n              </li>\n\n              {/* User Management */}\n              <li\n                className={`treeview ${\n                  isTreeviewOpen(\"user-management\") ? \"active menu-open\" : \"\"\n                }`}\n              >\n                <a\n                  href=\"#\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    toggleTreeview(\"user-management\");\n                  }}\n                >\n                  <i className=\"fa fa-users\"></i>\n                  <span className=\"title\">User Management</span>\n                  <span className=\"pull-right-container\">\n                    <i\n                      className={`fa fa-angle-left pull-right ${\n                        isTreeviewOpen(\"user-management\") ? \"fa-angle-down\" : \"\"\n                      }`}\n                    ></i>\n                  </span>\n                </a>\n                <ul\n                  className=\"treeview-menu\"\n                  style={{\n                    display: isTreeviewOpen(\"user-management\")\n                      ? \"block\"\n                      : \"none\",\n                    transition: \"all 0.3s ease-in-out\",\n                    overflow: \"hidden\",\n                  }}\n                >\n                  <li\n                    className={\n                      isActive(\"/admin/permissions\") ? \"active active-sub\" : \"\"\n                    }\n                  >\n                    <a href=\"/admin/permissions\">\n                      <i className=\"fa fa-briefcase\"></i>\n                      <span className=\"title\">Permissions</span>\n                    </a>\n                  </li>\n                  <li\n                    className={\n                      isActive(\"/admin/roles\") ? \"active active-sub\" : \"\"\n                    }\n                  >\n                    <a href=\"/admin/roles\">\n                      <i className=\"fa fa-briefcase\"></i>\n                      <span className=\"title\">Roles</span>\n                    </a>\n                  </li>\n                  <li\n                    className={\n                      isActive(\"/admin/users\") ? \"active active-sub\" : \"\"\n                    }\n                  >\n                    <a href=\"/admin/users\">\n                      <i className=\"fa fa-user\"></i>\n                      <span className=\"title\">Administrator Users</span>\n                    </a>\n                  </li>\n                </ul>\n              </li>\n\n              {/* Fees Update */}\n              <li\n                className={isActive(\"/admin/fees\") ? \"active active-sub\" : \"\"}\n              >\n                <a href=\"/admin/fees\">\n                  <i className=\"fa fa-briefcase\"></i>\n                  <span className=\"title\">Fees Update</span>\n                </a>\n              </li>\n\n              {/* Teacher Management */}\n              <li\n                className={\n                  isActive(\"/admin/teachers\") ? \"active active-sub\" : \"\"\n                }\n              >\n                <a href=\"/admin/teachers\">\n                  <i className=\"fa fa-user\"></i>\n                  <span className=\"title\">Teacher Management</span>\n                </a>\n              </li>\n\n              {/* Student Management */}\n              <li\n                className={\n                  isActive(\"/admin/students\") ? \"active active-sub\" : \"\"\n                }\n              >\n                <a href=\"/admin/students\">\n                  <i className=\"fa fa-user\"></i>\n                  <span className=\"title\">Student Management</span>\n                </a>\n              </li>\n\n              {/* Course Management */}\n              <li\n                className={`treeview ${\n                  isTreeviewOpen(\"course-management\") ? \"active menu-open\" : \"\"\n                }`}\n              >\n                <a\n                  href=\"#\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    toggleTreeview(\"course-management\");\n                  }}\n                >\n                  <i className=\"fa fa-graduation-cap\"></i>\n                  <span className=\"title\">Course Management</span>\n                  <span className=\"pull-right-container\">\n                    <i\n                      className={`fa fa-angle-left pull-right ${\n                        isTreeviewOpen(\"course-management\")\n                          ? \"fa-angle-down\"\n                          : \"\"\n                      }`}\n                    ></i>\n                  </span>\n                </a>\n                <ul\n                  className=\"treeview-menu\"\n                  style={{\n                    display: isTreeviewOpen(\"course-management\")\n                      ? \"block\"\n                      : \"none\",\n                    transition: \"all 0.3s ease-in-out\",\n                    overflow: \"hidden\",\n                  }}\n                >\n                  <li\n                    className={\n                      isActive(\"/admin/classes\") ? \"active active-sub\" : \"\"\n                    }\n                  >\n                    <a href=\"/admin/classes\">\n                      <i className=\"fa fa-graduation-cap\"></i>\n                      <span className=\"title\">Class Details</span>\n                    </a>\n                  </li>\n                  <li\n                    className={\n                      isActive(\"/admin/subjects\") ? \"active active-sub\" : \"\"\n                    }\n                  >\n                    <a href=\"/admin/subjects\">\n                      <i className=\"fa fa-graduation-cap\"></i>\n                      <span className=\"title\">Subjects Details</span>\n                    </a>\n                  </li>\n                </ul>\n              </li>\n\n              {/* Course & Category */}\n              <li\n                className={`treeview ${\n                  isTreeviewOpen(\"course-category\") ? \"active menu-open\" : \"\"\n                }`}\n              >\n                <a\n                  href=\"#\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    toggleTreeview(\"course-category\");\n                  }}\n                >\n                  <i className=\"fa fa-graduation-cap\"></i>\n                  <span className=\"title\">Course & Category</span>\n                  <span className=\"pull-right-container\">\n                    <i\n                      className={`fa fa-angle-left pull-right ${\n                        isTreeviewOpen(\"course-category\") ? \"fa-angle-down\" : \"\"\n                      }`}\n                    ></i>\n                  </span>\n                </a>\n                <ul\n                  className=\"treeview-menu\"\n                  style={{\n                    display: isTreeviewOpen(\"course-category\")\n                      ? \"block\"\n                      : \"none\",\n                    transition: \"all 0.3s ease-in-out\",\n                    overflow: \"hidden\",\n                  }}\n                >\n                  <li\n                    className={\n                      isActive(\"/admin/categories\") ? \"active active-sub\" : \"\"\n                    }\n                  >\n                    <a href=\"/admin/categories\">\n                      <i className=\"fa fa-graduation-cap\"></i>\n                      <span className=\"title\">Category Details</span>\n                    </a>\n                  </li>\n                  <li\n                    className={\n                      isActive(\"/admin/courses\") ? \"active active-sub\" : \"\"\n                    }\n                  >\n                    <a href=\"/admin/courses\">\n                      <i className=\"fa fa-graduation-cap\"></i>\n                      <span className=\"title\">Course Details</span>\n                    </a>\n                  </li>\n                </ul>\n              </li>\n\n              {/* Demo Request */}\n              <li\n                className={\n                  isActive(\"/admin/demo-requests\") ? \"active active-sub\" : \"\"\n                }\n              >\n                <a href=\"/admin/demo-requests\">\n                  <i className=\"fa fa-graduation-cap\"></i>\n                  <span className=\"title\">Demo Request</span>\n                </a>\n              </li>\n\n              {/* Withdrawal */}\n              <li\n                className={\n                  isActive(\"/admin/withdrawal\") ? \"active active-sub\" : \"\"\n                }\n              >\n                <a href=\"/admin/withdrawal\">\n                  <i className=\"fa fa-graduation-cap\"></i>\n                  <span className=\"title\">Withdrawal</span>\n                </a>\n              </li>\n\n              {/* Incomplete Classes */}\n              <li\n                className={\n                  isActive(\"/admin/incomplete-classes\")\n                    ? \"active active-sub\"\n                    : \"\"\n                }\n              >\n                <a href=\"/admin/incomplete-classes\">\n                  <i className=\"fa fa-graduation-cap\"></i>\n                  <span className=\"title\">Incomplete Classes</span>\n                </a>\n              </li>\n\n              {/* Student Purchase Class */}\n              <li\n                className={\n                  isActive(\"/admin/student-purchase-class\")\n                    ? \"active active-sub\"\n                    : \"\"\n                }\n              >\n                <a href=\"/admin/student-purchase-class\">\n                  <i className=\"fa fa-graduation-cap\"></i>\n                  <span className=\"title\">Student Purchase Class</span>\n                </a>\n              </li>\n\n              {/* Offers */}\n              <li\n                className={isActive(\"/admin/offers\") ? \"active active-sub\" : \"\"}\n              >\n                <a href=\"/admin/offers\">\n                  <i className=\"fa fa-graduation-cap\"></i>\n                  <span className=\"title\">Offers Details</span>\n                </a>\n              </li>\n\n              {/* Student Invoice */}\n              <li\n                className={\n                  isActive(\"/admin/student-invoice\") ? \"active active-sub\" : \"\"\n                }\n              >\n                <a href=\"/admin/student-invoice\">\n                  <i className=\"fa fa-graduation-cap\"></i>\n                  <span className=\"title\">Student Invoice</span>\n                </a>\n              </li>\n\n              {/* Quizzes */}\n              <li\n                className={`treeview ${\n                  isTreeviewOpen(\"quizzes\") ? \"active menu-open\" : \"\"\n                }`}\n              >\n                <a\n                  href=\"#\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    toggleTreeview(\"quizzes\");\n                  }}\n                >\n                  <i className=\"fa fa-quora\"></i>\n                  <span className=\"title\">Quizes</span>\n                  <span className=\"pull-right-container\">\n                    <i\n                      className={`fa fa-angle-left pull-right ${\n                        isTreeviewOpen(\"quizzes\") ? \"fa-angle-down\" : \"\"\n                      }`}\n                    ></i>\n                  </span>\n                </a>\n                <ul\n                  className=\"treeview-menu\"\n                  style={{\n                    display: isTreeviewOpen(\"quizzes\") ? \"block\" : \"none\",\n                    transition: \"all 0.3s ease-in-out\",\n                    overflow: \"hidden\",\n                  }}\n                >\n                  <li\n                    className={\n                      isActive(\"/admin/quizzes\") ? \"active active-sub\" : \"\"\n                    }\n                  >\n                    <a href=\"/admin/quizzes\">\n                      <i className=\"fa fa-quora\"></i>\n                      <span className=\"title\">Quizes</span>\n                    </a>\n                  </li>\n                </ul>\n              </li>\n\n              {/* Assignment */}\n              <li\n                className={`treeview ${\n                  isTreeviewOpen(\"assignments\") ? \"active menu-open\" : \"\"\n                }`}\n              >\n                <a\n                  href=\"#\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    toggleTreeview(\"assignments\");\n                  }}\n                >\n                  <i className=\"fa fa-list\"></i>\n                  <span className=\"title\">Assignment</span>\n                  <span className=\"pull-right-container\">\n                    <i\n                      className={`fa fa-angle-left pull-right ${\n                        isTreeviewOpen(\"assignments\") ? \"fa-angle-down\" : \"\"\n                      }`}\n                    ></i>\n                  </span>\n                </a>\n                <ul\n                  className=\"treeview-menu\"\n                  style={{\n                    display: isTreeviewOpen(\"assignments\") ? \"block\" : \"none\",\n                    transition: \"all 0.3s ease-in-out\",\n                    overflow: \"hidden\",\n                  }}\n                >\n                  <li\n                    className={\n                      isActive(\"/admin/assignments\") ? \"active active-sub\" : \"\"\n                    }\n                  >\n                    <a href=\"/admin/assignments\">\n                      <i className=\"fa fa-list\"></i>\n                      <span className=\"title\">Assignment Details</span>\n                    </a>\n                  </li>\n                </ul>\n              </li>\n\n              {/* Live Classes */}\n              <li\n                className={`treeview ${\n                  isTreeviewOpen(\"live-classes\") ? \"active menu-open\" : \"\"\n                }`}\n              >\n                <a\n                  href=\"#\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    toggleTreeview(\"live-classes\");\n                  }}\n                >\n                  <i className=\"fa fa-video-camera\"></i>\n                  <span className=\"title\">Live Classes</span>\n                  <span className=\"pull-right-container\">\n                    <i\n                      className={`fa fa-angle-left pull-right ${\n                        isTreeviewOpen(\"live-classes\") ? \"fa-angle-down\" : \"\"\n                      }`}\n                    ></i>\n                  </span>\n                </a>\n                <ul\n                  className=\"treeview-menu\"\n                  style={{\n                    display: isTreeviewOpen(\"live-classes\") ? \"block\" : \"none\",\n                    transition: \"all 0.3s ease-in-out\",\n                    overflow: \"hidden\",\n                  }}\n                >\n                  <li\n                    className={\n                      isActive(\"/admin/live-classes\") ? \"active active-sub\" : \"\"\n                    }\n                  >\n                    <a href=\"/admin/live-classes\">\n                      <i className=\"fa fa-graduation-cap\"></i>\n                      <span className=\"title\">Live Classes Details</span>\n                    </a>\n                  </li>\n                </ul>\n              </li>\n\n              {/* Banners */}\n              <li\n                className={\n                  isActive(\"/admin/banners\") ? \"active active-sub\" : \"\"\n                }\n              >\n                <a href=\"/admin/banners\">\n                  <i className=\"fa fa-picture-o\"></i>\n                  <span className=\"title\">Banners</span>\n                </a>\n              </li>\n\n              {/* Referral */}\n              <li\n                className={\n                  isActive(\"/admin/referral\") ? \"active active-sub\" : \"\"\n                }\n              >\n                <a href=\"/admin/referral\">\n                  <i className=\"fa fa-picture-o\"></i>\n                  <span className=\"title\">Referral</span>\n                </a>\n              </li>\n\n              {/* Contact Us */}\n              <li className={isActive(\"/admin/contact-us\") ? \"active\" : \"\"}>\n                <a href=\"/admin/contact-us\">\n                  <i className=\"fa fa-envelope-open-o\"></i>\n                  <span className=\"title\">Contact Us</span>\n                </a>\n              </li>\n\n              {/* Notification */}\n              <li\n                className={`treeview ${\n                  isTreeviewOpen(\"notifications\") ? \"active menu-open\" : \"\"\n                }`}\n              >\n                <a\n                  href=\"#\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    toggleTreeview(\"notifications\");\n                  }}\n                >\n                  <i className=\"fa fa-bell\"></i>\n                  <span className=\"title\">Notification</span>\n                  <span className=\"pull-right-container\">\n                    <i\n                      className={`fa fa-angle-left pull-right ${\n                        isTreeviewOpen(\"notifications\") ? \"fa-angle-down\" : \"\"\n                      }`}\n                    ></i>\n                  </span>\n                </a>\n                <ul\n                  className=\"treeview-menu\"\n                  style={{\n                    display: isTreeviewOpen(\"notifications\") ? \"block\" : \"none\",\n                    transition: \"all 0.3s ease-in-out\",\n                    overflow: \"hidden\",\n                  }}\n                >\n                  <li\n                    className={\n                      isActive(\"/admin/notifications\")\n                        ? \"active active-sub\"\n                        : \"\"\n                    }\n                  >\n                    <a href=\"/admin/notifications\">\n                      <i className=\"fa fa-bell\"></i>\n                      <span className=\"title\">Notifications</span>\n                    </a>\n                  </li>\n                </ul>\n              </li>\n\n              {/* Reports */}\n              <li className={isActive(\"/admin/reports\") ? \"active\" : \"\"}>\n                <a href=\"/admin/reports\">\n                  <i className=\"fa fa-bar-chart\"></i>\n                  <span className=\"title\">Reports</span>\n                </a>\n              </li>\n\n              {/* CMS */}\n              <li\n                className={`treeview ${\n                  isTreeviewOpen(\"cms\") ? \"active menu-open\" : \"\"\n                }`}\n              >\n                <a\n                  href=\"#\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    toggleTreeview(\"cms\");\n                  }}\n                >\n                  <i className=\"fa fa-graduation-cap\"></i>\n                  <span className=\"title\">CMS</span>\n                  <span className=\"pull-right-container\">\n                    <i\n                      className={`fa fa-angle-left pull-right ${\n                        isTreeviewOpen(\"cms\") ? \"fa-angle-down\" : \"\"\n                      }`}\n                    ></i>\n                  </span>\n                </a>\n                <ul\n                  className=\"treeview-menu\"\n                  style={{\n                    display: isTreeviewOpen(\"cms\") ? \"block\" : \"none\",\n                    transition: \"all 0.3s ease-in-out\",\n                    overflow: \"hidden\",\n                  }}\n                >\n                  <li\n                    className={\n                      isActive(\"/admin/about-us\") ? \"active active-sub\" : \"\"\n                    }\n                  >\n                    <a href=\"/admin/about-us\">\n                      <i className=\"fa fa-graduation-cap\"></i>\n                      <span className=\"title\">About Us</span>\n                    </a>\n                  </li>\n                  <li\n                    className={\n                      isActive(\"/admin/privacy-policy\")\n                        ? \"active active-sub\"\n                        : \"\"\n                    }\n                  >\n                    <a href=\"/admin/privacy-policy\">\n                      <i className=\"fa fa-graduation-cap\"></i>\n                      <span className=\"title\">Privacy Policy</span>\n                    </a>\n                  </li>\n                  <li\n                    className={\n                      isActive(\"/admin/terms-condition\")\n                        ? \"active active-sub\"\n                        : \"\"\n                    }\n                  >\n                    <a href=\"/admin/terms-condition\">\n                      <i className=\"fa fa-graduation-cap\"></i>\n                      <span className=\"title\">Terms & Condition</span>\n                    </a>\n                  </li>\n                </ul>\n              </li>\n            </ul>\n          </section>\n        </aside>\n\n        {/* Content Wrapper. Contains page content */}\n        <div className=\"content-wrapper\">\n          {/* Main content */}\n          <section className=\"content\">\n            {title && (\n              <h3\n                className=\"page-title\"\n                style={{\n                  margin: \"20px 0\",\n                  color: \"#444444\",\n                  fontSize: \"24px\",\n                  fontWeight: 400,\n                }}\n              >\n                {title}\n              </h3>\n            )}\n\n            <div className=\"row\">\n              <div className=\"col-md-12\">{children}</div>\n            </div>\n          </section>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;;AAUe,SAAS,eAAe,EACrC,QAAQ,EACR,KAAK,EACe;IACpB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,MAAM,sBAAsB;QAC1B,oBAAoB,CAAC;IACvB;IAEA,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,WAAW,CAAC;QAChB,OAAO,aAAa;IACtB;IAEA,MAAM,iBAAiB,CAAC;QACtB,4DAA4D;QAC5D,gBAAgB,CAAC,OAAU,SAAS,KAAK,OAAO;IAClD;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,iBAAiB;IAC1B;IAEA,oFAAoF;IACpF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IACE,SAAS,QAAQ,CAAC,yBAClB,SAAS,QAAQ,CAAC,mBAClB,SAAS,QAAQ,CAAC,iBAClB;YACA,gBAAgB;QAClB,OAAO,IACL,SAAS,QAAQ,CAAC,qBAClB,SAAS,QAAQ,CAAC,oBAClB;YACA,gBAAgB;QAClB,OAAO,IACL,SAAS,QAAQ,CAAC,wBAClB,SAAS,QAAQ,CAAC,mBAClB;YACA,gBAAgB;QAClB,OAAO,IAAI,SAAS,QAAQ,CAAC,mBAAmB;YAC9C,gBAAgB;QAClB,OAAO,IAAI,SAAS,QAAQ,CAAC,uBAAuB;YAClD,gBAAgB;QAClB,OAAO,IAAI,SAAS,QAAQ,CAAC,wBAAwB;YACnD,gBAAgB;QAClB,OAAO,IAAI,SAAS,QAAQ,CAAC,yBAAyB;YACpD,gBAAgB;QAClB,OAAO,IACL,SAAS,QAAQ,CAAC,sBAClB,SAAS,QAAQ,CAAC,4BAClB,SAAS,QAAQ,CAAC,2BAClB;YACA,gBAAgB;QAClB,OAAO;YACL,gDAAgD;YAChD,gBAAgB;QAClB;IACF,GAAG;QAAC;KAAS;IAEb,qBACE;;;;;oBAGoB,eAAe,WAAW;oBAI7B,eAAe,MAAM;;oFAJlB,eAAe,WAAW,iIAI7B,eAAe,MAAM;;0BAIpC,8OAAC;;;;;4BARiB,eAAe,WAAW;4BAI7B,eAAe,MAAM;;;2BAKvB,CAAC,kBAAkB,EAC5B,mBAAmB,qBAAqB,IACxC;;kCAGF,8OAAC;;;;;oCAde,eAAe,WAAW;oCAI7B,eAAe,MAAM;;;mCAUhB;;0CAEhB,8OAAC;gCACC,MAAK;gCAEL,OAAO;oCAAE,UAAU;oCAAQ,OAAO;oCAAQ,gBAAgB;gCAAO;;;;;4CAnBrD,eAAe,WAAW;4CAI7B,eAAe,MAAM;;;2CAcpB;;kDAGV,8OAAC;;;;;oDArBW,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;;kDAiB3B;;;;;;oCAAS;;;;;;;0CAId,8OAAC;gCAEC,OAAO;oCAAE,SAAS;oCAAS,SAAS;gCAAE;;;;;4CA3B1B,eAAe,WAAW;4CAI7B,eAAe,MAAM;;;2CAsBpB;0CAIV,cAAA,8OAAC;oCACC,MAAK;oCAEL,SAAS,CAAC;wCACR,EAAE,cAAc;wCAChB;oCACF;oCACA,MAAK;;;;;gDArCK,eAAe,WAAW;gDAI7B,eAAe,MAAM;;;+CA4BlB;;sDAOV,8OAAC;;;;;wDAvCS,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;uDAmCZ;sDAAU;;;;;;sDAC1B,8OAAC;;;;;wDAxCS,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;uDAoCZ;;;;;;sDAChB,8OAAC;;;;;wDAzCS,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;uDAqCZ;;;;;;sDAChB,8OAAC;;;;;wDA1CS,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;uDAsCZ;;;;;;;;;;;;;;;;;;;;;;;kCAMtB,8OAAC;;;;;oCAhDe,eAAe,WAAW;oCAI7B,eAAe,MAAM;;;mCA4CjB;kCAEf,cAAA,8OAAC;;;;;wCAlDa,eAAe,WAAW;wCAI7B,eAAe,MAAM;;;uCA8Cb;sCACjB,cAAA,8OAAC;;;;;4CAnDW,eAAe,WAAW;4CAI7B,eAAe,MAAM;;;2CA+ChB;;kDAEZ,8OAAC;;;;;oDArDS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDAiDb,CAAA,SAAS,sBAAsB,WAAW,EAAC;kDACxD,cAAA,8OAAC;4CAAE,MAAK;;;;;wDAtDA,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DAmDxB,8OAAC;;;;;gEAvDK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAmDX;;;;;;8DACb,8OAAC;;;;;gEAxDK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAoDR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,8OAAC;;;;;oDA7DS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;mDA0Df,CAAC,SAAS,EACnB,eAAe,qBAAqB,qBAAqB,IACzD;;0DAEF,8OAAC;gDACC,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,eAAe;gDACjB;;;;;4DAvEM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;;;kEAqExB,8OAAC;;;;;oEAzEK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAqEX;;;;;;kEACb,8OAAC;;;;;oEA1EK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAsER;kEAAQ;;;;;;kEACxB,8OAAC;;;;;oEA3EK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAuER;kEACd,cAAA,8OAAC;;;;;wEA5EG,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;uEAyET,CAAC,4BAA4B,EACtC,eAAe,qBAAqB,kBAAkB,IACtD;;;;;;;;;;;;;;;;;0DAIR,8OAAC;gDAEC,OAAO;oDACL,SAAS,eAAe,qBACpB,UACA;oDACJ,YAAY;oDACZ,UAAU;gDACZ;;;;;4DA3FM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;2DAgFd;;kEASV,8OAAC;;;;;oEA7FK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;oEA2FpB,CAAA,SAAS,wBAAwB,sBAAsB,EAAC;kEAG1D,cAAA,8OAAC;4DAAE,MAAK;;;;;wEAlGJ,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;;;8EA+FpB,8OAAC;;;;;gFAnGC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EA+FP;;;;;;8EACb,8OAAC;;;;;gFApGC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAgGJ;8EAAQ;;;;;;;;;;;;;;;;;kEAG5B,8OAAC;;;;;oEAvGK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;oEAqGpB,CAAA,SAAS,kBAAkB,sBAAsB,EAAC;kEAGpD,cAAA,8OAAC;4DAAE,MAAK;;;;;wEA5GJ,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;;;8EAyGpB,8OAAC;;;;;gFA7GC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAyGP;;;;;;8EACb,8OAAC;;;;;gFA9GC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EA0GJ;8EAAQ;;;;;;;;;;;;;;;;;kEAG5B,8OAAC;;;;;oEAjHK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;oEA+GpB,CAAA,SAAS,kBAAkB,sBAAsB,EAAC;kEAGpD,cAAA,8OAAC;4DAAE,MAAK;;;;;wEAtHJ,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;;;8EAmHpB,8OAAC;;;;;gFAvHC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAmHP;;;;;;8EACb,8OAAC;;;;;gFAxHC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAoHJ;8EAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOhC,8OAAC;;;;;oDA/HS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDA4Hf,CAAA,SAAS,iBAAiB,sBAAsB,EAAC;kDAE5D,cAAA,8OAAC;4CAAE,MAAK;;;;;wDAlIA,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DA+HxB,8OAAC;;;;;gEAnIK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DA+HX;;;;;;8DACb,8OAAC;;;;;gEApIK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAgIR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,8OAAC;;;;;oDAzIS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDAuIxB,CAAA,SAAS,qBAAqB,sBAAsB,EAAC;kDAGvD,cAAA,8OAAC;4CAAE,MAAK;;;;;wDA9IA,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DA2IxB,8OAAC;;;;;gEA/IK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DA2IX;;;;;;8DACb,8OAAC;;;;;gEAhJK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DA4IR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,8OAAC;;;;;oDArJS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDAmJxB,CAAA,SAAS,qBAAqB,sBAAsB,EAAC;kDAGvD,cAAA,8OAAC;4CAAE,MAAK;;;;;wDA1JA,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DAuJxB,8OAAC;;;;;gEA3JK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAuJX;;;;;;8DACb,8OAAC;;;;;gEA5JK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAwJR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,8OAAC;;;;;oDAjKS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;mDA8Jf,CAAC,SAAS,EACnB,eAAe,uBAAuB,qBAAqB,IAC3D;;0DAEF,8OAAC;gDACC,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,eAAe;gDACjB;;;;;4DA3KM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;;;kEAyKxB,8OAAC;;;;;oEA7KK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAyKX;;;;;;kEACb,8OAAC;;;;;oEA9KK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEA0KR;kEAAQ;;;;;;kEACxB,8OAAC;;;;;oEA/KK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEA2KR;kEACd,cAAA,8OAAC;;;;;wEAhLG,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;uEA6KT,CAAC,4BAA4B,EACtC,eAAe,uBACX,kBACA,IACJ;;;;;;;;;;;;;;;;;0DAIR,8OAAC;gDAEC,OAAO;oDACL,SAAS,eAAe,uBACpB,UACA;oDACJ,YAAY;oDACZ,UAAU;gDACZ;;;;;4DAjMM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;2DAsLd;;kEASV,8OAAC;;;;;oEAnMK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;oEAiMpB,CAAA,SAAS,oBAAoB,sBAAsB,EAAC;kEAGtD,cAAA,8OAAC;4DAAE,MAAK;;;;;wEAxMJ,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;;;8EAqMpB,8OAAC;;;;;gFAzMC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAqMP;;;;;;8EACb,8OAAC;;;;;gFA1MC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAsMJ;8EAAQ;;;;;;;;;;;;;;;;;kEAG5B,8OAAC;;;;;oEA7MK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;oEA2MpB,CAAA,SAAS,qBAAqB,sBAAsB,EAAC;kEAGvD,cAAA,8OAAC;4DAAE,MAAK;;;;;wEAlNJ,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;;;8EA+MpB,8OAAC;;;;;gFAnNC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EA+MP;;;;;;8EACb,8OAAC;;;;;gFApNC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAgNJ;8EAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOhC,8OAAC;;;;;oDA3NS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;mDAwNf,CAAC,SAAS,EACnB,eAAe,qBAAqB,qBAAqB,IACzD;;0DAEF,8OAAC;gDACC,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,eAAe;gDACjB;;;;;4DArOM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;;;kEAmOxB,8OAAC;;;;;oEAvOK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAmOX;;;;;;kEACb,8OAAC;;;;;oEAxOK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAoOR;kEAAQ;;;;;;kEACxB,8OAAC;;;;;oEAzOK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAqOR;kEACd,cAAA,8OAAC;;;;;wEA1OG,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;uEAuOT,CAAC,4BAA4B,EACtC,eAAe,qBAAqB,kBAAkB,IACtD;;;;;;;;;;;;;;;;;0DAIR,8OAAC;gDAEC,OAAO;oDACL,SAAS,eAAe,qBACpB,UACA;oDACJ,YAAY;oDACZ,UAAU;gDACZ;;;;;4DAzPM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;2DA8Od;;kEASV,8OAAC;;;;;oEA3PK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;oEAyPpB,CAAA,SAAS,uBAAuB,sBAAsB,EAAC;kEAGzD,cAAA,8OAAC;4DAAE,MAAK;;;;;wEAhQJ,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;;;8EA6PpB,8OAAC;;;;;gFAjQC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EA6PP;;;;;;8EACb,8OAAC;;;;;gFAlQC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EA8PJ;8EAAQ;;;;;;;;;;;;;;;;;kEAG5B,8OAAC;;;;;oEArQK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;oEAmQpB,CAAA,SAAS,oBAAoB,sBAAsB,EAAC;kEAGtD,cAAA,8OAAC;4DAAE,MAAK;;;;;wEA1QJ,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;;;8EAuQpB,8OAAC;;;;;gFA3QC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAuQP;;;;;;8EACb,8OAAC;;;;;gFA5QC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAwQJ;8EAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOhC,8OAAC;;;;;oDAnRS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDAiRxB,CAAA,SAAS,0BAA0B,sBAAsB,EAAC;kDAG5D,cAAA,8OAAC;4CAAE,MAAK;;;;;wDAxRA,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DAqRxB,8OAAC;;;;;gEAzRK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAqRX;;;;;;8DACb,8OAAC;;;;;gEA1RK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAsRR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,8OAAC;;;;;oDA/RS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDA6RxB,CAAA,SAAS,uBAAuB,sBAAsB,EAAC;kDAGzD,cAAA,8OAAC;4CAAE,MAAK;;;;;wDApSA,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DAiSxB,8OAAC;;;;;gEArSK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAiSX;;;;;;8DACb,8OAAC;;;;;gEAtSK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAkSR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,8OAAC;;;;;oDA3SS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDAySxB,CAAA,SAAS,+BACL,sBACA,EAAC;kDAGP,cAAA,8OAAC;4CAAE,MAAK;;;;;wDAlTA,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DA+SxB,8OAAC;;;;;gEAnTK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DA+SX;;;;;;8DACb,8OAAC;;;;;gEApTK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAgTR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,8OAAC;;;;;oDAzTS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDAuTxB,CAAA,SAAS,mCACL,sBACA,EAAC;kDAGP,cAAA,8OAAC;4CAAE,MAAK;;;;;wDAhUA,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DA6TxB,8OAAC;;;;;gEAjUK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DA6TX;;;;;;8DACb,8OAAC;;;;;gEAlUK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DA8TR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,8OAAC;;;;;oDAvUS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDAoUf,CAAA,SAAS,mBAAmB,sBAAsB,EAAC;kDAE9D,cAAA,8OAAC;4CAAE,MAAK;;;;;wDA1UA,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DAuUxB,8OAAC;;;;;gEA3UK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAuUX;;;;;;8DACb,8OAAC;;;;;gEA5UK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAwUR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,8OAAC;;;;;oDAjVS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDA+UxB,CAAA,SAAS,4BAA4B,sBAAsB,EAAC;kDAG9D,cAAA,8OAAC;4CAAE,MAAK;;;;;wDAtVA,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DAmVxB,8OAAC;;;;;gEAvVK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAmVX;;;;;;8DACb,8OAAC;;;;;gEAxVK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAoVR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,8OAAC;;;;;oDA7VS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;mDA0Vf,CAAC,SAAS,EACnB,eAAe,aAAa,qBAAqB,IACjD;;0DAEF,8OAAC;gDACC,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,eAAe;gDACjB;;;;;4DAvWM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;;;kEAqWxB,8OAAC;;;;;oEAzWK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAqWX;;;;;;kEACb,8OAAC;;;;;oEA1WK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAsWR;kEAAQ;;;;;;kEACxB,8OAAC;;;;;oEA3WK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAuWR;kEACd,cAAA,8OAAC;;;;;wEA5WG,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;uEAyWT,CAAC,4BAA4B,EACtC,eAAe,aAAa,kBAAkB,IAC9C;;;;;;;;;;;;;;;;;0DAIR,8OAAC;gDAEC,OAAO;oDACL,SAAS,eAAe,aAAa,UAAU;oDAC/C,YAAY;oDACZ,UAAU;gDACZ;;;;;4DAzXM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;2DAgXd;0DAOV,cAAA,8OAAC;;;;;gEA3XK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;gEAyXpB,CAAA,SAAS,oBAAoB,sBAAsB,EAAC;8DAGtD,cAAA,8OAAC;wDAAE,MAAK;;;;;oEAhYJ,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;;;0EA6XpB,8OAAC;;;;;4EAjYC,eAAe,WAAW;4EAI7B,eAAe,MAAM;;;2EA6XP;;;;;;0EACb,8OAAC;;;;;4EAlYC,eAAe,WAAW;4EAI7B,eAAe,MAAM;;;2EA8XJ;0EAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOhC,8OAAC;;;;;oDAzYS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;mDAsYf,CAAC,SAAS,EACnB,eAAe,iBAAiB,qBAAqB,IACrD;;0DAEF,8OAAC;gDACC,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,eAAe;gDACjB;;;;;4DAnZM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;;;kEAiZxB,8OAAC;;;;;oEArZK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAiZX;;;;;;kEACb,8OAAC;;;;;oEAtZK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAkZR;kEAAQ;;;;;;kEACxB,8OAAC;;;;;oEAvZK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAmZR;kEACd,cAAA,8OAAC;;;;;wEAxZG,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;uEAqZT,CAAC,4BAA4B,EACtC,eAAe,iBAAiB,kBAAkB,IAClD;;;;;;;;;;;;;;;;;0DAIR,8OAAC;gDAEC,OAAO;oDACL,SAAS,eAAe,iBAAiB,UAAU;oDACnD,YAAY;oDACZ,UAAU;gDACZ;;;;;4DAraM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;2DA4Zd;0DAOV,cAAA,8OAAC;;;;;gEAvaK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;gEAqapB,CAAA,SAAS,wBAAwB,sBAAsB,EAAC;8DAG1D,cAAA,8OAAC;wDAAE,MAAK;;;;;oEA5aJ,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;;;0EAyapB,8OAAC;;;;;4EA7aC,eAAe,WAAW;4EAI7B,eAAe,MAAM;;;2EAyaP;;;;;;0EACb,8OAAC;;;;;4EA9aC,eAAe,WAAW;4EAI7B,eAAe,MAAM;;;2EA0aJ;0EAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOhC,8OAAC;;;;;oDArbS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;mDAkbf,CAAC,SAAS,EACnB,eAAe,kBAAkB,qBAAqB,IACtD;;0DAEF,8OAAC;gDACC,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,eAAe;gDACjB;;;;;4DA/bM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;;;kEA6bxB,8OAAC;;;;;oEAjcK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEA6bX;;;;;;kEACb,8OAAC;;;;;oEAlcK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEA8bR;kEAAQ;;;;;;kEACxB,8OAAC;;;;;oEAncK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEA+bR;kEACd,cAAA,8OAAC;;;;;wEApcG,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;uEAicT,CAAC,4BAA4B,EACtC,eAAe,kBAAkB,kBAAkB,IACnD;;;;;;;;;;;;;;;;;0DAIR,8OAAC;gDAEC,OAAO;oDACL,SAAS,eAAe,kBAAkB,UAAU;oDACpD,YAAY;oDACZ,UAAU;gDACZ;;;;;4DAjdM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;2DAwcd;0DAOV,cAAA,8OAAC;;;;;gEAndK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;gEAidpB,CAAA,SAAS,yBAAyB,sBAAsB,EAAC;8DAG3D,cAAA,8OAAC;wDAAE,MAAK;;;;;oEAxdJ,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;;;0EAqdpB,8OAAC;;;;;4EAzdC,eAAe,WAAW;4EAI7B,eAAe,MAAM;;;2EAqdP;;;;;;0EACb,8OAAC;;;;;4EA1dC,eAAe,WAAW;4EAI7B,eAAe,MAAM;;;2EAsdJ;0EAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOhC,8OAAC;;;;;oDAjeS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDA+dxB,CAAA,SAAS,oBAAoB,sBAAsB,EAAC;kDAGtD,cAAA,8OAAC;4CAAE,MAAK;;;;;wDAteA,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DAmexB,8OAAC;;;;;gEAveK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAmeX;;;;;;8DACb,8OAAC;;;;;gEAxeK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAoeR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,8OAAC;;;;;oDA7eS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDA2exB,CAAA,SAAS,qBAAqB,sBAAsB,EAAC;kDAGvD,cAAA,8OAAC;4CAAE,MAAK;;;;;wDAlfA,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DA+exB,8OAAC;;;;;gEAnfK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DA+eX;;;;;;8DACb,8OAAC;;;;;gEApfK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAgfR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,8OAAC;;;;;oDAzfS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDAqfb,CAAA,SAAS,uBAAuB,WAAW,EAAC;kDACzD,cAAA,8OAAC;4CAAE,MAAK;;;;;wDA1fA,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DAufxB,8OAAC;;;;;gEA3fK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAufX;;;;;;8DACb,8OAAC;;;;;gEA5fK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DAwfR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,8OAAC;;;;;oDAjgBS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;mDA8ff,CAAC,SAAS,EACnB,eAAe,mBAAmB,qBAAqB,IACvD;;0DAEF,8OAAC;gDACC,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,eAAe;gDACjB;;;;;4DA3gBM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;;;kEAygBxB,8OAAC;;;;;oEA7gBK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAygBX;;;;;;kEACb,8OAAC;;;;;oEA9gBK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEA0gBR;kEAAQ;;;;;;kEACxB,8OAAC;;;;;oEA/gBK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEA2gBR;kEACd,cAAA,8OAAC;;;;;wEAhhBG,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;uEA6gBT,CAAC,4BAA4B,EACtC,eAAe,mBAAmB,kBAAkB,IACpD;;;;;;;;;;;;;;;;;0DAIR,8OAAC;gDAEC,OAAO;oDACL,SAAS,eAAe,mBAAmB,UAAU;oDACrD,YAAY;oDACZ,UAAU;gDACZ;;;;;4DA7hBM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;2DAohBd;0DAOV,cAAA,8OAAC;;;;;gEA/hBK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;gEA6hBpB,CAAA,SAAS,0BACL,sBACA,EAAC;8DAGP,cAAA,8OAAC;wDAAE,MAAK;;;;;oEAtiBJ,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;;;0EAmiBpB,8OAAC;;;;;4EAviBC,eAAe,WAAW;4EAI7B,eAAe,MAAM;;;2EAmiBP;;;;;;0EACb,8OAAC;;;;;4EAxiBC,eAAe,WAAW;4EAI7B,eAAe,MAAM;;;2EAoiBJ;0EAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOhC,8OAAC;;;;;oDA/iBS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;oDA2iBb,CAAA,SAAS,oBAAoB,WAAW,EAAC;kDACtD,cAAA,8OAAC;4CAAE,MAAK;;;;;wDAhjBA,eAAe,WAAW;wDAI7B,eAAe,MAAM;;;;;8DA6iBxB,8OAAC;;;;;gEAjjBK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DA6iBX;;;;;;8DACb,8OAAC;;;;;gEAljBK,eAAe,WAAW;gEAI7B,eAAe,MAAM;;;+DA8iBR;8DAAQ;;;;;;;;;;;;;;;;;kDAK5B,8OAAC;;;;;oDAvjBS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;mDAojBf,CAAC,SAAS,EACnB,eAAe,SAAS,qBAAqB,IAC7C;;0DAEF,8OAAC;gDACC,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,eAAe;gDACjB;;;;;4DAjkBM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;;;kEA+jBxB,8OAAC;;;;;oEAnkBK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEA+jBX;;;;;;kEACb,8OAAC;;;;;oEApkBK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAgkBR;kEAAQ;;;;;;kEACxB,8OAAC;;;;;oEArkBK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;mEAikBR;kEACd,cAAA,8OAAC;;;;;wEAtkBG,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;uEAmkBT,CAAC,4BAA4B,EACtC,eAAe,SAAS,kBAAkB,IAC1C;;;;;;;;;;;;;;;;;0DAIR,8OAAC;gDAEC,OAAO;oDACL,SAAS,eAAe,SAAS,UAAU;oDAC3C,YAAY;oDACZ,UAAU;gDACZ;;;;;4DAnlBM,eAAe,WAAW;4DAI7B,eAAe,MAAM;;;2DA0kBd;;kEAOV,8OAAC;;;;;oEArlBK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;oEAmlBpB,CAAA,SAAS,qBAAqB,sBAAsB,EAAC;kEAGvD,cAAA,8OAAC;4DAAE,MAAK;;;;;wEA1lBJ,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;;;8EAulBpB,8OAAC;;;;;gFA3lBC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAulBP;;;;;;8EACb,8OAAC;;;;;gFA5lBC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAwlBJ;8EAAQ;;;;;;;;;;;;;;;;;kEAG5B,8OAAC;;;;;oEA/lBK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;oEA6lBpB,CAAA,SAAS,2BACL,sBACA,EAAC;kEAGP,cAAA,8OAAC;4DAAE,MAAK;;;;;wEAtmBJ,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;;;8EAmmBpB,8OAAC;;;;;gFAvmBC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAmmBP;;;;;;8EACb,8OAAC;;;;;gFAxmBC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAomBJ;8EAAQ;;;;;;;;;;;;;;;;;kEAG5B,8OAAC;;;;;oEA3mBK,eAAe,WAAW;oEAI7B,eAAe,MAAM;;;oEAymBpB,CAAA,SAAS,4BACL,sBACA,EAAC;kEAGP,cAAA,8OAAC;4DAAE,MAAK;;;;;wEAlnBJ,eAAe,WAAW;wEAI7B,eAAe,MAAM;;;;;8EA+mBpB,8OAAC;;;;;gFAnnBC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EA+mBP;;;;;;8EACb,8OAAC;;;;;gFApnBC,eAAe,WAAW;gFAI7B,eAAe,MAAM;;;+EAgnBJ;8EAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUtC,8OAAC;;;;;oCA9nBe,eAAe,WAAW;oCAI7B,eAAe,MAAM;;;mCA0nBnB;kCAEb,cAAA,8OAAC;;;;;wCAhoBa,eAAe,WAAW;wCAI7B,eAAe,MAAM;;;uCA4nBb;;gCAChB,uBACC,8OAAC;oCAEC,OAAO;wCACL,QAAQ;wCACR,OAAO;wCACP,UAAU;wCACV,YAAY;oCACd;;;;;gDAzoBQ,eAAe,WAAW;gDAI7B,eAAe,MAAM;;;+CA+nBhB;8CAQT;;;;;;8CAIL,8OAAC;;;;;gDA/oBW,eAAe,WAAW;gDAI7B,eAAe,MAAM;;;+CA2oBf;8CACb,cAAA,8OAAC;;;;;oDAhpBS,eAAe,WAAW;oDAI7B,eAAe,MAAM;;;mDA4oBb;kDAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1C", "debugId": null}}, {"offset": {"line": 3179, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/src/app/admin/roles/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport AdminLTELayout from '@/components/layout/AdminLTELayout';\n\n// Dummy roles data that matches Laravel structure\nconst roles = [\n  {\n    id: 1,\n    name: 'Admin',\n    permissions: ['User Management', 'Permission', 'Role', 'Teacher Management', 'Student Management', 'Dashboard']\n  },\n  {\n    id: 2,\n    name: 'Teacher',\n    permissions: ['Student Management', 'Assignments', 'Quiz', 'Live Classes', 'Dashboard']\n  },\n  {\n    id: 3,\n    name: 'Student',\n    permissions: ['Dashboard', 'Assignments', 'Quiz']\n  },\n  {\n    id: 4,\n    name: 'Course Builder',\n    permissions: ['Category', 'Assignments', 'Quiz', 'Dashboard']\n  },\n  {\n    id: 5,\n    name: 'Director',\n    permissions: ['Teacher Management', 'Student Management', 'Reports', 'Dashboard']\n  }\n];\n\nexport default function RolesPage() {\n  return (\n    <AdminLTELayout>\n      <h3 className=\"page-title\">Roles</h3>\n      <p>\n        <a href=\"/admin/roles/create\" className=\"btn btn-success\">Add New</a>\n      </p>\n\n      <div className=\"panel panel-default\">\n        <div style={{ fontSize: '20px' }} className=\"panel-heading\">\n          List\n        </div>\n\n        <div className=\"panel-body scroll-table table-responsive\">\n          <table className={`table table-bordered table-striped ${roles.length > 0 ? 'datatable1' : ''} dt-select`}>\n            <thead>\n              <tr>\n                <th style={{ fontSize: '15px' }}>Sr.No</th>\n                <th style={{ fontSize: '15px' }}>Name</th>\n                <th style={{ fontSize: '15px' }}>Permissions</th>\n                <th style={{ fontSize: '15px' }}>Action</th>\n              </tr>\n            </thead>\n            \n            <tbody>\n              {roles.length > 0 ? (\n                roles.map((role, index) => (\n                  <tr key={role.id} data-entry-id={role.id}>\n                    <td>{index + 1}</td>\n                    <td>{role.name}</td>\n                    <td>\n                      {role.permissions.map((permission, permIndex) => (\n                        <span key={permIndex} className=\"label label-info label-many\" style={{ marginRight: '5px', marginBottom: '2px', display: 'inline-block' }}>\n                          {permission}\n                        </span>\n                      ))}\n                    </td>\n                    <td>\n                      <a \n                        style={{ fontSize: '15px' }} \n                        href={`/admin/roles/edit/${role.id}`} \n                        className=\"btn btn-primary btn-sm\"\n                      >\n                        Edit\n                      </a>\n                    </td>\n                  </tr>\n                ))\n              ) : (\n                <tr>\n                  <td colSpan={4}>No entries in table</td>\n                </tr>\n              )}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </AdminLTELayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,kDAAkD;AAClD,MAAM,QAAQ;IACZ;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YAAC;YAAmB;YAAc;YAAQ;YAAsB;YAAsB;SAAY;IACjH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YAAC;YAAsB;YAAe;YAAQ;YAAgB;SAAY;IACzF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YAAC;YAAa;YAAe;SAAO;IACnD;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YAAC;YAAY;YAAe;YAAQ;SAAY;IAC/D;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YAAC;YAAsB;YAAsB;YAAW;SAAY;IACnF;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC,8IAAA,CAAA,UAAc;;0BACb,8OAAC;gBAAG,WAAU;0BAAa;;;;;;0BAC3B,8OAAC;0BACC,cAAA,8OAAC;oBAAE,MAAK;oBAAsB,WAAU;8BAAkB;;;;;;;;;;;0BAG5D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,OAAO;4BAAE,UAAU;wBAAO;wBAAG,WAAU;kCAAgB;;;;;;kCAI5D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAM,WAAW,CAAC,mCAAmC,EAAE,MAAM,MAAM,GAAG,IAAI,eAAe,GAAG,UAAU,CAAC;;8CACtG,8OAAC;8CACC,cAAA,8OAAC;;0DACC,8OAAC;gDAAG,OAAO;oDAAE,UAAU;gDAAO;0DAAG;;;;;;0DACjC,8OAAC;gDAAG,OAAO;oDAAE,UAAU;gDAAO;0DAAG;;;;;;0DACjC,8OAAC;gDAAG,OAAO;oDAAE,UAAU;gDAAO;0DAAG;;;;;;0DACjC,8OAAC;gDAAG,OAAO;oDAAE,UAAU;gDAAO;0DAAG;;;;;;;;;;;;;;;;;8CAIrC,8OAAC;8CACE,MAAM,MAAM,GAAG,IACd,MAAM,GAAG,CAAC,CAAC,MAAM,sBACf,8OAAC;4CAAiB,iBAAe,KAAK,EAAE;;8DACtC,8OAAC;8DAAI,QAAQ;;;;;;8DACb,8OAAC;8DAAI,KAAK,IAAI;;;;;;8DACd,8OAAC;8DACE,KAAK,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY,0BACjC,8OAAC;4DAAqB,WAAU;4DAA8B,OAAO;gEAAE,aAAa;gEAAO,cAAc;gEAAO,SAAS;4DAAe;sEACrI;2DADQ;;;;;;;;;;8DAKf,8OAAC;8DACC,cAAA,8OAAC;wDACC,OAAO;4DAAE,UAAU;wDAAO;wDAC1B,MAAM,CAAC,kBAAkB,EAAE,KAAK,EAAE,EAAE;wDACpC,WAAU;kEACX;;;;;;;;;;;;2CAfI,KAAK,EAAE;;;;kEAsBlB,8OAAC;kDACC,cAAA,8OAAC;4CAAG,SAAS;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlC", "debugId": null}}]}