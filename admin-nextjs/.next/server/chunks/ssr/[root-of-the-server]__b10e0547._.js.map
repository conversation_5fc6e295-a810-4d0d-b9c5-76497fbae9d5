{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/src/components/layout/AdminLTELayout.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { useRouter, usePathname } from \"next/navigation\";\n\ninterface AdminLTELayoutProps {\n  children: React.ReactNode;\n  title?: string;\n}\n\nexport default function AdminLTELayout({\n  children,\n  title,\n}: AdminLTELayoutProps) {\n  const router = useRouter();\n  const pathname = usePathname();\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const [openTreeviews, setOpenTreeviews] = useState<string[]>([]);\n\n  const handleSidebarToggle = () => {\n    setSidebarCollapsed(!sidebarCollapsed);\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem(\"auth_token\");\n    localStorage.removeItem(\"user_role\");\n    router.push(\"/login\");\n  };\n\n  const isActive = (path: string) => {\n    return pathname === path;\n  };\n\n  const toggleTreeview = (id: string) => {\n    setOpenTreeviews((prev) =>\n      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]\n    );\n  };\n\n  const isTreeviewOpen = (id: string) => {\n    return openTreeviews.includes(id);\n  };\n\n  // Initialize treeview states based on current path\n  useEffect(() => {\n    if (\n      pathname.includes(\"/admin/permissions\") ||\n      pathname.includes(\"/admin/roles\") ||\n      pathname.includes(\"/admin/users\")\n    ) {\n      setOpenTreeviews((prev) => [...prev, \"user-management\"]);\n    }\n    if (\n      pathname.includes(\"/admin/classes\") ||\n      pathname.includes(\"/admin/subjects\")\n    ) {\n      setOpenTreeviews((prev) => [...prev, \"course-management\"]);\n    }\n    if (\n      pathname.includes(\"/admin/categories\") ||\n      pathname.includes(\"/admin/courses\")\n    ) {\n      setOpenTreeviews((prev) => [...prev, \"course-category\"]);\n    }\n    if (pathname.includes(\"/admin/quizzes\")) {\n      setOpenTreeviews((prev) => [...prev, \"quizzes\"]);\n    }\n    if (pathname.includes(\"/admin/assignments\")) {\n      setOpenTreeviews((prev) => [...prev, \"assignments\"]);\n    }\n    if (pathname.includes(\"/admin/live-classes\")) {\n      setOpenTreeviews((prev) => [...prev, \"live-classes\"]);\n    }\n    if (pathname.includes(\"/admin/notifications\")) {\n      setOpenTreeviews((prev) => [...prev, \"notifications\"]);\n    }\n    if (\n      pathname.includes(\"/admin/about-us\") ||\n      pathname.includes(\"/admin/privacy-policy\") ||\n      pathname.includes(\"/admin/terms-condition\")\n    ) {\n      setOpenTreeviews((prev) => [...prev, \"cms\"]);\n    }\n  }, [pathname]);\n\n  return (\n    <div\n      className={`wrapper skin-blue ${\n        sidebarCollapsed ? \"sidebar-collapse\" : \"\"\n      }`}\n    >\n      {/* Main Header */}\n      <header className=\"main-header\">\n        {/* Logo */}\n        <a\n          href=\"#\"\n          className=\"logo\"\n          style={{ fontSize: \"16px\", color: \"#fff\", textDecoration: \"none\" }}\n        >\n          <b>Admin</b>LTE\n        </a>\n\n        {/* Header Navbar */}\n        <nav\n          className=\"navbar navbar-static-top\"\n          style={{ display: \"block\", padding: 0 }}\n        >\n          {/* Sidebar toggle button */}\n          <a\n            href=\"#\"\n            className=\"sidebar-toggle\"\n            onClick={(e) => {\n              e.preventDefault();\n              handleSidebarToggle();\n            }}\n            role=\"button\"\n          >\n            <span className=\"sr-only\">Toggle navigation</span>\n            <span className=\"icon-bar\"></span>\n            <span className=\"icon-bar\"></span>\n            <span className=\"icon-bar\"></span>\n          </a>\n        </nav>\n      </header>\n\n      {/* Left side column. contains the sidebar */}\n      <aside className=\"main-sidebar\">\n        {/* sidebar: style can be found in sidebar.less */}\n        <section className=\"sidebar\">\n          <ul className=\"sidebar-menu scroll-table scroll-table-two\">\n            {/* Dashboard */}\n            <li className={isActive(\"/admin/dashboard\") ? \"active\" : \"\"}>\n              <a href=\"/admin/dashboard\">\n                <i className=\"fa fa-dashboard\"></i>\n                <span className=\"title\">Dashboard</span>\n              </a>\n            </li>\n\n            {/* User Management */}\n            <li\n              className={`treeview ${\n                isTreeviewOpen(\"user-management\") ? \"active menu-open\" : \"\"\n              }`}\n            >\n              <a\n                href=\"#\"\n                onClick={(e) => {\n                  e.preventDefault();\n                  toggleTreeview(\"user-management\");\n                }}\n              >\n                <i className=\"fa fa-users\"></i>\n                <span className=\"title\">User Management</span>\n                <span className=\"pull-right-container\">\n                  <i\n                    className={`fa fa-angle-left pull-right ${\n                      isTreeviewOpen(\"user-management\") ? \"fa-angle-down\" : \"\"\n                    }`}\n                  ></i>\n                </span>\n              </a>\n              <ul\n                className=\"treeview-menu\"\n                style={{\n                  display: isTreeviewOpen(\"user-management\") ? \"block\" : \"none\",\n                }}\n              >\n                <li\n                  className={\n                    isActive(\"/admin/permissions\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/permissions\">\n                    <i className=\"fa fa-briefcase\"></i>\n                    <span className=\"title\">Permissions</span>\n                  </a>\n                </li>\n                <li\n                  className={\n                    isActive(\"/admin/roles\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/roles\">\n                    <i className=\"fa fa-briefcase\"></i>\n                    <span className=\"title\">Roles</span>\n                  </a>\n                </li>\n                <li\n                  className={\n                    isActive(\"/admin/users\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/users\">\n                    <i className=\"fa fa-user\"></i>\n                    <span className=\"title\">Administrator Users</span>\n                  </a>\n                </li>\n              </ul>\n            </li>\n\n            {/* Fees Update */}\n            <li className={isActive(\"/admin/fees\") ? \"active active-sub\" : \"\"}>\n              <a href=\"/admin/fees\">\n                <i className=\"fa fa-briefcase\"></i>\n                <span className=\"title\">Fees Update</span>\n              </a>\n            </li>\n\n            {/* Teacher Management */}\n            <li\n              className={isActive(\"/admin/teachers\") ? \"active active-sub\" : \"\"}\n            >\n              <a href=\"/admin/teachers\">\n                <i className=\"fa fa-user\"></i>\n                <span className=\"title\">Teacher Management</span>\n              </a>\n            </li>\n\n            {/* Student Management */}\n            <li\n              className={isActive(\"/admin/students\") ? \"active active-sub\" : \"\"}\n            >\n              <a href=\"/admin/students\">\n                <i className=\"fa fa-user\"></i>\n                <span className=\"title\">Student Management</span>\n              </a>\n            </li>\n\n            {/* Course Management */}\n            <li\n              className={`treeview ${\n                isTreeviewOpen(\"course-management\") ? \"active menu-open\" : \"\"\n              }`}\n            >\n              <a\n                href=\"#\"\n                onClick={(e) => {\n                  e.preventDefault();\n                  toggleTreeview(\"course-management\");\n                }}\n              >\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Course Management</span>\n                <span className=\"pull-right-container\">\n                  <i\n                    className={`fa fa-angle-left pull-right ${\n                      isTreeviewOpen(\"course-management\") ? \"fa-angle-down\" : \"\"\n                    }`}\n                  ></i>\n                </span>\n              </a>\n              <ul\n                className=\"treeview-menu\"\n                style={{\n                  display: isTreeviewOpen(\"course-management\")\n                    ? \"block\"\n                    : \"none\",\n                }}\n              >\n                <li\n                  className={\n                    isActive(\"/admin/classes\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/classes\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">Class Details</span>\n                  </a>\n                </li>\n                <li\n                  className={\n                    isActive(\"/admin/subjects\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/subjects\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">Subjects Details</span>\n                  </a>\n                </li>\n              </ul>\n            </li>\n\n            {/* Course & Category */}\n            <li\n              className={`treeview ${\n                isTreeviewOpen(\"course-category\") ? \"active menu-open\" : \"\"\n              }`}\n            >\n              <a\n                href=\"#\"\n                onClick={(e) => {\n                  e.preventDefault();\n                  toggleTreeview(\"course-category\");\n                }}\n              >\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Course & Category</span>\n                <span className=\"pull-right-container\">\n                  <i\n                    className={`fa fa-angle-left pull-right ${\n                      isTreeviewOpen(\"course-category\") ? \"fa-angle-down\" : \"\"\n                    }`}\n                  ></i>\n                </span>\n              </a>\n              <ul\n                className=\"treeview-menu\"\n                style={{\n                  display: isTreeviewOpen(\"course-category\") ? \"block\" : \"none\",\n                }}\n              >\n                <li\n                  className={\n                    isActive(\"/admin/categories\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/categories\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">Category Details</span>\n                  </a>\n                </li>\n                <li\n                  className={\n                    isActive(\"/admin/courses\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/courses\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">Course Details</span>\n                  </a>\n                </li>\n              </ul>\n            </li>\n\n            {/* Demo Request */}\n            <li\n              className={\n                isActive(\"/admin/demo-requests\") ? \"active active-sub\" : \"\"\n              }\n            >\n              <a href=\"/admin/demo-requests\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Demo Request</span>\n              </a>\n            </li>\n\n            {/* Withdrawal */}\n            <li\n              className={\n                isActive(\"/admin/withdrawal\") ? \"active active-sub\" : \"\"\n              }\n            >\n              <a href=\"/admin/withdrawal\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Withdrawal</span>\n              </a>\n            </li>\n\n            {/* Incomplete Classes */}\n            <li\n              className={\n                isActive(\"/admin/incomplete-classes\") ? \"active active-sub\" : \"\"\n              }\n            >\n              <a href=\"/admin/incomplete-classes\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Incomplete Classes</span>\n              </a>\n            </li>\n\n            {/* Student Purchase Class */}\n            <li\n              className={\n                isActive(\"/admin/student-purchase-class\")\n                  ? \"active active-sub\"\n                  : \"\"\n              }\n            >\n              <a href=\"/admin/student-purchase-class\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Student Purchase Class</span>\n              </a>\n            </li>\n\n            {/* Offers */}\n            <li\n              className={isActive(\"/admin/offers\") ? \"active active-sub\" : \"\"}\n            >\n              <a href=\"/admin/offers\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Offers Details</span>\n              </a>\n            </li>\n\n            {/* Student Invoice */}\n            <li\n              className={\n                isActive(\"/admin/student-invoice\") ? \"active active-sub\" : \"\"\n              }\n            >\n              <a href=\"/admin/student-invoice\">\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">Student Invoice</span>\n              </a>\n            </li>\n\n            {/* Quizzes */}\n            <li\n              className={`treeview ${\n                isTreeviewOpen(\"quizzes\") ? \"active menu-open\" : \"\"\n              }`}\n            >\n              <a\n                href=\"#\"\n                onClick={(e) => {\n                  e.preventDefault();\n                  toggleTreeview(\"quizzes\");\n                }}\n              >\n                <i className=\"fa fa-quora\"></i>\n                <span className=\"title\">Quizes</span>\n                <span className=\"pull-right-container\">\n                  <i\n                    className={`fa fa-angle-left pull-right ${\n                      isTreeviewOpen(\"quizzes\") ? \"fa-angle-down\" : \"\"\n                    }`}\n                  ></i>\n                </span>\n              </a>\n              <ul\n                className=\"treeview-menu\"\n                style={{\n                  display: isTreeviewOpen(\"quizzes\") ? \"block\" : \"none\",\n                }}\n              >\n                <li\n                  className={\n                    isActive(\"/admin/quizzes\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/quizzes\">\n                    <i className=\"fa fa-quora\"></i>\n                    <span className=\"title\">Quizes</span>\n                  </a>\n                </li>\n              </ul>\n            </li>\n\n            {/* Assignment */}\n            <li\n              className={`treeview ${\n                isTreeviewOpen(\"assignments\") ? \"active menu-open\" : \"\"\n              }`}\n            >\n              <a\n                href=\"#\"\n                onClick={(e) => {\n                  e.preventDefault();\n                  toggleTreeview(\"assignments\");\n                }}\n              >\n                <i className=\"fa fa-list\"></i>\n                <span className=\"title\">Assignment</span>\n                <span className=\"pull-right-container\">\n                  <i\n                    className={`fa fa-angle-left pull-right ${\n                      isTreeviewOpen(\"assignments\") ? \"fa-angle-down\" : \"\"\n                    }`}\n                  ></i>\n                </span>\n              </a>\n              <ul\n                className=\"treeview-menu\"\n                style={{\n                  display: isTreeviewOpen(\"assignments\") ? \"block\" : \"none\",\n                }}\n              >\n                <li\n                  className={\n                    isActive(\"/admin/assignments\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/assignments\">\n                    <i className=\"fa fa-list\"></i>\n                    <span className=\"title\">Assignment Details</span>\n                  </a>\n                </li>\n              </ul>\n            </li>\n\n            {/* Live Classes */}\n            <li\n              className={`treeview ${\n                isTreeviewOpen(\"live-classes\") ? \"active menu-open\" : \"\"\n              }`}\n            >\n              <a\n                href=\"#\"\n                onClick={(e) => {\n                  e.preventDefault();\n                  toggleTreeview(\"live-classes\");\n                }}\n              >\n                <i className=\"fa fa-video-camera\"></i>\n                <span className=\"title\">Live Classes</span>\n                <span className=\"pull-right-container\">\n                  <i\n                    className={`fa fa-angle-left pull-right ${\n                      isTreeviewOpen(\"live-classes\") ? \"fa-angle-down\" : \"\"\n                    }`}\n                  ></i>\n                </span>\n              </a>\n              <ul\n                className=\"treeview-menu\"\n                style={{\n                  display: isTreeviewOpen(\"live-classes\") ? \"block\" : \"none\",\n                }}\n              >\n                <li\n                  className={\n                    isActive(\"/admin/live-classes\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/live-classes\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">Live Classes Details</span>\n                  </a>\n                </li>\n              </ul>\n            </li>\n\n            {/* Banners */}\n            <li\n              className={isActive(\"/admin/banners\") ? \"active active-sub\" : \"\"}\n            >\n              <a href=\"/admin/banners\">\n                <i className=\"fa fa-picture-o\"></i>\n                <span className=\"title\">Banners</span>\n              </a>\n            </li>\n\n            {/* Referral */}\n            <li\n              className={isActive(\"/admin/referral\") ? \"active active-sub\" : \"\"}\n            >\n              <a href=\"/admin/referral\">\n                <i className=\"fa fa-picture-o\"></i>\n                <span className=\"title\">Referral</span>\n              </a>\n            </li>\n\n            {/* Contact Us */}\n            <li className={isActive(\"/admin/contact-us\") ? \"active\" : \"\"}>\n              <a href=\"/admin/contact-us\">\n                <i className=\"fa fa-envelope-open-o\"></i>\n                <span className=\"title\">Contact Us</span>\n              </a>\n            </li>\n\n            {/* Notification */}\n            <li\n              className={`treeview ${\n                isTreeviewOpen(\"notifications\") ? \"active menu-open\" : \"\"\n              }`}\n            >\n              <a\n                href=\"#\"\n                onClick={(e) => {\n                  e.preventDefault();\n                  toggleTreeview(\"notifications\");\n                }}\n              >\n                <i className=\"fa fa-bell\"></i>\n                <span className=\"title\">Notification</span>\n                <span className=\"pull-right-container\">\n                  <i\n                    className={`fa fa-angle-left pull-right ${\n                      isTreeviewOpen(\"notifications\") ? \"fa-angle-down\" : \"\"\n                    }`}\n                  ></i>\n                </span>\n              </a>\n              <ul\n                className=\"treeview-menu\"\n                style={{\n                  display: isTreeviewOpen(\"notifications\") ? \"block\" : \"none\",\n                }}\n              >\n                <li\n                  className={\n                    isActive(\"/admin/notifications\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/notifications\">\n                    <i className=\"fa fa-bell\"></i>\n                    <span className=\"title\">Notifications</span>\n                  </a>\n                </li>\n              </ul>\n            </li>\n\n            {/* Reports */}\n            <li className={isActive(\"/admin/reports\") ? \"active\" : \"\"}>\n              <a href=\"/admin/reports\">\n                <i className=\"fa fa-bar-chart\"></i>\n                <span className=\"title\">Reports</span>\n              </a>\n            </li>\n\n            {/* CMS */}\n            <li\n              className={`treeview ${\n                isTreeviewOpen(\"cms\") ? \"active menu-open\" : \"\"\n              }`}\n            >\n              <a\n                href=\"#\"\n                onClick={(e) => {\n                  e.preventDefault();\n                  toggleTreeview(\"cms\");\n                }}\n              >\n                <i className=\"fa fa-graduation-cap\"></i>\n                <span className=\"title\">CMS</span>\n                <span className=\"pull-right-container\">\n                  <i\n                    className={`fa fa-angle-left pull-right ${\n                      isTreeviewOpen(\"cms\") ? \"fa-angle-down\" : \"\"\n                    }`}\n                  ></i>\n                </span>\n              </a>\n              <ul\n                className=\"treeview-menu\"\n                style={{\n                  display: isTreeviewOpen(\"cms\") ? \"block\" : \"none\",\n                }}\n              >\n                <li\n                  className={\n                    isActive(\"/admin/about-us\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/about-us\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">About Us</span>\n                  </a>\n                </li>\n                <li\n                  className={\n                    isActive(\"/admin/privacy-policy\") ? \"active active-sub\" : \"\"\n                  }\n                >\n                  <a href=\"/admin/privacy-policy\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">Privacy Policy</span>\n                  </a>\n                </li>\n                <li\n                  className={\n                    isActive(\"/admin/terms-condition\")\n                      ? \"active active-sub\"\n                      : \"\"\n                  }\n                >\n                  <a href=\"/admin/terms-condition\">\n                    <i className=\"fa fa-graduation-cap\"></i>\n                    <span className=\"title\">Terms & Condition</span>\n                  </a>\n                </li>\n              </ul>\n            </li>\n          </ul>\n        </section>\n      </aside>\n\n      {/* Content Wrapper. Contains page content */}\n      <div className=\"content-wrapper\">\n        {/* Main content */}\n        <section className=\"content\">\n          {title && (\n            <h3\n              className=\"page-title\"\n              style={{\n                margin: \"20px 0\",\n                color: \"#444444\",\n                fontSize: \"24px\",\n                fontWeight: 400,\n              }}\n            >\n              {title}\n            </h3>\n          )}\n\n          <div className=\"row\">\n            <div className=\"col-md-12\">{children}</div>\n          </div>\n        </section>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUe,SAAS,eAAe,EACrC,QAAQ,EACR,KAAK,EACe;IACpB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,MAAM,sBAAsB;QAC1B,oBAAoB,CAAC;IACvB;IAEA,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,WAAW,CAAC;QAChB,OAAO,aAAa;IACtB;IAEA,MAAM,iBAAiB,CAAC;QACtB,iBAAiB,CAAC,OAChB,KAAK,QAAQ,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,OAAS,SAAS,MAAM;mBAAI;gBAAM;aAAG;IAE1E;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,cAAc,QAAQ,CAAC;IAChC;IAEA,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IACE,SAAS,QAAQ,CAAC,yBAClB,SAAS,QAAQ,CAAC,mBAClB,SAAS,QAAQ,CAAC,iBAClB;YACA,iBAAiB,CAAC,OAAS;uBAAI;oBAAM;iBAAkB;QACzD;QACA,IACE,SAAS,QAAQ,CAAC,qBAClB,SAAS,QAAQ,CAAC,oBAClB;YACA,iBAAiB,CAAC,OAAS;uBAAI;oBAAM;iBAAoB;QAC3D;QACA,IACE,SAAS,QAAQ,CAAC,wBAClB,SAAS,QAAQ,CAAC,mBAClB;YACA,iBAAiB,CAAC,OAAS;uBAAI;oBAAM;iBAAkB;QACzD;QACA,IAAI,SAAS,QAAQ,CAAC,mBAAmB;YACvC,iBAAiB,CAAC,OAAS;uBAAI;oBAAM;iBAAU;QACjD;QACA,IAAI,SAAS,QAAQ,CAAC,uBAAuB;YAC3C,iBAAiB,CAAC,OAAS;uBAAI;oBAAM;iBAAc;QACrD;QACA,IAAI,SAAS,QAAQ,CAAC,wBAAwB;YAC5C,iBAAiB,CAAC,OAAS;uBAAI;oBAAM;iBAAe;QACtD;QACA,IAAI,SAAS,QAAQ,CAAC,yBAAyB;YAC7C,iBAAiB,CAAC,OAAS;uBAAI;oBAAM;iBAAgB;QACvD;QACA,IACE,SAAS,QAAQ,CAAC,sBAClB,SAAS,QAAQ,CAAC,4BAClB,SAAS,QAAQ,CAAC,2BAClB;YACA,iBAAiB,CAAC,OAAS;uBAAI;oBAAM;iBAAM;QAC7C;IACF,GAAG;QAAC;KAAS;IAEb,qBACE,8OAAC;QACC,WAAW,CAAC,kBAAkB,EAC5B,mBAAmB,qBAAqB,IACxC;;0BAGF,8OAAC;gBAAO,WAAU;;kCAEhB,8OAAC;wBACC,MAAK;wBACL,WAAU;wBACV,OAAO;4BAAE,UAAU;4BAAQ,OAAO;4BAAQ,gBAAgB;wBAAO;;0CAEjE,8OAAC;0CAAE;;;;;;4BAAS;;;;;;;kCAId,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,SAAS;4BAAS,SAAS;wBAAE;kCAGtC,cAAA,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS,CAAC;gCACR,EAAE,cAAc;gCAChB;4BACF;4BACA,MAAK;;8CAEL,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;oCAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMtB,8OAAC;gBAAM,WAAU;0BAEf,cAAA,8OAAC;oBAAQ,WAAU;8BACjB,cAAA,8OAAC;wBAAG,WAAU;;0CAEZ,8OAAC;gCAAG,WAAW,SAAS,sBAAsB,WAAW;0CACvD,cAAA,8OAAC;oCAAE,MAAK;;sDACN,8OAAC;4CAAE,WAAU;;;;;;sDACb,8OAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,8OAAC;gCACC,WAAW,CAAC,SAAS,EACnB,eAAe,qBAAqB,qBAAqB,IACzD;;kDAEF,8OAAC;wCACC,MAAK;wCACL,SAAS,CAAC;4CACR,EAAE,cAAc;4CAChB,eAAe;wCACjB;;0DAEA,8OAAC;gDAAE,WAAU;;;;;;0DACb,8OAAC;gDAAK,WAAU;0DAAQ;;;;;;0DACxB,8OAAC;gDAAK,WAAU;0DACd,cAAA,8OAAC;oDACC,WAAW,CAAC,4BAA4B,EACtC,eAAe,qBAAqB,kBAAkB,IACtD;;;;;;;;;;;;;;;;;kDAIR,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,SAAS,eAAe,qBAAqB,UAAU;wCACzD;;0DAEA,8OAAC;gDACC,WACE,SAAS,wBAAwB,sBAAsB;0DAGzD,cAAA,8OAAC;oDAAE,MAAK;;sEACN,8OAAC;4DAAE,WAAU;;;;;;sEACb,8OAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;0DAG5B,8OAAC;gDACC,WACE,SAAS,kBAAkB,sBAAsB;0DAGnD,cAAA,8OAAC;oDAAE,MAAK;;sEACN,8OAAC;4DAAE,WAAU;;;;;;sEACb,8OAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;0DAG5B,8OAAC;gDACC,WACE,SAAS,kBAAkB,sBAAsB;0DAGnD,cAAA,8OAAC;oDAAE,MAAK;;sEACN,8OAAC;4DAAE,WAAU;;;;;;sEACb,8OAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhC,8OAAC;gCAAG,WAAW,SAAS,iBAAiB,sBAAsB;0CAC7D,cAAA,8OAAC;oCAAE,MAAK;;sDACN,8OAAC;4CAAE,WAAU;;;;;;sDACb,8OAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,8OAAC;gCACC,WAAW,SAAS,qBAAqB,sBAAsB;0CAE/D,cAAA,8OAAC;oCAAE,MAAK;;sDACN,8OAAC;4CAAE,WAAU;;;;;;sDACb,8OAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,8OAAC;gCACC,WAAW,SAAS,qBAAqB,sBAAsB;0CAE/D,cAAA,8OAAC;oCAAE,MAAK;;sDACN,8OAAC;4CAAE,WAAU;;;;;;sDACb,8OAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,8OAAC;gCACC,WAAW,CAAC,SAAS,EACnB,eAAe,uBAAuB,qBAAqB,IAC3D;;kDAEF,8OAAC;wCACC,MAAK;wCACL,SAAS,CAAC;4CACR,EAAE,cAAc;4CAChB,eAAe;wCACjB;;0DAEA,8OAAC;gDAAE,WAAU;;;;;;0DACb,8OAAC;gDAAK,WAAU;0DAAQ;;;;;;0DACxB,8OAAC;gDAAK,WAAU;0DACd,cAAA,8OAAC;oDACC,WAAW,CAAC,4BAA4B,EACtC,eAAe,uBAAuB,kBAAkB,IACxD;;;;;;;;;;;;;;;;;kDAIR,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,SAAS,eAAe,uBACpB,UACA;wCACN;;0DAEA,8OAAC;gDACC,WACE,SAAS,oBAAoB,sBAAsB;0DAGrD,cAAA,8OAAC;oDAAE,MAAK;;sEACN,8OAAC;4DAAE,WAAU;;;;;;sEACb,8OAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;0DAG5B,8OAAC;gDACC,WACE,SAAS,qBAAqB,sBAAsB;0DAGtD,cAAA,8OAAC;oDAAE,MAAK;;sEACN,8OAAC;4DAAE,WAAU;;;;;;sEACb,8OAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhC,8OAAC;gCACC,WAAW,CAAC,SAAS,EACnB,eAAe,qBAAqB,qBAAqB,IACzD;;kDAEF,8OAAC;wCACC,MAAK;wCACL,SAAS,CAAC;4CACR,EAAE,cAAc;4CAChB,eAAe;wCACjB;;0DAEA,8OAAC;gDAAE,WAAU;;;;;;0DACb,8OAAC;gDAAK,WAAU;0DAAQ;;;;;;0DACxB,8OAAC;gDAAK,WAAU;0DACd,cAAA,8OAAC;oDACC,WAAW,CAAC,4BAA4B,EACtC,eAAe,qBAAqB,kBAAkB,IACtD;;;;;;;;;;;;;;;;;kDAIR,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,SAAS,eAAe,qBAAqB,UAAU;wCACzD;;0DAEA,8OAAC;gDACC,WACE,SAAS,uBAAuB,sBAAsB;0DAGxD,cAAA,8OAAC;oDAAE,MAAK;;sEACN,8OAAC;4DAAE,WAAU;;;;;;sEACb,8OAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;0DAG5B,8OAAC;gDACC,WACE,SAAS,oBAAoB,sBAAsB;0DAGrD,cAAA,8OAAC;oDAAE,MAAK;;sEACN,8OAAC;4DAAE,WAAU;;;;;;sEACb,8OAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhC,8OAAC;gCACC,WACE,SAAS,0BAA0B,sBAAsB;0CAG3D,cAAA,8OAAC;oCAAE,MAAK;;sDACN,8OAAC;4CAAE,WAAU;;;;;;sDACb,8OAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,8OAAC;gCACC,WACE,SAAS,uBAAuB,sBAAsB;0CAGxD,cAAA,8OAAC;oCAAE,MAAK;;sDACN,8OAAC;4CAAE,WAAU;;;;;;sDACb,8OAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,8OAAC;gCACC,WACE,SAAS,+BAA+B,sBAAsB;0CAGhE,cAAA,8OAAC;oCAAE,MAAK;;sDACN,8OAAC;4CAAE,WAAU;;;;;;sDACb,8OAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,8OAAC;gCACC,WACE,SAAS,mCACL,sBACA;0CAGN,cAAA,8OAAC;oCAAE,MAAK;;sDACN,8OAAC;4CAAE,WAAU;;;;;;sDACb,8OAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,8OAAC;gCACC,WAAW,SAAS,mBAAmB,sBAAsB;0CAE7D,cAAA,8OAAC;oCAAE,MAAK;;sDACN,8OAAC;4CAAE,WAAU;;;;;;sDACb,8OAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,8OAAC;gCACC,WACE,SAAS,4BAA4B,sBAAsB;0CAG7D,cAAA,8OAAC;oCAAE,MAAK;;sDACN,8OAAC;4CAAE,WAAU;;;;;;sDACb,8OAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,8OAAC;gCACC,WAAW,CAAC,SAAS,EACnB,eAAe,aAAa,qBAAqB,IACjD;;kDAEF,8OAAC;wCACC,MAAK;wCACL,SAAS,CAAC;4CACR,EAAE,cAAc;4CAChB,eAAe;wCACjB;;0DAEA,8OAAC;gDAAE,WAAU;;;;;;0DACb,8OAAC;gDAAK,WAAU;0DAAQ;;;;;;0DACxB,8OAAC;gDAAK,WAAU;0DACd,cAAA,8OAAC;oDACC,WAAW,CAAC,4BAA4B,EACtC,eAAe,aAAa,kBAAkB,IAC9C;;;;;;;;;;;;;;;;;kDAIR,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,SAAS,eAAe,aAAa,UAAU;wCACjD;kDAEA,cAAA,8OAAC;4CACC,WACE,SAAS,oBAAoB,sBAAsB;sDAGrD,cAAA,8OAAC;gDAAE,MAAK;;kEACN,8OAAC;wDAAE,WAAU;;;;;;kEACb,8OAAC;wDAAK,WAAU;kEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhC,8OAAC;gCACC,WAAW,CAAC,SAAS,EACnB,eAAe,iBAAiB,qBAAqB,IACrD;;kDAEF,8OAAC;wCACC,MAAK;wCACL,SAAS,CAAC;4CACR,EAAE,cAAc;4CAChB,eAAe;wCACjB;;0DAEA,8OAAC;gDAAE,WAAU;;;;;;0DACb,8OAAC;gDAAK,WAAU;0DAAQ;;;;;;0DACxB,8OAAC;gDAAK,WAAU;0DACd,cAAA,8OAAC;oDACC,WAAW,CAAC,4BAA4B,EACtC,eAAe,iBAAiB,kBAAkB,IAClD;;;;;;;;;;;;;;;;;kDAIR,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,SAAS,eAAe,iBAAiB,UAAU;wCACrD;kDAEA,cAAA,8OAAC;4CACC,WACE,SAAS,wBAAwB,sBAAsB;sDAGzD,cAAA,8OAAC;gDAAE,MAAK;;kEACN,8OAAC;wDAAE,WAAU;;;;;;kEACb,8OAAC;wDAAK,WAAU;kEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhC,8OAAC;gCACC,WAAW,CAAC,SAAS,EACnB,eAAe,kBAAkB,qBAAqB,IACtD;;kDAEF,8OAAC;wCACC,MAAK;wCACL,SAAS,CAAC;4CACR,EAAE,cAAc;4CAChB,eAAe;wCACjB;;0DAEA,8OAAC;gDAAE,WAAU;;;;;;0DACb,8OAAC;gDAAK,WAAU;0DAAQ;;;;;;0DACxB,8OAAC;gDAAK,WAAU;0DACd,cAAA,8OAAC;oDACC,WAAW,CAAC,4BAA4B,EACtC,eAAe,kBAAkB,kBAAkB,IACnD;;;;;;;;;;;;;;;;;kDAIR,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,SAAS,eAAe,kBAAkB,UAAU;wCACtD;kDAEA,cAAA,8OAAC;4CACC,WACE,SAAS,yBAAyB,sBAAsB;sDAG1D,cAAA,8OAAC;gDAAE,MAAK;;kEACN,8OAAC;wDAAE,WAAU;;;;;;kEACb,8OAAC;wDAAK,WAAU;kEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhC,8OAAC;gCACC,WAAW,SAAS,oBAAoB,sBAAsB;0CAE9D,cAAA,8OAAC;oCAAE,MAAK;;sDACN,8OAAC;4CAAE,WAAU;;;;;;sDACb,8OAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,8OAAC;gCACC,WAAW,SAAS,qBAAqB,sBAAsB;0CAE/D,cAAA,8OAAC;oCAAE,MAAK;;sDACN,8OAAC;4CAAE,WAAU;;;;;;sDACb,8OAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,8OAAC;gCAAG,WAAW,SAAS,uBAAuB,WAAW;0CACxD,cAAA,8OAAC;oCAAE,MAAK;;sDACN,8OAAC;4CAAE,WAAU;;;;;;sDACb,8OAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,8OAAC;gCACC,WAAW,CAAC,SAAS,EACnB,eAAe,mBAAmB,qBAAqB,IACvD;;kDAEF,8OAAC;wCACC,MAAK;wCACL,SAAS,CAAC;4CACR,EAAE,cAAc;4CAChB,eAAe;wCACjB;;0DAEA,8OAAC;gDAAE,WAAU;;;;;;0DACb,8OAAC;gDAAK,WAAU;0DAAQ;;;;;;0DACxB,8OAAC;gDAAK,WAAU;0DACd,cAAA,8OAAC;oDACC,WAAW,CAAC,4BAA4B,EACtC,eAAe,mBAAmB,kBAAkB,IACpD;;;;;;;;;;;;;;;;;kDAIR,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,SAAS,eAAe,mBAAmB,UAAU;wCACvD;kDAEA,cAAA,8OAAC;4CACC,WACE,SAAS,0BAA0B,sBAAsB;sDAG3D,cAAA,8OAAC;gDAAE,MAAK;;kEACN,8OAAC;wDAAE,WAAU;;;;;;kEACb,8OAAC;wDAAK,WAAU;kEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhC,8OAAC;gCAAG,WAAW,SAAS,oBAAoB,WAAW;0CACrD,cAAA,8OAAC;oCAAE,MAAK;;sDACN,8OAAC;4CAAE,WAAU;;;;;;sDACb,8OAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;0CAK5B,8OAAC;gCACC,WAAW,CAAC,SAAS,EACnB,eAAe,SAAS,qBAAqB,IAC7C;;kDAEF,8OAAC;wCACC,MAAK;wCACL,SAAS,CAAC;4CACR,EAAE,cAAc;4CAChB,eAAe;wCACjB;;0DAEA,8OAAC;gDAAE,WAAU;;;;;;0DACb,8OAAC;gDAAK,WAAU;0DAAQ;;;;;;0DACxB,8OAAC;gDAAK,WAAU;0DACd,cAAA,8OAAC;oDACC,WAAW,CAAC,4BAA4B,EACtC,eAAe,SAAS,kBAAkB,IAC1C;;;;;;;;;;;;;;;;;kDAIR,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,SAAS,eAAe,SAAS,UAAU;wCAC7C;;0DAEA,8OAAC;gDACC,WACE,SAAS,qBAAqB,sBAAsB;0DAGtD,cAAA,8OAAC;oDAAE,MAAK;;sEACN,8OAAC;4DAAE,WAAU;;;;;;sEACb,8OAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;0DAG5B,8OAAC;gDACC,WACE,SAAS,2BAA2B,sBAAsB;0DAG5D,cAAA,8OAAC;oDAAE,MAAK;;sEACN,8OAAC;4DAAE,WAAU;;;;;;sEACb,8OAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;0DAG5B,8OAAC;gDACC,WACE,SAAS,4BACL,sBACA;0DAGN,cAAA,8OAAC;oDAAE,MAAK;;sEACN,8OAAC;4DAAE,WAAU;;;;;;sEACb,8OAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUtC,8OAAC;gBAAI,WAAU;0BAEb,cAAA,8OAAC;oBAAQ,WAAU;;wBAChB,uBACC,8OAAC;4BACC,WAAU;4BACV,OAAO;gCACL,QAAQ;gCACR,OAAO;gCACP,UAAU;gCACV,YAAY;4BACd;sCAEC;;;;;;sCAIL,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxC", "debugId": null}}, {"offset": {"line": 1654, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/projects/Laravel/Admin-main/admin-nextjs/src/app/admin/roles/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport AdminLTELayout from '@/components/layout/AdminLTELayout';\n\n// Dummy roles data that matches Laravel structure\nconst roles = [\n  {\n    id: 1,\n    name: 'Admin',\n    permissions: ['User Management', 'Permission', 'Role', 'Teacher Management', 'Student Management', 'Dashboard']\n  },\n  {\n    id: 2,\n    name: 'Teacher',\n    permissions: ['Student Management', 'Assignments', 'Quiz', 'Live Classes', 'Dashboard']\n  },\n  {\n    id: 3,\n    name: 'Student',\n    permissions: ['Dashboard', 'Assignments', 'Quiz']\n  },\n  {\n    id: 4,\n    name: 'Course Builder',\n    permissions: ['Category', 'Assignments', 'Quiz', 'Dashboard']\n  },\n  {\n    id: 5,\n    name: 'Director',\n    permissions: ['Teacher Management', 'Student Management', 'Reports', 'Dashboard']\n  }\n];\n\nexport default function RolesPage() {\n  return (\n    <AdminLTELayout>\n      <h3 className=\"page-title\">Roles</h3>\n      <p>\n        <a href=\"/admin/roles/create\" className=\"btn btn-success\">Add New</a>\n      </p>\n\n      <div className=\"panel panel-default\">\n        <div style={{ fontSize: '20px' }} className=\"panel-heading\">\n          List\n        </div>\n\n        <div className=\"panel-body scroll-table table-responsive\">\n          <table className={`table table-bordered table-striped ${roles.length > 0 ? 'datatable1' : ''} dt-select`}>\n            <thead>\n              <tr>\n                <th style={{ fontSize: '15px' }}>Sr.No</th>\n                <th style={{ fontSize: '15px' }}>Name</th>\n                <th style={{ fontSize: '15px' }}>Permissions</th>\n                <th style={{ fontSize: '15px' }}>Action</th>\n              </tr>\n            </thead>\n            \n            <tbody>\n              {roles.length > 0 ? (\n                roles.map((role, index) => (\n                  <tr key={role.id} data-entry-id={role.id}>\n                    <td>{index + 1}</td>\n                    <td>{role.name}</td>\n                    <td>\n                      {role.permissions.map((permission, permIndex) => (\n                        <span key={permIndex} className=\"label label-info label-many\" style={{ marginRight: '5px', marginBottom: '2px', display: 'inline-block' }}>\n                          {permission}\n                        </span>\n                      ))}\n                    </td>\n                    <td>\n                      <a \n                        style={{ fontSize: '15px' }} \n                        href={`/admin/roles/edit/${role.id}`} \n                        className=\"btn btn-primary btn-sm\"\n                      >\n                        Edit\n                      </a>\n                    </td>\n                  </tr>\n                ))\n              ) : (\n                <tr>\n                  <td colSpan={4}>No entries in table</td>\n                </tr>\n              )}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </AdminLTELayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,kDAAkD;AAClD,MAAM,QAAQ;IACZ;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YAAC;YAAmB;YAAc;YAAQ;YAAsB;YAAsB;SAAY;IACjH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YAAC;YAAsB;YAAe;YAAQ;YAAgB;SAAY;IACzF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YAAC;YAAa;YAAe;SAAO;IACnD;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YAAC;YAAY;YAAe;YAAQ;SAAY;IAC/D;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YAAC;YAAsB;YAAsB;YAAW;SAAY;IACnF;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC,8IAAA,CAAA,UAAc;;0BACb,8OAAC;gBAAG,WAAU;0BAAa;;;;;;0BAC3B,8OAAC;0BACC,cAAA,8OAAC;oBAAE,MAAK;oBAAsB,WAAU;8BAAkB;;;;;;;;;;;0BAG5D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,OAAO;4BAAE,UAAU;wBAAO;wBAAG,WAAU;kCAAgB;;;;;;kCAI5D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAM,WAAW,CAAC,mCAAmC,EAAE,MAAM,MAAM,GAAG,IAAI,eAAe,GAAG,UAAU,CAAC;;8CACtG,8OAAC;8CACC,cAAA,8OAAC;;0DACC,8OAAC;gDAAG,OAAO;oDAAE,UAAU;gDAAO;0DAAG;;;;;;0DACjC,8OAAC;gDAAG,OAAO;oDAAE,UAAU;gDAAO;0DAAG;;;;;;0DACjC,8OAAC;gDAAG,OAAO;oDAAE,UAAU;gDAAO;0DAAG;;;;;;0DACjC,8OAAC;gDAAG,OAAO;oDAAE,UAAU;gDAAO;0DAAG;;;;;;;;;;;;;;;;;8CAIrC,8OAAC;8CACE,MAAM,MAAM,GAAG,IACd,MAAM,GAAG,CAAC,CAAC,MAAM,sBACf,8OAAC;4CAAiB,iBAAe,KAAK,EAAE;;8DACtC,8OAAC;8DAAI,QAAQ;;;;;;8DACb,8OAAC;8DAAI,KAAK,IAAI;;;;;;8DACd,8OAAC;8DACE,KAAK,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY,0BACjC,8OAAC;4DAAqB,WAAU;4DAA8B,OAAO;gEAAE,aAAa;gEAAO,cAAc;gEAAO,SAAS;4DAAe;sEACrI;2DADQ;;;;;;;;;;8DAKf,8OAAC;8DACC,cAAA,8OAAC;wDACC,OAAO;4DAAE,UAAU;wDAAO;wDAC1B,MAAM,CAAC,kBAAkB,EAAE,KAAK,EAAE,EAAE;wDACpC,WAAU;kEACX;;;;;;;;;;;;2CAfI,KAAK,EAAE;;;;kEAsBlB,8OAAC;kDACC,cAAA,8OAAC;4CAAG,SAAS;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlC", "debugId": null}}]}