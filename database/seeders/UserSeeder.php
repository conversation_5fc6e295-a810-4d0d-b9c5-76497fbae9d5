<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;

class UserSeeder extends Seeder
{
    public function run()
    {
        $user = User::create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'status' => 1,
            'role_id' => 1
        ]);

        // Assign admin role
        $user->assignRole('admin');
    }
} 